app.saasId=kiki

############### ignite éç½® ###############
namespace=kikitrade
environment.local=true
environment.namespace=kikitrade-dev

###########################zookeeper###################################
zookeeper.url=127.0.0.1:2181

############### dubbo éç½® ###############
dubbo.consumer.group=kktd
dubbo.provider.group=kktd

################ MySQL Spring Config ##############
jdbc.ds.url=*********************************************************************************************************************************************************************************************************************************************************************************************************************
jdbc.username=root
jdbc.password=Qwer!234

#jdbc.ds.url=***********************************************************************************************************************************************************************************************************************************************************************
#jdbc.username=root
#jdbc.password=root1234

########################### dtm config ###################################
activity.dtm.participant=kactivity
activity.dtm.serverAddress=api.dev.dipbit.xyz
activity.dtm.serverPort=20880
activity.dtm.local=true

########################### tablestore æ¯è´¦æ·çº§å«çak skï¼ ä¸ossï¼åªåå«kikiçossææåè½ï¼ å¬ç¨ak sk ###################################
tableStore.endpoint=https://KikiTradeTest.ap-southeast-1.ots.aliyuncs.com
tableStore.instanceName=KikiTradeTest

########ons env###########
ons.address=http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
ons.env=_DEV
ons.tag=dev

#################elasticjob config ##################
elasticjob.reg-center.server-lists=@zookeeper.url@

#æ´»å¨è°åº¦å¤±è´¥Job æ£æ¥é¢çï¼åæºæ¨¡å¼ï¼
activity.fail.check.time=0 0/1 * * * ?
#æ´»å¨ç¶æå®æ¶æ´æ°Job è°ç¨æ¶é´ï¼åæºæ¨¡å¼ï¼
activity.status.check.time=0 0/1 * * * ?

#spring çº¿ä¸ç¯å¢å¯å¨, é»è®¤ä¸ºçº¿ä¸ç¯å¢ï¼ éåéç½®èªå¨æ¿æ¢çæ¹å¼
activity.server.port=8081

#å®æ¶ä»»å¡åæºï¼éç¾¤æ å¿
#activity.schedule.single=false
#æ´»å¨åå¥åçè°åº¦é»è®¤ä¸æ¬¡è°åº¦ç¬æ°ï¼åæºæ¨¡å¼ï¼
activity.dispatch.limit=100
#æ´»å¨åå¥åçè°åº¦ååurl
activity.dispatch.notifyurl=http://x-activity.beta.svc.kiki.local:8080/activity/jobexecute/

#ååç¬æ°ï¼éè¦èèHttpgetè¯·æ±æ¥æé¿åº¦ï¼è°åº¦èç¹æ¯æ¬¡åæ§è¡èç¹åéç¬æ°ï¼
#activity.page.num=100
#æ´»å¨å®æ¶åå¥è°ç¨æ¶é´ï¼åæºæ¨¡å¼ï¼
#activity.time.reward.time=0 0/10 * * * ?

########################### OSS ###################################
app.oss.rolename=ECSDipbitRoleBeta
app.oss.endpoint=http://oss-ap-southeast-1.aliyuncs.com
app.oss.bucket.private=kiki-dev
app.oss.path.activity=activity
app.oss.activity.kolExtraReward.key=KolExtraReward.csv
app.oss.accesskey.id=LTAI5t61acrmFKw5SnkGQyYg
app.oss.accesskey.secret=******************************
app.oss.bucket.referer=http://admin.dev.dipbit.xyz/

################ redis Config ##############
#redis.url=exkikidevo.redis.singapore.rds.aliyuncs.com
redis.url=127.0.0.1
redis.port=6379
redis.password=
redis.dbIndex=5
redis.maxTotal=32

zipkin.host=jaeger-collector.beta.svc.kiki.local

## log alarm owner config
log.alarm.owner.config={"18629360912":"spot_trade,margin_trade,copy_trade,activity,member", "13335395197":"market,trader"}

manage.grpc.port=30880

kiki.odps.accessId=LTAIZr2b7AyF0bNC
kiki.odps.accessKey=******************************
kiki.odps.endPoint=http://service.ap-southeast-1.maxcompute.aliyun-inc.com/api
kiki.odps.project=dbit_beta
