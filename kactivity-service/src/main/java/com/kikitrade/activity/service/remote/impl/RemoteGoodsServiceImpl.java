package com.kikitrade.activity.service.remote.impl;

import cn.hutool.core.bean.BeanUtil;
import com.kikitrade.activity.api.RemoteGoodsService;
import com.kikitrade.activity.api.model.response.GoodsDetailResponse;
import com.kikitrade.activity.api.model.response.GoodsResponse;
import com.kikitrade.activity.dal.tablestore.model.Goods;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.goods.GiftPackService;
import com.kikitrade.activity.service.goods.GoodsService;
import com.kikitrade.framework.common.model.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/12/28 11:43
 */
@DubboService
@Slf4j
public class RemoteGoodsServiceImpl implements RemoteGoodsService {

    @Resource
    private GoodsService goodsService;

    @Resource
    private GiftPackService giftPackService;

    @Override
    public List<GoodsResponse> goodsList(int offset, int limit, String saasId, String exclude) {
        log.info("goodsList request:{},{},{}", offset, limit, saasId);
        Page<Goods> page = goodsService.findAll(offset, limit, saasId, exclude);
        log.info("goodsList response:{}", page);
        if(page == null || CollectionUtils.isEmpty(page.getRows())){
            return null;
        }
        List<Goods> goodses = page.getRows();
        List<GoodsResponse> responses = new ArrayList<>();
        for(Goods goods : goodses){
            GoodsResponse goodsResponse = BeanUtil.copyProperties(goods, GoodsResponse.class);
            if(goods.getEndTime() < System.currentTimeMillis()){
                goodsResponse.setStatus(ActivityConstant.GoodsStatus.EXPIRE.getCode());
            }
            responses.add(goodsResponse);
        }
        return responses;
    }

    @Override
    public GoodsDetailResponse goods(String goodsId) {
        log.info("查询商品详情: goodsId={}", goodsId);

        if (StringUtils.isBlank(goodsId)) {
            log.warn("商品ID为空");
            return null;
        }

        try {
            // 查询商品基本信息
            Goods goods = goodsService.findById(goodsId);
            if (goods == null) {
                log.warn("商品不存在: goodsId={}", goodsId);
                return null;
            }

            // 复制基本属性
            GoodsDetailResponse goodsResponse = BeanUtil.copyProperties(goods, GoodsDetailResponse.class);

            // 处理图片信息
            Map<String, String> imageMap = goods.getImageMap();
            goodsResponse.setImageMap(imageMap != null ? imageMap : new HashMap<>());

            // 检查商品是否过期
            if (goods.getEndTime() < System.currentTimeMillis()) {
                goodsResponse.setStatus(ActivityConstant.GoodsStatus.EXPIRE.getCode());
            }

            // 如果是礼包类型，查询礼包内物品信息
            if (giftPackService.isGiftPack(goods.getType())) {
                log.debug("处理礼包商品: goodsId={}, type={}", goodsId, goods.getType());
                List<GoodsDetailResponse.GiftPackItem> giftPackItems =
                    giftPackService.getGiftPackItems(goods.getOutId(), goods.getSaasId());
                goodsResponse.setGiftPackItems(giftPackItems);
                log.debug("礼包物品数量: {}", giftPackItems.size());
            }

            log.info("商品详情查询完成: goodsId={}, type={}", goodsId, goods.getType());
            return goodsResponse;

        } catch (Exception e) {
            log.error("查询商品详情异常: goodsId={}", goodsId, e);
            return null;
        }
    }

    @Override
    public List<GoodsResponse> findBySceneCode(String sceneCode) {
        log.info("根据场景代码查询商品列表: sceneCode={}", sceneCode);

        if (StringUtils.isBlank(sceneCode)) {
            log.warn("场景代码为空");
            return new ArrayList<>();
        }

        try {
            // 查询商品列表
            List<Goods> goodsList = goodsService.findBySceneCode(sceneCode);

            if (CollectionUtils.isEmpty(goodsList)) {
                log.info("未找到商品: sceneCode={}", sceneCode);
                return new ArrayList<>();
            }

            // 过滤有效商品并按order字段排序
            List<Goods> validGoods = goodsList.stream()
                .filter(goods -> goods.getStatus() != null &&
                    goods.getStatus().equals(ActivityConstant.GoodsStatus.ONLINE.getCode()))
                .sorted(Comparator.comparing(goods ->
                    goods.getOrder() != null ? goods.getOrder() : Integer.MAX_VALUE))
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(validGoods)) {
                log.info("未找到有效商品: sceneCode={}", sceneCode);
                return new ArrayList<>();
            }

            // 提取礼包类型商品的ID，用于批量查询礼包信息
            List<String> giftPackIds = validGoods.stream()
                .filter(goods -> giftPackService.isGiftPack(goods.getType()))
                .map(Goods::getOutId)
                .collect(Collectors.toList());

            // 批量查询礼包信息，避免N+1问题
            Map<String, List<GoodsDetailResponse.GiftPackItem>> giftPackItemsMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(giftPackIds)) {
                // 获取第一个商品的saasId（假设同一场景下的商品属于同一saas）
                String saasId = validGoods.get(0).getSaasId();
                giftPackItemsMap = giftPackService.batchGetGiftPackItems(giftPackIds, saasId);
                log.debug("批量查询礼包信息完成: giftPackCount={}", giftPackItemsMap.size());
            }

            // 转换为响应对象
            List<GoodsResponse> responses = new ArrayList<>();
            for (Goods goods : validGoods) {
                GoodsResponse goodsResponse = BeanUtil.copyProperties(goods, GoodsResponse.class);

                // 检查商品是否过期
                if (goods.getEndTime() < System.currentTimeMillis()) {
                    goodsResponse.setStatus(ActivityConstant.GoodsStatus.EXPIRE.getCode());
                }

                // 如果是礼包类型，设置礼包物品信息
                if (giftPackService.isGiftPack(goods.getType())) {
                    List<GoodsDetailResponse.GiftPackItem> giftPackItems =
                        giftPackItemsMap.get(goods.getGoodsId());
                    if (giftPackItems != null) {
                        // 转换为GoodsResponse的GiftPackItem格式
                        List<GoodsResponse.GiftPackItem> responseItems = giftPackItems.stream()
                            .map(this::convertToGoodsResponseItem)
                            .collect(Collectors.toList());
                        goodsResponse.setGiftPackItems(responseItems);
                    }
                }

                responses.add(goodsResponse);
            }

            log.info("场景商品查询完成: sceneCode={}, totalCount={}, validCount={}",
                sceneCode, goodsList.size(), responses.size());
            return responses;

        } catch (Exception e) {
            log.error("根据场景代码查询商品列表异常: sceneCode={}", sceneCode, e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换礼包物品格式
     * 将GoodsDetailResponse.GiftPackItem转换为GoodsResponse.GiftPackItem
     */
    private GoodsResponse.GiftPackItem convertToGoodsResponseItem(GoodsDetailResponse.GiftPackItem detailItem) {
        GoodsResponse.GiftPackItem responseItem = new GoodsResponse.GiftPackItem();
        responseItem.setItemId(detailItem.getItemId());
        responseItem.setItemName(detailItem.getItemName());
        responseItem.setItemType(detailItem.getItemType());
        responseItem.setQuantity(detailItem.getQuantity());
        responseItem.setDescription(detailItem.getDescription());
        responseItem.setIconUrl(detailItem.getIconUrl());
        return responseItem;
    }
}
