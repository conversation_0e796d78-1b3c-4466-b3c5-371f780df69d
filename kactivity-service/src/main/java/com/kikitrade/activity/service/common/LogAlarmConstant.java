package com.kikitrade.activity.service.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/11/16 14:47
 **/

public class LogAlarmConstant {

    public static final String TRACE_ID = "traceId";
    public static final String SPAN_ID = "spanId";
    public static final String CUSTOMER_ID = "customerId";
    public static final String OWNER = "owner";
    public static final String BUSI_TYPE = "busiType";
    public static final String BUSI_CATEGORY = "busiCategory";
    public static final String LOG_ALARM = "logAlarm";
    public static final String CHECK_POINT = "checkPoint";
    public static final String ERROR_MSG = "errorMsg";
    public static final String STACK_TRACE = "stackTrace";

    //业务种类
    @Getter
    @AllArgsConstructor
    public enum BusiCategory {
        //SPOT
        //ACTIVITY
        ACTIVITY_MSG_CONSUME(Business.activity, "ACTIVITY_MSG_CONSUME", "活动消息消费"),
        ACTIVITY_CHECK(Business.activity, "ACTIVITY_CHECK", "活动业务规则检查"),
        ACTIVITY_ACTION(Business.activity, "ACTIVITY_ACTION", "活动奖励发放"),
        ;

        Business business;
        String category;
        String desc;

    }


    //业务检查点
    @Getter
    @AllArgsConstructor
    public enum CheckPoint {

        // ACTIVITY
        ACTIVITY_MSG_CONSUME_ERROR(BusiCategory.ACTIVITY_MSG_CONSUME, "活动消息消费处理异常", true),
        ACTIVITY_DO_CHECK_ERROR(BusiCategory.ACTIVITY_CHECK, "活动参与业务规则检查异常", true),
        ACTIVITY_DO_ACTION_ERROR(BusiCategory.ACTIVITY_ACTION, "活动奖励发放处理异常", true),

        ACTIVITY_FIAT_REWARD_ERROR(BusiCategory.ACTIVITY_ACTION, "法币入金奖励发奖异常", true),
        ACTIVITY_SLOT_REWARD_ERROR(BusiCategory.ACTIVITY_ACTION, "老虎机奖励发奖异常", true),
        ACTIVITY_INVITE_KYC_REWARD_ERROR(BusiCategory.ACTIVITY_ACTION, "邀请KYC奖励发奖异常", true);

        private BusiCategory busiCategory;
        private String desc;
        private boolean alarm;

    }


}
