/**
 * Copyright:   北京互融时代软件有限公司
 * @author:      <PERSON>
 * @version:      V1.0 
 * @Date:        2015年11月4日 下午1:22:49
 */
package com.kikitrade.activity.service.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;


@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class PropertiesUtils {
	private ApplicationContext applicationContext;

	@Autowired
	public void setApplicationContext(ApplicationContext applicationContext) {
		this.applicationContext = applicationContext;
		APP = applicationContext.getEnvironment();
	}

	public static Environment APP = null;
	public final static String LOADWEBKEY = "app.loadweb";

	
	
	/**
	 * 获得加载了多少个站点
	 * <p> TODO</p>
	 * @author:         <PERSON>
	 * @param:    @return
	 * @return: ArrayList<String> 
	 * @Date :          2016年7月5日 上午11:15:57   
	 * @throws:
	 */
	public static Map<String,String> getLoadWeb(){
		
		Map<String,String> map = new HashMap<String,String>();
//		Set<Object> keySet = APP.keySet();
//		Iterator<Object> iterator = keySet.iterator();
//		while (iterator.hasNext()) {
//			String key = (String) iterator.next();
//			if(key.contains(LOADWEBKEY)){
//				String[] split = APP.getProperty(key).split("=");
//				map.put(split[0], split[1]);
//				break;
//			}
//		}

		String[] split = APP.getProperty(LOADWEBKEY).split("=");
		map.put(split[0], split[1]);
		return map;
	}

	/**
	 * front_oauth 获得sso同步地址
	 * <p> TODO</p>
	 *
	 * @author: Liu Shilei
	 * @param: @return
	 * @return: ArrayList<String>
	 * @Date :          2016年7月5日 上午11:15:57
	 * @throws:
	 */
	public static String getOuathStr() {

		ArrayList<String> arrayList = new ArrayList<String>();
		arrayList.add(APP.getProperty("oauth.filter"));
//		Set<Object> keySet = APP.keySet();
//		Iterator<Object> iterator = keySet.iterator();
//		while (iterator.hasNext()) {
//			String key = (String) iterator.next();
//			if (key.contains("oauth.filter")) {
//				arrayList.add(APP.getProperty(key));
//			}
//		}

		StringBuffer sb = new StringBuffer();

		for (int i = 0; i < arrayList.size(); i++) {
			sb.append(arrayList.get(i));
			if (i != arrayList.size() - 1) {
				sb.append(",");
			}
		}
		return sb.toString();
	}

	/**
	 * 返回网站维护页面
	 * <p> TODO</p>
	 *
	 * @author: Shangxl
	 * @param: @return
	 * @return: String
	 * @Date :          2017年7月15日 下午6:18:47
	 * @throws:
	 */
	public static String getfixUrl() {

//		Set<Object> keySet = APP.keySet();
//		Iterator<Object> iterator = keySet.iterator();
//		while (iterator.hasNext()) {
//			String key = (String) iterator.next();
//			if (key.contains("site.fix")) {
//				return APP.getProperty(key);
//			}
//		}
		if(APP.getProperty("site.fix")!=null){
			return APP.getProperty("site.fix");
		}
		return APP.getProperty("app.url");
	}

	/**
	 * 遍历properties
	 * <p> TODO</p>
	 * @author:         Shangxl
	 * @param:    @param url
	 * @param:    @return
	 * @return: Map<String,String>
	 * @Date :          2017年9月14日 上午11:29:07
	 * @throws:
	 */
	public static Map<String, String> printAll(String url) {
		Properties prop = new Properties();
		Map<String, String> map = null;
		InputStream input = null;
		try {
			input = PropertiesUtils.class.getClassLoader().getResourceAsStream(url);
			if (input == null) {
				System.out.println("Sorry, unable to find " + url);
			}
			prop.load(input);
			Set<Map.Entry<Object, Object>> entrys = prop.entrySet();// 返回的属性键值对实体
			map = new HashMap<String, String>();
			for (Map.Entry<Object, Object> entry : entrys) {
				map.put(entry.getKey().toString(), entry.getValue().toString());
			}
			return map;
		} catch (IOException ex) {
			ex.printStackTrace();
		} finally {
			if (input != null) {
				try {
					input.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return map;
	}
}
