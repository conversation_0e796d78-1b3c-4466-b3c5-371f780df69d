package com.kikitrade.activity.service.common;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.util.Assert;

import java.util.List;

public class AssertUtil {

    public static void isNotBlank(String str, String message) throws IllegalArgumentException{
        Assert.hasText(str, message);
    }

    public static void isNotNull(Object obj, String message) throws IllegalArgumentException{
        Assert.notNull(obj, message);
    }

    public static void isNotEmpty(List obj, String message) throws IllegalArgumentException{
        Assert.notEmpty(obj, message);
    }

    public static void isNumber(String str, String message) throws IllegalArgumentException{
        if(!NumberUtils.isNumber(str)){
            throw new IllegalArgumentException(message);
        }
    }
}
