package com.kikitrade.activity.service.model;

import lombok.Data;

@Data
public class TemplateVO {

    private String id;
    //字段标识
    private String code;
    //字段名称
    private String name;
    //字段类型
    private String type;
    //默认值，可能为空
    private String defaultValue;
    //要求的最小长度
    private Integer ruleMinLength;
    //要求的最大长度
    private Integer ruleMaxLength;
    //是否允许为空
    private Boolean ruleAllowNUll;
    //顺序
    private Integer seq;
}
