package com.kikitrade.activity.service.draw;

import com.kikitrade.activity.dal.tablestore.model.PrizeConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 概率算法服务
 * 实现OVERALL和SINGLE两种概率策略
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class ProbabilityAlgorithmService {

    private final Random random = new SecureRandom();

    /**
     * OVERALL概率策略：整体概率模式
     * 所有奖品的概率之和应该等于1.0（100%）
     */
    public PrizeConfig drawWithOverallStrategy(List<PrizeConfig> prizeConfigs) {
        log.info("执行OVERALL概率策略抽奖，奖品数量: {}", prizeConfigs.size());

        if (prizeConfigs == null || prizeConfigs.isEmpty()) {
            log.warn("奖品配置为空，无法进行抽奖");
            return null;
        }

        // 1. 验证概率总和
        BigDecimal totalProbability = prizeConfigs.stream()
                .map(PrizeConfig::getWinningProbability)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        log.info("总概率: {}", totalProbability);

        if (totalProbability.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("总概率为0或负数，无法进行抽奖");
            return null;
        }

        // 2. 生成随机数 [0, totalProbability)
        double randomValue = random.nextDouble() * totalProbability.doubleValue();
        log.debug("生成随机数: {}", randomValue);

        // 3. 根据概率区间确定中奖奖品
        BigDecimal currentSum = BigDecimal.ZERO;
        for (PrizeConfig prizeConfig : prizeConfigs) {
            currentSum = currentSum.add(prizeConfig.getWinningProbability());

            if (randomValue <= currentSum.doubleValue()) {
                log.info("OVERALL策略中奖: 奖品={}, 概率={}",
                        prizeConfig.getPrizeName(), prizeConfig.getWinningProbability());
                return prizeConfig;
            }
        }

        // 4. 理论上不应该到达这里，返回最后一个奖品作为保底
        PrizeConfig fallbackPrize = prizeConfigs.get(prizeConfigs.size() - 1);
        log.warn("OVERALL策略未命中任何奖品，返回保底奖品: {}", fallbackPrize.getPrizeName());
        return fallbackPrize;
    }

    /**
     * SINGLE概率策略：单品概率模式
     * 每个奖品独立计算是否中奖，需要兜底奖品
     */
    public PrizeConfig drawWithSingleStrategy(List<PrizeConfig> prizeConfigs, PrizeConfig fallbackPrize) {
        log.info("执行SINGLE概率策略抽奖，奖品数量: {}, 兜底奖品: {}",
                prizeConfigs.size(), fallbackPrize != null ? fallbackPrize.getPrizeName() : "无");

        if (prizeConfigs == null || prizeConfigs.isEmpty()) {
            log.warn("奖品配置为空，返回兜底奖品");
            return fallbackPrize;
        }

        // 1. 按概率从高到低排序，优先判断高价值奖品
        List<PrizeConfig> sortedPrizes = new ArrayList<>(prizeConfigs);
        sortedPrizes.sort((a, b) -> b.getWinningProbability().compareTo(a.getWinningProbability()));

        // 2. 逐个判断是否中奖
        for (PrizeConfig prizeConfig : sortedPrizes) {
            double randomValue = random.nextDouble();
            double probability = prizeConfig.getWinningProbability().doubleValue();

            log.debug("SINGLE策略判断: 奖品={}, 概率={}, 随机数={}",
                    prizeConfig.getPrizeName(), probability, randomValue);

            if (randomValue <= probability) {
                log.info("SINGLE策略中奖: 奖品={}, 概率={}",
                        prizeConfig.getPrizeName(), prizeConfig.getWinningProbability());
                return prizeConfig;
            }
        }

        // 3. 没有中奖，返回兜底奖品
        log.info("SINGLE策略未中奖，返回兜底奖品: {}",
                fallbackPrize != null ? fallbackPrize.getPrizeName() : "无");
        return fallbackPrize;
    }

    /**
     * 批量抽奖：支持一次抽取多个奖品
     */
    public List<PrizeConfig> batchDraw(List<PrizeConfig> prizeConfigs, String strategy,
                                      PrizeConfig fallbackPrize, int drawCount) {
        log.info("批量抽奖: 策略={}, 次数={}", strategy, drawCount);

        if (prizeConfigs == null || prizeConfigs.isEmpty()) {
            log.warn("奖品配置为空，无法进行抽奖");
            return new ArrayList<>();
        }

        List<PrizeConfig> results = new ArrayList<>();

        if (ActivityConstant.LotteryStrategyEnum.OVERALL.name().equals(strategy)) {
            // 对OVERALL策略，提前计算概率区间
            BigDecimal totalProbability = prizeConfigs.stream()
                    .map(PrizeConfig::getWinningProbability)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (totalProbability.compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("总概率为0或负数，无法进行抽奖");
                return results;
            }

            // 构建概率区间数组
            List<ProbabilityRange> ranges = buildProbabilityRanges(prizeConfigs);

            // 批量抽奖
            for (int i = 0; i < drawCount; i++) {
                double randomValue = random.nextDouble() * totalProbability.doubleValue();
                PrizeConfig prize = findPrizeInRanges(ranges, randomValue);
                if (prize != null) {
                    results.add(prize);
                }
            }
        } else if (ActivityConstant.LotteryStrategyEnum.SINGLE.name().equals(strategy)) {
            // SINGLE策略保持不变，因为每次都需要独立计算
            for (int i = 0; i < drawCount; i++) {
                PrizeConfig prize = drawWithSingleStrategy(prizeConfigs, fallbackPrize);
                if (prize != null) {
                    results.add(prize);
                }
            }
        } else {
            log.warn("未知的概率策略: {}, 使用OVERALL策略", strategy);
            return batchDraw(prizeConfigs, ActivityConstant.LotteryStrategyEnum.OVERALL.name(), fallbackPrize, drawCount);
        }

        log.info("批量抽奖完成，获得奖品数量: {}", results.size());
        return results;
    }

    /**
     * 概率区间类，用于存储每个奖品的概率范围
     */
    private static class ProbabilityRange {
        private final PrizeConfig prize;
        private final BigDecimal start;
        private final BigDecimal end;

        public ProbabilityRange(PrizeConfig prize, BigDecimal start, BigDecimal end) {
            this.prize = prize;
            this.start = start;
            this.end = end;
        }
    }

    /**
     * 构建概率区间列表
     */
    private List<ProbabilityRange> buildProbabilityRanges(List<PrizeConfig> prizeConfigs) {
        //TODO 斐波那契算法 128位数组
        List<ProbabilityRange> ranges = new ArrayList<>();
        BigDecimal currentSum = BigDecimal.ZERO;

        for (PrizeConfig prize : prizeConfigs) {
            BigDecimal start = currentSum;
            currentSum = currentSum.add(prize.getWinningProbability());
            ranges.add(new ProbabilityRange(prize, start, currentSum));
        }

        return ranges;
    }

    /**
     * 在概率区间中查找中奖奖品
     */
    private PrizeConfig findPrizeInRanges(List<ProbabilityRange> ranges, double randomValue) {
        for (ProbabilityRange range : ranges) {
            if (randomValue <= range.end.doubleValue()) {
                log.debug("OVERALL批量策略中奖: 奖品={}, 概率区间=[{}, {}], 随机数={}",
                        range.prize.getPrizeName(), range.start, range.end, randomValue);
                return range.prize;
            }
        }

        // 理论上不应该到达这里，返回最后一个奖品作为保底
        if (!ranges.isEmpty()) {
            ProbabilityRange lastRange = ranges.get(ranges.size() - 1);
            log.warn("OVERALL批量策略未命中任何奖品，返回保底奖品: {}", lastRange.prize.getPrizeName());
            return lastRange.prize;
        }

        return null;
    }
}