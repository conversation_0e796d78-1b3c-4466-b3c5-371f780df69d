package com.kikitrade.activity.service.common;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.FutureTask;

@Configuration
public class TaskExecutor {

    @Bean
    public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置核心线程数
        executor.setCorePoolSize(8);
        // 设置最大线程数
        executor.setMaxPoolSize(32);
        // 设置队列容量
        executor.setQueueCapacity(300);
        // 设置线程活跃时间（秒）
        executor.setKeepAliveSeconds(60);
        // 设置默认线程名称
        executor.setThreadNamePrefix("batch.csv-");
        // 设置拒绝策略
        executor.setRejectedExecutionHandler((r, exe) -> {
            if (null != r && r instanceof FutureTask) {
                ((FutureTask) r).cancel(true);
            } else {

            }
        });
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.initialize();
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor rewardPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置核心线程数
        executor.setCorePoolSize(8);
        // 设置最大线程数
        executor.setMaxPoolSize(32);
        // 设置队列容量
        executor.setQueueCapacity(300);
        // 设置线程活跃时间（秒）
        executor.setKeepAliveSeconds(60);
        // 设置默认线程名称
        executor.setThreadNamePrefix("reward-");
        // 设置拒绝策略
        executor.setRejectedExecutionHandler((r, exe) -> {
            if (null != r && r instanceof FutureTask) {
                ((FutureTask) r).cancel(true);
            } else {

            }
        });
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.initialize();
        return executor;
    }


    @Bean
    public ThreadPoolTaskExecutor activityPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置核心线程数
        executor.setCorePoolSize(8);
        // 设置最大线程数
        executor.setMaxPoolSize(32);
        // 设置队列容量
        executor.setQueueCapacity(300);
        // 设置线程活跃时间（秒）
        executor.setKeepAliveSeconds(60);
        // 设置默认线程名称
        executor.setThreadNamePrefix("activity-");
        // 设置拒绝策略
        executor.setRejectedExecutionHandler((r, exe) -> {
            if (null != r && r instanceof FutureTask) {
                ((FutureTask) r).cancel(true);
            } else {

            }
        });
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.initialize();
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor activityPoolTaskListExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置核心线程数
        executor.setCorePoolSize(16);
        // 设置最大线程数
        executor.setMaxPoolSize(32);
        // 设置队列容量
        executor.setQueueCapacity(300);
        // 设置线程活跃时间（秒）
        executor.setKeepAliveSeconds(60);
        // 设置默认线程名称
        executor.setThreadNamePrefix("activity-");
        // 设置拒绝策略
        executor.setRejectedExecutionHandler((r, exe) -> {
            if (null != r && r instanceof FutureTask) {
                ((FutureTask) r).cancel(true);
            } else {

            }
        });
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.initialize();
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor luckFortunePoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置核心线程数
        executor.setCorePoolSize(8);
        // 设置最大线程数
        executor.setMaxPoolSize(32);
        // 设置队列容量
        executor.setQueueCapacity(300);
        // 设置线程活跃时间（秒）
        executor.setKeepAliveSeconds(60);
        // 设置默认线程名称
        executor.setThreadNamePrefix("luck-");
        // 设置拒绝策略
        executor.setRejectedExecutionHandler((r, exe) -> {
            if (null != r && r instanceof FutureTask) {
                ((FutureTask) r).cancel(true);
            } else {

            }
        });
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.initialize();
        return executor;
    }
}
