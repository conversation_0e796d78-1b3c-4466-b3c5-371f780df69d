package com.kikitrade.activity.service.draw;

import com.kikitrade.activity.dal.tablestore.model.PrizeConfig;
import com.kikitrade.activity.service.draw.filter.PrizePoolFilterStrategy;

import java.util.List;

/**
 * 动态奖池构建器接口
 * 支持插件化的过滤策略，构建个性化的动态奖池
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface DynamicPrizePoolBuilder {

    /**
     * 构建动态奖池
     * 根据用户信息和注册的过滤策略，构建个性化的奖池
     *
     * @param userId 用户ID
     * @param prizePoolCode 奖池编码
     * @param saasId SaaS ID
     * @return 动态构建的奖品配置列表
     */
    List<PrizeConfig> buildDynamicPool(String userId, String prizePoolCode, String saasId);

    /**
     * 注册过滤策略
     *
     * @param strategy 过滤策略实例
     */
    void registerFilterStrategy(PrizePoolFilterStrategy strategy);

    /**
     * 移除过滤策略
     *
     * @param strategyName 策略名称
     */
    void removeFilterStrategy(String strategyName);

    /**
     * 获取所有已注册的策略
     *
     * @return 所有策略列表
     */
    List<PrizePoolFilterStrategy> getAllStrategies();

    /**
     * 获取指定策略
     *
     * @param strategyName 策略名称
     * @return 策略实例，如果不存在则返回null
     */
    PrizePoolFilterStrategy getStrategy(String strategyName);

    /**
     * 检查策略是否已注册
     *
     * @param strategyName 策略名称
     * @return true表示已注册，false表示未注册
     */
    boolean hasStrategy(String strategyName);
}