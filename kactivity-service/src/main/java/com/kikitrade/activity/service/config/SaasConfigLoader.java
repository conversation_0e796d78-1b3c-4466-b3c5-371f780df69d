package com.kikitrade.activity.service.config;

import cn.hutool.core.io.IoUtil;
import com.kikitrade.activity.model.constant.ActivityConstant;
import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;
import org.yaml.snakeyaml.introspector.Property;
import org.yaml.snakeyaml.introspector.PropertyUtils;

import java.io.Serializable;
import java.io.StringReader;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/28 16:03
 */
@Slf4j
public class SaasConfigLoader implements Serializable {

    private static Map<String, SaasConfig> saasConfig;

    public static void load(String config){
        Yaml yaml = buildYamlInstance(SaasConfigDTO.class);
        SaasConfigDTO var4;
        StringReader reader = new StringReader(config);
        try {
            var4 = yaml.loadAs(reader, SaasConfigDTO.class);
            if(var4 != null){
                saasConfig = var4.getSaasConfig();
            }
            log.info("saasConfig:{}", saasConfig);
        } catch (Exception ex){
            log.error("saasConfig exception:{}", saasConfig, ex);
        } finally {
            IoUtil.close(reader);
        }
    }

    public static SaasConfig getConfig(String saasId){
        if(saasConfig != null && saasConfig.containsKey(saasId)){
            return saasConfig.get(saasId);
        }
        return new SaasConfig();
    }

    private static Yaml buildYamlInstance(Class<? extends Object> theRoot){
        Constructor c = new Constructor(theRoot);
        c.setPropertyUtils(new PropertyUtils() {
            @Override
            public Property getProperty(Class<? extends Object> type, String name){
                // 关键代码 忽略yaml中无法在类中找到属性的字段
                setSkipMissingProperties(true);
                return super.getProperty(type, name);
            }
        });
        Yaml parser = new Yaml(c);
        return parser;
    }

    public static SeasonTime getSeason(String saasId){
        SaasConfig config = getConfig(saasId);
        if(config.getSeasonTime() == null){
            return null;
        }
        long now = System.currentTimeMillis();
        if(config.getSeasonTimeType() == ActivityConstant.SeasonTimeType.period_not_fixed){
            for(Map.Entry<Integer, SeasonTime> seasonTimeEntry : config.getSeasonTime().entrySet()){
                if(now >= seasonTimeEntry.getValue().getStartTime() && now < seasonTimeEntry.getValue ().getEndTime()){
                    SeasonTime seasonTime = seasonTimeEntry.getValue();
                    seasonTime.setSettleWeek(seasonTimeEntry.getValue().getSettleWeek());
                    seasonTime.setMaxLevel(seasonTimeEntry.getValue().getMaxLevel());
                    seasonTime.setSeasonId(seasonTimeEntry.getKey());
                    seasonTime.setSeasonName("season"+seasonTimeEntry.getKey());
                    seasonTime.setSettlementTime(Date.from(LocalDateTime.ofInstant(Instant.ofEpochMilli(seasonTime.getStartTime()), ZoneId.systemDefault()).with(TemporalAdjusters.next(seasonTimeEntry.getValue().getSettleWeek())).toInstant(ZoneOffset.UTC)).getTime());
                    //计算获取cycle
                    long c = 1;
                    if(OffsetDateTime.now().toEpochSecond() * 1000L >= seasonTime.getSettlementTime()){
                        c = ((OffsetDateTime.now().toEpochSecond() * 1000L  - seasonTime.getSettlementTime())/1000 / 60 / 60 / 24 / 7) + 2;
                    }
                    seasonTime.setCycle(String.format("%02d", c));
                    LocalDateTime last = new Date().toInstant().atOffset(ZoneOffset.UTC).toLocalDate().atTime(0, 0, 0);
                    long time = Date.from(last.with(TemporalAdjusters.next(seasonTimeEntry.getValue().getSettleWeek())).toInstant(ZoneOffset.UTC)).getTime();
                    seasonTime.setCycleEndTime(time > seasonTime.getEndTime() ? seasonTime.getEndTime() : time);
                    return seasonTime;
                }
            }
        }else if(config.getSeasonTimeType() == ActivityConstant.SeasonTimeType.monthly_period_fixed){
            //固定周期取seasonTime最新的一个
            Map.Entry<Integer, SeasonTime> seasonTimeEntry = config.getSeasonTime().entrySet().stream().sorted((o1, o2) -> o2.getKey().compareTo(o1.getKey())).findFirst().get();
            LocalDate lastSeasonStartTime = new Date(seasonTimeEntry.getValue().getStartTime()).toInstant().atOffset(ZoneOffset.UTC).toLocalDate();
            LocalDateTime seasonStartTime = OffsetDateTime.now(ZoneOffset.UTC).toLocalDate().with(TemporalAdjusters.firstDayOfMonth()).atTime(0, 0, 0);
            LocalDateTime seasonEndTime = OffsetDateTime.now(ZoneOffset.UTC).toLocalDate().with(TemporalAdjusters.firstDayOfNextMonth()).atTime(0, 0, 0);
            int seasonIdx = Period.between(lastSeasonStartTime, seasonStartTime.toLocalDate()).getYears() * 12 + Period.between(lastSeasonStartTime, seasonStartTime.toLocalDate()).getMonths() + 1;

            SeasonTime seasonTime = new SeasonTime();
            seasonTime.setSettleWeek(seasonTimeEntry.getValue().getSettleWeek());
            seasonTime.setMaxLevel(seasonTimeEntry.getValue().getMaxLevel());
            seasonTime.setSeasonId(seasonIdx);
            seasonTime.setSeasonName("season"+seasonTimeEntry.getKey());
            seasonTime.setStartTime(seasonStartTime.toEpochSecond(ZoneOffset.UTC) * 1000);
            seasonTime.setEndTime(seasonEndTime.toEpochSecond(ZoneOffset.UTC) * 1000);
            seasonTime.setSettlementTime(Date.from(seasonStartTime.with(TemporalAdjusters.next(seasonTimeEntry.getValue().getSettleWeek())).toInstant(ZoneOffset.UTC)).getTime());
            //计算获取cycle
            long c = 1;
            if(OffsetDateTime.now().toEpochSecond() * 1000L >= seasonTime.getSettlementTime()){
                c = ((OffsetDateTime.now().toEpochSecond() * 1000L  - seasonTime.getSettlementTime())/1000 / 60 / 60 / 24 / 7) + 2;
            }
            seasonTime.setCycle(String.format("%02d", c));
            LocalDateTime last = new Date().toInstant().atOffset(ZoneOffset.UTC).toLocalDate().atTime(0, 0, 0);
            long time = Date.from(last.with(TemporalAdjusters.next(seasonTimeEntry.getValue().getSettleWeek())).toInstant(ZoneOffset.UTC)).getTime();
            seasonTime.setCycleEndTime(time > seasonTime.getEndTime() ? seasonTime.getEndTime() : time);
            return seasonTime;
        }
        return null;
    }
}
