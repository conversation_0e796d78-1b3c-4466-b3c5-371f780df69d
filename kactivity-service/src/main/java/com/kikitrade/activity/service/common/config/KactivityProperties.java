package com.kikitrade.activity.service.common.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Solid Config
 *
 * <AUTHOR>
 * @create 2021/9/6 11:44 下午
 * @modify
 */
@Configuration
@ConfigurationProperties
@Getter
public class KactivityProperties {

    @Value("${saas-id}")
    private String saasId;

    // 新手专享任务，自用户注册 x 天后对该用户过期，过期后任务列表不展示，后续完成不发奖
    @Value("${activity.novice.task.expire.days:15}")
    private Integer noviceTaskExpireDays;

    @Value("${task.white.date:5}")
    private Integer taskWhiteDate;

    @Value("${activity.quests.api.host}")
    private String questsApiHost;

    @Value("${activity.quests.app.key.suffix}")
    private String questsAppKeySuffix = "%s:%s:quests";
}
