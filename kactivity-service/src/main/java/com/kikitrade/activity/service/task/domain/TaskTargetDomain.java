package com.kikitrade.activity.service.task.domain;

import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.config.TaskCodeConfig;
import com.kikitrade.activity.service.mq.ActivityEventMessage;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/2/5 18:12
 */
public class TaskTargetDomain {

    public static String getTargetId(ActivityEventMessage activityEventMessage, TaskCodeConfig codeConfig, TaskConfigDTO taskConfig){
        String targetId = activityEventMessage.getTargetId() == null ? codeConfig.getCode() : activityEventMessage.getTargetId();
        if(taskConfig.getProgressType() == ActivityTaskConstant.ProgressTypeEnum.series){
            //连续任务需要根据时间查询
            targetId = TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDD);
        }
       if (taskConfig.getCycle() == ActivityTaskConstant.TaskCycleEnum.once_daily) {
            //一次性任务，每天只能完成一次
            targetId = TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDD);
        }
        //社区任务，一个用户只能做一次
        Object socialCustomerId = activityEventMessage.getBody().get("socialCustomerId");
        if(socialCustomerId != null){
            targetId = String.valueOf(socialCustomerId);
        }
        return targetId;
    }
}
