package com.kikitrade.activity.service.task.filter;

import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.mq.ActivityEventMessage;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.activity.service.reward.impl.CustomerCacheDTO;
import com.kikitrade.activity.service.task.TaskFilter;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component("activityNoviceTaskFilter")
@Order(4)
@Slf4j
public class ActivityNoviceTaskFilter implements TaskFilter {

    @Resource
    private CustomerService customerService;
    @Resource
    private KactivityProperties kactivityProperties;

    @Override
    public void filter(ActivityEventMessage activityEventMessage, TaskConfigDTO config) throws ActivityException {
        if(activityEventMessage.getCustomerId() == null || activityEventMessage.getCustomerId().startsWith("address:")){
            return;
        }
        CustomerCacheDTO customerCacheDTO = customerService.getById(activityEventMessage.getCustomerId());
        if (customerCacheDTO == null) {
            throw new ActivityException(ActivityResponseCode.CUSTOMER_EMPTY);
        }

        if (DateUtils.addDays(customerCacheDTO.getRegisterTime(), kactivityProperties.getNoviceTaskExpireDays()).getTime()
                < System.currentTimeMillis()) {
            log.info("doTask filter, novice task is expire, customerId: {}, registerTime: {}", customerCacheDTO.getId(), customerCacheDTO.getRegisterTime().getTime());
            throw new ActivityException(ActivityResponseCode.TASK_NOVICE_EXPIRED);
        }
    }

    @Override
    public FilterScope getScope() {
        return FilterScope.TASK;
    }
}
