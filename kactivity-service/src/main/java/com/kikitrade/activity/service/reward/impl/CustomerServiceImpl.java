package com.kikitrade.activity.service.reward.impl;

import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.reward.CustomerService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CustomerServiceImpl implements CustomerService {

    @Resource
    private KactivityProperties kactivityProperties;

    @Override
    public CustomerCacheDTO getById(String customerId) {
        String saasId = kactivityProperties.getSaasId();
        String key = RedisKeyConst.getKey(RedisKeyConst.ACTIVITY_CUSTOMER_KEY.getPrefix(), saasId + customerId);
        try{

        }catch (Exception ex){
            log.error("customerService getById error:{}", customerId,ex);
        }
        return null;
    }
}
