package com.kikitrade.activity.service.task.domain;

import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.model.util.TimeFormat;
import com.kikitrade.activity.model.util.TimeUtil;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/2/5 15:25
 */
public class TaskCycleDomain {

    private static final String CYCLE_ONCE = "19990101";
    private static final String CYCLE_ONCE_END = "30000101";

    public static String getCycleOnce() {
        return CYCLE_ONCE;
    }
    public static String getCurrencyCycle(TaskConfigDTO config, Long eventTime) {

        if(eventTime != null){
            eventTime = String.valueOf(eventTime).length() == 10 ? eventTime * 1000 : eventTime;
        }

        Date now = eventTime == null ? new Date() : new Date(eventTime);

        switch (config.getCycle()){
            case once:
            case once_daily:
                return CYCLE_ONCE;
            case once_season:
                return TimeUtil.getDataStr(new Date(config.getStartTime()), TimeFormat.YYYYMMDD_PATTERN);
            case weekly:
                return TimeUtil.getWeekStart(now, TimeFormat.YYYYMMDD_PATTERN);
            case monthly:
                return TimeUtil.getMonthStart(now, TimeFormat.YYYYMMDD_PATTERN);
            default:
                return TimeUtil.getDataStr(now, TimeUtil.YYYYMMDD);
        }
    }

    public static String getCurrencyCycleEnd(TaskConfigDTO config, Long eventTime) {
        if(eventTime != null){
            eventTime = String.valueOf(eventTime).length() == 10 ? eventTime * 1000 : eventTime;
        }
        Date now = eventTime == null ? new Date() : new Date(eventTime);

        switch (config.getCycle()){
            case once:
            case once_daily:
                return CYCLE_ONCE_END;
            case once_season:
                return TimeUtil.getDataStr(new Date(config.getEndTime()), TimeFormat.YYYYMMDD_PATTERN);
            case weekly:
                return TimeUtil.getWeekEnd(now, TimeFormat.YYYYMMDD_PATTERN);
            case monthly:
                return TimeUtil.getMonthEnd(now, TimeFormat.YYYYMMDD_PATTERN);
            default:
                return TimeUtil.getDataStr(now, TimeFormat.YYYYMMDD235959PATTERN);
        }
    }

    /**
     * 目前支持天
     * @param config
     * @return
     */
    public static OffsetDateTime getCurrencyOffsetCycle(TaskConfigDTO config) {
        return TimeUtil.parse(TimeUtil.getDataStr(new Date(), TimeFormat.YYYYMMDD_000000_PATTERN)).toInstant().atOffset(ZoneOffset.UTC);
    }

    public static OffsetDateTime getCurrencyOffsetCycleEnd(TaskConfigDTO config) {
        return TimeUtil.parse(TimeUtil.getDataStr(new Date(), TimeFormat.YYYYMMDDHHMMSS_PATTERN)).toInstant().atOffset(ZoneOffset.UTC).plusSeconds(-11);
    }
}
