package com.kikitrade.activity.service.draw.impl;

import com.kikitrade.activity.dal.tablestore.builder.ProgressChestConfigBuilder;
import com.kikitrade.activity.dal.tablestore.builder.RandomRewardPoolBuilder;
import com.kikitrade.activity.dal.tablestore.model.GiftPackConfig;
import com.kikitrade.activity.dal.tablestore.model.ProgressChestConfig;
import com.kikitrade.activity.dal.tablestore.model.RandomRewardPool;
import com.kikitrade.activity.service.draw.RewardIssueService;
import com.kikitrade.activity.service.goods.GiftPackConfigProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;

/**
 * 奖励发放服务实现
 * 严格按照技术规格书要求实现动态奖励组合逻辑
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class RewardIssueServiceImpl implements RewardIssueService {

    @Resource
    private GiftPackConfigProcessor giftPackConfigProcessor;

    @Resource
    private ProgressChestConfigBuilder progressChestConfigBuilder;

    @Resource
    private RandomRewardPoolBuilder randomRewardPoolBuilder;
    
    @Override
    public List<IssuedReward> issueRewardsByPackId(String packId, String saasId) {
        log.info("开始发放礼包奖励: packId={}, saasId={}", packId, saasId);
        
        List<IssuedReward> issuedRewards = new ArrayList<>();
        
        try {
            // 定义固定物品处理器
            Function<GiftPackConfig, IssuedReward> fixedItemProcessor = this::createIssuedReward;

            // 定义随机池处理器
            Function<GiftPackConfig, List<IssuedReward>> randomPoolProcessor = config ->
                pickFromRandomPool(config.getRandomPoolId(), config.getPickCount(), saasId);

            // 使用通用处理器处理配置
            issuedRewards = giftPackConfigProcessor.processGiftPackConfigs(
                packId, saasId, fixedItemProcessor, randomPoolProcessor);

            
            log.info("礼包奖励发放完成: packId={}, 总奖励数量={}", packId, issuedRewards.size());
            
        } catch (Exception e) {
            log.error("发放礼包奖励异常: packId={}, saasId={}", packId, saasId, e);
        }
        
        return issuedRewards;
    }

    @Override
    public List<IssuedReward> issueRewardsByChestId(String chestId, String saasId) {
        log.info("开始发放宝箱奖励: chestId={}, saasId={}", chestId, saasId);

        List<IssuedReward> issuedRewards = new ArrayList<>();

        try {
            // 1. 根据宝箱ID从 chest_config 表中查询宝箱的基础配置信息
            ProgressChestConfig chestConfig = progressChestConfigBuilder.findByChestId(chestId, saasId);

            if (chestConfig == null) {
                log.warn("未找到宝箱配置: chestId={}, saasId={}", chestId, saasId);
                return issuedRewards;
            }

            return issueRewardsByPackId(chestConfig.getPackIdOnUnlock(), saasId);
        } catch (Exception e) {
            log.error("发放宝箱奖励异常: chestId={}, saasId={}", chestId, saasId, e);
        }

        return issuedRewards;
    }
    
    @Override
    public List<IssuedReward> pickFromRandomPool(String poolId, Integer pickCount, String saasId) {
        log.info("从随机池抽取奖励: poolId={}, pickCount={}, saasId={}", poolId, pickCount, saasId);
        
        List<IssuedReward> pickedRewards = new ArrayList<>();
        
        try {
            // 1. 获取奖励池关联的所有奖励配置
            List<RandomRewardPool> rewardPools = randomRewardPoolBuilder.findByPoolId(poolId, saasId);
            
            if (CollectionUtils.isEmpty(rewardPools)) {
                log.warn("随机奖励池为空: poolId={}, saasId={}", poolId, saasId);
                return pickedRewards;
            }
            
            log.info("随机奖励池物品数量: {}", rewardPools.size());
            
            // 2. 根据奖励池的随机算法和权重配置，动态组合生成最终的奖励内容
            List<RandomRewardPool> selectedPools = selectByWeight(rewardPools, pickCount);
            
            // 3. 生成最终奖励
            for (RandomRewardPool pool : selectedPools) {
                IssuedReward reward = createRewardFromPool(pool);
                if (reward != null) {
                    pickedRewards.add(reward);
                    log.info("抽取到奖励: {}", reward);
                }
            }
            
            log.info("随机池抽取完成: poolId={}, 实际获得奖励数量={}", poolId, pickedRewards.size());
            
        } catch (Exception e) {
            log.error("从随机池抽取奖励异常: poolId={}, pickCount={}", poolId, pickCount, e);
        }
        
        return pickedRewards;
    }
    
    @Override
    public IssuedReward processFixedItem(GiftPackConfig giftPackConfig) {
        return createIssuedReward(giftPackConfig);
    }

    /**
     * 创建发放奖励对象（固定物品）
     */
    private IssuedReward createIssuedReward(GiftPackConfig config) {
        try {
            // 计算随机数量（在最小值和最大值之间）
            Integer quantity = giftPackConfigProcessor.calculateRandomQuantity(
                    config.getQuantityMin(), config.getQuantityMax());

            // 创建奖励对象
            IssuedReward reward = new IssuedReward();
            reward.setItemId(config.getItemId());
            reward.setItemType(config.getItemType()); // 固定物品类型
            reward.setItemName("固定物品_" + config.getItemId()); // 实际应该从物品表查询
            reward.setQuantity(quantity);
            reward.setDescription("固定发放的物品");

            return reward;

        } catch (Exception e) {
            log.error("创建发放奖励对象异常: configId={}", config.getId(), e);
            return null;
        }
    }
    
    @Override
    public List<RandomRewardPool> selectByWeight(List<RandomRewardPool> rewardPools, int count) {
        if (CollectionUtils.isEmpty(rewardPools) || count <= 0) {
            return new ArrayList<>();
        }
        
        List<RandomRewardPool> selected = new ArrayList<>();
        List<RandomRewardPool> availablePools = new ArrayList<>(rewardPools);
        
        try {
            // 计算总权重
            int totalWeight = availablePools.stream()
                    .mapToInt(pool -> pool.getWeight() != null ? pool.getWeight() : 1)
                    .sum();
            
            log.debug("权重随机选择: 总权重={}, 选择数量={}", totalWeight, count);
            
            // 按权重随机选择
            for (int i = 0; i < count && !availablePools.isEmpty(); i++) {
                RandomRewardPool selectedPool = selectSingleByWeight(availablePools, totalWeight);
                if (selectedPool != null) {
                    selected.add(selectedPool);
                    
                    // 从可选池中移除已选择的物品（避免重复）
                    availablePools.remove(selectedPool);
                    totalWeight -= (selectedPool.getWeight() != null ? selectedPool.getWeight() : 1);
                    
                    log.debug("选中奖励: itemId={}, weight={}", selectedPool.getItemId(), selectedPool.getWeight());
                }
            }
            
        } catch (Exception e) {
            log.error("权重随机选择异常: poolSize={}, count={}", rewardPools.size(), count, e);
        }
        
        return selected;
    }
    
    /**
     * 按权重随机选择单个奖励
     */
    private RandomRewardPool selectSingleByWeight(List<RandomRewardPool> pools, int totalWeight) {
        if (totalWeight <= 0) {
            return pools.get(ThreadLocalRandom.current().nextInt(pools.size()));
        }
        
        int randomValue = ThreadLocalRandom.current().nextInt(totalWeight);
        int currentWeight = 0;
        
        for (RandomRewardPool pool : pools) {
            int weight = pool.getWeight() != null ? pool.getWeight() : 1;
            currentWeight += weight;
            
            if (randomValue < currentWeight) {
                return pool;
            }
        }
        
        // 降级方案：如果权重计算有问题，随机选择一个
        return pools.get(ThreadLocalRandom.current().nextInt(pools.size()));
    }
    
    /**
     * 从奖励池配置创建奖励对象
     */
    private IssuedReward createRewardFromPool(RandomRewardPool pool) {
        try {
            // 计算随机数量
            Integer quantity = giftPackConfigProcessor.calculateRandomQuantity(pool.getQuantityMin(), pool.getQuantityMax());
            
            return new IssuedReward(
                    pool.getItemId(),
                    pool.getItemType(),
                    pool.getItemName(),
                    quantity,
                    pool.getDescription(),
                    pool.getIconUrl()
            );
            
        } catch (Exception e) {
            log.error("创建奖励对象异常: poolId={}, itemId={}", pool.getPoolId(), pool.getItemId(), e);
            return null;
        }
    }
    

}
