package com.kikitrade.activity.service.model;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class RewardRule implements Serializable {

    private String level;
    private Double min;
    private Double max;
    private String side;
    private String userType;
    private String awardType;
    private String awardAmount;
    private String award;
    private String vipLevel;

    public RewardRule toEntity(com.kikitrade.activity.facade.award.RewardRule rewardRule){
        this.setLevel(rewardRule.getLevel());
        this.setMin(StringUtils.isNotBlank(rewardRule.getMin()) ? Double.parseDouble(rewardRule.getMin()) : null);
        this.setMax(StringUtils.isNotBlank(rewardRule.getMax()) ? Double.parseDouble(rewardRule.getMax()) : null);
        this.setSide(rewardRule.getSide());
        this.setUserType(rewardRule.getUserType());
        this.setAward(rewardRule.getAward());
        this.setAwardType(rewardRule.getAwardType());
        this.setAwardAmount(rewardRule.getAwardAmount());
        this.setVipLevel(rewardRule.getVipLevel());
        return this;
    }

    public com.kikitrade.activity.facade.award.RewardRule toFacade(){
        com.kikitrade.activity.facade.award.RewardRule.Builder builder = com.kikitrade.activity.facade.award.RewardRule.newBuilder();
        if(this.getLevel() != null){
            builder.setLevel(this.getLevel());
        }
        if(this.getMin() != null){
            builder.setMin(String.valueOf(this.getMin()));
        }
        if(this.getMax() != null){
            builder.setMax(String.valueOf(this.getMax()));
        }
        if(this.getSide() != null){
            builder.setSide(this.getSide());
        }
        if(this.getUserType() != null){
            builder.setUserType(this.getUserType());
        }
        if(this.getAward() != null){
            builder.setAward(this.getAward());
        }
        if(this.getAwardType() != null){
            builder.setAwardType(this.getAwardType());
        }
        if(this.getAwardAmount() != null){
            builder.setAwardAmount(this.getAwardAmount());
        }
        if(this.getVipLevel() != null){
            builder.setVipLevel(this.getVipLevel());
        }
        return builder.build();
    }
}
