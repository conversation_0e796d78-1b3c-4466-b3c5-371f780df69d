package com.kikitrade.activity.service.draw.filter.impl;

import com.kikitrade.activity.dal.tablestore.builder.PrizeConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.PrizeConfig;
import com.kikitrade.activity.service.draw.filter.PrizePoolFilterStrategy;
import com.kikitrade.activity.service.draw.preference.UserPreferenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 英雄过滤策略实现
 * 根据用户选择的英雄过滤奖品配置
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
@Slf4j
public class HeroFilterStrategy implements PrizePoolFilterStrategy {

    public static final String STRATEGY_NAME = "HERO";
    public static final String PREFERENCE_KEY = "SELECTED_HERO";

    @Resource
    private PrizeConfigBuilder prizeConfigBuilder;

    @Resource
    private UserPreferenceService userPreferenceService;

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getUserFilterValue(String userId, String saasId) {
        try {
            return userPreferenceService.getUserPreference(userId, saasId, PREFERENCE_KEY);
        } catch (Exception e) {
            log.error("获取用户英雄偏好失败: userId={}, saasId={}", userId, saasId, e);
            return null;
        }
    }

    @Override
    public List<PrizeConfig> filterPrizes(String prizePoolCode, String saasId, String filterValue) {
        if (filterValue == null || filterValue.trim().isEmpty()) {
            log.debug("英雄过滤值为空，跳过过滤: prizePoolCode={}", prizePoolCode);
            return Collections.emptyList();
        }

        try {
            List<PrizeConfig> heroPrizes = prizeConfigBuilder.findByPrizePoolCodeAndPreference(saasId, prizePoolCode, PREFERENCE_KEY, filterValue);
            log.debug("英雄过滤策略找到奖品数量: {}, heroId={}", heroPrizes.size(), filterValue);
            return heroPrizes;
        } catch (Exception e) {
            log.error("英雄过滤策略执行失败: prizePoolCode={}, heroId={}", prizePoolCode, filterValue, e);
            return Collections.emptyList();
        }
    }

    @Override
    public int getPriority() {
        return 100; // 英雄策略优先级：100
    }

    @Override
    public boolean isEnabled() {
        // 可以通过配置控制是否启用英雄过滤策略
        return true;
    }
}