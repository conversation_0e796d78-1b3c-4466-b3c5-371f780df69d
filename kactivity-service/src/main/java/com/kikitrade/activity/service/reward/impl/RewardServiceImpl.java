package com.kikitrade.activity.service.reward.impl;

import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.activity.service.reward.RewardService;
import com.kikitrade.activity.service.reward.RewardTccService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.mzt.logapi.starter.annotation.LogRecord;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * OperationRewardService Implementation
 *
 * <AUTHOR>
 * @create 2021/10/25 7:39 下午
 * @modify
 */
@Slf4j
@Component
public class RewardServiceImpl implements RewardService {

    @Resource
    private RewardTccService rewardTccService;
    @Resource
    private CustomerService customerService;

    @Override
    @LogRecord(success = "【{{#request.customerId}}】reward_success【{{#request.amount}}】【{{#request.currency}}】", type = "ACTIVITY", subType = "REWARD", bizNo = "{{#request.rewardId}}", successCondition = "{{#_ret.code.success}}"
            , fail = "【{{#request.customerId}}】reward_fail【{{#request.amount}}】【{{#request.currency}}】result：{{#_errorMsg}}{{#_ret}}")
    public ActivityResponse reward(RewardRequest request) throws Exception {
        log.info("[reward] reward request:{}", request);
        if (StringUtils.isBlank(request.getCustomerId())
                || request.getAmount() == null) {
            return ActivityResponse.builder()
                    .code(ActivityResponseCode.INVALID_PARAMETER)
                    .msg("customerId or currency or amount is null").build();
        }
        rewardTccService.getService(request.getType()).tryReward(request);
        return ActivityResponse.builder().code(ActivityResponseCode.SUCCESS).msg("success").build();
    }
}
