package com.kikitrade.activity.service.goods;

import com.kikitrade.activity.dal.tablestore.builder.GoodsBuilder;
import com.kikitrade.activity.dal.tablestore.model.Goods;
import com.kikitrade.framework.common.model.Page;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/12/26 15:43
 */
@Service
public class GoodsServiceImpl implements GoodsService{

    @Resource
    private GoodsBuilder goodsBuilder;

    /**
     * 查询商品详情
     * @param goodsId
     * @return
     */
    @Override
    public Goods findById(String goodsId) {
        return goodsBuilder.findById(goodsId);
    }

    /**
     * 商品列表
     * @param offset
     * @param limit
     * @return
     */
    @Override
    public Page<Goods> findAll(int offset, int limit, String saasId, String exclude) {
        return goodsBuilder.findAll(offset, limit, saasId, exclude);
    }

    /**
     * 根据场景代码查询商品列表
     * @param sceneCode 场景代码
     * @return 商品列表
     */
    @Override
    public List<Goods> findBySceneCode(String sceneCode) {
        return goodsBuilder.findBySceneCode(sceneCode);
    }
}
