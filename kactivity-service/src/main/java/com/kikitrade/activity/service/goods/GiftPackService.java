package com.kikitrade.activity.service.goods;

import com.kikitrade.activity.api.model.response.GoodsDetailResponse;

import java.util.List;

/**
 * 礼包处理服务接口
 * 提供礼包相关的业务逻辑处理
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
public interface GiftPackService {

    /**
     * 获取礼包内的物品信息
     * 
     * @param packId 礼包ID（商品ID）
     * @param saasId SaaS ID
     * @return 礼包物品信息列表
     */
    List<GoodsDetailResponse.GiftPackItem> getGiftPackItems(String packId, String saasId);

    /**
     * 批量获取礼包内的物品信息
     * 用于优化批量查询，避免N+1问题
     * 
     * @param packIds 礼包ID列表
     * @param saasId SaaS ID
     * @return 礼包ID到物品信息列表的映射
     */
    java.util.Map<String, List<GoodsDetailResponse.GiftPackItem>> batchGetGiftPackItems(List<String> packIds, String saasId);

    /**
     * 判断商品是否为礼包类型
     * 
     * @param goodsType 商品类型
     * @return 是否为礼包类型
     */
    boolean isGiftPack(String goodsType);
}
