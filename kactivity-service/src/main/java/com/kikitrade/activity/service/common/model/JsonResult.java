package com.kikitrade.activity.service.common.model;

import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;

public class JsonResult implements Serializable {

    private Boolean success = false;// 返回是否成功
    private String msg = "";// 返回信息
    private String msgKey = "";
    private String[] msgParam = {};
    private Object obj = null;// 返回其他对象信息
    private String code = "";//提示代码

    public JsonResult() {
    }

    public JsonResult(Boolean success) {
        this.success = success;
    }

    public Boolean getSuccess() {
        return success;
    }

    public JsonResult setSuccess(Boolean success) {
        this.success = success;
        return this;
    }

    public String getMsg() {
        return msg;
    }

    public JsonResult setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public Object getObj() {
        return obj;
    }

    public JsonResult setObj(Object obj) {
        this.obj = obj;
        return this;
    }

    public String getCode() {
        return code;
    }

    public JsonResult setCode(String code) {
        this.code = code;
        return this;
    }

    public String getMsgKey() {
        return msgKey;
    }

    public JsonResult setMsgKey(String msgKey) {
        this.msgKey = msgKey;
        return this;
    }

    public String[] getMsgParam() {
        return msgParam;
    }

    public JsonResult setMsgParam(String[] msgParam) {
        this.msgParam = msgParam;
        return this;
    }

    public String toJSONString() {
        return JSONObject.toJSONString(this);
    }

}
