package com.kikitrade.activity.service.valid.impl;

import com.kikitrade.activity.service.common.AssertUtil;
import com.kikitrade.activity.service.common.model.ValidParam;
import com.kikitrade.activity.service.exception.BussinessException;
import com.kikitrade.activity.service.valid.ActivityValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RewardStatusValidator implements ActivityValidator {

    @Override
    public void valid(ValidParam model) throws BussinessException {
        if(StringUtils.isBlank(model.getBusinessId())){
            throw new BussinessException("businessId cannot ne empty");
        }
        if(StringUtils.isBlank(model.getCustomerId())){
            throw new BussinessException("customerId cannot ne empty");
        }
        if(StringUtils.isBlank(model.getCurrency())){
            throw new BussinessException("currency cannot ne empty");
        }
        if(StringUtils.isBlank(model.getAmount())){
            throw new BussinessException("amount cannot ne empty");
        }
    }
}
