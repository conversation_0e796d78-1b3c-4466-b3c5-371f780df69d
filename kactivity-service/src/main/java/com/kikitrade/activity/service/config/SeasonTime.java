package com.kikitrade.activity.service.config;

import lombok.Data;

import java.io.Serializable;
import java.time.DayOfWeek;

@Data
public class SeasonTime implements Serializable {

    private Long startTime;
    private Long endTime;
    private DayOfWeek settleWeek;
    private Integer maxLevel = 6;

    private Integer seasonId;
    private String seasonName;
    private Long settlementTime;
    private String cycle;
    private Long cycleEndTime;

}
