package com.kikitrade.activity.service.reward.impl;

import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.reward.RewardTccService;
import lombok.extern.slf4j.Slf4j;

import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;

@Slf4j
public abstract class AbstractRewardTccService {

    @Resource
    @Lazy
    private RewardTccService rewardTccService;
    @Resource
    @Lazy
    private RewardTccService rewardAssetTccService;
    @Resource
    @Lazy
    private RewardTccService rewardNFTTccService;
    @Resource
    @Lazy
    private RewardTccService rewardGameItemTccService;
    @Resource
    @Lazy
    private RewardTccService rewardBadgeTccService;
    @Resource
    @Lazy
    private RewardTccService rewardSetsTccService;
    @Resource
    @Lazy
    private RewardTccService rewardExtraBoostTccService;
    @Resource
    @Lazy
    private RewardTccService rewardEarnallianceCustomerService;
    @Resource
    @Lazy
    private RewardTccService rewardEarnallianceService;

    public RewardTccService getService(String awardType){
        switch (ActivityConstant.AwardTypeEnum.getType(awardType)){
            case GAME_ITEM:
                return rewardGameItemTccService;
            case POINT, EXPERIENCE, TICKET, VOUCHER, BOOST, AURA, EXPERIENCE_VOUCHER, OJO, SEASON_POINT:
                return rewardAssetTccService;
            case NFT:
                return rewardNFTTccService;
            case BADGE:
                return rewardBadgeTccService;
            case SETS:
                return rewardSetsTccService;
            case EXTRA_BOOST:
                return rewardExtraBoostTccService;
            case EARNALLIANCE:
                return rewardEarnallianceService;
            case EARNALLIANCE_CUSTOMER:
                return rewardEarnallianceCustomerService;
            default:
                return rewardTccService;
        }
    }
}
