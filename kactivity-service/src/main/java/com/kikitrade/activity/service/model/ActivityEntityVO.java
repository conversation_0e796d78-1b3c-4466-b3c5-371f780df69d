package com.kikitrade.activity.service.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ActivityEntityVO implements Serializable {

    private String id;

    private String name;

    private String type;

    private String startTime;

    private String endTime;

    private String remark;

    private Integer status;

    private Boolean autoCreateBatch;

    //批次创建周期 one、每一天
    private String cycle;

    private Date nextCreateTime;

    //活动规则
    private String rewardConfig;

    private String area;
}
