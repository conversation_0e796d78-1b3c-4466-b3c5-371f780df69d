package com.kikitrade.activity.service.config;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.service.task.TaskFilter;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/20 16:28
 */
@Data
public class TaskCodeConfig {

    private String code;
    private String mainCode;
    private int inc;
    private String desc;
    private String splitCode;
    private List<Action> action;

    @Data
    public static class Action{
        private String app;
        private String bean;
    }

    private static final Map<String, TaskCodeConfig> map = new HashMap<>();

    public static void load(String config){
        List<TaskCodeConfig> codeConfig = JSON.parseArray(config, TaskCodeConfig.class);
        for(TaskCodeConfig config1 : codeConfig){
            map.put(config1.getCode(), config1);
        }
    }

    public static TaskCodeConfig getValue(String code){
        return map.get(code);
    }

    public List<TaskFilter> getAction(List<TaskFilter> taskFilters, String app) {
        if(CollectionUtils.isEmpty(action)){
            return null;
        }
        List<String> actions = action.stream().filter(a -> app.equalsIgnoreCase(a.getApp())).map(Action::getBean).toList();
        if(CollectionUtils.isEmpty(action)){
            return null;
        }
        List<TaskFilter> result = new ArrayList<>();
        for(TaskFilter filter : taskFilters){
            if(actions.contains(SpringUtil.getBeanNamesForType(filter.getClass())[0])){
                result.add(filter);
            }
        }
        return result;
    }

    public static Map<String, TaskCodeConfig> getAll(){
        return map;
    }
}
