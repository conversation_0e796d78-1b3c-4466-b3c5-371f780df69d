package com.kikitrade.activity.service.reward;

import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.service.reward.model.RewardRequest;

/**
 * Reward Service
 *
 * <AUTHOR>
 * @create 2021/10/25 7:52 下午
 * @modify
 */
public interface RewardService {

    /**
     * 统一发奖入口
     *
     * @param request
     * @return
     * @throws Exception
     */
    ActivityResponse reward(RewardRequest request) throws Exception;

}
