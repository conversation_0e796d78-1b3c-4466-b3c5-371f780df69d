package com.kikitrade.activity.service.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.listener.AbstractSharedListener;
import com.alibaba.nacos.api.exception.NacosException;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.dal.tablestore.builder.GoodsBuilder;
import com.kikitrade.activity.dal.tablestore.model.Goods;
import com.kikitrade.activity.service.task.TaskConfigService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class KactivityConfigListener{

    private static final String MATERIAL_TEMPLATE_DATA_ID = "kactivity-material-template.json";
    private static final String ACTIVITY_TASK = "kactivity-task.json";
    private static final String GOODS = "goods.json";

    @Resource
    private PropertiesConfigService propertiesConfigService;
    @Resource
    private TaskConfigService taskConfigService;
    @Resource
    private GoodsBuilder goodsBuilder;

    @PostConstruct
    public void init(){
        try {
            addKactivityTaskListener();
        } catch (NacosException e) {
            log.error("nacos addListener exception",e);
        }
    }

    /**
     * 监听物料模版变化
     * @throws NacosException
     */
    private void addKactivityMaterialTemplateListener() throws NacosException{
        String config = propertiesConfigService.getConfigService().getConfigAndSignListener(MATERIAL_TEMPLATE_DATA_ID, "kactivity", 1000, new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->kactivity-material change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                KactivityMaterialTemplateConfig.load(s2);
            }
        });
        log.info("mes->kactivity-material init:{}", config);
        KactivityMaterialTemplateConfig.load(config);
    }

    /**
     * 任务配置
     * @throws NacosException
     */
    private void addKactivityTaskListener() throws NacosException{
        propertiesConfigService.getConfigService().addListener(ACTIVITY_TASK, "kactivity", new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->kactivity-task change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                List<TaskConfigDTO> dtoList = new ArrayList<>();
                if(s2.trim().startsWith("[") && s2.trim().endsWith("]")){
                    dtoList = JSON.parseArray(s2, TaskConfigDTO.class);
                }else{
                    dtoList.add(JSON.parseObject(s2, TaskConfigDTO.class));
                }
                for(TaskConfigDTO configDTO : dtoList){
                    taskConfigService.upsert(configDTO, false);
                }
            }
        });
    }

    private void addGoodsListener() throws NacosException{
        propertiesConfigService.getConfigService().addListener(GOODS, "kactivity", new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->goods change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                List<Goods> goodses = JSON.parseArray(s2, Goods.class);
                for(Goods goods : goodses){
                    Goods obj = goodsBuilder.findById(goods.getGoodsId());
                    if(obj != null){
                        goodsBuilder.update(goods);
                    }else{
                        goodsBuilder.insert(goods);
                    }
                }
            }
        });
    }
}
