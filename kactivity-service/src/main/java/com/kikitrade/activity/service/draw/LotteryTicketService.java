package com.kikitrade.activity.service.draw;

import com.kikitrade.activity.api.model.draw.ExchangeTicketsDTO;
import com.kikitrade.activity.api.model.request.reward.ExchangeTicketsRequest;
import com.kikitrade.activity.api.model.response.reward.ExchangeTicketsResponse;

/**
 * 抽奖券管理服务接口
 * 支持两阶段抽奖系统的抽奖券兑换、消费、查询等功能
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface LotteryTicketService {
    
    /**
     * 资产兑换抽奖券
     * 
     * @param request 兑换请求
     * @return 兑换结果
     */
    ExchangeTicketsResponse exchangeTickets(ExchangeTicketsRequest request);

    /**
     * 冻结资产
     */
    boolean freezeAsset(ExchangeTicketsDTO exchangeTicketsDTO);

    /**
     * 解冻资产
     */
    boolean unfreezeAsset(ExchangeTicketsDTO exchangeTicketsDTO);

    /**
     * 解冻并扣减资产
     */
    boolean unfreezeSubtractAsset(ExchangeTicketsDTO exchangeTicketsDTO);

    /**
     * 消费抽奖券（用于抽奖）
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param ticketType 抽奖券类型
     * @param count 消费数量
     * @return 是否成功
     */
    boolean consumeTickets(String userId, String saasId, String ticketType, Integer count);
    
    /**
     * 退还抽奖券（抽奖失败时）
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param ticketType 抽奖券类型
     * @param count 退还数量
     * @return 是否成功
     */
    boolean refundTickets(String userId, String saasId, String ticketType, Integer count);
}
