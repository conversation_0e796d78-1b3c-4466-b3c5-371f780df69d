package com.kikitrade.activity.service.config;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class KactivityMaterialTemplateConfig {

    private static Map<String, List<MaterialTemplate>> map = new ConcurrentHashMap<>();

    public static List<MaterialTemplate> getValue(){
        return getValue("default", null);
    }

    public static List<MaterialTemplate> getValue(String templateCode, String superCode){
        return JSON.parseArray(map.get(templateCode) == null ? map.get("default").toString() : map.get(templateCode).toString(), MaterialTemplate.class);
    }

    public static void load(String content){
        if(StringUtils.isBlank(content)){
            return;
        }
        Map<String, List<MaterialTemplate>> materialTemplate = JSON.parseObject(content, Map.class);
        try{
            if(MapUtils.isNotEmpty(materialTemplate)){
                map = materialTemplate;
            }
        }catch (Exception ex){
            log.error("kactivity-sql parse error", ex);
        }
    }

    @Data
    public static class MaterialTemplate{
        private String superCode;
        private String code;
        private String alias;
        private String name;
        private String defaultValue;
        private String locale;
    }
}
