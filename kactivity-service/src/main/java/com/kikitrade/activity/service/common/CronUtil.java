package com.kikitrade.activity.service.common;

public class CronUtil {

    //秒 分 小时
    private static final String cronTemp = "0 * * * * ?";

    public static String getCronForHour(int hour){
        if(hour > 0){
            return String.format("0 0 0/%s * * ?", hour);
        }
        return null;
    }

    public static String getCronForMinute(int minute){
        if(minute > 0){
            return String.format("0 0/%s * * * ?", minute);
        }
        return null;
    }
}
