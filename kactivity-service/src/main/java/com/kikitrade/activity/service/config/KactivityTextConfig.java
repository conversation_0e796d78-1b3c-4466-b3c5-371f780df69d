package com.kikitrade.activity.service.config;

import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.StringReader;
import java.util.Map;
import java.util.Properties;

@Slf4j
public class KactivityTextConfig {

    private static final Properties properties = new Properties();

    public static void load(String content){
        if(StringUtils.isBlank(content)){
            return;
        }
        try {
            properties.load(new StringReader(content));
        } catch (IOException e) {
            log.error("kactivityTextConfig load error");
        }
    }

    private static final String CSV_HEAD_PREFIX = "reward.csv.head.";
    private static final String CSV_SHOW_HEAD_PREFIX = "reward.csv.show.head.";

    public static String getCsvHead(String activityType){
        return properties.getProperty(CSV_HEAD_PREFIX + activityType, properties.getProperty(CSV_HEAD_PREFIX + "default"));
    }

    public static String getCsvShowHead(String activityType){
        return properties.getProperty(CSV_SHOW_HEAD_PREFIX + activityType, properties.getProperty(CSV_SHOW_HEAD_PREFIX + "default"));
    }

    public static Properties getProperties() {
        return properties;
    }
}
