package com.kikitrade.activity.service.draw.preference;

import java.util.Map;

/**
 * 用户偏好管理服务接口
 * 支持用户各种偏好设置的存储和查询
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface UserPreferenceService {

    /**
     * 设置用户偏好
     *
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param preferenceType 偏好类型（如：SELECTED_HERO、VIP_LEVEL等）
     * @param preferenceValue 偏好值
     * @return 设置是否成功
     */
    boolean setUserPreference(String userId, String prizePoolCode, String preferenceType, String preferenceValue);

    /**
     * 获取用户偏好
     *
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param preferenceType 偏好类型
     * @return 偏好值，如果不存在则返回null
     */
    String getUserPreference(String userId, String prizePoolCode, String preferenceType);

    /**
     * 获取用户所有偏好
     *
     * @param userId 用户ID
     * @return 用户所有偏好的Map，key为偏好类型，value为偏好值
     */
    Map<String, String> getAllUserPreferences(String userId);

    /**
     * 获取用户针对特定奖池的所有偏好
     *
     * @param userId 用户ID
     * @param prizePoolCode 奖池编码
     * @return 用户所有偏好的Map，key为偏好类型，value为偏好值
     */
    Map<String, String> getAllUserPreferences(String userId, String prizePoolCode);

    /**
     * 删除用户偏好
     *
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param preferenceType 偏好类型
     * @return 删除是否成功
     */
    boolean removeUserPreference(String userId, String prizePoolCode, String preferenceType);

    /**
     * 清空用户所有偏好
     *
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @return 清空是否成功
     */
    boolean clearAllUserPreferences(String userId);
}