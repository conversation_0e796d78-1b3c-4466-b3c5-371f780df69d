package com.kikitrade.activity.service.goods;

import com.kikitrade.activity.dal.tablestore.model.Goods;
import com.kikitrade.framework.common.model.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/12/26 15:42
 */
public interface GoodsService {

    /**
     * 查询商品详情
     * @param goodsId
     * @return
     */
    Goods findById(String goodsId);

    /**
     * 商品列表
     * @param offset
     * @param limit
     * @return
     */
    Page<Goods> findAll(int offset, int limit, String saasId, String exclude);

    /**
     * 根据场景代码查询商品列表
     * @param sceneCode 场景代码
     * @return 商品列表
     */
    List<Goods> findBySceneCode(String sceneCode);
}
