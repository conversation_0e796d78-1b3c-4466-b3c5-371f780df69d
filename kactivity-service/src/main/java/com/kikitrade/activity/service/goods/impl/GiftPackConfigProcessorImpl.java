package com.kikitrade.activity.service.goods.impl;

import com.kikitrade.activity.dal.tablestore.builder.GiftPackConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.GiftPackConfig;
import com.kikitrade.activity.service.goods.GiftPackConfigProcessor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 礼包配置处理器实现类
 * 封装礼包配置的通用处理逻辑
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
@Slf4j
@Service
public class GiftPackConfigProcessorImpl implements GiftPackConfigProcessor {

    @Resource
    private GiftPackConfigBuilder giftPackConfigBuilder;

    @Override
    public <T> List<T> processGiftPackConfigs(String packId, String saasId,
                                              Function<GiftPackConfig, T> fixedItemProcessor,
                                              Function<GiftPackConfig, List<T>> randomPoolProcessor) {
        log.debug("处理礼包配置: packId={}, saasId={}", packId, saasId);
        
        try {
            // 获取有效配置
            List<GiftPackConfig> configs = getActiveConfigs(packId, saasId);
            
            if (CollectionUtils.isEmpty(configs)) {
                log.warn("未找到有效的礼包配置: packId={}, saasId={}", packId, saasId);
                return new ArrayList<>();
            }

            List<T> results = new ArrayList<>();
            
            // 处理每个配置规则
            for (GiftPackConfig config : configs) {
                try {
                    if (isFixedItem(config)) {
                        // 处理固定物品
                        if (isValidFixedItemConfig(config)) {
                            T item = fixedItemProcessor.apply(config);
                            if (item != null) {
                                results.add(item);
                                log.debug("处理固定物品成功: itemId={}", config.getItemId());
                            }
                        } else {
                            log.warn("固定物品配置无效: configId={}, itemId={}", config.getId(), config.getItemId());
                        }
                        
                    } else if (isRandomPool(config)) {
                        // 处理随机池物品
                        if (isValidRandomPoolConfig(config)) {
                            List<T> randomItems = randomPoolProcessor.apply(config);
                            if (CollectionUtils.isNotEmpty(randomItems)) {
                                results.addAll(randomItems);
                                log.debug("处理随机池成功: poolId={}, itemCount={}", 
                                    config.getRandomPoolId(), randomItems.size());
                            }
                        } else {
                            log.warn("随机池配置无效: configId={}, poolId={}, pickCount={}", 
                                config.getId(), config.getRandomPoolId(), config.getPickCount());
                        }
                        
                    } else {
                        log.warn("未知的规则类型: configId={}, ruleType={}", config.getId(), config.getRuleType());
                    }
                    
                } catch (Exception e) {
                    log.error("处理配置规则异常: configId={}, ruleType={}", config.getId(), config.getRuleType(), e);
                }
            }
            
            log.debug("礼包配置处理完成: packId={}, configCount={}, resultCount={}", 
                packId, configs.size(), results.size());
            return results;
            
        } catch (Exception e) {
            log.error("处理礼包配置异常: packId={}, saasId={}", packId, saasId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public <T> Map<String, List<T>> batchProcessGiftPackConfigs(List<String> packIds, String saasId,
                                                                 Function<GiftPackConfig, T> fixedItemProcessor,
                                                                 Function<GiftPackConfig, List<T>> randomPoolProcessor) {
        log.debug("批量处理礼包配置: packIds={}, saasId={}", packIds, saasId);
        
        Map<String, List<T>> result = new HashMap<>();
        
        if (CollectionUtils.isEmpty(packIds)) {
            return result;
        }
        
        try {
            // 批量获取配置
            Map<String, List<GiftPackConfig>> configMap = batchGetActiveConfigs(packIds, saasId);
            
            // 处理每个礼包的配置
            for (String packId : packIds) {
                List<GiftPackConfig> configs = configMap.get(packId);
                if (CollectionUtils.isEmpty(configs)) {
                    result.put(packId, new ArrayList<>());
                    continue;
                }
                
                List<T> items = new ArrayList<>();
                
                for (GiftPackConfig config : configs) {
                    try {
                        if (isFixedItem(config) && isValidFixedItemConfig(config)) {
                            T item = fixedItemProcessor.apply(config);
                            if (item != null) {
                                items.add(item);
                            }
                        } else if (isRandomPool(config) && isValidRandomPoolConfig(config)) {
                            List<T> randomItems = randomPoolProcessor.apply(config);
                            if (CollectionUtils.isNotEmpty(randomItems)) {
                                items.addAll(randomItems);
                            }
                        }
                    } catch (Exception e) {
                        log.error("批量处理配置规则异常: packId={}, configId={}", packId, config.getId(), e);
                    }
                }
                
                result.put(packId, items);
            }
            
            log.debug("批量处理礼包配置完成: packCount={}, resultCount={}", packIds.size(), result.size());
            return result;
            
        } catch (Exception e) {
            log.error("批量处理礼包配置异常: packIds={}, saasId={}", packIds, saasId, e);
            return result;
        }
    }

    @Override
    public List<GiftPackConfig> getActiveConfigs(String packId, String saasId) {
        try {
            // 查询礼包配置
            List<GiftPackConfig> configs = giftPackConfigBuilder.findByPackId(packId, saasId);
            
            if (CollectionUtils.isEmpty(configs)) {
                return new ArrayList<>();
            }
            
            // 过滤激活状态的配置并按规则顺序排序
            return configs.stream()
                .filter(config -> Boolean.TRUE.equals(config.getIsActive()))
                .sorted(Comparator.comparing(config -> 
                    config.getRuleOrder() != null ? config.getRuleOrder() : Integer.MAX_VALUE))
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            log.error("获取有效配置异常: packId={}, saasId={}", packId, saasId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, List<GiftPackConfig>> batchGetActiveConfigs(List<String> packIds, String saasId) {
        Map<String, List<GiftPackConfig>> result = new HashMap<>();
        
        if (CollectionUtils.isEmpty(packIds)) {
            return result;
        }
        
        try {
            // 批量查询所有礼包配置
            for (String packId : packIds) {
                List<GiftPackConfig> configs = getActiveConfigs(packId, saasId);
                result.put(packId, configs);
            }
            
        } catch (Exception e) {
            log.error("批量获取有效配置异常: packIds={}, saasId={}", packIds, saasId, e);
        }
        
        return result;
    }

    @Override
    public Integer calculateRandomQuantity(Integer min, Integer max) {
        if (min == null) min = 1;
        if (max == null) max = min;
        
        if (min.equals(max)) {
            return min;
        }
        
        // 确保min <= max
        if (min > max) {
            int temp = min;
            min = max;
            max = temp;
        }
        
        return ThreadLocalRandom.current().nextInt(min, max + 1);
    }

    @Override
    public Integer calculateDisplayQuantity(Integer min, Integer max) {
        if (min == null && max == null) {
            return 1;
        }
        if (min == null) {
            return max;
        }
        if (max == null) {
            return min;
        }
        if (min.equals(max)) {
            return min;
        }
        
        // 返回中间值作为展示数量
        return (min + max) / 2;
    }
}
