package com.kikitrade.activity.service.task;

import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.mq.ActivityEventMessage;

public interface TaskFilter {

    void filter(ActivityEventMessage activityEventMessage, TaskConfigDTO config) throws ActivityException;

    FilterScope getScope();

    enum FilterScope{
        COMMON,
        CODE,
        TASK
        ;
    }
}
