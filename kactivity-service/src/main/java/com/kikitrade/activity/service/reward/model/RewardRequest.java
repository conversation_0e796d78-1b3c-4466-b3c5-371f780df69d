package com.kikitrade.activity.service.reward.model;

import com.kikitrade.activity.model.constant.ActivityConstant;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * OperationRewardRequest model
 *
 * <AUTHOR>
 * @create 2021/10/25 8:38 下午
 * @modify
 */
@Data
@Builder
public class RewardRequest implements Serializable {
    private String customerId;
    private String currency;
    private BigDecimal amount;
    private String type;
    private String rewardId;
    private String nickName;
    private String activityName;
    private ActivityConstant.SideEnum side;
    private String businessType;
    private String desc;
    private String address;
    private String goodsId;
    private String saasId;
    private Long receiveEndTime;
    @Builder.Default
    private ActivityConstant.BadgeTypeEnum badgeType = ActivityConstant.BadgeTypeEnum.BADGE_ID;
    private Integer level;
    private String seasonId;
    private String cycle;
    private String twitterId;
}
