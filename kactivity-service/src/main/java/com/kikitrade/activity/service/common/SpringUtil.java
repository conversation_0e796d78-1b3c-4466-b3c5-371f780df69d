package com.kikitrade.activity.service.common;

import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;


public class SpringUtil {

    // Spring应用上下文环境
    private static ApplicationContext applicationContext;

    /**
     * 实现ApplicationContextAware接口的回调方法，设置上下文环境
     *
     * @param applicationContext
     */
    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        SpringUtil.applicationContext = applicationContext;
    }

    /**
     * @return ApplicationContext
     */
    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }


    /**
     * 获取注入对象
     * <p> TODO</p>
     *
     * @author: Liu <PERSON>i
     * @param: @param name
     * @param: @return
     * @return: Object
     * @Date :          2015年9月17日 上午11:56:57
     * @throws:
     */
    public static <T> T getBean(String name) {
        try {
            return (T) applicationContext.getBean(name);
        } catch (NoSuchBeanDefinitionException e) {
        }
        return null;
    }

    public static <T> T getBean(Class<T> clazz) {
        try {
            return (T) applicationContext.getBean(clazz);
        } catch (NoSuchBeanDefinitionException e) {
        }
        return null;
    }

    public static <T> T getBean(String name, Class<T> cls) {
        try {
            return applicationContext.getBean(name, cls);
        } catch (NoSuchBeanDefinitionException e) {
        }
        return null;
    }

    /**
     * 国际化
     *
     * @param code key值
     * @return
     */
    public static String diff(String code) {
        String message = "";
        Locale locale = LocaleContextHolder.getLocale();

        try {
            String[] split = code.split("~");
            if (split.length > 1) {
                message = applicationContext.getMessage(split[0], new Object[]{split[1]}, locale);
                if (message == null || "".equals(message)) {
                    return "错误001";
                }
            } else {
                message = applicationContext.getMessage(code, null, locale);
                if (message == null || "".equals(message)) {
                    return "";
                }
            }
        } catch (Exception e) {
			// e.printStackTrace();
            // 找不到msgKey，忽略错误，不会再使用后端的msg信息了
            return "";

        }
        return message;
    }

    /**
     * 国际化
     *
     * @param language 当前语言
     * @param code     key值
     * @return
     */
    public static String diff(String code, String language) {
        String message = "";
        //Locale locale = LocaleContextHolder.getLocale();
        String[] strings = language.split("_");
        Locale locale = null;

        if (strings.length > 1) {
            locale = new Locale(strings[0], strings[1]);
        } else {
            locale = new Locale(strings[0]);
        }

        try {
            if (code == null || code.equals("")) {
                return "";
            }
            String[] split = code.split("~");
            if (split.length > 1) {
                message = applicationContext.getMessage(split[0], new Object[]{split[1]}, locale);
                if (message == null || "".equals(message)) {
                    return "错误001";
                }
            } else {
                message = applicationContext.getMessage(code, null, locale);
                if (message == null || "".equals(message)) {
                    return "错误001";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "错误002";

        }
        return message;
    }
}