package com.kikitrade.activity.service.task;

import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.service.config.TaskCodeConfig;
import com.kikitrade.activity.service.mq.ActivityEventMessage;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

public interface ActivityTaskTccService {

    /**
     * 做任务
     * @param activityEventMessage
     */
    List<Award> doTask(ActivityTaskItem taskItem, ActivityEventMessage activityEventMessage, TaskConfigDTO taskConfig, TaskCodeConfig codeConfig) throws Exception;

    List<Award> calAndReward(AtomicLong progress, ActivityTaskItem taskItem, TaskConfigDTO taskConfig, Map<String, Object> param) throws Exception;
}
