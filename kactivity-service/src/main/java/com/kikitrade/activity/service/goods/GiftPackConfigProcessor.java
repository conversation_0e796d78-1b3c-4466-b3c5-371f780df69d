package com.kikitrade.activity.service.goods;

import com.kikitrade.activity.dal.tablestore.model.GiftPackConfig;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 礼包配置处理器接口
 * 封装礼包配置的通用处理逻辑，避免重复代码
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
public interface GiftPackConfigProcessor {

    /**
     * 礼包规则类型常量
     */
    String RULE_TYPE_FIXED_ITEM = "FIXED_ITEM";
    String RULE_TYPE_RANDOM_POOL_PICK = "RANDOM_POOL_PICK";

    /**
     * 处理单个礼包的配置，生成对应的物品列表
     * 
     * @param packId 礼包ID
     * @param saasId SaaS ID
     * @param fixedItemProcessor 固定物品处理器
     * @param randomPoolProcessor 随机池处理器
     * @param <T> 返回的物品类型
     * @return 处理后的物品列表
     */
    <T> List<T> processGiftPackConfigs(String packId, String saasId,
                                       Function<GiftPackConfig, T> fixedItemProcessor,
                                       Function<GiftPackConfig, List<T>> randomPoolProcessor);

    /**
     * 批量处理多个礼包的配置
     * 
     * @param packIds 礼包ID列表
     * @param saasId SaaS ID
     * @param fixedItemProcessor 固定物品处理器
     * @param randomPoolProcessor 随机池处理器
     * @param <T> 返回的物品类型
     * @return 礼包ID到物品列表的映射
     */
    <T> Map<String, List<T>> batchProcessGiftPackConfigs(List<String> packIds, String saasId,
                                                          Function<GiftPackConfig, T> fixedItemProcessor,
                                                          Function<GiftPackConfig, List<T>> randomPoolProcessor);

    /**
     * 获取礼包的有效配置列表
     * 
     * @param packId 礼包ID
     * @param saasId SaaS ID
     * @return 有效的配置列表（已过滤未激活的配置并按规则顺序排序）
     */
    List<GiftPackConfig> getActiveConfigs(String packId, String saasId);

    /**
     * 批量获取礼包的有效配置列表
     * 
     * @param packIds 礼包ID列表
     * @param saasId SaaS ID
     * @return 礼包ID到配置列表的映射
     */
    Map<String, List<GiftPackConfig>> batchGetActiveConfigs(List<String> packIds, String saasId);

    /**
     * 计算随机数量（在最小值和最大值之间）
     * 用于实际发放时的随机数量计算
     * 
     * @param min 最小值
     * @param max 最大值
     * @return 随机数量
     */
    Integer calculateRandomQuantity(Integer min, Integer max);

    /**
     * 计算展示数量
     * 用于展示礼包内容时的数量计算（通常取中间值或固定值）
     * 
     * @param min 最小值
     * @param max 最大值
     * @return 展示数量
     */
    Integer calculateDisplayQuantity(Integer min, Integer max);

    /**
     * 判断配置是否为固定物品类型
     * 
     * @param config 礼包配置
     * @return 是否为固定物品类型
     */
    default boolean isFixedItem(GiftPackConfig config) {
        return RULE_TYPE_FIXED_ITEM.equals(config.getRuleType());
    }

    /**
     * 判断配置是否为随机池类型
     * 
     * @param config 礼包配置
     * @return 是否为随机池类型
     */
    default boolean isRandomPool(GiftPackConfig config) {
        return RULE_TYPE_RANDOM_POOL_PICK.equals(config.getRuleType());
    }

    /**
     * 验证固定物品配置的有效性
     * 
     * @param config 礼包配置
     * @return 是否有效
     */
    default boolean isValidFixedItemConfig(GiftPackConfig config) {
        return config != null && config.getItemId() != null;
    }

    /**
     * 验证随机池配置的有效性
     * 
     * @param config 礼包配置
     * @return 是否有效
     */
    default boolean isValidRandomPoolConfig(GiftPackConfig config) {
        return config != null && config.getRandomPoolId() != null && config.getPickCount() != null;
    }
}
