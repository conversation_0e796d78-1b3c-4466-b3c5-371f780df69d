package com.kikitrade.activity.service.remote.impl;

import com.kikitrade.activity.api.RemoteRewardService;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.api.model.response.TaskCompletedResult;
import com.kikitrade.activity.dal.tablestore.builder.ActivityTaskItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
import com.kikitrade.activity.service.task.ActivityTaskService;
import com.kikitrade.activity.service.task.ActivityTaskTccService;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.activity.service.task.domain.TaskCycleDomain;
import com.kikitrade.kseq.api.SeqClient;
import com.kikitrade.kseq.api.model.SeqRule;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/27 17:47
 */
@DubboService
@Slf4j
public class RemoteRewardServiceImpl implements RemoteRewardService {

    @Resource
    private TaskConfigService taskConfigService;
    @Resource
    private ActivityTaskItemBuilder activityTaskItemBuilder;
    @Resource
    private ActivityTaskService activityTaskService;
    @Resource
    private ActivityTaskTccService activityTaskTccService;
    @Resource
    private ActivityRealTimeRewardTccService activityRealTimeRewardTccService;
    @Resource
    private SeqClient seqClient;

    /**
     * 手动领取某个任务的奖励
     * @param customerId
     * @param address
     * @param taskId
     * @return
     * @throws Exception
     */
    @Override
    public Boolean receiveReward(String customerId, String address, String taskId) throws Exception {
        log.info("receiveReward request:{}, {}, {}", customerId, address, taskId);
        //检查任务是否都完成
        TaskConfigDTO taskConfigDTO = taskConfigService.findByTaskIdAndWhiteFlag(taskId, customerId, ActivityTaskConstant.TaskConfigScope.SUB_TASK);
        if (Objects.isNull(taskConfigDTO)) {
            log.warn("receiveReward taskConfigDTO is null");
            return false;
        }
        List<TaskConfigDTO> subTask = taskConfigDTO.getSubTask();
        for(TaskConfigDTO configDTO : subTask){
            TaskCompletedResult taskResult = activityTaskService.getTaskResult(customerId, TaskCycleDomain.getCurrencyCycle(configDTO, null), configDTO.getTaskId());
            log.info("receiveReward taskResult:{}", taskResult);
            if(!taskResult.isDone()){
                log.info("receiveReward customerId:{}, cycle:{}, taskId:{}, not done", customerId, TaskCycleDomain.getCurrencyCycle(configDTO, null), configDTO.getTaskId());
                return false;
            }
        }
        try{
            //发奖
            LauncherParameter parameter = new LauncherParameter();
            ActivityCustomReward activityCustomReward = new ActivityCustomReward();
            activityCustomReward.setCustomerId(customerId);
            activityCustomReward.setAddress(address);

            Award reward = taskConfigDTO.getReward().get("0").get(0);
            activityCustomReward.setSaasId(taskConfigDTO.getSaasId());
            activityCustomReward.setAmount(reward.getAmount());
            activityCustomReward.setCurrency(reward.getCurrency());
            activityCustomReward.setRewardType(reward.getType());
            activityCustomReward.setBatchId(TaskCycleDomain.getCurrencyCycle(taskConfigDTO.getSubTask().get(0), null));
            activityCustomReward.setSeq(taskConfigDTO.getTaskId());
            activityCustomReward.addExtendParam("desc", "mugen game");
            activityCustomReward.setBusinessType(ActivityConstant.RewardBusinessType.reward.name());
            activityCustomReward.setBusinessId(seqClient.next(new SeqRule("IDX_ACTIVITY_REWARD", 1, "yyyyMMddHHmmss", null)));
            parameter.setActivityCustomReward(activityCustomReward);

            log.info("receiveReward reward:{}", activityCustomReward);
            activityRealTimeRewardTccService.reward(parameter);
            return true;
        }catch (Exception exception){
            log.error("receiveReward error:{}, {}, {}", customerId, taskId, address, exception);
            throw exception;
        }
    }

    @Override
    public Boolean rewardPoint(String customerId, String price, String saasId, String desc, String businessId) throws Exception{
        try{
            //发奖
            LauncherParameter parameter = new LauncherParameter();
            ActivityCustomReward activityCustomReward = new ActivityCustomReward();
            activityCustomReward.setCustomerId(customerId);
            activityCustomReward.setSaasId(saasId);
            activityCustomReward.setAmount(price);
            activityCustomReward.setCurrency(ActivityConstant.AwardTypeEnum.POINT.name());
            activityCustomReward.setRewardType(ActivityConstant.AwardTypeEnum.POINT.name());
            activityCustomReward.setBatchId(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHmmss));
            activityCustomReward.setSeq("fixed:"+customerId);
            activityCustomReward.addExtendParam("desc", desc == null ? "fixed point" : desc);
            activityCustomReward.setBusinessId(businessId == null ? seqClient.next(new SeqRule("IDX_ACTIVITY_REWARD", 1, "yyyyMMddHHmmss", null)) : businessId);
            parameter.setActivityCustomReward(activityCustomReward);

            log.info("receiveReward reward:{}", activityCustomReward);
            activityRealTimeRewardTccService.reward(parameter);
            return true;
        }catch (Exception exception){
            log.error("receiveReward error:{}, {}", customerId, price, exception);
            throw exception;
        }
    }
}
