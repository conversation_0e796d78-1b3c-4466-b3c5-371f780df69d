package com.kikitrade.activity.service.goods.impl;

import com.kikitrade.activity.api.model.response.GoodsDetailResponse;
import com.kikitrade.activity.dal.tablestore.model.GiftPackConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.goods.GiftPackConfigProcessor;
import com.kikitrade.activity.service.goods.GiftPackService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;

/**
 * 礼包处理服务实现类
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
@Slf4j
@Service
public class GiftPackServiceImpl implements GiftPackService {

    @Resource
    private GiftPackConfigProcessor giftPackConfigProcessor;

    @Override
    public List<GoodsDetailResponse.GiftPackItem> getGiftPackItems(String packId, String saasId) {
        log.debug("获取礼包物品信息: packId={}, saasId={}", packId, saasId);

        // 定义固定物品处理器
        Function<GiftPackConfig, GoodsDetailResponse.GiftPackItem> fixedItemProcessor = this::createGiftPackItem;

        // 定义随机池处理器
        Function<GiftPackConfig, List<GoodsDetailResponse.GiftPackItem>> randomPoolProcessor = this::createRandomPoolItems;

        // 使用通用处理器处理配置
        return giftPackConfigProcessor.processGiftPackConfigs(packId, saasId, fixedItemProcessor, randomPoolProcessor);
    }

    @Override
    public Map<String, List<GoodsDetailResponse.GiftPackItem>> batchGetGiftPackItems(List<String> packIds, String saasId) {
        log.debug("批量获取礼包物品信息: packIds={}, saasId={}", packIds, saasId);

        if (CollectionUtils.isEmpty(packIds)) {
            return new HashMap<>();
        }

        // 定义固定物品处理器
        Function<GiftPackConfig, GoodsDetailResponse.GiftPackItem> fixedItemProcessor = this::createGiftPackItem;

        // 定义随机池处理器
        Function<GiftPackConfig, List<GoodsDetailResponse.GiftPackItem>> randomPoolProcessor = this::createRandomPoolItems;

        // 使用通用处理器批量处理配置
        return giftPackConfigProcessor.batchProcessGiftPackConfigs(packIds, saasId, fixedItemProcessor, randomPoolProcessor);
    }

    @Override
    public boolean isGiftPack(String goodsType) {
        return ActivityConstant.GoodsType.GIFT_PACK.getCode().equals(goodsType);
    }

    /**
     * 创建礼包物品对象（固定物品）
     */
    private GoodsDetailResponse.GiftPackItem createGiftPackItem(GiftPackConfig config) {
        try {
            GoodsDetailResponse.GiftPackItem item = new GoodsDetailResponse.GiftPackItem();
            item.setItemId(config.getItemId());
            item.setItemType(config.getRuleType());
//            item.setItemName(); // 实际应该从物品表查询

            // 计算展示数量
            Integer quantity = giftPackConfigProcessor.calculateDisplayQuantity(config.getQuantityMin(), config.getQuantityMax());
            item.setQuantity(quantity);

            item.setDescription("固定发放的物品");
            // item.setIconUrl(); // 实际应该从物品表查询

            return item;

        } catch (Exception e) {
            log.error("创建礼包物品对象异常: configId={}", config.getId(), e);
            return null;
        }
    }

    /**
     * 创建随机池物品对象列表
     */
    private List<GoodsDetailResponse.GiftPackItem> createRandomPoolItems(GiftPackConfig config) {
        // 这里只是示例实现，实际应该查询随机池配置
        List<GoodsDetailResponse.GiftPackItem> items = new ArrayList<>();

        try {
            // 创建一个示例随机物品（实际应该查询随机池表）
            GoodsDetailResponse.GiftPackItem item = new GoodsDetailResponse.GiftPackItem();
            item.setItemId("random_" + config.getRandomPoolId());
            item.setItemType("RANDOM");
            item.setItemName("随机物品池_" + config.getRandomPoolId());
            item.setQuantity(config.getPickCount());
            item.setDescription("从随机池中抽取 " + config.getPickCount() + " 个物品");

            items.add(item);

        } catch (Exception e) {
            log.error("创建随机池物品对象异常: configId={}", config.getId(), e);
        }

        return items;
    }
}
