package com.kikitrade.activity.service.reward;

import com.kikitrade.activity.service.reward.model.RewardRequest;

/**
 * Reward Tcc Service Interface
 *
 * <AUTHOR>
 * @create 2022/3/9 11:13 上午
 * @modify
 */
public interface RewardTccService {

    /**
     * 根据不同的发奖类型，返回不同的service
     * @param awardType
     * @return
     */
    RewardTccService getService(String awardType);

    /**
     * @param request
     * @return
     * @throws Exception
     */
    void tryReward(RewardRequest request) throws Exception;
}
