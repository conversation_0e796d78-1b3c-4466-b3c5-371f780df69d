package com.kikitrade.activity.service.task;

import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/20 13:52
 */
public interface TaskConfigService {

    /**
     * 修改或创建任务配置
     * @param taskConfigDTO
     * @return
     */
    boolean upsert(TaskConfigDTO taskConfigDTO, Boolean isGray);


    /**
     * 修改或创建任务配置
     * @param mainTaskConfigDTO
     * @param subTaskConfigDTO
     * @return
     */
    boolean upsert(TaskConfigDTO mainTaskConfigDTO, List<TaskConfigDTO> subTaskConfigDTO);

    /**
     * 根据任务id查询任务
     * @param taskId
     * @return
     */
    TaskConfigDTO findByTaskId(String taskId, ActivityTaskConstant.TaskConfigScope... scopes);

    /**
     * 根据任务id和白名单状态查询任务配置信息
     * @param taskId
     * @param customerId
     * @param scopes
     * @return
     */
    TaskConfigDTO findByTaskIdAndWhiteFlag(String taskId, String customerId, ActivityTaskConstant.TaskConfigScope... scopes);

    /**
     * 根据任务id查询任务
     * @param groupTaskId
     * @return
     */
    List<TaskConfigDTO> findByGroupTaskId(String groupTaskId, boolean taskWhiteFlag);

    /**
     * 根据code查询任务项
     * @param code
     * @return
     */
    List<TaskConfigDTO> findByTaskCode(String saasId, String code, ActivityTaskConstant.TaskConfigScope... scopes);

    /**
     * 根据taskCode和白名单状态查询任务配置信息
     * @param saasId
     * @param code
     * @param customerId
     * @param scopes
     * @return
     */
    List<TaskConfigDTO> findByTaskCodeAndWhiteFlag(String saasId, String code, String customerId, ActivityTaskConstant.TaskConfigScope... scopes);

    /**
     * 根据code查询任务项
     * @param clientType ios, android
     * @return
     */
    List<TaskConfigDTO> findTaskBySaasId(String saasId, String channel, String position, String clientType, boolean taskWhiteFlag);

    String nextId(Boolean isSub);

    void delete(String id);
}
