package com.kikitrade.activity.service.mq;

import lombok.Data;

import java.util.Map;

@Data
public class ActivityEventMessage {

    /**
     * 事件code
     */
    private String eventCode;

    /**
     * 用户id
     */
    private String customerId;

    /**
     * 来源，文章id等
     */
    private String globalUid;

    /**
     * 事件发生事件
     */
    private Long eventTime;


    /**
     * 文章id，达人id等
     */
    private String targetId;

    /**
     * 文章类型区分
     */
    private String verb;

    private String targetCustomerId;

    private String sourceCustomerId;

    private Map<String, Object> body;

    private Integer inc;
}