package com.kikitrade.activity.service.config;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/1/5 17:06
 */
@Data
@Slf4j
public class KactivityClassicsLotteryConfig {

    private String valid;
    private String type;
    private Integer amount;
    private Integer limitTimes;
    private String currency;
    private Integer sameNum;
    private List<List<String>> nodes;
    private List<Award> awards;
    @Data
    public static class Award {
        private String name;
        private Integer multiple;
        private String currency;
        private Condition condition;
    }

    @Data
    public static class Condition {
        private Integer num;
        private String min;
        private String max;
    }

    private static KactivityClassicsLotteryConfig lotteryConfigs;

    public static KactivityClassicsLotteryConfig getLotteryConfigs() {
        return lotteryConfigs;
    }

    public static void load(KactivityClassicsLotteryConfig lottery){
        lotteryConfigs = lottery;
        log.info("lotteryConfigs:{}", lotteryConfigs);
    }
}
