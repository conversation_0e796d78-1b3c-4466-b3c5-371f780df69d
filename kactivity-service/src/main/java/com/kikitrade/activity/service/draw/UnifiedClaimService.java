package com.kikitrade.activity.service.draw;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 统一领奖服务接口
 * 按照技术规格书要求实现统一的领奖能力
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface UnifiedClaimService {
    
    /**
     * 创建领奖凭证
     * 
     * @param request 创建凭证请求
     * @return 创建结果
     */
    EntitlementCreateResult createEntitlement(CreateEntitlementRequest request);
    
    /**
     * 统一领奖接口
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param claimId 凭证ID
     * @return 领奖结果
     */
    UnifiedClaimResult claimReward(String userId, String claimId);
    
    /**
     * 获取用户可领取的奖励列表
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @return 可领取的奖励列表
     */
    List<ClaimableReward> getClaimableRewards(String userId);
    
    /**
     * 批量创建凭证（用于进度宝箱等自动生成场景）
     * 
     * @param requests 创建凭证请求列表
     * @return 创建结果列表
     */
    List<EntitlementCreateResult> batchCreateEntitlements(List<CreateEntitlementRequest> requests);
    
    /**
     * 清理过期凭证
     * 
     * @param saasId SaaS ID
     * @return 清理的凭证数量
     */
    int cleanupExpiredEntitlements(String saasId);
    
    /**
     * 创建凭证请求
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class CreateEntitlementRequest {
        private String userId;
        private String saasId;
        private String rewardType;
        private String rewardSourceId;
        private String rewardName;
        private String rewardIcon;
        private String rewardDescription;
        private String sourceChannel;
        private String sourceTransactionId;
        private Long expireTime;
    }
    
    /**
     * 凭证创建结果
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class EntitlementCreateResult {
        private boolean success;
        private String claimId;
        private String errorCode;
        private String message;
    }
    
    /**
     * 统一领奖结果
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class UnifiedClaimResult {
        private boolean success;
        private String errorCode;
        private String message;
        private List<ClaimedReward> rewards;
    }
    
    /**
     * 可领取的奖励信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ClaimableReward {
        private String claimId;
        private String rewardType;
        private String rewardName;
        private String rewardIcon;
        private String rewardDescription;
        private Long createTime;
        private Long expireTime;
    }
    
    /**
     * 已领取的奖励信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ClaimedReward {
        private String itemId;
        private String itemName;
        private Integer quantity;
        private String itemType;
        private String description;
        
        public ClaimedReward(String itemId, String itemName, Integer quantity) {
            this.itemId = itemId;
            this.itemName = itemName;
            this.quantity = quantity;
        }
    }
}
