package com.kikitrade.activity.service.task.domain;

import com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/2/5 17:57
 */
public class TaskProgressDomain {

    private static final long INIT_PROGRESS = 1L;

    public static Long getProgress(ActivityTaskConstant.ProgressTypeEnum progressTypeEnum, ActivityTaskItem yesterdayTaskItem, Long progress){
        if(progressTypeEnum == ActivityTaskConstant.ProgressTypeEnum.series){
            if(yesterdayTaskItem != null){
                return progress;
            }else{
                return INIT_PROGRESS;
            }
        }else{
            return progress;
        }
    }
}
