package com.kikitrade.activity.service.config;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.facade.roster.FilterType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@Slf4j
public class KactivitySqlConfig {

    private static Map<String, SqlTemplate> map = new ConcurrentHashMap<>();

    public static SqlTemplate getValue(String templateId){
        log.info("KactivitySqlConfig:{},{}", templateId, map);
        return map.get(templateId);
    }

    public static Set<String> values(){
        return map.keySet();
    }

    public static void load(String content){
        map.clear();
        if(StringUtils.isBlank(content)){
            return;
        }
        try{
            List<SqlTemplate> sqlTemplates = JSON.parseArray(content, SqlTemplate.class);
            log.info("sqlTemplates:{}", JSON.toJSONString(sqlTemplates));
            Map<String, SqlTemplate> templateMap = sqlTemplates.stream().collect(Collectors.toMap(SqlTemplate::getTemplateCode, Function.identity(), (k1, k2) -> k2));
            if(MapUtils.isNotEmpty(templateMap)){
                map = templateMap;
            }
        }catch (Exception ex){
            log.error("kactivity-sql parse error", ex);
        }
    }

    @Data
    public static class SqlTemplate{
        private String templateCode;
        private String sql;
        private List<Condition> conditions;
    }

    @Data
    public static class Condition{
        private String name;
        private String alias;
        private String filter;
    }
}
