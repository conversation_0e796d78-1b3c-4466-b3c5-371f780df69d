{"abi": [{"type": "function", "name": "approve", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getApproved", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "isApprovedForAll", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "ownerOf", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "safeTransferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "safeTransferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setApprovalForAll", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "_approved", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "approved", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ApprovalForAll", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "approved", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "getApproved(uint256)": "081812fc", "isApprovedForAll(address,address)": "e985e9c5", "ownerOf(uint256)": "6352211e", "safeTransferFrom(address,address,uint256)": "42842e0e", "safeTransferFrom(address,address,uint256,bytes)": "b88d4fde", "setApprovalForAll(address,bool)": "a22cb465", "transferFrom(address,address,uint256)": "23b872dd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.23+commit.f704f362\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"approved\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"ApprovalForAll\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"getApproved\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isApprovedForAll\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"ownerOf\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"_approved\",\"type\":\"bool\"}],\"name\":\"setApprovalForAll\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Required interface of an ERC721 compliant contract.\",\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when `owner` enables `approved` to manage the `tokenId` token.\"},\"ApprovalForAll(address,address,bool)\":{\"details\":\"Emitted when `owner` enables or disables (`approved`) `operator` to manage all of its assets.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `tokenId` token is transferred from `from` to `to`.\"}},\"kind\":\"dev\",\"methods\":{\"approve(address,uint256)\":{\"details\":\"Gives permission to `to` to transfer `tokenId` token to another account. The approval is cleared when the token is transferred. Only a single account can be approved at a time, so approving the zero address clears previous approvals. Requirements: - The caller must own the token or be an approved operator. - `tokenId` must exist. Emits an {Approval} event.\"},\"balanceOf(address)\":{\"details\":\"Returns the number of tokens in ``owner``'s account.\"},\"getApproved(uint256)\":{\"details\":\"Returns the account approved for `tokenId` token. Requirements: - `tokenId` must exist.\"},\"isApprovedForAll(address,address)\":{\"details\":\"Returns if the `operator` is allowed to manage all of the assets of `owner`. See {setApprovalForAll}\"},\"ownerOf(uint256)\":{\"details\":\"Returns the owner of the `tokenId` token. Requirements: - `tokenId` must exist.\"},\"safeTransferFrom(address,address,uint256)\":{\"details\":\"Safely transfers `tokenId` token from `from` to `to`, checking first that contract recipients are aware of the ERC721 protocol to prevent tokens from being forever locked. Requirements: - `from` cannot be the zero address. - `to` cannot be the zero address. - `tokenId` token must exist and be owned by `from`. - If the caller is not `from`, it must be have been allowed to move this token by either {approve} or {setApprovalForAll}. - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer. Emits a {Transfer} event.\"},\"safeTransferFrom(address,address,uint256,bytes)\":{\"details\":\"Safely transfers `tokenId` token from `from` to `to`. Requirements: - `from` cannot be the zero address. - `to` cannot be the zero address. - `tokenId` token must exist and be owned by `from`. - If the caller is not `from`, it must be approved to move this token by either {approve} or {setApprovalForAll}. - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer. Emits a {Transfer} event.\"},\"setApprovalForAll(address,bool)\":{\"details\":\"Approve or remove `operator` as an operator for the caller. Operators can call {transferFrom} or {safeTransferFrom} for any token owned by the caller. Requirements: - The `operator` cannot be the caller. Emits an {ApprovalForAll} event.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"Transfers `tokenId` token from `from` to `to`. WARNING: Usage of this method is discouraged, use {safeTransferFrom} whenever possible. Requirements: - `from` cannot be the zero address. - `to` cannot be the zero address. - `tokenId` token must be owned by `from`. - If the caller is not `from`, it must be approved to move this token by either {approve} or {setApprovalForAll}. Emits a {Transfer} event.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/eip/interface/IERC721.sol\":\"IERC721\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":20},\"remappings\":[\":@chainlink/=lib/chainlink/\",\":@ds-test/=lib/ds-test/src/\",\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":@rari-capital/solmate/=lib/seaport/lib/solmate/\",\":@seaport/=lib/seaport/contracts/\",\":@std/=lib/forge-std/src/\",\":@thirdweb-dev/dynamic-contracts/=lib/dynamic-contracts/\",\":ERC721A-Upgradeable/=lib/ERC721A-Upgradeable/contracts/\",\":ERC721A/=lib/ERC721A/contracts/\",\":chainlink/=lib/chainlink/contracts/\",\":contracts/=contracts/\",\":ds-test/=lib/ds-test/src/\",\":dynamic-contracts/=lib/dynamic-contracts/src/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":erc721a-upgradeable/=lib/ERC721A-Upgradeable/\",\":erc721a/=lib/ERC721A/\",\":forge-std/=lib/forge-std/src/\",\":lib/sstore2/=lib/dynamic-contracts/lib/sstore2/\",\":murky/=lib/murky/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":seaport-core/=lib/seaport/lib/seaport-core/\",\":seaport-sol/=lib/seaport-sol/src/\",\":seaport-types/=lib/seaport/lib/seaport-types/\",\":seaport/=lib/seaport/\",\":solady/=lib/solady/\",\":solarray/=lib/seaport/lib/solarray/src/\",\":solmate/=lib/seaport/lib/solmate/src/\",\":sstore2/=lib/dynamic-contracts/lib/sstore2/contracts/\"]},\"sources\":{\"contracts/eip/interface/IERC721.sol\":{\"keccak256\":\"0x00754fd93079350acb6b1d0451320cc4b4252bf7990aa39cde29abb5ca4a60ea\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c91392521b426c02b342d4ba0f2c5cad2c6074e1be90203c9525751dd23b36bf\",\"dweb:/ipfs/QmNjjCttJ9A39HMn3FTMqjQN5y7UoQrPUhQ7Nd8Ci7jFZj\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.23+commit.f704f362"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "approved", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "tokenId", "type": "uint256", "indexed": true}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "operator", "type": "address", "indexed": true}, {"internalType": "bool", "name": "approved", "type": "bool", "indexed": false}], "type": "event", "name": "ApprovalForAll", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "tokenId", "type": "uint256", "indexed": true}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getApproved", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "ownerOf", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "safeTransferFrom"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "safeTransferFrom"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "_approved", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setApprovalForAll"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom"}], "devdoc": {"kind": "dev", "methods": {"approve(address,uint256)": {"details": "Gives permission to `to` to transfer `tokenId` token to another account. The approval is cleared when the token is transferred. Only a single account can be approved at a time, so approving the zero address clears previous approvals. Requirements: - The caller must own the token or be an approved operator. - `tokenId` must exist. Emits an {Approval} event."}, "balanceOf(address)": {"details": "Returns the number of tokens in ``owner``'s account."}, "getApproved(uint256)": {"details": "Returns the account approved for `tokenId` token. Requirements: - `tokenId` must exist."}, "isApprovedForAll(address,address)": {"details": "Returns if the `operator` is allowed to manage all of the assets of `owner`. See {setApprovalForAll}"}, "ownerOf(uint256)": {"details": "Returns the owner of the `tokenId` token. Requirements: - `tokenId` must exist."}, "safeTransferFrom(address,address,uint256)": {"details": "Safely transfers `tokenId` token from `from` to `to`, checking first that contract recipients are aware of the ERC721 protocol to prevent tokens from being forever locked. Requirements: - `from` cannot be the zero address. - `to` cannot be the zero address. - `tokenId` token must exist and be owned by `from`. - If the caller is not `from`, it must be have been allowed to move this token by either {approve} or {setApprovalForAll}. - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer. Emits a {Transfer} event."}, "safeTransferFrom(address,address,uint256,bytes)": {"details": "Safely transfers `tokenId` token from `from` to `to`. Requirements: - `from` cannot be the zero address. - `to` cannot be the zero address. - `tokenId` token must exist and be owned by `from`. - If the caller is not `from`, it must be approved to move this token by either {approve} or {setApprovalForAll}. - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer. Emits a {Transfer} event."}, "setApprovalForAll(address,bool)": {"details": "Approve or remove `operator` as an operator for the caller. Operators can call {transferFrom} or {safeTransferFrom} for any token owned by the caller. Requirements: - The `operator` cannot be the caller. Emits an {ApprovalForAll} event."}, "transferFrom(address,address,uint256)": {"details": "Transfers `tokenId` token from `from` to `to`. WARNING: Usage of this method is discouraged, use {safeTransferFrom} whenever possible. Requirements: - `from` cannot be the zero address. - `to` cannot be the zero address. - `tokenId` token must be owned by `from`. - If the caller is not `from`, it must be approved to move this token by either {approve} or {setApprovalForAll}. Emits a {Transfer} event."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=lib/chainlink/", "@ds-test/=lib/ds-test/src/", "@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "@rari-capital/solmate/=lib/seaport/lib/solmate/", "@seaport/=lib/seaport/contracts/", "@std/=lib/forge-std/src/", "@thirdweb-dev/dynamic-contracts/=lib/dynamic-contracts/", "ERC721A-Upgradeable/=lib/ERC721A-Upgradeable/contracts/", "ERC721A/=lib/ERC721A/contracts/", "chainlink/=lib/chainlink/contracts/", "contracts/=contracts/", "ds-test/=lib/ds-test/src/", "dynamic-contracts/=lib/dynamic-contracts/src/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "erc721a-upgradeable/=lib/ERC721A-Upgradeable/", "erc721a/=lib/ERC721A/", "forge-std/=lib/forge-std/src/", "lib/sstore2/=lib/dynamic-contracts/lib/sstore2/", "murky/=lib/murky/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin/=lib/openzeppelin-contracts-upgradeable/contracts/", "seaport-core/=lib/seaport/lib/seaport-core/", "seaport-sol/=lib/seaport-sol/src/", "seaport-types/=lib/seaport/lib/seaport-types/", "seaport/=lib/seaport/", "solady/=lib/solady/", "solarray/=lib/seaport/lib/solarray/src/", "solmate/=lib/seaport/lib/solmate/src/", "sstore2/=lib/dynamic-contracts/lib/sstore2/contracts/"], "optimizer": {"enabled": true, "runs": 20}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/eip/interface/IERC721.sol": "IERC721"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/eip/interface/IERC721.sol": {"keccak256": "0x00754fd93079350acb6b1d0451320cc4b4252bf7990aa39cde29abb5ca4a60ea", "urls": ["bzz-raw://c91392521b426c02b342d4ba0f2c5cad2c6074e1be90203c9525751dd23b36bf", "dweb:/ipfs/QmNjjCttJ9A39HMn3FTMqjQN5y7UoQrPUhQ7Nd8Ci7jFZj"], "license": "MIT"}}, "version": 1}, "ast": {"absolutePath": "contracts/eip/interface/IERC721.sol", "id": 14254, "exportedSymbols": {"IERC721": [14253]}, "nodeType": "SourceUnit", "src": "93:4462:38", "nodes": [{"id": 14142, "nodeType": "PragmaDirective", "src": "93:23:38", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 14253, "nodeType": "ContractDefinition", "src": "186:4368:38", "nodes": [{"id": 14152, "nodeType": "EventDefinition", "src": "303:82:38", "nodes": [], "anonymous": false, "documentation": {"id": 14144, "nodeType": "StructuredDocumentation", "src": "210:88:38", "text": " @dev Emitted when `tokenId` token is transferred from `from` to `to`."}, "eventSelector": "ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef", "name": "Transfer", "nameLocation": "309:8:38", "parameters": {"id": 14151, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14146, "indexed": true, "mutability": "mutable", "name": "from", "nameLocation": "334:4:38", "nodeType": "VariableDeclaration", "scope": 14152, "src": "318:20:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14145, "name": "address", "nodeType": "ElementaryTypeName", "src": "318:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 14148, "indexed": true, "mutability": "mutable", "name": "to", "nameLocation": "356:2:38", "nodeType": "VariableDeclaration", "scope": 14152, "src": "340:18:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14147, "name": "address", "nodeType": "ElementaryTypeName", "src": "340:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 14150, "indexed": true, "mutability": "mutable", "name": "tokenId", "nameLocation": "376:7:38", "nodeType": "VariableDeclaration", "scope": 14152, "src": "360:23:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 14149, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "360:7:38", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "317:67:38"}}, {"id": 14161, "nodeType": "EventDefinition", "src": "490:89:38", "nodes": [], "anonymous": false, "documentation": {"id": 14153, "nodeType": "StructuredDocumentation", "src": "391:94:38", "text": " @dev Emitted when `owner` enables `approved` to manage the `tokenId` token."}, "eventSelector": "8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925", "name": "Approval", "nameLocation": "496:8:38", "parameters": {"id": 14160, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14155, "indexed": true, "mutability": "mutable", "name": "owner", "nameLocation": "521:5:38", "nodeType": "VariableDeclaration", "scope": 14161, "src": "505:21:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14154, "name": "address", "nodeType": "ElementaryTypeName", "src": "505:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 14157, "indexed": true, "mutability": "mutable", "name": "approved", "nameLocation": "544:8:38", "nodeType": "VariableDeclaration", "scope": 14161, "src": "528:24:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14156, "name": "address", "nodeType": "ElementaryTypeName", "src": "528:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 14159, "indexed": true, "mutability": "mutable", "name": "tokenId", "nameLocation": "570:7:38", "nodeType": "VariableDeclaration", "scope": 14161, "src": "554:23:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 14158, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "554:7:38", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "504:74:38"}}, {"id": 14170, "nodeType": "EventDefinition", "src": "707:85:38", "nodes": [], "anonymous": false, "documentation": {"id": 14162, "nodeType": "StructuredDocumentation", "src": "585:117:38", "text": " @dev Emitted when `owner` enables or disables (`approved`) `operator` to manage all of its assets."}, "eventSelector": "17307eab39ab6107e8899845ad3d59bd9653f200f220920489ca2b5937696c31", "name": "ApprovalForAll", "nameLocation": "713:14:38", "parameters": {"id": 14169, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14164, "indexed": true, "mutability": "mutable", "name": "owner", "nameLocation": "744:5:38", "nodeType": "VariableDeclaration", "scope": 14170, "src": "728:21:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14163, "name": "address", "nodeType": "ElementaryTypeName", "src": "728:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 14166, "indexed": true, "mutability": "mutable", "name": "operator", "nameLocation": "767:8:38", "nodeType": "VariableDeclaration", "scope": 14170, "src": "751:24:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14165, "name": "address", "nodeType": "ElementaryTypeName", "src": "751:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 14168, "indexed": false, "mutability": "mutable", "name": "approved", "nameLocation": "782:8:38", "nodeType": "VariableDeclaration", "scope": 14170, "src": "777:13:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 14167, "name": "bool", "nodeType": "ElementaryTypeName", "src": "777:4:38", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "727:64:38"}}, {"id": 14178, "nodeType": "FunctionDefinition", "src": "879:66:38", "nodes": [], "documentation": {"id": 14171, "nodeType": "StructuredDocumentation", "src": "798:76:38", "text": " @dev Returns the number of tokens in ``owner``'s account."}, "functionSelector": "70a08231", "implemented": false, "kind": "function", "modifiers": [], "name": "balanceOf", "nameLocation": "888:9:38", "parameters": {"id": 14174, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14173, "mutability": "mutable", "name": "owner", "nameLocation": "906:5:38", "nodeType": "VariableDeclaration", "scope": 14178, "src": "898:13:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14172, "name": "address", "nodeType": "ElementaryTypeName", "src": "898:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "897:15:38"}, "returnParameters": {"id": 14177, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14176, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 14178, "src": "936:7:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 14175, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "936:7:38", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "935:9:38"}, "scope": 14253, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 14186, "nodeType": "FunctionDefinition", "src": "1087:66:38", "nodes": [], "documentation": {"id": 14179, "nodeType": "StructuredDocumentation", "src": "951:131:38", "text": " @dev Returns the owner of the `tokenId` token.\n Requirements:\n - `tokenId` must exist."}, "functionSelector": "6352211e", "implemented": false, "kind": "function", "modifiers": [], "name": "ownerOf", "nameLocation": "1096:7:38", "parameters": {"id": 14182, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14181, "mutability": "mutable", "name": "tokenId", "nameLocation": "1112:7:38", "nodeType": "VariableDeclaration", "scope": 14186, "src": "1104:15:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 14180, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1104:7:38", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1103:17:38"}, "returnParameters": {"id": 14185, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14184, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 14186, "src": "1144:7:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14183, "name": "address", "nodeType": "ElementaryTypeName", "src": "1144:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1143:9:38"}, "scope": 14253, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 14196, "nodeType": "FunctionDefinition", "src": "1854:78:38", "nodes": [], "documentation": {"id": 14187, "nodeType": "StructuredDocumentation", "src": "1159:690:38", "text": " @dev Safely transfers `tokenId` token from `from` to `to`, checking first that contract recipients\n are aware of the ERC721 protocol to prevent tokens from being forever locked.\n Requirements:\n - `from` cannot be the zero address.\n - `to` cannot be the zero address.\n - `tokenId` token must exist and be owned by `from`.\n - If the caller is not `from`, it must be have been allowed to move this token by either {approve} or {setApprovalForAll}.\n - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer.\n Emits a {Transfer} event."}, "functionSelector": "42842e0e", "implemented": false, "kind": "function", "modifiers": [], "name": "safeTransferFrom", "nameLocation": "1863:16:38", "parameters": {"id": 14194, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14189, "mutability": "mutable", "name": "from", "nameLocation": "1888:4:38", "nodeType": "VariableDeclaration", "scope": 14196, "src": "1880:12:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14188, "name": "address", "nodeType": "ElementaryTypeName", "src": "1880:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 14191, "mutability": "mutable", "name": "to", "nameLocation": "1902:2:38", "nodeType": "VariableDeclaration", "scope": 14196, "src": "1894:10:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14190, "name": "address", "nodeType": "ElementaryTypeName", "src": "1894:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 14193, "mutability": "mutable", "name": "tokenId", "nameLocation": "1914:7:38", "nodeType": "VariableDeclaration", "scope": 14196, "src": "1906:15:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 14192, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1906:7:38", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1879:43:38"}, "returnParameters": {"id": 14195, "nodeType": "ParameterList", "parameters": [], "src": "1931:0:38"}, "scope": 14253, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 14206, "nodeType": "FunctionDefinition", "src": "2447:74:38", "nodes": [], "documentation": {"id": 14197, "nodeType": "StructuredDocumentation", "src": "1938:504:38", "text": " @dev Transfers `tokenId` token from `from` to `to`.\n WARNING: Usage of this method is discouraged, use {safeTransferFrom} whenever possible.\n Requirements:\n - `from` cannot be the zero address.\n - `to` cannot be the zero address.\n - `tokenId` token must be owned by `from`.\n - If the caller is not `from`, it must be approved to move this token by either {approve} or {setApprovalForAll}.\n Emits a {Transfer} event."}, "functionSelector": "23b872dd", "implemented": false, "kind": "function", "modifiers": [], "name": "transferFrom", "nameLocation": "2456:12:38", "parameters": {"id": 14204, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14199, "mutability": "mutable", "name": "from", "nameLocation": "2477:4:38", "nodeType": "VariableDeclaration", "scope": 14206, "src": "2469:12:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14198, "name": "address", "nodeType": "ElementaryTypeName", "src": "2469:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 14201, "mutability": "mutable", "name": "to", "nameLocation": "2491:2:38", "nodeType": "VariableDeclaration", "scope": 14206, "src": "2483:10:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14200, "name": "address", "nodeType": "ElementaryTypeName", "src": "2483:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 14203, "mutability": "mutable", "name": "tokenId", "nameLocation": "2503:7:38", "nodeType": "VariableDeclaration", "scope": 14206, "src": "2495:15:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 14202, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2495:7:38", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2468:43:38"}, "returnParameters": {"id": 14205, "nodeType": "ParameterList", "parameters": [], "src": "2520:0:38"}, "scope": 14253, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 14214, "nodeType": "FunctionDefinition", "src": "2984:55:38", "nodes": [], "documentation": {"id": 14207, "nodeType": "StructuredDocumentation", "src": "2527:452:38", "text": " @dev Gives permission to `to` to transfer `tokenId` token to another account.\n The approval is cleared when the token is transferred.\n Only a single account can be approved at a time, so approving the zero address clears previous approvals.\n Requirements:\n - The caller must own the token or be an approved operator.\n - `tokenId` must exist.\n Emits an {Approval} event."}, "functionSelector": "095ea7b3", "implemented": false, "kind": "function", "modifiers": [], "name": "approve", "nameLocation": "2993:7:38", "parameters": {"id": 14212, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14209, "mutability": "mutable", "name": "to", "nameLocation": "3009:2:38", "nodeType": "VariableDeclaration", "scope": 14214, "src": "3001:10:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14208, "name": "address", "nodeType": "ElementaryTypeName", "src": "3001:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 14211, "mutability": "mutable", "name": "tokenId", "nameLocation": "3021:7:38", "nodeType": "VariableDeclaration", "scope": 14214, "src": "3013:15:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 14210, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3013:7:38", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3000:29:38"}, "returnParameters": {"id": 14213, "nodeType": "ParameterList", "parameters": [], "src": "3038:0:38"}, "scope": 14253, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 14222, "nodeType": "FunctionDefinition", "src": "3189:70:38", "nodes": [], "documentation": {"id": 14215, "nodeType": "StructuredDocumentation", "src": "3045:139:38", "text": " @dev Returns the account approved for `tokenId` token.\n Requirements:\n - `tokenId` must exist."}, "functionSelector": "081812fc", "implemented": false, "kind": "function", "modifiers": [], "name": "getApproved", "nameLocation": "3198:11:38", "parameters": {"id": 14218, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14217, "mutability": "mutable", "name": "tokenId", "nameLocation": "3218:7:38", "nodeType": "VariableDeclaration", "scope": 14222, "src": "3210:15:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 14216, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3210:7:38", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3209:17:38"}, "returnParameters": {"id": 14221, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14220, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 14222, "src": "3250:7:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14219, "name": "address", "nodeType": "ElementaryTypeName", "src": "3250:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3249:9:38"}, "scope": 14253, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 14230, "nodeType": "FunctionDefinition", "src": "3579:70:38", "nodes": [], "documentation": {"id": 14223, "nodeType": "StructuredDocumentation", "src": "3265:309:38", "text": " @dev Approve or remove `operator` as an operator for the caller.\n Operators can call {transferFrom} or {safeTransferFrom} for any token owned by the caller.\n Requirements:\n - The `operator` cannot be the caller.\n Emits an {ApprovalForAll} event."}, "functionSelector": "a22cb465", "implemented": false, "kind": "function", "modifiers": [], "name": "setApprovalForAll", "nameLocation": "3588:17:38", "parameters": {"id": 14228, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14225, "mutability": "mutable", "name": "operator", "nameLocation": "3614:8:38", "nodeType": "VariableDeclaration", "scope": 14230, "src": "3606:16:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14224, "name": "address", "nodeType": "ElementaryTypeName", "src": "3606:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 14227, "mutability": "mutable", "name": "_approved", "nameLocation": "3629:9:38", "nodeType": "VariableDeclaration", "scope": 14230, "src": "3624:14:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 14226, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3624:4:38", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3605:34:38"}, "returnParameters": {"id": 14229, "nodeType": "ParameterList", "parameters": [], "src": "3648:0:38"}, "scope": 14253, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 14240, "nodeType": "FunctionDefinition", "src": "3798:88:38", "nodes": [], "documentation": {"id": 14231, "nodeType": "StructuredDocumentation", "src": "3655:138:38", "text": " @dev Returns if the `operator` is allowed to manage all of the assets of `owner`.\n See {setApprovalForAll}"}, "functionSelector": "e985e9c5", "implemented": false, "kind": "function", "modifiers": [], "name": "isApprovedForAll", "nameLocation": "3807:16:38", "parameters": {"id": 14236, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14233, "mutability": "mutable", "name": "owner", "nameLocation": "3832:5:38", "nodeType": "VariableDeclaration", "scope": 14240, "src": "3824:13:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14232, "name": "address", "nodeType": "ElementaryTypeName", "src": "3824:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 14235, "mutability": "mutable", "name": "operator", "nameLocation": "3847:8:38", "nodeType": "VariableDeclaration", "scope": 14240, "src": "3839:16:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14234, "name": "address", "nodeType": "ElementaryTypeName", "src": "3839:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3823:33:38"}, "returnParameters": {"id": 14239, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14238, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 14240, "src": "3880:4:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 14237, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3880:4:38", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3879:6:38"}, "scope": 14253, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 14252, "nodeType": "FunctionDefinition", "src": "4453:99:38", "nodes": [], "documentation": {"id": 14241, "nodeType": "StructuredDocumentation", "src": "3892:556:38", "text": " @dev Safely transfers `tokenId` token from `from` to `to`.\n Requirements:\n - `from` cannot be the zero address.\n - `to` cannot be the zero address.\n - `tokenId` token must exist and be owned by `from`.\n - If the caller is not `from`, it must be approved to move this token by either {approve} or {setApprovalForAll}.\n - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer.\n Emits a {Transfer} event."}, "functionSelector": "b88d4fde", "implemented": false, "kind": "function", "modifiers": [], "name": "safeTransferFrom", "nameLocation": "4462:16:38", "parameters": {"id": 14250, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 14243, "mutability": "mutable", "name": "from", "nameLocation": "4487:4:38", "nodeType": "VariableDeclaration", "scope": 14252, "src": "4479:12:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14242, "name": "address", "nodeType": "ElementaryTypeName", "src": "4479:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 14245, "mutability": "mutable", "name": "to", "nameLocation": "4501:2:38", "nodeType": "VariableDeclaration", "scope": 14252, "src": "4493:10:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14244, "name": "address", "nodeType": "ElementaryTypeName", "src": "4493:7:38", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 14247, "mutability": "mutable", "name": "tokenId", "nameLocation": "4513:7:38", "nodeType": "VariableDeclaration", "scope": 14252, "src": "4505:15:38", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 14246, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4505:7:38", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 14249, "mutability": "mutable", "name": "data", "nameLocation": "4537:4:38", "nodeType": "VariableDeclaration", "scope": 14252, "src": "4522:19:38", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes"}, "typeName": {"id": 14248, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "4522:5:38", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "4478:64:38"}, "returnParameters": {"id": 14251, "nodeType": "ParameterList", "parameters": [], "src": "4551:0:38"}, "scope": 14253, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [], "canonicalName": "IERC721", "contractDependencies": [], "contractKind": "interface", "documentation": {"id": 14143, "nodeType": "StructuredDocumentation", "src": "118:67:38", "text": " @dev Required interface of an ERC721 compliant contract."}, "fullyImplemented": false, "linearizedBaseContracts": [14253], "name": "IERC721", "nameLocation": "196:7:38", "scope": 14254, "usedErrors": [], "usedEvents": [14152, 14161, 14170]}], "license": "MIT"}, "id": 38}