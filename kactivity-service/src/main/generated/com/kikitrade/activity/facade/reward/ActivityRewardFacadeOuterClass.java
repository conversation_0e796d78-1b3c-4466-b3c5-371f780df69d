// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityRewardFacade.proto

package com.kikitrade.activity.facade.reward;

public final class ActivityRewardFacadeOuterClass {
  private ActivityRewardFacadeOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_reward_Decimal_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_reward_Decimal_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_reward_ManualRewardRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_reward_ManualRewardRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_reward_ManualRewardResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_reward_ManualRewardResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\032ActivityRewardFacade.proto\022$com.kikitr" +
      "ade.activity.facade.reward\032\037google/proto" +
      "buf/timestamp.proto\"\030\n\007Decimal\022\r\n\005value\030" +
      "\001 \001(\t\"\325\001\n\023ManualRewardRequest\022\022\n\ncustome" +
      "rId\030\001 \001(\t\022=\n\006amount\030\002 \001(\0132-.com.kikitrad" +
      "e.activity.facade.reward.Decimal\022\020\n\010curr" +
      "ency\030\003 \001(\t\022G\n\004type\030\004 \001(\01629.com.kikitrade" +
      ".activity.facade.reward.TransactionTypeE" +
      "num\022\020\n\010rewardId\030\005 \001(\003\"8\n\024ManualRewardRes" +
      "ponse\022\017\n\007success\030\001 \001(\010\022\017\n\007message\030\002 \001(\t*" +
      ",\n\023TransactionTypeEnum\022\n\n\006REWARD\020\000\022\t\n\005PO" +
      "INT\020\0012\236\001\n\024ActivityRewardFacade\022\205\001\n\014manua" +
      "lReward\0229.com.kikitrade.activity.facade." +
      "reward.ManualRewardRequest\032:.com.kikitra" +
      "de.activity.facade.reward.ManualRewardRe" +
      "sponseB(\n$com.kikitrade.activity.facade." +
      "rewardP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.TimestampProto.getDescriptor(),
        });
    internal_static_com_kikitrade_activity_facade_reward_Decimal_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_activity_facade_reward_Decimal_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_reward_Decimal_descriptor,
        new java.lang.String[] { "Value", });
    internal_static_com_kikitrade_activity_facade_reward_ManualRewardRequest_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_activity_facade_reward_ManualRewardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_reward_ManualRewardRequest_descriptor,
        new java.lang.String[] { "CustomerId", "Amount", "Currency", "Type", "RewardId", });
    internal_static_com_kikitrade_activity_facade_reward_ManualRewardResponse_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_kikitrade_activity_facade_reward_ManualRewardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_reward_ManualRewardResponse_descriptor,
        new java.lang.String[] { "Success", "Message", });
    com.google.protobuf.TimestampProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
