// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Goods.proto

package com.kikitrade.activity.facade.goods;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.goods.GoodsDTO}
 */
public final class GoodsDTO extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.goods.GoodsDTO)
    GoodsDTOOrBuilder {
private static final long serialVersionUID = 0L;
  // Use GoodsDTO.newBuilder() to construct.
  private GoodsDTO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private GoodsDTO() {
    id_ = "";
    name_ = "";
    status_ = 0;
    desc_ = "";
    type_ = 0;
    startTime_ = "";
    endTime_ = "";
    chain_ = 0;
    price_ = "";
    listImage_ = "";
    detailImage_ = "";
    shareImage_ = "";
    labelName_ = "";
    labelColor_ = "";
    outId_ = "";
    preQuestId_ = "";
    saasId_ = "";
    test_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new GoodsDTO();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.goods.GoodFacadeOutClass.internal_static_com_kikitrade_activity_facade_goods_GoodsDTO_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.goods.GoodFacadeOutClass.internal_static_com_kikitrade_activity_facade_goods_GoodsDTO_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.goods.GoodsDTO.class, com.kikitrade.activity.facade.goods.GoodsDTO.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <pre>
   *商品id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *商品id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object name_ = "";
  /**
   * <pre>
   *商品名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *商品名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATUS_FIELD_NUMBER = 3;
  private int status_ = 0;
  /**
   * <pre>
   *商品状态
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.goods.GoodsStatus status = 3;</code>
   * @return The enum numeric value on the wire for status.
   */
  @java.lang.Override public int getStatusValue() {
    return status_;
  }
  /**
   * <pre>
   *商品状态
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.goods.GoodsStatus status = 3;</code>
   * @return The status.
   */
  @java.lang.Override public com.kikitrade.activity.facade.goods.GoodsStatus getStatus() {
    com.kikitrade.activity.facade.goods.GoodsStatus result = com.kikitrade.activity.facade.goods.GoodsStatus.forNumber(status_);
    return result == null ? com.kikitrade.activity.facade.goods.GoodsStatus.UNRECOGNIZED : result;
  }

  public static final int DESC_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object desc_ = "";
  /**
   * <pre>
   *商品描述
   * </pre>
   *
   * <code>string desc = 4;</code>
   * @return The desc.
   */
  @java.lang.Override
  public java.lang.String getDesc() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      desc_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *商品描述
   * </pre>
   *
   * <code>string desc = 4;</code>
   * @return The bytes for desc.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescBytes() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      desc_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TYPE_FIELD_NUMBER = 5;
  private int type_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.goods.GoodsType type = 5;</code>
   * @return The enum numeric value on the wire for type.
   */
  @java.lang.Override public int getTypeValue() {
    return type_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.goods.GoodsType type = 5;</code>
   * @return The type.
   */
  @java.lang.Override public com.kikitrade.activity.facade.goods.GoodsType getType() {
    com.kikitrade.activity.facade.goods.GoodsType result = com.kikitrade.activity.facade.goods.GoodsType.forNumber(type_);
    return result == null ? com.kikitrade.activity.facade.goods.GoodsType.UNRECOGNIZED : result;
  }

  public static final int STARTTIME_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object startTime_ = "";
  /**
   * <pre>
   *商品售卖时间
   * </pre>
   *
   * <code>string startTime = 6;</code>
   * @return The startTime.
   */
  @java.lang.Override
  public java.lang.String getStartTime() {
    java.lang.Object ref = startTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      startTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *商品售卖时间
   * </pre>
   *
   * <code>string startTime = 6;</code>
   * @return The bytes for startTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStartTimeBytes() {
    java.lang.Object ref = startTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      startTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ENDTIME_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object endTime_ = "";
  /**
   * <pre>
   *商品售卖时间
   * </pre>
   *
   * <code>string endTime = 7;</code>
   * @return The endTime.
   */
  @java.lang.Override
  public java.lang.String getEndTime() {
    java.lang.Object ref = endTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      endTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *商品售卖时间
   * </pre>
   *
   * <code>string endTime = 7;</code>
   * @return The bytes for endTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getEndTimeBytes() {
    java.lang.Object ref = endTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      endTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHAIN_FIELD_NUMBER = 8;
  private int chain_ = 0;
  /**
   * <pre>
   *商品所属链
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.goods.BlockChain chain = 8;</code>
   * @return The enum numeric value on the wire for chain.
   */
  @java.lang.Override public int getChainValue() {
    return chain_;
  }
  /**
   * <pre>
   *商品所属链
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.goods.BlockChain chain = 8;</code>
   * @return The chain.
   */
  @java.lang.Override public com.kikitrade.activity.facade.goods.BlockChain getChain() {
    com.kikitrade.activity.facade.goods.BlockChain result = com.kikitrade.activity.facade.goods.BlockChain.forNumber(chain_);
    return result == null ? com.kikitrade.activity.facade.goods.BlockChain.UNRECOGNIZED : result;
  }

  public static final int PRICE_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object price_ = "";
  /**
   * <pre>
   *商品价格
   * </pre>
   *
   * <code>string price = 9;</code>
   * @return The price.
   */
  @java.lang.Override
  public java.lang.String getPrice() {
    java.lang.Object ref = price_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      price_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *商品价格
   * </pre>
   *
   * <code>string price = 9;</code>
   * @return The bytes for price.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPriceBytes() {
    java.lang.Object ref = price_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      price_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STOCK_FIELD_NUMBER = 10;
  private int stock_ = 0;
  /**
   * <pre>
   *库存
   * </pre>
   *
   * <code>int32 stock = 10;</code>
   * @return The stock.
   */
  @java.lang.Override
  public int getStock() {
    return stock_;
  }

  public static final int LISTIMAGE_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private volatile java.lang.Object listImage_ = "";
  /**
   * <pre>
   *列表图片
   * </pre>
   *
   * <code>string listImage = 11;</code>
   * @return The listImage.
   */
  @java.lang.Override
  public java.lang.String getListImage() {
    java.lang.Object ref = listImage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      listImage_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *列表图片
   * </pre>
   *
   * <code>string listImage = 11;</code>
   * @return The bytes for listImage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getListImageBytes() {
    java.lang.Object ref = listImage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      listImage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DETAILIMAGE_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private volatile java.lang.Object detailImage_ = "";
  /**
   * <pre>
   *详情图片
   * </pre>
   *
   * <code>string detailImage = 12;</code>
   * @return The detailImage.
   */
  @java.lang.Override
  public java.lang.String getDetailImage() {
    java.lang.Object ref = detailImage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      detailImage_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *详情图片
   * </pre>
   *
   * <code>string detailImage = 12;</code>
   * @return The bytes for detailImage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDetailImageBytes() {
    java.lang.Object ref = detailImage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      detailImage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SHAREIMAGE_FIELD_NUMBER = 13;
  @SuppressWarnings("serial")
  private volatile java.lang.Object shareImage_ = "";
  /**
   * <pre>
   *分享图片
   * </pre>
   *
   * <code>string shareImage = 13;</code>
   * @return The shareImage.
   */
  @java.lang.Override
  public java.lang.String getShareImage() {
    java.lang.Object ref = shareImage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      shareImage_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *分享图片
   * </pre>
   *
   * <code>string shareImage = 13;</code>
   * @return The bytes for shareImage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getShareImageBytes() {
    java.lang.Object ref = shareImage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      shareImage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LABELNAME_FIELD_NUMBER = 14;
  @SuppressWarnings("serial")
  private volatile java.lang.Object labelName_ = "";
  /**
   * <pre>
   *标签名称
   * </pre>
   *
   * <code>string labelName = 14;</code>
   * @return The labelName.
   */
  @java.lang.Override
  public java.lang.String getLabelName() {
    java.lang.Object ref = labelName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      labelName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *标签名称
   * </pre>
   *
   * <code>string labelName = 14;</code>
   * @return The bytes for labelName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLabelNameBytes() {
    java.lang.Object ref = labelName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      labelName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LABELCOLOR_FIELD_NUMBER = 15;
  @SuppressWarnings("serial")
  private volatile java.lang.Object labelColor_ = "";
  /**
   * <pre>
   *标签颜色
   * </pre>
   *
   * <code>string labelColor = 15;</code>
   * @return The labelColor.
   */
  @java.lang.Override
  public java.lang.String getLabelColor() {
    java.lang.Object ref = labelColor_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      labelColor_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *标签颜色
   * </pre>
   *
   * <code>string labelColor = 15;</code>
   * @return The bytes for labelColor.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLabelColorBytes() {
    java.lang.Object ref = labelColor_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      labelColor_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int OUTID_FIELD_NUMBER = 16;
  @SuppressWarnings("serial")
  private volatile java.lang.Object outId_ = "";
  /**
   * <pre>
   *外部商品id
   * </pre>
   *
   * <code>string outId = 16;</code>
   * @return The outId.
   */
  @java.lang.Override
  public java.lang.String getOutId() {
    java.lang.Object ref = outId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      outId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *外部商品id
   * </pre>
   *
   * <code>string outId = 16;</code>
   * @return The bytes for outId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOutIdBytes() {
    java.lang.Object ref = outId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      outId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PREQUESTID_FIELD_NUMBER = 17;
  @SuppressWarnings("serial")
  private volatile java.lang.Object preQuestId_ = "";
  /**
   * <pre>
   *前置任务id
   * </pre>
   *
   * <code>string preQuestId = 17;</code>
   * @return The preQuestId.
   */
  @java.lang.Override
  public java.lang.String getPreQuestId() {
    java.lang.Object ref = preQuestId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      preQuestId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *前置任务id
   * </pre>
   *
   * <code>string preQuestId = 17;</code>
   * @return The bytes for preQuestId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPreQuestIdBytes() {
    java.lang.Object ref = preQuestId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      preQuestId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SAASID_FIELD_NUMBER = 18;
  @SuppressWarnings("serial")
  private volatile java.lang.Object saasId_ = "";
  /**
   * <pre>
   *saasId
   * </pre>
   *
   * <code>string saasId = 18;</code>
   * @return The saasId.
   */
  @java.lang.Override
  public java.lang.String getSaasId() {
    java.lang.Object ref = saasId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      saasId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *saasId
   * </pre>
   *
   * <code>string saasId = 18;</code>
   * @return The bytes for saasId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSaasIdBytes() {
    java.lang.Object ref = saasId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      saasId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TEST_FIELD_NUMBER = 19;
  @SuppressWarnings("serial")
  private java.util.List<java.lang.Integer> test_;
  private static final com.google.protobuf.Internal.ListAdapter.Converter<
      java.lang.Integer, com.kikitrade.activity.facade.goods.GoodsType> test_converter_ =
          new com.google.protobuf.Internal.ListAdapter.Converter<
              java.lang.Integer, com.kikitrade.activity.facade.goods.GoodsType>() {
            public com.kikitrade.activity.facade.goods.GoodsType convert(java.lang.Integer from) {
              com.kikitrade.activity.facade.goods.GoodsType result = com.kikitrade.activity.facade.goods.GoodsType.forNumber(from);
              return result == null ? com.kikitrade.activity.facade.goods.GoodsType.UNRECOGNIZED : result;
            }
          };
  /**
   * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
   * @return A list containing the test.
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.goods.GoodsType> getTestList() {
    return new com.google.protobuf.Internal.ListAdapter<
        java.lang.Integer, com.kikitrade.activity.facade.goods.GoodsType>(test_, test_converter_);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
   * @return The count of test.
   */
  @java.lang.Override
  public int getTestCount() {
    return test_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
   * @param index The index of the element to return.
   * @return The test at the given index.
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.goods.GoodsType getTest(int index) {
    return test_converter_.convert(test_.get(index));
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
   * @return A list containing the enum numeric values on the wire for test.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
  getTestValueList() {
    return test_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
   * @param index The index of the value to return.
   * @return The enum numeric value on the wire of test at the given index.
   */
  @java.lang.Override
  public int getTestValue(int index) {
    return test_.get(index);
  }
  private int testMemoizedSerializedSize;

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    getSerializedSize();
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
    }
    if (status_ != com.kikitrade.activity.facade.goods.GoodsStatus.ONLINE.getNumber()) {
      output.writeEnum(3, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(desc_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, desc_);
    }
    if (type_ != com.kikitrade.activity.facade.goods.GoodsType.NFT.getNumber()) {
      output.writeEnum(5, type_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(startTime_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, startTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(endTime_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, endTime_);
    }
    if (chain_ != com.kikitrade.activity.facade.goods.BlockChain.evm.getNumber()) {
      output.writeEnum(8, chain_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(price_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, price_);
    }
    if (stock_ != 0) {
      output.writeInt32(10, stock_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(listImage_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, listImage_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(detailImage_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 12, detailImage_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(shareImage_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 13, shareImage_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(labelName_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 14, labelName_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(labelColor_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 15, labelColor_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(outId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 16, outId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(preQuestId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 17, preQuestId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(saasId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 18, saasId_);
    }
    if (getTestList().size() > 0) {
      output.writeUInt32NoTag(154);
      output.writeUInt32NoTag(testMemoizedSerializedSize);
    }
    for (int i = 0; i < test_.size(); i++) {
      output.writeEnumNoTag(test_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
    }
    if (status_ != com.kikitrade.activity.facade.goods.GoodsStatus.ONLINE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(3, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(desc_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, desc_);
    }
    if (type_ != com.kikitrade.activity.facade.goods.GoodsType.NFT.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(5, type_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(startTime_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, startTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(endTime_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, endTime_);
    }
    if (chain_ != com.kikitrade.activity.facade.goods.BlockChain.evm.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(8, chain_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(price_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, price_);
    }
    if (stock_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(10, stock_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(listImage_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, listImage_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(detailImage_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, detailImage_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(shareImage_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, shareImage_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(labelName_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, labelName_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(labelColor_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, labelColor_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(outId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, outId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(preQuestId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, preQuestId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(saasId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(18, saasId_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < test_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeEnumSizeNoTag(test_.get(i));
      }
      size += dataSize;
      if (!getTestList().isEmpty()) {  size += 2;
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32SizeNoTag(dataSize);
      }testMemoizedSerializedSize = dataSize;
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.goods.GoodsDTO)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.goods.GoodsDTO other = (com.kikitrade.activity.facade.goods.GoodsDTO) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (status_ != other.status_) return false;
    if (!getDesc()
        .equals(other.getDesc())) return false;
    if (type_ != other.type_) return false;
    if (!getStartTime()
        .equals(other.getStartTime())) return false;
    if (!getEndTime()
        .equals(other.getEndTime())) return false;
    if (chain_ != other.chain_) return false;
    if (!getPrice()
        .equals(other.getPrice())) return false;
    if (getStock()
        != other.getStock()) return false;
    if (!getListImage()
        .equals(other.getListImage())) return false;
    if (!getDetailImage()
        .equals(other.getDetailImage())) return false;
    if (!getShareImage()
        .equals(other.getShareImage())) return false;
    if (!getLabelName()
        .equals(other.getLabelName())) return false;
    if (!getLabelColor()
        .equals(other.getLabelColor())) return false;
    if (!getOutId()
        .equals(other.getOutId())) return false;
    if (!getPreQuestId()
        .equals(other.getPreQuestId())) return false;
    if (!getSaasId()
        .equals(other.getSaasId())) return false;
    if (!test_.equals(other.test_)) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + status_;
    hash = (37 * hash) + DESC_FIELD_NUMBER;
    hash = (53 * hash) + getDesc().hashCode();
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + type_;
    hash = (37 * hash) + STARTTIME_FIELD_NUMBER;
    hash = (53 * hash) + getStartTime().hashCode();
    hash = (37 * hash) + ENDTIME_FIELD_NUMBER;
    hash = (53 * hash) + getEndTime().hashCode();
    hash = (37 * hash) + CHAIN_FIELD_NUMBER;
    hash = (53 * hash) + chain_;
    hash = (37 * hash) + PRICE_FIELD_NUMBER;
    hash = (53 * hash) + getPrice().hashCode();
    hash = (37 * hash) + STOCK_FIELD_NUMBER;
    hash = (53 * hash) + getStock();
    hash = (37 * hash) + LISTIMAGE_FIELD_NUMBER;
    hash = (53 * hash) + getListImage().hashCode();
    hash = (37 * hash) + DETAILIMAGE_FIELD_NUMBER;
    hash = (53 * hash) + getDetailImage().hashCode();
    hash = (37 * hash) + SHAREIMAGE_FIELD_NUMBER;
    hash = (53 * hash) + getShareImage().hashCode();
    hash = (37 * hash) + LABELNAME_FIELD_NUMBER;
    hash = (53 * hash) + getLabelName().hashCode();
    hash = (37 * hash) + LABELCOLOR_FIELD_NUMBER;
    hash = (53 * hash) + getLabelColor().hashCode();
    hash = (37 * hash) + OUTID_FIELD_NUMBER;
    hash = (53 * hash) + getOutId().hashCode();
    hash = (37 * hash) + PREQUESTID_FIELD_NUMBER;
    hash = (53 * hash) + getPreQuestId().hashCode();
    hash = (37 * hash) + SAASID_FIELD_NUMBER;
    hash = (53 * hash) + getSaasId().hashCode();
    if (getTestCount() > 0) {
      hash = (37 * hash) + TEST_FIELD_NUMBER;
      hash = (53 * hash) + test_.hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.goods.GoodsDTO parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.goods.GoodsDTO parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.goods.GoodsDTO parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.goods.GoodsDTO parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.goods.GoodsDTO parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.goods.GoodsDTO parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.goods.GoodsDTO parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.goods.GoodsDTO parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.goods.GoodsDTO parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.goods.GoodsDTO parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.goods.GoodsDTO parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.goods.GoodsDTO parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.goods.GoodsDTO prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.goods.GoodsDTO}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.goods.GoodsDTO)
      com.kikitrade.activity.facade.goods.GoodsDTOOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.goods.GoodFacadeOutClass.internal_static_com_kikitrade_activity_facade_goods_GoodsDTO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.goods.GoodFacadeOutClass.internal_static_com_kikitrade_activity_facade_goods_GoodsDTO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.goods.GoodsDTO.class, com.kikitrade.activity.facade.goods.GoodsDTO.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.goods.GoodsDTO.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      name_ = "";
      status_ = 0;
      desc_ = "";
      type_ = 0;
      startTime_ = "";
      endTime_ = "";
      chain_ = 0;
      price_ = "";
      stock_ = 0;
      listImage_ = "";
      detailImage_ = "";
      shareImage_ = "";
      labelName_ = "";
      labelColor_ = "";
      outId_ = "";
      preQuestId_ = "";
      saasId_ = "";
      test_ = java.util.Collections.emptyList();
      bitField0_ = (bitField0_ & ~0x00040000);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.goods.GoodFacadeOutClass.internal_static_com_kikitrade_activity_facade_goods_GoodsDTO_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.goods.GoodsDTO getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.goods.GoodsDTO.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.goods.GoodsDTO build() {
      com.kikitrade.activity.facade.goods.GoodsDTO result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.goods.GoodsDTO buildPartial() {
      com.kikitrade.activity.facade.goods.GoodsDTO result = new com.kikitrade.activity.facade.goods.GoodsDTO(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.goods.GoodsDTO result) {
      if (((bitField0_ & 0x00040000) != 0)) {
        test_ = java.util.Collections.unmodifiableList(test_);
        bitField0_ = (bitField0_ & ~0x00040000);
      }
      result.test_ = test_;
    }

    private void buildPartial0(com.kikitrade.activity.facade.goods.GoodsDTO result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.name_ = name_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.status_ = status_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.desc_ = desc_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.type_ = type_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.startTime_ = startTime_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.endTime_ = endTime_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.chain_ = chain_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.price_ = price_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.stock_ = stock_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.listImage_ = listImage_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.detailImage_ = detailImage_;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.shareImage_ = shareImage_;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.labelName_ = labelName_;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.labelColor_ = labelColor_;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.outId_ = outId_;
      }
      if (((from_bitField0_ & 0x00010000) != 0)) {
        result.preQuestId_ = preQuestId_;
      }
      if (((from_bitField0_ & 0x00020000) != 0)) {
        result.saasId_ = saasId_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.goods.GoodsDTO) {
        return mergeFrom((com.kikitrade.activity.facade.goods.GoodsDTO)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.goods.GoodsDTO other) {
      if (other == com.kikitrade.activity.facade.goods.GoodsDTO.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.status_ != 0) {
        setStatusValue(other.getStatusValue());
      }
      if (!other.getDesc().isEmpty()) {
        desc_ = other.desc_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.type_ != 0) {
        setTypeValue(other.getTypeValue());
      }
      if (!other.getStartTime().isEmpty()) {
        startTime_ = other.startTime_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (!other.getEndTime().isEmpty()) {
        endTime_ = other.endTime_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (other.chain_ != 0) {
        setChainValue(other.getChainValue());
      }
      if (!other.getPrice().isEmpty()) {
        price_ = other.price_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (other.getStock() != 0) {
        setStock(other.getStock());
      }
      if (!other.getListImage().isEmpty()) {
        listImage_ = other.listImage_;
        bitField0_ |= 0x00000400;
        onChanged();
      }
      if (!other.getDetailImage().isEmpty()) {
        detailImage_ = other.detailImage_;
        bitField0_ |= 0x00000800;
        onChanged();
      }
      if (!other.getShareImage().isEmpty()) {
        shareImage_ = other.shareImage_;
        bitField0_ |= 0x00001000;
        onChanged();
      }
      if (!other.getLabelName().isEmpty()) {
        labelName_ = other.labelName_;
        bitField0_ |= 0x00002000;
        onChanged();
      }
      if (!other.getLabelColor().isEmpty()) {
        labelColor_ = other.labelColor_;
        bitField0_ |= 0x00004000;
        onChanged();
      }
      if (!other.getOutId().isEmpty()) {
        outId_ = other.outId_;
        bitField0_ |= 0x00008000;
        onChanged();
      }
      if (!other.getPreQuestId().isEmpty()) {
        preQuestId_ = other.preQuestId_;
        bitField0_ |= 0x00010000;
        onChanged();
      }
      if (!other.getSaasId().isEmpty()) {
        saasId_ = other.saasId_;
        bitField0_ |= 0x00020000;
        onChanged();
      }
      if (!other.test_.isEmpty()) {
        if (test_.isEmpty()) {
          test_ = other.test_;
          bitField0_ = (bitField0_ & ~0x00040000);
        } else {
          ensureTestIsMutable();
          test_.addAll(other.test_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              name_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              status_ = input.readEnum();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              desc_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              type_ = input.readEnum();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              startTime_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              endTime_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 64: {
              chain_ = input.readEnum();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 74: {
              price_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 80: {
              stock_ = input.readInt32();
              bitField0_ |= 0x00000200;
              break;
            } // case 80
            case 90: {
              listImage_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 98: {
              detailImage_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000800;
              break;
            } // case 98
            case 106: {
              shareImage_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00001000;
              break;
            } // case 106
            case 114: {
              labelName_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00002000;
              break;
            } // case 114
            case 122: {
              labelColor_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00004000;
              break;
            } // case 122
            case 130: {
              outId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00008000;
              break;
            } // case 130
            case 138: {
              preQuestId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00010000;
              break;
            } // case 138
            case 146: {
              saasId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00020000;
              break;
            } // case 146
            case 152: {
              int tmpRaw = input.readEnum();
              ensureTestIsMutable();
              test_.add(tmpRaw);
              break;
            } // case 152
            case 154: {
              int length = input.readRawVarint32();
              int oldLimit = input.pushLimit(length);
              while(input.getBytesUntilLimit() > 0) {
                int tmpRaw = input.readEnum();
                ensureTestIsMutable();
                test_.add(tmpRaw);
              }
              input.popLimit(oldLimit);
              break;
            } // case 154
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <pre>
     *商品id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *商品id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *商品id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <pre>
     *商品名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *商品名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *商品名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      name_ = getDefaultInstance().getName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      name_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private int status_ = 0;
    /**
     * <pre>
     *商品状态
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.goods.GoodsStatus status = 3;</code>
     * @return The enum numeric value on the wire for status.
     */
    @java.lang.Override public int getStatusValue() {
      return status_;
    }
    /**
     * <pre>
     *商品状态
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.goods.GoodsStatus status = 3;</code>
     * @param value The enum numeric value on the wire for status to set.
     * @return This builder for chaining.
     */
    public Builder setStatusValue(int value) {
      status_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品状态
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.goods.GoodsStatus status = 3;</code>
     * @return The status.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.goods.GoodsStatus getStatus() {
      com.kikitrade.activity.facade.goods.GoodsStatus result = com.kikitrade.activity.facade.goods.GoodsStatus.forNumber(status_);
      return result == null ? com.kikitrade.activity.facade.goods.GoodsStatus.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *商品状态
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.goods.GoodsStatus status = 3;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(com.kikitrade.activity.facade.goods.GoodsStatus value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000004;
      status_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品状态
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.goods.GoodsStatus status = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      bitField0_ = (bitField0_ & ~0x00000004);
      status_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object desc_ = "";
    /**
     * <pre>
     *商品描述
     * </pre>
     *
     * <code>string desc = 4;</code>
     * @return The desc.
     */
    public java.lang.String getDesc() {
      java.lang.Object ref = desc_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        desc_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *商品描述
     * </pre>
     *
     * <code>string desc = 4;</code>
     * @return The bytes for desc.
     */
    public com.google.protobuf.ByteString
        getDescBytes() {
      java.lang.Object ref = desc_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        desc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *商品描述
     * </pre>
     *
     * <code>string desc = 4;</code>
     * @param value The desc to set.
     * @return This builder for chaining.
     */
    public Builder setDesc(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      desc_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品描述
     * </pre>
     *
     * <code>string desc = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearDesc() {
      desc_ = getDefaultInstance().getDesc();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品描述
     * </pre>
     *
     * <code>string desc = 4;</code>
     * @param value The bytes for desc to set.
     * @return This builder for chaining.
     */
    public Builder setDescBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      desc_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private int type_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.goods.GoodsType type = 5;</code>
     * @return The enum numeric value on the wire for type.
     */
    @java.lang.Override public int getTypeValue() {
      return type_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.goods.GoodsType type = 5;</code>
     * @param value The enum numeric value on the wire for type to set.
     * @return This builder for chaining.
     */
    public Builder setTypeValue(int value) {
      type_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.goods.GoodsType type = 5;</code>
     * @return The type.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.goods.GoodsType getType() {
      com.kikitrade.activity.facade.goods.GoodsType result = com.kikitrade.activity.facade.goods.GoodsType.forNumber(type_);
      return result == null ? com.kikitrade.activity.facade.goods.GoodsType.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.goods.GoodsType type = 5;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(com.kikitrade.activity.facade.goods.GoodsType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000010;
      type_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.goods.GoodsType type = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000010);
      type_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object startTime_ = "";
    /**
     * <pre>
     *商品售卖时间
     * </pre>
     *
     * <code>string startTime = 6;</code>
     * @return The startTime.
     */
    public java.lang.String getStartTime() {
      java.lang.Object ref = startTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        startTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *商品售卖时间
     * </pre>
     *
     * <code>string startTime = 6;</code>
     * @return The bytes for startTime.
     */
    public com.google.protobuf.ByteString
        getStartTimeBytes() {
      java.lang.Object ref = startTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        startTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *商品售卖时间
     * </pre>
     *
     * <code>string startTime = 6;</code>
     * @param value The startTime to set.
     * @return This builder for chaining.
     */
    public Builder setStartTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      startTime_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品售卖时间
     * </pre>
     *
     * <code>string startTime = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearStartTime() {
      startTime_ = getDefaultInstance().getStartTime();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品售卖时间
     * </pre>
     *
     * <code>string startTime = 6;</code>
     * @param value The bytes for startTime to set.
     * @return This builder for chaining.
     */
    public Builder setStartTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      startTime_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object endTime_ = "";
    /**
     * <pre>
     *商品售卖时间
     * </pre>
     *
     * <code>string endTime = 7;</code>
     * @return The endTime.
     */
    public java.lang.String getEndTime() {
      java.lang.Object ref = endTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        endTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *商品售卖时间
     * </pre>
     *
     * <code>string endTime = 7;</code>
     * @return The bytes for endTime.
     */
    public com.google.protobuf.ByteString
        getEndTimeBytes() {
      java.lang.Object ref = endTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        endTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *商品售卖时间
     * </pre>
     *
     * <code>string endTime = 7;</code>
     * @param value The endTime to set.
     * @return This builder for chaining.
     */
    public Builder setEndTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      endTime_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品售卖时间
     * </pre>
     *
     * <code>string endTime = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearEndTime() {
      endTime_ = getDefaultInstance().getEndTime();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品售卖时间
     * </pre>
     *
     * <code>string endTime = 7;</code>
     * @param value The bytes for endTime to set.
     * @return This builder for chaining.
     */
    public Builder setEndTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      endTime_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private int chain_ = 0;
    /**
     * <pre>
     *商品所属链
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.goods.BlockChain chain = 8;</code>
     * @return The enum numeric value on the wire for chain.
     */
    @java.lang.Override public int getChainValue() {
      return chain_;
    }
    /**
     * <pre>
     *商品所属链
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.goods.BlockChain chain = 8;</code>
     * @param value The enum numeric value on the wire for chain to set.
     * @return This builder for chaining.
     */
    public Builder setChainValue(int value) {
      chain_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品所属链
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.goods.BlockChain chain = 8;</code>
     * @return The chain.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.goods.BlockChain getChain() {
      com.kikitrade.activity.facade.goods.BlockChain result = com.kikitrade.activity.facade.goods.BlockChain.forNumber(chain_);
      return result == null ? com.kikitrade.activity.facade.goods.BlockChain.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *商品所属链
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.goods.BlockChain chain = 8;</code>
     * @param value The chain to set.
     * @return This builder for chaining.
     */
    public Builder setChain(com.kikitrade.activity.facade.goods.BlockChain value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000080;
      chain_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品所属链
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.goods.BlockChain chain = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearChain() {
      bitField0_ = (bitField0_ & ~0x00000080);
      chain_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object price_ = "";
    /**
     * <pre>
     *商品价格
     * </pre>
     *
     * <code>string price = 9;</code>
     * @return The price.
     */
    public java.lang.String getPrice() {
      java.lang.Object ref = price_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        price_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *商品价格
     * </pre>
     *
     * <code>string price = 9;</code>
     * @return The bytes for price.
     */
    public com.google.protobuf.ByteString
        getPriceBytes() {
      java.lang.Object ref = price_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        price_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *商品价格
     * </pre>
     *
     * <code>string price = 9;</code>
     * @param value The price to set.
     * @return This builder for chaining.
     */
    public Builder setPrice(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      price_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品价格
     * </pre>
     *
     * <code>string price = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrice() {
      price_ = getDefaultInstance().getPrice();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品价格
     * </pre>
     *
     * <code>string price = 9;</code>
     * @param value The bytes for price to set.
     * @return This builder for chaining.
     */
    public Builder setPriceBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      price_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private int stock_ ;
    /**
     * <pre>
     *库存
     * </pre>
     *
     * <code>int32 stock = 10;</code>
     * @return The stock.
     */
    @java.lang.Override
    public int getStock() {
      return stock_;
    }
    /**
     * <pre>
     *库存
     * </pre>
     *
     * <code>int32 stock = 10;</code>
     * @param value The stock to set.
     * @return This builder for chaining.
     */
    public Builder setStock(int value) {

      stock_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *库存
     * </pre>
     *
     * <code>int32 stock = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearStock() {
      bitField0_ = (bitField0_ & ~0x00000200);
      stock_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object listImage_ = "";
    /**
     * <pre>
     *列表图片
     * </pre>
     *
     * <code>string listImage = 11;</code>
     * @return The listImage.
     */
    public java.lang.String getListImage() {
      java.lang.Object ref = listImage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        listImage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *列表图片
     * </pre>
     *
     * <code>string listImage = 11;</code>
     * @return The bytes for listImage.
     */
    public com.google.protobuf.ByteString
        getListImageBytes() {
      java.lang.Object ref = listImage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        listImage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *列表图片
     * </pre>
     *
     * <code>string listImage = 11;</code>
     * @param value The listImage to set.
     * @return This builder for chaining.
     */
    public Builder setListImage(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      listImage_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *列表图片
     * </pre>
     *
     * <code>string listImage = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearListImage() {
      listImage_ = getDefaultInstance().getListImage();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *列表图片
     * </pre>
     *
     * <code>string listImage = 11;</code>
     * @param value The bytes for listImage to set.
     * @return This builder for chaining.
     */
    public Builder setListImageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      listImage_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }

    private java.lang.Object detailImage_ = "";
    /**
     * <pre>
     *详情图片
     * </pre>
     *
     * <code>string detailImage = 12;</code>
     * @return The detailImage.
     */
    public java.lang.String getDetailImage() {
      java.lang.Object ref = detailImage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        detailImage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *详情图片
     * </pre>
     *
     * <code>string detailImage = 12;</code>
     * @return The bytes for detailImage.
     */
    public com.google.protobuf.ByteString
        getDetailImageBytes() {
      java.lang.Object ref = detailImage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        detailImage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *详情图片
     * </pre>
     *
     * <code>string detailImage = 12;</code>
     * @param value The detailImage to set.
     * @return This builder for chaining.
     */
    public Builder setDetailImage(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      detailImage_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *详情图片
     * </pre>
     *
     * <code>string detailImage = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearDetailImage() {
      detailImage_ = getDefaultInstance().getDetailImage();
      bitField0_ = (bitField0_ & ~0x00000800);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *详情图片
     * </pre>
     *
     * <code>string detailImage = 12;</code>
     * @param value The bytes for detailImage to set.
     * @return This builder for chaining.
     */
    public Builder setDetailImageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      detailImage_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }

    private java.lang.Object shareImage_ = "";
    /**
     * <pre>
     *分享图片
     * </pre>
     *
     * <code>string shareImage = 13;</code>
     * @return The shareImage.
     */
    public java.lang.String getShareImage() {
      java.lang.Object ref = shareImage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        shareImage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *分享图片
     * </pre>
     *
     * <code>string shareImage = 13;</code>
     * @return The bytes for shareImage.
     */
    public com.google.protobuf.ByteString
        getShareImageBytes() {
      java.lang.Object ref = shareImage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        shareImage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *分享图片
     * </pre>
     *
     * <code>string shareImage = 13;</code>
     * @param value The shareImage to set.
     * @return This builder for chaining.
     */
    public Builder setShareImage(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      shareImage_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *分享图片
     * </pre>
     *
     * <code>string shareImage = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearShareImage() {
      shareImage_ = getDefaultInstance().getShareImage();
      bitField0_ = (bitField0_ & ~0x00001000);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *分享图片
     * </pre>
     *
     * <code>string shareImage = 13;</code>
     * @param value The bytes for shareImage to set.
     * @return This builder for chaining.
     */
    public Builder setShareImageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      shareImage_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }

    private java.lang.Object labelName_ = "";
    /**
     * <pre>
     *标签名称
     * </pre>
     *
     * <code>string labelName = 14;</code>
     * @return The labelName.
     */
    public java.lang.String getLabelName() {
      java.lang.Object ref = labelName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        labelName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *标签名称
     * </pre>
     *
     * <code>string labelName = 14;</code>
     * @return The bytes for labelName.
     */
    public com.google.protobuf.ByteString
        getLabelNameBytes() {
      java.lang.Object ref = labelName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        labelName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *标签名称
     * </pre>
     *
     * <code>string labelName = 14;</code>
     * @param value The labelName to set.
     * @return This builder for chaining.
     */
    public Builder setLabelName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      labelName_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *标签名称
     * </pre>
     *
     * <code>string labelName = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearLabelName() {
      labelName_ = getDefaultInstance().getLabelName();
      bitField0_ = (bitField0_ & ~0x00002000);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *标签名称
     * </pre>
     *
     * <code>string labelName = 14;</code>
     * @param value The bytes for labelName to set.
     * @return This builder for chaining.
     */
    public Builder setLabelNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      labelName_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }

    private java.lang.Object labelColor_ = "";
    /**
     * <pre>
     *标签颜色
     * </pre>
     *
     * <code>string labelColor = 15;</code>
     * @return The labelColor.
     */
    public java.lang.String getLabelColor() {
      java.lang.Object ref = labelColor_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        labelColor_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *标签颜色
     * </pre>
     *
     * <code>string labelColor = 15;</code>
     * @return The bytes for labelColor.
     */
    public com.google.protobuf.ByteString
        getLabelColorBytes() {
      java.lang.Object ref = labelColor_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        labelColor_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *标签颜色
     * </pre>
     *
     * <code>string labelColor = 15;</code>
     * @param value The labelColor to set.
     * @return This builder for chaining.
     */
    public Builder setLabelColor(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      labelColor_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *标签颜色
     * </pre>
     *
     * <code>string labelColor = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearLabelColor() {
      labelColor_ = getDefaultInstance().getLabelColor();
      bitField0_ = (bitField0_ & ~0x00004000);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *标签颜色
     * </pre>
     *
     * <code>string labelColor = 15;</code>
     * @param value The bytes for labelColor to set.
     * @return This builder for chaining.
     */
    public Builder setLabelColorBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      labelColor_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }

    private java.lang.Object outId_ = "";
    /**
     * <pre>
     *外部商品id
     * </pre>
     *
     * <code>string outId = 16;</code>
     * @return The outId.
     */
    public java.lang.String getOutId() {
      java.lang.Object ref = outId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        outId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *外部商品id
     * </pre>
     *
     * <code>string outId = 16;</code>
     * @return The bytes for outId.
     */
    public com.google.protobuf.ByteString
        getOutIdBytes() {
      java.lang.Object ref = outId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        outId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *外部商品id
     * </pre>
     *
     * <code>string outId = 16;</code>
     * @param value The outId to set.
     * @return This builder for chaining.
     */
    public Builder setOutId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      outId_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *外部商品id
     * </pre>
     *
     * <code>string outId = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearOutId() {
      outId_ = getDefaultInstance().getOutId();
      bitField0_ = (bitField0_ & ~0x00008000);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *外部商品id
     * </pre>
     *
     * <code>string outId = 16;</code>
     * @param value The bytes for outId to set.
     * @return This builder for chaining.
     */
    public Builder setOutIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      outId_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }

    private java.lang.Object preQuestId_ = "";
    /**
     * <pre>
     *前置任务id
     * </pre>
     *
     * <code>string preQuestId = 17;</code>
     * @return The preQuestId.
     */
    public java.lang.String getPreQuestId() {
      java.lang.Object ref = preQuestId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        preQuestId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *前置任务id
     * </pre>
     *
     * <code>string preQuestId = 17;</code>
     * @return The bytes for preQuestId.
     */
    public com.google.protobuf.ByteString
        getPreQuestIdBytes() {
      java.lang.Object ref = preQuestId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        preQuestId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *前置任务id
     * </pre>
     *
     * <code>string preQuestId = 17;</code>
     * @param value The preQuestId to set.
     * @return This builder for chaining.
     */
    public Builder setPreQuestId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      preQuestId_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *前置任务id
     * </pre>
     *
     * <code>string preQuestId = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearPreQuestId() {
      preQuestId_ = getDefaultInstance().getPreQuestId();
      bitField0_ = (bitField0_ & ~0x00010000);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *前置任务id
     * </pre>
     *
     * <code>string preQuestId = 17;</code>
     * @param value The bytes for preQuestId to set.
     * @return This builder for chaining.
     */
    public Builder setPreQuestIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      preQuestId_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }

    private java.lang.Object saasId_ = "";
    /**
     * <pre>
     *saasId
     * </pre>
     *
     * <code>string saasId = 18;</code>
     * @return The saasId.
     */
    public java.lang.String getSaasId() {
      java.lang.Object ref = saasId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        saasId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *saasId
     * </pre>
     *
     * <code>string saasId = 18;</code>
     * @return The bytes for saasId.
     */
    public com.google.protobuf.ByteString
        getSaasIdBytes() {
      java.lang.Object ref = saasId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        saasId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *saasId
     * </pre>
     *
     * <code>string saasId = 18;</code>
     * @param value The saasId to set.
     * @return This builder for chaining.
     */
    public Builder setSaasId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      saasId_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *saasId
     * </pre>
     *
     * <code>string saasId = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearSaasId() {
      saasId_ = getDefaultInstance().getSaasId();
      bitField0_ = (bitField0_ & ~0x00020000);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *saasId
     * </pre>
     *
     * <code>string saasId = 18;</code>
     * @param value The bytes for saasId to set.
     * @return This builder for chaining.
     */
    public Builder setSaasIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      saasId_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }

    private java.util.List<java.lang.Integer> test_ =
      java.util.Collections.emptyList();
    private void ensureTestIsMutable() {
      if (!((bitField0_ & 0x00040000) != 0)) {
        test_ = new java.util.ArrayList<java.lang.Integer>(test_);
        bitField0_ |= 0x00040000;
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
     * @return A list containing the test.
     */
    public java.util.List<com.kikitrade.activity.facade.goods.GoodsType> getTestList() {
      return new com.google.protobuf.Internal.ListAdapter<
          java.lang.Integer, com.kikitrade.activity.facade.goods.GoodsType>(test_, test_converter_);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
     * @return The count of test.
     */
    public int getTestCount() {
      return test_.size();
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
     * @param index The index of the element to return.
     * @return The test at the given index.
     */
    public com.kikitrade.activity.facade.goods.GoodsType getTest(int index) {
      return test_converter_.convert(test_.get(index));
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
     * @param index The index to set the value at.
     * @param value The test to set.
     * @return This builder for chaining.
     */
    public Builder setTest(
        int index, com.kikitrade.activity.facade.goods.GoodsType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      ensureTestIsMutable();
      test_.set(index, value.getNumber());
      onChanged();
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
     * @param value The test to add.
     * @return This builder for chaining.
     */
    public Builder addTest(com.kikitrade.activity.facade.goods.GoodsType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      ensureTestIsMutable();
      test_.add(value.getNumber());
      onChanged();
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
     * @param values The test to add.
     * @return This builder for chaining.
     */
    public Builder addAllTest(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.goods.GoodsType> values) {
      ensureTestIsMutable();
      for (com.kikitrade.activity.facade.goods.GoodsType value : values) {
        test_.add(value.getNumber());
      }
      onChanged();
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearTest() {
      test_ = java.util.Collections.emptyList();
      bitField0_ = (bitField0_ & ~0x00040000);
      onChanged();
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
     * @return A list containing the enum numeric values on the wire for test.
     */
    public java.util.List<java.lang.Integer>
    getTestValueList() {
      return java.util.Collections.unmodifiableList(test_);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of test at the given index.
     */
    public int getTestValue(int index) {
      return test_.get(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
     * @param index The index to set the value at.
     * @param value The enum numeric value on the wire for test to set.
     * @return This builder for chaining.
     */
    public Builder setTestValue(
        int index, int value) {
      ensureTestIsMutable();
      test_.set(index, value);
      onChanged();
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
     * @param value The enum numeric value on the wire for test to add.
     * @return This builder for chaining.
     */
    public Builder addTestValue(int value) {
      ensureTestIsMutable();
      test_.add(value);
      onChanged();
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
     * @param values The enum numeric values on the wire for test to add.
     * @return This builder for chaining.
     */
    public Builder addAllTestValue(
        java.lang.Iterable<java.lang.Integer> values) {
      ensureTestIsMutable();
      for (int value : values) {
        test_.add(value);
      }
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.goods.GoodsDTO)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.goods.GoodsDTO)
  private static final com.kikitrade.activity.facade.goods.GoodsDTO DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.goods.GoodsDTO();
  }

  public static com.kikitrade.activity.facade.goods.GoodsDTO getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GoodsDTO>
      PARSER = new com.google.protobuf.AbstractParser<GoodsDTO>() {
    @java.lang.Override
    public GoodsDTO parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GoodsDTO> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GoodsDTO> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.goods.GoodsDTO getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

