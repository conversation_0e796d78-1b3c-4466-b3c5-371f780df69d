// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * <pre>
 **
 *活动保存实体类
 * </pre>
 *
 * Protobuf type {@code com.kikitrade.activity.facade.award.ActivityDTO}
 */
public final class ActivityDTO extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.award.ActivityDTO)
    ActivityDTOOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ActivityDTO.newBuilder() to construct.
  private ActivityDTO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ActivityDTO() {
    id_ = "";
    activityName_ = "";
    type_ = 0;
    startTime_ = "";
    endTime_ = "";
    remark_ = "";
    status_ = 0;
    activityArea_ = "";
    batchFrequency_ = 0;
    rewardRule_ = java.util.Collections.emptyList();
    conditionRule_ = java.util.Collections.emptyList();
    taskId_ = "";
    conditionCode_ = "";
    subType_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ActivityDTO();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ActivityDTO_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ActivityDTO_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.award.ActivityDTO.class, com.kikitrade.activity.facade.award.ActivityDTO.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <pre>
   *activityId 为空时新增，不为空时修改
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *activityId 为空时新增，不为空时修改
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACTIVITYNAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object activityName_ = "";
  /**
   * <code>string activityName = 2;</code>
   * @return The activityName.
   */
  @java.lang.Override
  public java.lang.String getActivityName() {
    java.lang.Object ref = activityName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      activityName_ = s;
      return s;
    }
  }
  /**
   * <code>string activityName = 2;</code>
   * @return The bytes for activityName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getActivityNameBytes() {
    java.lang.Object ref = activityName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      activityName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TYPE_FIELD_NUMBER = 3;
  private int type_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.award.ActivityTypeEnum type = 3;</code>
   * @return The enum numeric value on the wire for type.
   */
  @java.lang.Override public int getTypeValue() {
    return type_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.award.ActivityTypeEnum type = 3;</code>
   * @return The type.
   */
  @java.lang.Override public com.kikitrade.activity.facade.award.ActivityTypeEnum getType() {
    com.kikitrade.activity.facade.award.ActivityTypeEnum result = com.kikitrade.activity.facade.award.ActivityTypeEnum.forNumber(type_);
    return result == null ? com.kikitrade.activity.facade.award.ActivityTypeEnum.UNRECOGNIZED : result;
  }

  public static final int STARTTIME_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object startTime_ = "";
  /**
   * <code>string startTime = 4;</code>
   * @return The startTime.
   */
  @java.lang.Override
  public java.lang.String getStartTime() {
    java.lang.Object ref = startTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      startTime_ = s;
      return s;
    }
  }
  /**
   * <code>string startTime = 4;</code>
   * @return The bytes for startTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStartTimeBytes() {
    java.lang.Object ref = startTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      startTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ENDTIME_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object endTime_ = "";
  /**
   * <pre>
   *结束时间大于开始时间
   * </pre>
   *
   * <code>string endTime = 5;</code>
   * @return The endTime.
   */
  @java.lang.Override
  public java.lang.String getEndTime() {
    java.lang.Object ref = endTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      endTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *结束时间大于开始时间
   * </pre>
   *
   * <code>string endTime = 5;</code>
   * @return The bytes for endTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getEndTimeBytes() {
    java.lang.Object ref = endTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      endTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REMARK_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object remark_ = "";
  /**
   * <code>string remark = 6;</code>
   * @return The remark.
   */
  @java.lang.Override
  public java.lang.String getRemark() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      remark_ = s;
      return s;
    }
  }
  /**
   * <code>string remark = 6;</code>
   * @return The bytes for remark.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRemarkBytes() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      remark_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATUS_FIELD_NUMBER = 7;
  private int status_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.award.ActivityStatusEnum status = 7;</code>
   * @return The enum numeric value on the wire for status.
   */
  @java.lang.Override public int getStatusValue() {
    return status_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.award.ActivityStatusEnum status = 7;</code>
   * @return The status.
   */
  @java.lang.Override public com.kikitrade.activity.facade.award.ActivityStatusEnum getStatus() {
    com.kikitrade.activity.facade.award.ActivityStatusEnum result = com.kikitrade.activity.facade.award.ActivityStatusEnum.forNumber(status_);
    return result == null ? com.kikitrade.activity.facade.award.ActivityStatusEnum.UNRECOGNIZED : result;
  }

  public static final int ACTIVITYAREA_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object activityArea_ = "";
  /**
   * <code>string activityArea = 8;</code>
   * @return The activityArea.
   */
  @java.lang.Override
  public java.lang.String getActivityArea() {
    java.lang.Object ref = activityArea_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      activityArea_ = s;
      return s;
    }
  }
  /**
   * <code>string activityArea = 8;</code>
   * @return The bytes for activityArea.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getActivityAreaBytes() {
    java.lang.Object ref = activityArea_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      activityArea_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AUTOCREATEBATCH_FIELD_NUMBER = 9;
  private boolean autoCreateBatch_ = false;
  /**
   * <code>bool autoCreateBatch = 9;</code>
   * @return The autoCreateBatch.
   */
  @java.lang.Override
  public boolean getAutoCreateBatch() {
    return autoCreateBatch_;
  }

  public static final int BATCHFREQUENCY_FIELD_NUMBER = 10;
  private int batchFrequency_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.award.BatchFrequency batchFrequency = 10;</code>
   * @return The enum numeric value on the wire for batchFrequency.
   */
  @java.lang.Override public int getBatchFrequencyValue() {
    return batchFrequency_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.award.BatchFrequency batchFrequency = 10;</code>
   * @return The batchFrequency.
   */
  @java.lang.Override public com.kikitrade.activity.facade.award.BatchFrequency getBatchFrequency() {
    com.kikitrade.activity.facade.award.BatchFrequency result = com.kikitrade.activity.facade.award.BatchFrequency.forNumber(batchFrequency_);
    return result == null ? com.kikitrade.activity.facade.award.BatchFrequency.UNRECOGNIZED : result;
  }

  public static final int REWARDRULE_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.award.RewardRule> rewardRule_;
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.award.RewardRule> getRewardRuleList() {
    return rewardRule_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.award.RewardRuleOrBuilder> 
      getRewardRuleOrBuilderList() {
    return rewardRule_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
   */
  @java.lang.Override
  public int getRewardRuleCount() {
    return rewardRule_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.RewardRule getRewardRule(int index) {
    return rewardRule_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.RewardRuleOrBuilder getRewardRuleOrBuilder(
      int index) {
    return rewardRule_.get(index);
  }

  public static final int CONDITIONRULE_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.award.ConditionRule> conditionRule_;
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.award.ConditionRule> getConditionRuleList() {
    return conditionRule_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.award.ConditionRuleOrBuilder> 
      getConditionRuleOrBuilderList() {
    return conditionRule_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
   */
  @java.lang.Override
  public int getConditionRuleCount() {
    return conditionRule_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.ConditionRule getConditionRule(int index) {
    return conditionRule_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.ConditionRuleOrBuilder getConditionRuleOrBuilder(
      int index) {
    return conditionRule_.get(index);
  }

  public static final int AUTOAPPROVE_FIELD_NUMBER = 13;
  private boolean autoApprove_ = false;
  /**
   * <code>bool autoApprove = 13;</code>
   * @return The autoApprove.
   */
  @java.lang.Override
  public boolean getAutoApprove() {
    return autoApprove_;
  }

  public static final int TASKID_FIELD_NUMBER = 14;
  @SuppressWarnings("serial")
  private volatile java.lang.Object taskId_ = "";
  /**
   * <code>string taskId = 14;</code>
   * @return The taskId.
   */
  @java.lang.Override
  public java.lang.String getTaskId() {
    java.lang.Object ref = taskId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      taskId_ = s;
      return s;
    }
  }
  /**
   * <code>string taskId = 14;</code>
   * @return The bytes for taskId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTaskIdBytes() {
    java.lang.Object ref = taskId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      taskId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CONDITIONCODE_FIELD_NUMBER = 15;
  @SuppressWarnings("serial")
  private volatile java.lang.Object conditionCode_ = "";
  /**
   * <code>string conditionCode = 15;</code>
   * @return The conditionCode.
   */
  @java.lang.Override
  public java.lang.String getConditionCode() {
    java.lang.Object ref = conditionCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      conditionCode_ = s;
      return s;
    }
  }
  /**
   * <code>string conditionCode = 15;</code>
   * @return The bytes for conditionCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getConditionCodeBytes() {
    java.lang.Object ref = conditionCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      conditionCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SUBTYPE_FIELD_NUMBER = 16;
  private int subType_ = 0;
  /**
   * <pre>
   * 子活动类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.ActivitySubTypeEnum subType = 16;</code>
   * @return The enum numeric value on the wire for subType.
   */
  @java.lang.Override public int getSubTypeValue() {
    return subType_;
  }
  /**
   * <pre>
   * 子活动类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.ActivitySubTypeEnum subType = 16;</code>
   * @return The subType.
   */
  @java.lang.Override public com.kikitrade.activity.facade.award.ActivitySubTypeEnum getSubType() {
    com.kikitrade.activity.facade.award.ActivitySubTypeEnum result = com.kikitrade.activity.facade.award.ActivitySubTypeEnum.forNumber(subType_);
    return result == null ? com.kikitrade.activity.facade.award.ActivitySubTypeEnum.UNRECOGNIZED : result;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityName_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, activityName_);
    }
    if (type_ != com.kikitrade.activity.facade.award.ActivityTypeEnum.HIERARCHY.getNumber()) {
      output.writeEnum(3, type_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(startTime_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, startTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(endTime_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, endTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(remark_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, remark_);
    }
    if (status_ != com.kikitrade.activity.facade.award.ActivityStatusEnum.DRAFT.getNumber()) {
      output.writeEnum(7, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityArea_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, activityArea_);
    }
    if (autoCreateBatch_ != false) {
      output.writeBool(9, autoCreateBatch_);
    }
    if (batchFrequency_ != com.kikitrade.activity.facade.award.BatchFrequency.EVERY_DAY.getNumber()) {
      output.writeEnum(10, batchFrequency_);
    }
    for (int i = 0; i < rewardRule_.size(); i++) {
      output.writeMessage(11, rewardRule_.get(i));
    }
    for (int i = 0; i < conditionRule_.size(); i++) {
      output.writeMessage(12, conditionRule_.get(i));
    }
    if (autoApprove_ != false) {
      output.writeBool(13, autoApprove_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(taskId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 14, taskId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(conditionCode_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 15, conditionCode_);
    }
    if (subType_ != com.kikitrade.activity.facade.award.ActivitySubTypeEnum.NONE.getNumber()) {
      output.writeEnum(16, subType_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityName_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, activityName_);
    }
    if (type_ != com.kikitrade.activity.facade.award.ActivityTypeEnum.HIERARCHY.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(3, type_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(startTime_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, startTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(endTime_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, endTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(remark_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, remark_);
    }
    if (status_ != com.kikitrade.activity.facade.award.ActivityStatusEnum.DRAFT.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(7, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityArea_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, activityArea_);
    }
    if (autoCreateBatch_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(9, autoCreateBatch_);
    }
    if (batchFrequency_ != com.kikitrade.activity.facade.award.BatchFrequency.EVERY_DAY.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(10, batchFrequency_);
    }
    for (int i = 0; i < rewardRule_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(11, rewardRule_.get(i));
    }
    for (int i = 0; i < conditionRule_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(12, conditionRule_.get(i));
    }
    if (autoApprove_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(13, autoApprove_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(taskId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, taskId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(conditionCode_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, conditionCode_);
    }
    if (subType_ != com.kikitrade.activity.facade.award.ActivitySubTypeEnum.NONE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(16, subType_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.award.ActivityDTO)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.award.ActivityDTO other = (com.kikitrade.activity.facade.award.ActivityDTO) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getActivityName()
        .equals(other.getActivityName())) return false;
    if (type_ != other.type_) return false;
    if (!getStartTime()
        .equals(other.getStartTime())) return false;
    if (!getEndTime()
        .equals(other.getEndTime())) return false;
    if (!getRemark()
        .equals(other.getRemark())) return false;
    if (status_ != other.status_) return false;
    if (!getActivityArea()
        .equals(other.getActivityArea())) return false;
    if (getAutoCreateBatch()
        != other.getAutoCreateBatch()) return false;
    if (batchFrequency_ != other.batchFrequency_) return false;
    if (!getRewardRuleList()
        .equals(other.getRewardRuleList())) return false;
    if (!getConditionRuleList()
        .equals(other.getConditionRuleList())) return false;
    if (getAutoApprove()
        != other.getAutoApprove()) return false;
    if (!getTaskId()
        .equals(other.getTaskId())) return false;
    if (!getConditionCode()
        .equals(other.getConditionCode())) return false;
    if (subType_ != other.subType_) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + ACTIVITYNAME_FIELD_NUMBER;
    hash = (53 * hash) + getActivityName().hashCode();
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + type_;
    hash = (37 * hash) + STARTTIME_FIELD_NUMBER;
    hash = (53 * hash) + getStartTime().hashCode();
    hash = (37 * hash) + ENDTIME_FIELD_NUMBER;
    hash = (53 * hash) + getEndTime().hashCode();
    hash = (37 * hash) + REMARK_FIELD_NUMBER;
    hash = (53 * hash) + getRemark().hashCode();
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + status_;
    hash = (37 * hash) + ACTIVITYAREA_FIELD_NUMBER;
    hash = (53 * hash) + getActivityArea().hashCode();
    hash = (37 * hash) + AUTOCREATEBATCH_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getAutoCreateBatch());
    hash = (37 * hash) + BATCHFREQUENCY_FIELD_NUMBER;
    hash = (53 * hash) + batchFrequency_;
    if (getRewardRuleCount() > 0) {
      hash = (37 * hash) + REWARDRULE_FIELD_NUMBER;
      hash = (53 * hash) + getRewardRuleList().hashCode();
    }
    if (getConditionRuleCount() > 0) {
      hash = (37 * hash) + CONDITIONRULE_FIELD_NUMBER;
      hash = (53 * hash) + getConditionRuleList().hashCode();
    }
    hash = (37 * hash) + AUTOAPPROVE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getAutoApprove());
    hash = (37 * hash) + TASKID_FIELD_NUMBER;
    hash = (53 * hash) + getTaskId().hashCode();
    hash = (37 * hash) + CONDITIONCODE_FIELD_NUMBER;
    hash = (53 * hash) + getConditionCode().hashCode();
    hash = (37 * hash) + SUBTYPE_FIELD_NUMBER;
    hash = (53 * hash) + subType_;
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.award.ActivityDTO parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ActivityDTO parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ActivityDTO parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ActivityDTO parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ActivityDTO parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ActivityDTO parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ActivityDTO parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.ActivityDTO parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.award.ActivityDTO parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.award.ActivityDTO parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ActivityDTO parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.ActivityDTO parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.award.ActivityDTO prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   **
   *活动保存实体类
   * </pre>
   *
   * Protobuf type {@code com.kikitrade.activity.facade.award.ActivityDTO}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.award.ActivityDTO)
      com.kikitrade.activity.facade.award.ActivityDTOOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ActivityDTO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ActivityDTO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.award.ActivityDTO.class, com.kikitrade.activity.facade.award.ActivityDTO.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.award.ActivityDTO.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      activityName_ = "";
      type_ = 0;
      startTime_ = "";
      endTime_ = "";
      remark_ = "";
      status_ = 0;
      activityArea_ = "";
      autoCreateBatch_ = false;
      batchFrequency_ = 0;
      if (rewardRuleBuilder_ == null) {
        rewardRule_ = java.util.Collections.emptyList();
      } else {
        rewardRule_ = null;
        rewardRuleBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000400);
      if (conditionRuleBuilder_ == null) {
        conditionRule_ = java.util.Collections.emptyList();
      } else {
        conditionRule_ = null;
        conditionRuleBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000800);
      autoApprove_ = false;
      taskId_ = "";
      conditionCode_ = "";
      subType_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ActivityDTO_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ActivityDTO getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.award.ActivityDTO.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ActivityDTO build() {
      com.kikitrade.activity.facade.award.ActivityDTO result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ActivityDTO buildPartial() {
      com.kikitrade.activity.facade.award.ActivityDTO result = new com.kikitrade.activity.facade.award.ActivityDTO(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.award.ActivityDTO result) {
      if (rewardRuleBuilder_ == null) {
        if (((bitField0_ & 0x00000400) != 0)) {
          rewardRule_ = java.util.Collections.unmodifiableList(rewardRule_);
          bitField0_ = (bitField0_ & ~0x00000400);
        }
        result.rewardRule_ = rewardRule_;
      } else {
        result.rewardRule_ = rewardRuleBuilder_.build();
      }
      if (conditionRuleBuilder_ == null) {
        if (((bitField0_ & 0x00000800) != 0)) {
          conditionRule_ = java.util.Collections.unmodifiableList(conditionRule_);
          bitField0_ = (bitField0_ & ~0x00000800);
        }
        result.conditionRule_ = conditionRule_;
      } else {
        result.conditionRule_ = conditionRuleBuilder_.build();
      }
    }

    private void buildPartial0(com.kikitrade.activity.facade.award.ActivityDTO result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.activityName_ = activityName_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.type_ = type_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.startTime_ = startTime_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.endTime_ = endTime_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.remark_ = remark_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.status_ = status_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.activityArea_ = activityArea_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.autoCreateBatch_ = autoCreateBatch_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.batchFrequency_ = batchFrequency_;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.autoApprove_ = autoApprove_;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.taskId_ = taskId_;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.conditionCode_ = conditionCode_;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.subType_ = subType_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.award.ActivityDTO) {
        return mergeFrom((com.kikitrade.activity.facade.award.ActivityDTO)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.award.ActivityDTO other) {
      if (other == com.kikitrade.activity.facade.award.ActivityDTO.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getActivityName().isEmpty()) {
        activityName_ = other.activityName_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.type_ != 0) {
        setTypeValue(other.getTypeValue());
      }
      if (!other.getStartTime().isEmpty()) {
        startTime_ = other.startTime_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (!other.getEndTime().isEmpty()) {
        endTime_ = other.endTime_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (!other.getRemark().isEmpty()) {
        remark_ = other.remark_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (other.status_ != 0) {
        setStatusValue(other.getStatusValue());
      }
      if (!other.getActivityArea().isEmpty()) {
        activityArea_ = other.activityArea_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (other.getAutoCreateBatch() != false) {
        setAutoCreateBatch(other.getAutoCreateBatch());
      }
      if (other.batchFrequency_ != 0) {
        setBatchFrequencyValue(other.getBatchFrequencyValue());
      }
      if (rewardRuleBuilder_ == null) {
        if (!other.rewardRule_.isEmpty()) {
          if (rewardRule_.isEmpty()) {
            rewardRule_ = other.rewardRule_;
            bitField0_ = (bitField0_ & ~0x00000400);
          } else {
            ensureRewardRuleIsMutable();
            rewardRule_.addAll(other.rewardRule_);
          }
          onChanged();
        }
      } else {
        if (!other.rewardRule_.isEmpty()) {
          if (rewardRuleBuilder_.isEmpty()) {
            rewardRuleBuilder_.dispose();
            rewardRuleBuilder_ = null;
            rewardRule_ = other.rewardRule_;
            bitField0_ = (bitField0_ & ~0x00000400);
            rewardRuleBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getRewardRuleFieldBuilder() : null;
          } else {
            rewardRuleBuilder_.addAllMessages(other.rewardRule_);
          }
        }
      }
      if (conditionRuleBuilder_ == null) {
        if (!other.conditionRule_.isEmpty()) {
          if (conditionRule_.isEmpty()) {
            conditionRule_ = other.conditionRule_;
            bitField0_ = (bitField0_ & ~0x00000800);
          } else {
            ensureConditionRuleIsMutable();
            conditionRule_.addAll(other.conditionRule_);
          }
          onChanged();
        }
      } else {
        if (!other.conditionRule_.isEmpty()) {
          if (conditionRuleBuilder_.isEmpty()) {
            conditionRuleBuilder_.dispose();
            conditionRuleBuilder_ = null;
            conditionRule_ = other.conditionRule_;
            bitField0_ = (bitField0_ & ~0x00000800);
            conditionRuleBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getConditionRuleFieldBuilder() : null;
          } else {
            conditionRuleBuilder_.addAllMessages(other.conditionRule_);
          }
        }
      }
      if (other.getAutoApprove() != false) {
        setAutoApprove(other.getAutoApprove());
      }
      if (!other.getTaskId().isEmpty()) {
        taskId_ = other.taskId_;
        bitField0_ |= 0x00002000;
        onChanged();
      }
      if (!other.getConditionCode().isEmpty()) {
        conditionCode_ = other.conditionCode_;
        bitField0_ |= 0x00004000;
        onChanged();
      }
      if (other.subType_ != 0) {
        setSubTypeValue(other.getSubTypeValue());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              activityName_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              type_ = input.readEnum();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              startTime_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              endTime_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              remark_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 56: {
              status_ = input.readEnum();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 66: {
              activityArea_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 72: {
              autoCreateBatch_ = input.readBool();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 80: {
              batchFrequency_ = input.readEnum();
              bitField0_ |= 0x00000200;
              break;
            } // case 80
            case 90: {
              com.kikitrade.activity.facade.award.RewardRule m =
                  input.readMessage(
                      com.kikitrade.activity.facade.award.RewardRule.parser(),
                      extensionRegistry);
              if (rewardRuleBuilder_ == null) {
                ensureRewardRuleIsMutable();
                rewardRule_.add(m);
              } else {
                rewardRuleBuilder_.addMessage(m);
              }
              break;
            } // case 90
            case 98: {
              com.kikitrade.activity.facade.award.ConditionRule m =
                  input.readMessage(
                      com.kikitrade.activity.facade.award.ConditionRule.parser(),
                      extensionRegistry);
              if (conditionRuleBuilder_ == null) {
                ensureConditionRuleIsMutable();
                conditionRule_.add(m);
              } else {
                conditionRuleBuilder_.addMessage(m);
              }
              break;
            } // case 98
            case 104: {
              autoApprove_ = input.readBool();
              bitField0_ |= 0x00001000;
              break;
            } // case 104
            case 114: {
              taskId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00002000;
              break;
            } // case 114
            case 122: {
              conditionCode_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00004000;
              break;
            } // case 122
            case 128: {
              subType_ = input.readEnum();
              bitField0_ |= 0x00008000;
              break;
            } // case 128
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <pre>
     *activityId 为空时新增，不为空时修改
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *activityId 为空时新增，不为空时修改
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *activityId 为空时新增，不为空时修改
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *activityId 为空时新增，不为空时修改
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *activityId 为空时新增，不为空时修改
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object activityName_ = "";
    /**
     * <code>string activityName = 2;</code>
     * @return The activityName.
     */
    public java.lang.String getActivityName() {
      java.lang.Object ref = activityName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        activityName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string activityName = 2;</code>
     * @return The bytes for activityName.
     */
    public com.google.protobuf.ByteString
        getActivityNameBytes() {
      java.lang.Object ref = activityName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        activityName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string activityName = 2;</code>
     * @param value The activityName to set.
     * @return This builder for chaining.
     */
    public Builder setActivityName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      activityName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string activityName = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityName() {
      activityName_ = getDefaultInstance().getActivityName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string activityName = 2;</code>
     * @param value The bytes for activityName to set.
     * @return This builder for chaining.
     */
    public Builder setActivityNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      activityName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private int type_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.award.ActivityTypeEnum type = 3;</code>
     * @return The enum numeric value on the wire for type.
     */
    @java.lang.Override public int getTypeValue() {
      return type_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.ActivityTypeEnum type = 3;</code>
     * @param value The enum numeric value on the wire for type to set.
     * @return This builder for chaining.
     */
    public Builder setTypeValue(int value) {
      type_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.ActivityTypeEnum type = 3;</code>
     * @return The type.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.award.ActivityTypeEnum getType() {
      com.kikitrade.activity.facade.award.ActivityTypeEnum result = com.kikitrade.activity.facade.award.ActivityTypeEnum.forNumber(type_);
      return result == null ? com.kikitrade.activity.facade.award.ActivityTypeEnum.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.ActivityTypeEnum type = 3;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(com.kikitrade.activity.facade.award.ActivityTypeEnum value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000004;
      type_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.ActivityTypeEnum type = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000004);
      type_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object startTime_ = "";
    /**
     * <code>string startTime = 4;</code>
     * @return The startTime.
     */
    public java.lang.String getStartTime() {
      java.lang.Object ref = startTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        startTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string startTime = 4;</code>
     * @return The bytes for startTime.
     */
    public com.google.protobuf.ByteString
        getStartTimeBytes() {
      java.lang.Object ref = startTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        startTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string startTime = 4;</code>
     * @param value The startTime to set.
     * @return This builder for chaining.
     */
    public Builder setStartTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      startTime_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>string startTime = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearStartTime() {
      startTime_ = getDefaultInstance().getStartTime();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>string startTime = 4;</code>
     * @param value The bytes for startTime to set.
     * @return This builder for chaining.
     */
    public Builder setStartTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      startTime_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object endTime_ = "";
    /**
     * <pre>
     *结束时间大于开始时间
     * </pre>
     *
     * <code>string endTime = 5;</code>
     * @return The endTime.
     */
    public java.lang.String getEndTime() {
      java.lang.Object ref = endTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        endTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *结束时间大于开始时间
     * </pre>
     *
     * <code>string endTime = 5;</code>
     * @return The bytes for endTime.
     */
    public com.google.protobuf.ByteString
        getEndTimeBytes() {
      java.lang.Object ref = endTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        endTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *结束时间大于开始时间
     * </pre>
     *
     * <code>string endTime = 5;</code>
     * @param value The endTime to set.
     * @return This builder for chaining.
     */
    public Builder setEndTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      endTime_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *结束时间大于开始时间
     * </pre>
     *
     * <code>string endTime = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearEndTime() {
      endTime_ = getDefaultInstance().getEndTime();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *结束时间大于开始时间
     * </pre>
     *
     * <code>string endTime = 5;</code>
     * @param value The bytes for endTime to set.
     * @return This builder for chaining.
     */
    public Builder setEndTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      endTime_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private java.lang.Object remark_ = "";
    /**
     * <code>string remark = 6;</code>
     * @return The remark.
     */
    public java.lang.String getRemark() {
      java.lang.Object ref = remark_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        remark_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string remark = 6;</code>
     * @return The bytes for remark.
     */
    public com.google.protobuf.ByteString
        getRemarkBytes() {
      java.lang.Object ref = remark_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        remark_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string remark = 6;</code>
     * @param value The remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemark(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      remark_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>string remark = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearRemark() {
      remark_ = getDefaultInstance().getRemark();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>string remark = 6;</code>
     * @param value The bytes for remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemarkBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      remark_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private int status_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.award.ActivityStatusEnum status = 7;</code>
     * @return The enum numeric value on the wire for status.
     */
    @java.lang.Override public int getStatusValue() {
      return status_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.ActivityStatusEnum status = 7;</code>
     * @param value The enum numeric value on the wire for status to set.
     * @return This builder for chaining.
     */
    public Builder setStatusValue(int value) {
      status_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.ActivityStatusEnum status = 7;</code>
     * @return The status.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.award.ActivityStatusEnum getStatus() {
      com.kikitrade.activity.facade.award.ActivityStatusEnum result = com.kikitrade.activity.facade.award.ActivityStatusEnum.forNumber(status_);
      return result == null ? com.kikitrade.activity.facade.award.ActivityStatusEnum.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.ActivityStatusEnum status = 7;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(com.kikitrade.activity.facade.award.ActivityStatusEnum value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000040;
      status_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.ActivityStatusEnum status = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      bitField0_ = (bitField0_ & ~0x00000040);
      status_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object activityArea_ = "";
    /**
     * <code>string activityArea = 8;</code>
     * @return The activityArea.
     */
    public java.lang.String getActivityArea() {
      java.lang.Object ref = activityArea_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        activityArea_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string activityArea = 8;</code>
     * @return The bytes for activityArea.
     */
    public com.google.protobuf.ByteString
        getActivityAreaBytes() {
      java.lang.Object ref = activityArea_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        activityArea_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string activityArea = 8;</code>
     * @param value The activityArea to set.
     * @return This builder for chaining.
     */
    public Builder setActivityArea(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      activityArea_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>string activityArea = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityArea() {
      activityArea_ = getDefaultInstance().getActivityArea();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <code>string activityArea = 8;</code>
     * @param value The bytes for activityArea to set.
     * @return This builder for chaining.
     */
    public Builder setActivityAreaBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      activityArea_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private boolean autoCreateBatch_ ;
    /**
     * <code>bool autoCreateBatch = 9;</code>
     * @return The autoCreateBatch.
     */
    @java.lang.Override
    public boolean getAutoCreateBatch() {
      return autoCreateBatch_;
    }
    /**
     * <code>bool autoCreateBatch = 9;</code>
     * @param value The autoCreateBatch to set.
     * @return This builder for chaining.
     */
    public Builder setAutoCreateBatch(boolean value) {

      autoCreateBatch_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>bool autoCreateBatch = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearAutoCreateBatch() {
      bitField0_ = (bitField0_ & ~0x00000100);
      autoCreateBatch_ = false;
      onChanged();
      return this;
    }

    private int batchFrequency_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.award.BatchFrequency batchFrequency = 10;</code>
     * @return The enum numeric value on the wire for batchFrequency.
     */
    @java.lang.Override public int getBatchFrequencyValue() {
      return batchFrequency_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.BatchFrequency batchFrequency = 10;</code>
     * @param value The enum numeric value on the wire for batchFrequency to set.
     * @return This builder for chaining.
     */
    public Builder setBatchFrequencyValue(int value) {
      batchFrequency_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.BatchFrequency batchFrequency = 10;</code>
     * @return The batchFrequency.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.award.BatchFrequency getBatchFrequency() {
      com.kikitrade.activity.facade.award.BatchFrequency result = com.kikitrade.activity.facade.award.BatchFrequency.forNumber(batchFrequency_);
      return result == null ? com.kikitrade.activity.facade.award.BatchFrequency.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.BatchFrequency batchFrequency = 10;</code>
     * @param value The batchFrequency to set.
     * @return This builder for chaining.
     */
    public Builder setBatchFrequency(com.kikitrade.activity.facade.award.BatchFrequency value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000200;
      batchFrequency_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.BatchFrequency batchFrequency = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearBatchFrequency() {
      bitField0_ = (bitField0_ & ~0x00000200);
      batchFrequency_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<com.kikitrade.activity.facade.award.RewardRule> rewardRule_ =
      java.util.Collections.emptyList();
    private void ensureRewardRuleIsMutable() {
      if (!((bitField0_ & 0x00000400) != 0)) {
        rewardRule_ = new java.util.ArrayList<com.kikitrade.activity.facade.award.RewardRule>(rewardRule_);
        bitField0_ |= 0x00000400;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.RewardRule, com.kikitrade.activity.facade.award.RewardRule.Builder, com.kikitrade.activity.facade.award.RewardRuleOrBuilder> rewardRuleBuilder_;

    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.RewardRule> getRewardRuleList() {
      if (rewardRuleBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rewardRule_);
      } else {
        return rewardRuleBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public int getRewardRuleCount() {
      if (rewardRuleBuilder_ == null) {
        return rewardRule_.size();
      } else {
        return rewardRuleBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public com.kikitrade.activity.facade.award.RewardRule getRewardRule(int index) {
      if (rewardRuleBuilder_ == null) {
        return rewardRule_.get(index);
      } else {
        return rewardRuleBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public Builder setRewardRule(
        int index, com.kikitrade.activity.facade.award.RewardRule value) {
      if (rewardRuleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardRuleIsMutable();
        rewardRule_.set(index, value);
        onChanged();
      } else {
        rewardRuleBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public Builder setRewardRule(
        int index, com.kikitrade.activity.facade.award.RewardRule.Builder builderForValue) {
      if (rewardRuleBuilder_ == null) {
        ensureRewardRuleIsMutable();
        rewardRule_.set(index, builderForValue.build());
        onChanged();
      } else {
        rewardRuleBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public Builder addRewardRule(com.kikitrade.activity.facade.award.RewardRule value) {
      if (rewardRuleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardRuleIsMutable();
        rewardRule_.add(value);
        onChanged();
      } else {
        rewardRuleBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public Builder addRewardRule(
        int index, com.kikitrade.activity.facade.award.RewardRule value) {
      if (rewardRuleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardRuleIsMutable();
        rewardRule_.add(index, value);
        onChanged();
      } else {
        rewardRuleBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public Builder addRewardRule(
        com.kikitrade.activity.facade.award.RewardRule.Builder builderForValue) {
      if (rewardRuleBuilder_ == null) {
        ensureRewardRuleIsMutable();
        rewardRule_.add(builderForValue.build());
        onChanged();
      } else {
        rewardRuleBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public Builder addRewardRule(
        int index, com.kikitrade.activity.facade.award.RewardRule.Builder builderForValue) {
      if (rewardRuleBuilder_ == null) {
        ensureRewardRuleIsMutable();
        rewardRule_.add(index, builderForValue.build());
        onChanged();
      } else {
        rewardRuleBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public Builder addAllRewardRule(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.award.RewardRule> values) {
      if (rewardRuleBuilder_ == null) {
        ensureRewardRuleIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rewardRule_);
        onChanged();
      } else {
        rewardRuleBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public Builder clearRewardRule() {
      if (rewardRuleBuilder_ == null) {
        rewardRule_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
      } else {
        rewardRuleBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public Builder removeRewardRule(int index) {
      if (rewardRuleBuilder_ == null) {
        ensureRewardRuleIsMutable();
        rewardRule_.remove(index);
        onChanged();
      } else {
        rewardRuleBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public com.kikitrade.activity.facade.award.RewardRule.Builder getRewardRuleBuilder(
        int index) {
      return getRewardRuleFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public com.kikitrade.activity.facade.award.RewardRuleOrBuilder getRewardRuleOrBuilder(
        int index) {
      if (rewardRuleBuilder_ == null) {
        return rewardRule_.get(index);  } else {
        return rewardRuleBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.award.RewardRuleOrBuilder> 
         getRewardRuleOrBuilderList() {
      if (rewardRuleBuilder_ != null) {
        return rewardRuleBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rewardRule_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public com.kikitrade.activity.facade.award.RewardRule.Builder addRewardRuleBuilder() {
      return getRewardRuleFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.award.RewardRule.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public com.kikitrade.activity.facade.award.RewardRule.Builder addRewardRuleBuilder(
        int index) {
      return getRewardRuleFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.award.RewardRule.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.RewardRule.Builder> 
         getRewardRuleBuilderList() {
      return getRewardRuleFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.RewardRule, com.kikitrade.activity.facade.award.RewardRule.Builder, com.kikitrade.activity.facade.award.RewardRuleOrBuilder> 
        getRewardRuleFieldBuilder() {
      if (rewardRuleBuilder_ == null) {
        rewardRuleBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.award.RewardRule, com.kikitrade.activity.facade.award.RewardRule.Builder, com.kikitrade.activity.facade.award.RewardRuleOrBuilder>(
                rewardRule_,
                ((bitField0_ & 0x00000400) != 0),
                getParentForChildren(),
                isClean());
        rewardRule_ = null;
      }
      return rewardRuleBuilder_;
    }

    private java.util.List<com.kikitrade.activity.facade.award.ConditionRule> conditionRule_ =
      java.util.Collections.emptyList();
    private void ensureConditionRuleIsMutable() {
      if (!((bitField0_ & 0x00000800) != 0)) {
        conditionRule_ = new java.util.ArrayList<com.kikitrade.activity.facade.award.ConditionRule>(conditionRule_);
        bitField0_ |= 0x00000800;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.ConditionRule, com.kikitrade.activity.facade.award.ConditionRule.Builder, com.kikitrade.activity.facade.award.ConditionRuleOrBuilder> conditionRuleBuilder_;

    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.ConditionRule> getConditionRuleList() {
      if (conditionRuleBuilder_ == null) {
        return java.util.Collections.unmodifiableList(conditionRule_);
      } else {
        return conditionRuleBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public int getConditionRuleCount() {
      if (conditionRuleBuilder_ == null) {
        return conditionRule_.size();
      } else {
        return conditionRuleBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public com.kikitrade.activity.facade.award.ConditionRule getConditionRule(int index) {
      if (conditionRuleBuilder_ == null) {
        return conditionRule_.get(index);
      } else {
        return conditionRuleBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public Builder setConditionRule(
        int index, com.kikitrade.activity.facade.award.ConditionRule value) {
      if (conditionRuleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionRuleIsMutable();
        conditionRule_.set(index, value);
        onChanged();
      } else {
        conditionRuleBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public Builder setConditionRule(
        int index, com.kikitrade.activity.facade.award.ConditionRule.Builder builderForValue) {
      if (conditionRuleBuilder_ == null) {
        ensureConditionRuleIsMutable();
        conditionRule_.set(index, builderForValue.build());
        onChanged();
      } else {
        conditionRuleBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public Builder addConditionRule(com.kikitrade.activity.facade.award.ConditionRule value) {
      if (conditionRuleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionRuleIsMutable();
        conditionRule_.add(value);
        onChanged();
      } else {
        conditionRuleBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public Builder addConditionRule(
        int index, com.kikitrade.activity.facade.award.ConditionRule value) {
      if (conditionRuleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionRuleIsMutable();
        conditionRule_.add(index, value);
        onChanged();
      } else {
        conditionRuleBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public Builder addConditionRule(
        com.kikitrade.activity.facade.award.ConditionRule.Builder builderForValue) {
      if (conditionRuleBuilder_ == null) {
        ensureConditionRuleIsMutable();
        conditionRule_.add(builderForValue.build());
        onChanged();
      } else {
        conditionRuleBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public Builder addConditionRule(
        int index, com.kikitrade.activity.facade.award.ConditionRule.Builder builderForValue) {
      if (conditionRuleBuilder_ == null) {
        ensureConditionRuleIsMutable();
        conditionRule_.add(index, builderForValue.build());
        onChanged();
      } else {
        conditionRuleBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public Builder addAllConditionRule(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.award.ConditionRule> values) {
      if (conditionRuleBuilder_ == null) {
        ensureConditionRuleIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, conditionRule_);
        onChanged();
      } else {
        conditionRuleBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public Builder clearConditionRule() {
      if (conditionRuleBuilder_ == null) {
        conditionRule_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000800);
        onChanged();
      } else {
        conditionRuleBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public Builder removeConditionRule(int index) {
      if (conditionRuleBuilder_ == null) {
        ensureConditionRuleIsMutable();
        conditionRule_.remove(index);
        onChanged();
      } else {
        conditionRuleBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public com.kikitrade.activity.facade.award.ConditionRule.Builder getConditionRuleBuilder(
        int index) {
      return getConditionRuleFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public com.kikitrade.activity.facade.award.ConditionRuleOrBuilder getConditionRuleOrBuilder(
        int index) {
      if (conditionRuleBuilder_ == null) {
        return conditionRule_.get(index);  } else {
        return conditionRuleBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.award.ConditionRuleOrBuilder> 
         getConditionRuleOrBuilderList() {
      if (conditionRuleBuilder_ != null) {
        return conditionRuleBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(conditionRule_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public com.kikitrade.activity.facade.award.ConditionRule.Builder addConditionRuleBuilder() {
      return getConditionRuleFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.award.ConditionRule.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public com.kikitrade.activity.facade.award.ConditionRule.Builder addConditionRuleBuilder(
        int index) {
      return getConditionRuleFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.award.ConditionRule.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.ConditionRule.Builder> 
         getConditionRuleBuilderList() {
      return getConditionRuleFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.ConditionRule, com.kikitrade.activity.facade.award.ConditionRule.Builder, com.kikitrade.activity.facade.award.ConditionRuleOrBuilder> 
        getConditionRuleFieldBuilder() {
      if (conditionRuleBuilder_ == null) {
        conditionRuleBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.award.ConditionRule, com.kikitrade.activity.facade.award.ConditionRule.Builder, com.kikitrade.activity.facade.award.ConditionRuleOrBuilder>(
                conditionRule_,
                ((bitField0_ & 0x00000800) != 0),
                getParentForChildren(),
                isClean());
        conditionRule_ = null;
      }
      return conditionRuleBuilder_;
    }

    private boolean autoApprove_ ;
    /**
     * <code>bool autoApprove = 13;</code>
     * @return The autoApprove.
     */
    @java.lang.Override
    public boolean getAutoApprove() {
      return autoApprove_;
    }
    /**
     * <code>bool autoApprove = 13;</code>
     * @param value The autoApprove to set.
     * @return This builder for chaining.
     */
    public Builder setAutoApprove(boolean value) {

      autoApprove_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>bool autoApprove = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearAutoApprove() {
      bitField0_ = (bitField0_ & ~0x00001000);
      autoApprove_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object taskId_ = "";
    /**
     * <code>string taskId = 14;</code>
     * @return The taskId.
     */
    public java.lang.String getTaskId() {
      java.lang.Object ref = taskId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        taskId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string taskId = 14;</code>
     * @return The bytes for taskId.
     */
    public com.google.protobuf.ByteString
        getTaskIdBytes() {
      java.lang.Object ref = taskId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        taskId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string taskId = 14;</code>
     * @param value The taskId to set.
     * @return This builder for chaining.
     */
    public Builder setTaskId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      taskId_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>string taskId = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearTaskId() {
      taskId_ = getDefaultInstance().getTaskId();
      bitField0_ = (bitField0_ & ~0x00002000);
      onChanged();
      return this;
    }
    /**
     * <code>string taskId = 14;</code>
     * @param value The bytes for taskId to set.
     * @return This builder for chaining.
     */
    public Builder setTaskIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      taskId_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }

    private java.lang.Object conditionCode_ = "";
    /**
     * <code>string conditionCode = 15;</code>
     * @return The conditionCode.
     */
    public java.lang.String getConditionCode() {
      java.lang.Object ref = conditionCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        conditionCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string conditionCode = 15;</code>
     * @return The bytes for conditionCode.
     */
    public com.google.protobuf.ByteString
        getConditionCodeBytes() {
      java.lang.Object ref = conditionCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        conditionCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string conditionCode = 15;</code>
     * @param value The conditionCode to set.
     * @return This builder for chaining.
     */
    public Builder setConditionCode(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      conditionCode_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <code>string conditionCode = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearConditionCode() {
      conditionCode_ = getDefaultInstance().getConditionCode();
      bitField0_ = (bitField0_ & ~0x00004000);
      onChanged();
      return this;
    }
    /**
     * <code>string conditionCode = 15;</code>
     * @param value The bytes for conditionCode to set.
     * @return This builder for chaining.
     */
    public Builder setConditionCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      conditionCode_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }

    private int subType_ = 0;
    /**
     * <pre>
     * 子活动类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.ActivitySubTypeEnum subType = 16;</code>
     * @return The enum numeric value on the wire for subType.
     */
    @java.lang.Override public int getSubTypeValue() {
      return subType_;
    }
    /**
     * <pre>
     * 子活动类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.ActivitySubTypeEnum subType = 16;</code>
     * @param value The enum numeric value on the wire for subType to set.
     * @return This builder for chaining.
     */
    public Builder setSubTypeValue(int value) {
      subType_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 子活动类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.ActivitySubTypeEnum subType = 16;</code>
     * @return The subType.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.award.ActivitySubTypeEnum getSubType() {
      com.kikitrade.activity.facade.award.ActivitySubTypeEnum result = com.kikitrade.activity.facade.award.ActivitySubTypeEnum.forNumber(subType_);
      return result == null ? com.kikitrade.activity.facade.award.ActivitySubTypeEnum.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 子活动类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.ActivitySubTypeEnum subType = 16;</code>
     * @param value The subType to set.
     * @return This builder for chaining.
     */
    public Builder setSubType(com.kikitrade.activity.facade.award.ActivitySubTypeEnum value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00008000;
      subType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 子活动类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.ActivitySubTypeEnum subType = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearSubType() {
      bitField0_ = (bitField0_ & ~0x00008000);
      subType_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.award.ActivityDTO)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.award.ActivityDTO)
  private static final com.kikitrade.activity.facade.award.ActivityDTO DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.award.ActivityDTO();
  }

  public static com.kikitrade.activity.facade.award.ActivityDTO getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ActivityDTO>
      PARSER = new com.google.protobuf.AbstractParser<ActivityDTO>() {
    @java.lang.Override
    public ActivityDTO parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ActivityDTO> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ActivityDTO> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.award.ActivityDTO getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

