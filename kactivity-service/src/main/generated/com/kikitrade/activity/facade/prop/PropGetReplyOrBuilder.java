// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PropFacade.proto

package com.kikitrade.activity.facade.prop;

public interface PropGetReplyOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.prop.PropGetReply)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>.com.kikitrade.activity.facade.prop.Prop prop = 3;</code>
   * @return Whether the prop field is set.
   */
  boolean hasProp();
  /**
   * <code>.com.kikitrade.activity.facade.prop.Prop prop = 3;</code>
   * @return The prop.
   */
  com.kikitrade.activity.facade.prop.Prop getProp();
  /**
   * <code>.com.kikitrade.activity.facade.prop.Prop prop = 3;</code>
   */
  com.kikitrade.activity.facade.prop.PropOrBuilder getPropOrBuilder();
}
