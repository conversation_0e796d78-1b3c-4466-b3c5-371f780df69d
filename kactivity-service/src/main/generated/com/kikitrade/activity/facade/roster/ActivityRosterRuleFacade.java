/*
* Licensed to the Apache Software Foundation (ASF) under one or more
* contributor license agreements.  See the NOTICE file distributed with
* this work for additional information regarding copyright ownership.
* The ASF licenses this file to You under the Apache License, Version 2.0
* (the "License"); you may not use this file except in compliance with
* the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

    package com.kikitrade.activity.facade.roster;

import org.apache.dubbo.common.stream.StreamObserver;
import com.google.protobuf.Message;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.concurrent.CompletableFuture;

public interface ActivityRosterRuleFacade extends org.apache.dubbo.rpc.model.DubboStub {

    String JAVA_SERVICE_NAME = "com.kikitrade.activity.facade.roster.ActivityRosterRuleFacade";
    String SERVICE_NAME = "com.kikitrade.activity.facade.roster.ActivityRosterRuleFacade";

    com.kikitrade.activity.facade.roster.ConditionParameterResponse getConditionNames(com.kikitrade.activity.facade.roster.EmptyRequest request);

    default CompletableFuture<com.kikitrade.activity.facade.roster.ConditionParameterResponse> getConditionNamesAsync(com.kikitrade.activity.facade.roster.EmptyRequest request){
        return CompletableFuture.completedFuture(getConditionNames(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void getConditionNames(com.kikitrade.activity.facade.roster.EmptyRequest request, StreamObserver<com.kikitrade.activity.facade.roster.ConditionParameterResponse> responseObserver){
        getConditionNamesAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

    com.kikitrade.activity.facade.roster.CommonResponse saveCondition(com.kikitrade.activity.facade.roster.ConditionDTO request);

    default CompletableFuture<com.kikitrade.activity.facade.roster.CommonResponse> saveConditionAsync(com.kikitrade.activity.facade.roster.ConditionDTO request){
        return CompletableFuture.completedFuture(saveCondition(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void saveCondition(com.kikitrade.activity.facade.roster.ConditionDTO request, StreamObserver<com.kikitrade.activity.facade.roster.CommonResponse> responseObserver){
        saveConditionAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }






}
