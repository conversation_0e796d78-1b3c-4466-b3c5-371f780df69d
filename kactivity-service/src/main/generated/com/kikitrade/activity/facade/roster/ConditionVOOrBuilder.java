// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityRosterRule.proto

package com.kikitrade.activity.facade.roster;

public interface ConditionVOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.roster.ConditionVO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string name = 1;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <code>string name = 1;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
   * @return A list containing the filterTypes.
   */
  java.util.List<com.kikitrade.activity.facade.roster.FilterType> getFilterTypesList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
   * @return The count of filterTypes.
   */
  int getFilterTypesCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
   * @param index The index of the element to return.
   * @return The filterTypes at the given index.
   */
  com.kikitrade.activity.facade.roster.FilterType getFilterTypes(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
   * @return A list containing the enum numeric values on the wire for filterTypes.
   */
  java.util.List<java.lang.Integer>
  getFilterTypesValueList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
   * @param index The index of the value to return.
   * @return The enum numeric value on the wire of filterTypes at the given index.
   */
  int getFilterTypesValue(int index);

  /**
   * <code>string alisa = 3;</code>
   * @return The alisa.
   */
  java.lang.String getAlisa();
  /**
   * <code>string alisa = 3;</code>
   * @return The bytes for alisa.
   */
  com.google.protobuf.ByteString
      getAlisaBytes();

  /**
   * <code>bool dynamic = 4;</code>
   * @return The dynamic.
   */
  boolean getDynamic();
}
