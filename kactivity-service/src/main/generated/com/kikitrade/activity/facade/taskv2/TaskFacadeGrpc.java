package com.kikitrade.activity.facade.taskv2;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.58.0)",
    comments = "Source: Task.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class TaskFacadeGrpc {

  private TaskFacadeGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.kikitrade.activity.facade.taskv2.TaskFacade";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.taskv2.TaskDTO,
      com.kikitrade.activity.facade.taskv2.IdVOResponse> getSaveMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "save",
      requestType = com.kikitrade.activity.facade.taskv2.TaskDTO.class,
      responseType = com.kikitrade.activity.facade.taskv2.IdVOResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.taskv2.TaskDTO,
      com.kikitrade.activity.facade.taskv2.IdVOResponse> getSaveMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.taskv2.TaskDTO, com.kikitrade.activity.facade.taskv2.IdVOResponse> getSaveMethod;
    if ((getSaveMethod = TaskFacadeGrpc.getSaveMethod) == null) {
      synchronized (TaskFacadeGrpc.class) {
        if ((getSaveMethod = TaskFacadeGrpc.getSaveMethod) == null) {
          TaskFacadeGrpc.getSaveMethod = getSaveMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.taskv2.TaskDTO, com.kikitrade.activity.facade.taskv2.IdVOResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "save"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.taskv2.TaskDTO.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.taskv2.IdVOResponse.getDefaultInstance()))
              .setSchemaDescriptor(new TaskFacadeMethodDescriptorSupplier("save"))
              .build();
        }
      }
    }
    return getSaveMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.taskv2.EmptyRequest,
      com.kikitrade.activity.facade.taskv2.CodeVOResponse> getGetCodeMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "getCode",
      requestType = com.kikitrade.activity.facade.taskv2.EmptyRequest.class,
      responseType = com.kikitrade.activity.facade.taskv2.CodeVOResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.taskv2.EmptyRequest,
      com.kikitrade.activity.facade.taskv2.CodeVOResponse> getGetCodeMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.taskv2.EmptyRequest, com.kikitrade.activity.facade.taskv2.CodeVOResponse> getGetCodeMethod;
    if ((getGetCodeMethod = TaskFacadeGrpc.getGetCodeMethod) == null) {
      synchronized (TaskFacadeGrpc.class) {
        if ((getGetCodeMethod = TaskFacadeGrpc.getGetCodeMethod) == null) {
          TaskFacadeGrpc.getGetCodeMethod = getGetCodeMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.taskv2.EmptyRequest, com.kikitrade.activity.facade.taskv2.CodeVOResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "getCode"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.taskv2.EmptyRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.taskv2.CodeVOResponse.getDefaultInstance()))
              .setSchemaDescriptor(new TaskFacadeMethodDescriptorSupplier("getCode"))
              .build();
        }
      }
    }
    return getGetCodeMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static TaskFacadeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<TaskFacadeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<TaskFacadeStub>() {
        @java.lang.Override
        public TaskFacadeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new TaskFacadeStub(channel, callOptions);
        }
      };
    return TaskFacadeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static TaskFacadeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<TaskFacadeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<TaskFacadeBlockingStub>() {
        @java.lang.Override
        public TaskFacadeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new TaskFacadeBlockingStub(channel, callOptions);
        }
      };
    return TaskFacadeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static TaskFacadeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<TaskFacadeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<TaskFacadeFutureStub>() {
        @java.lang.Override
        public TaskFacadeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new TaskFacadeFutureStub(channel, callOptions);
        }
      };
    return TaskFacadeFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     */
    default void save(com.kikitrade.activity.facade.taskv2.TaskDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.taskv2.IdVOResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSaveMethod(), responseObserver);
    }

    /**
     */
    default void getCode(com.kikitrade.activity.facade.taskv2.EmptyRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.taskv2.CodeVOResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetCodeMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service TaskFacade.
   */
  public static abstract class TaskFacadeImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return TaskFacadeGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service TaskFacade.
   */
  public static final class TaskFacadeStub
      extends io.grpc.stub.AbstractAsyncStub<TaskFacadeStub> {
    private TaskFacadeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected TaskFacadeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new TaskFacadeStub(channel, callOptions);
    }

    /**
     */
    public void save(com.kikitrade.activity.facade.taskv2.TaskDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.taskv2.IdVOResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSaveMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void getCode(com.kikitrade.activity.facade.taskv2.EmptyRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.taskv2.CodeVOResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetCodeMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service TaskFacade.
   */
  public static final class TaskFacadeBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<TaskFacadeBlockingStub> {
    private TaskFacadeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected TaskFacadeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new TaskFacadeBlockingStub(channel, callOptions);
    }

    /**
     */
    public com.kikitrade.activity.facade.taskv2.IdVOResponse save(com.kikitrade.activity.facade.taskv2.TaskDTO request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSaveMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.kikitrade.activity.facade.taskv2.CodeVOResponse getCode(com.kikitrade.activity.facade.taskv2.EmptyRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetCodeMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service TaskFacade.
   */
  public static final class TaskFacadeFutureStub
      extends io.grpc.stub.AbstractFutureStub<TaskFacadeFutureStub> {
    private TaskFacadeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected TaskFacadeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new TaskFacadeFutureStub(channel, callOptions);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.taskv2.IdVOResponse> save(
        com.kikitrade.activity.facade.taskv2.TaskDTO request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSaveMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.taskv2.CodeVOResponse> getCode(
        com.kikitrade.activity.facade.taskv2.EmptyRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetCodeMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_SAVE = 0;
  private static final int METHODID_GET_CODE = 1;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_SAVE:
          serviceImpl.save((com.kikitrade.activity.facade.taskv2.TaskDTO) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.taskv2.IdVOResponse>) responseObserver);
          break;
        case METHODID_GET_CODE:
          serviceImpl.getCode((com.kikitrade.activity.facade.taskv2.EmptyRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.taskv2.CodeVOResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getSaveMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.taskv2.TaskDTO,
              com.kikitrade.activity.facade.taskv2.IdVOResponse>(
                service, METHODID_SAVE)))
        .addMethod(
          getGetCodeMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.taskv2.EmptyRequest,
              com.kikitrade.activity.facade.taskv2.CodeVOResponse>(
                service, METHODID_GET_CODE)))
        .build();
  }

  private static abstract class TaskFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    TaskFacadeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("TaskFacade");
    }
  }

  private static final class TaskFacadeFileDescriptorSupplier
      extends TaskFacadeBaseDescriptorSupplier {
    TaskFacadeFileDescriptorSupplier() {}
  }

  private static final class TaskFacadeMethodDescriptorSupplier
      extends TaskFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    TaskFacadeMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (TaskFacadeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new TaskFacadeFileDescriptorSupplier())
              .addMethod(getSaveMethod())
              .addMethod(getGetCodeMethod())
              .build();
        }
      }
    }
    return result;
  }
}
