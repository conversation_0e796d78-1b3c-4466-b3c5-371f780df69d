// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: TaskFacade.proto

package com.kikitrade.activity.facade.task;

public interface TaskVOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.task.TaskVO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string activityId = 2;</code>
   * @return The activityId.
   */
  java.lang.String getActivityId();
  /**
   * <code>string activityId = 2;</code>
   * @return The bytes for activityId.
   */
  com.google.protobuf.ByteString
      getActivityIdBytes();
}
