// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface ActivityBatchOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.ActivityBatch)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string name = 2;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <code>string activityId = 3;</code>
   * @return The activityId.
   */
  java.lang.String getActivityId();
  /**
   * <code>string activityId = 3;</code>
   * @return The bytes for activityId.
   */
  com.google.protobuf.ByteString
      getActivityIdBytes();

  /**
   * <code>string activityName = 4;</code>
   * @return The activityName.
   */
  java.lang.String getActivityName();
  /**
   * <code>string activityName = 4;</code>
   * @return The bytes for activityName.
   */
  com.google.protobuf.ByteString
      getActivityNameBytes();

  /**
   * <pre>
   *数字货币、道具
   * </pre>
   *
   * <code>string rewardType = 5;</code>
   * @return The rewardType.
   */
  java.lang.String getRewardType();
  /**
   * <pre>
   *数字货币、道具
   * </pre>
   *
   * <code>string rewardType = 5;</code>
   * @return The bytes for rewardType.
   */
  com.google.protobuf.ByteString
      getRewardTypeBytes();

  /**
   * <pre>
   *货币金额
   * </pre>
   *
   * <code>string amount = 6;</code>
   * @return The amount.
   */
  java.lang.String getAmount();
  /**
   * <pre>
   *货币金额
   * </pre>
   *
   * <code>string amount = 6;</code>
   * @return The bytes for amount.
   */
  com.google.protobuf.ByteString
      getAmountBytes();

  /**
   * <pre>
   *货币单位
   * </pre>
   *
   * <code>string currency = 7;</code>
   * @return The currency.
   */
  java.lang.String getCurrency();
  /**
   * <pre>
   *货币单位
   * </pre>
   *
   * <code>string currency = 7;</code>
   * @return The bytes for currency.
   */
  com.google.protobuf.ByteString
      getCurrencyBytes();

  /**
   * <pre>
   *批次描述
   * </pre>
   *
   * <code>string remark = 8;</code>
   * @return The remark.
   */
  java.lang.String getRemark();
  /**
   * <pre>
   *批次描述
   * </pre>
   *
   * <code>string remark = 8;</code>
   * @return The bytes for remark.
   */
  com.google.protobuf.ByteString
      getRemarkBytes();

  /**
   * <pre>
   *是否立即发奖
   * </pre>
   *
   * <code>bool scheduled = 9;</code>
   * @return The scheduled.
   */
  boolean getScheduled();

  /**
   * <pre>
   *发奖时间
   * </pre>
   *
   * <code>string scheduledTime = 10;</code>
   * @return The scheduledTime.
   */
  java.lang.String getScheduledTime();
  /**
   * <pre>
   *发奖时间
   * </pre>
   *
   * <code>string scheduledTime = 10;</code>
   * @return The bytes for scheduledTime.
   */
  com.google.protobuf.ByteString
      getScheduledTimeBytes();

  /**
   * <pre>
   *saasId
   * </pre>
   *
   * <code>string saasId = 11;</code>
   * @return The saasId.
   */
  java.lang.String getSaasId();
  /**
   * <pre>
   *saasId
   * </pre>
   *
   * <code>string saasId = 11;</code>
   * @return The bytes for saasId.
   */
  com.google.protobuf.ByteString
      getSaasIdBytes();

  /**
   * <code>string prizeAmount = 12;</code>
   * @return The prizeAmount.
   */
  java.lang.String getPrizeAmount();
  /**
   * <code>string prizeAmount = 12;</code>
   * @return The bytes for prizeAmount.
   */
  com.google.protobuf.ByteString
      getPrizeAmountBytes();

  /**
   * <code>string winners = 13;</code>
   * @return The winners.
   */
  java.lang.String getWinners();
  /**
   * <code>string winners = 13;</code>
   * @return The bytes for winners.
   */
  com.google.protobuf.ByteString
      getWinnersBytes();

  /**
   * <code>string amended = 14;</code>
   * @return The amended.
   */
  java.lang.String getAmended();
  /**
   * <code>string amended = 14;</code>
   * @return The bytes for amended.
   */
  com.google.protobuf.ByteString
      getAmendedBytes();

  /**
   * <code>string modified = 15;</code>
   * @return The modified.
   */
  java.lang.String getModified();
  /**
   * <code>string modified = 15;</code>
   * @return The bytes for modified.
   */
  com.google.protobuf.ByteString
      getModifiedBytes();

  /**
   * <code>.com.kikitrade.activity.facade.award.BatchStatusEnum status = 16;</code>
   * @return The enum numeric value on the wire for status.
   */
  int getStatusValue();
  /**
   * <code>.com.kikitrade.activity.facade.award.BatchStatusEnum status = 16;</code>
   * @return The status.
   */
  com.kikitrade.activity.facade.award.BatchStatusEnum getStatus();

  /**
   * <pre>
   *下载地址
   * </pre>
   *
   * <code>string ossUrl = 17;</code>
   * @return The ossUrl.
   */
  java.lang.String getOssUrl();
  /**
   * <pre>
   *下载地址
   * </pre>
   *
   * <code>string ossUrl = 17;</code>
   * @return The bytes for ossUrl.
   */
  com.google.protobuf.ByteString
      getOssUrlBytes();

  /**
   * <code>string generateTime = 18;</code>
   * @return The generateTime.
   */
  java.lang.String getGenerateTime();
  /**
   * <code>string generateTime = 18;</code>
   * @return The bytes for generateTime.
   */
  com.google.protobuf.ByteString
      getGenerateTimeBytes();

  /**
   * <code>string sourceOssUrl = 19;</code>
   * @return The sourceOssUrl.
   */
  java.lang.String getSourceOssUrl();
  /**
   * <code>string sourceOssUrl = 19;</code>
   * @return The bytes for sourceOssUrl.
   */
  com.google.protobuf.ByteString
      getSourceOssUrlBytes();

  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
   */
  java.util.List<com.kikitrade.activity.facade.award.RewardRule> 
      getRewardRuleList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
   */
  com.kikitrade.activity.facade.award.RewardRule getRewardRule(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
   */
  int getRewardRuleCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.award.RewardRuleOrBuilder> 
      getRewardRuleOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
   */
  com.kikitrade.activity.facade.award.RewardRuleOrBuilder getRewardRuleOrBuilder(
      int index);

  /**
   * <code>string activityType = 21;</code>
   * @return The activityType.
   */
  java.lang.String getActivityType();
  /**
   * <code>string activityType = 21;</code>
   * @return The bytes for activityType.
   */
  com.google.protobuf.ByteString
      getActivityTypeBytes();

  /**
   * <pre>
   *TASK,OPERATE
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.ActivitySourceEnum source = 22;</code>
   * @return The enum numeric value on the wire for source.
   */
  int getSourceValue();
  /**
   * <pre>
   *TASK,OPERATE
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.ActivitySourceEnum source = 22;</code>
   * @return The source.
   */
  com.kikitrade.activity.facade.award.ActivitySourceEnum getSource();
}
