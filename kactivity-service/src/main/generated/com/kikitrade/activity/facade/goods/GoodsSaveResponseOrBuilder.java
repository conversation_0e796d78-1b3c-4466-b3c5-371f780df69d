// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Goods.proto

package com.kikitrade.activity.facade.goods;

public interface GoodsSaveResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.goods.GoodsSaveResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>string id = 3;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 3;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();
}
