// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityRosterRule.proto

package com.kikitrade.activity.facade.roster;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.roster.ConditionParameter}
 */
public final class ConditionParameter extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.roster.ConditionParameter)
    ConditionParameterOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ConditionParameter.newBuilder() to construct.
  private ConditionParameter(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ConditionParameter() {
    code_ = "";
    condition_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ConditionParameter();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionParameter_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionParameter_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.roster.ConditionParameter.class, com.kikitrade.activity.facade.roster.ConditionParameter.Builder.class);
  }

  public static final int CODE_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object code_ = "";
  /**
   * <code>string code = 1;</code>
   * @return The code.
   */
  @java.lang.Override
  public java.lang.String getCode() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      code_ = s;
      return s;
    }
  }
  /**
   * <code>string code = 1;</code>
   * @return The bytes for code.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCodeBytes() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      code_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CONDITION_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.roster.ConditionVO> condition_;
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.roster.ConditionVO> getConditionList() {
    return condition_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.roster.ConditionVOOrBuilder> 
      getConditionOrBuilderList() {
    return condition_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
   */
  @java.lang.Override
  public int getConditionCount() {
    return condition_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.roster.ConditionVO getCondition(int index) {
    return condition_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.roster.ConditionVOOrBuilder getConditionOrBuilder(
      int index) {
    return condition_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(code_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, code_);
    }
    for (int i = 0; i < condition_.size(); i++) {
      output.writeMessage(2, condition_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(code_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, code_);
    }
    for (int i = 0; i < condition_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, condition_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.roster.ConditionParameter)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.roster.ConditionParameter other = (com.kikitrade.activity.facade.roster.ConditionParameter) obj;

    if (!getCode()
        .equals(other.getCode())) return false;
    if (!getConditionList()
        .equals(other.getConditionList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCode().hashCode();
    if (getConditionCount() > 0) {
      hash = (37 * hash) + CONDITION_FIELD_NUMBER;
      hash = (53 * hash) + getConditionList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.roster.ConditionParameter parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameter parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameter parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameter parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameter parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameter parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameter parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameter parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.roster.ConditionParameter parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.roster.ConditionParameter parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameter parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameter parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.roster.ConditionParameter prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.roster.ConditionParameter}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.roster.ConditionParameter)
      com.kikitrade.activity.facade.roster.ConditionParameterOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionParameter_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionParameter_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.roster.ConditionParameter.class, com.kikitrade.activity.facade.roster.ConditionParameter.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.roster.ConditionParameter.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      code_ = "";
      if (conditionBuilder_ == null) {
        condition_ = java.util.Collections.emptyList();
      } else {
        condition_ = null;
        conditionBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionParameter_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.roster.ConditionParameter getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.roster.ConditionParameter.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.roster.ConditionParameter build() {
      com.kikitrade.activity.facade.roster.ConditionParameter result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.roster.ConditionParameter buildPartial() {
      com.kikitrade.activity.facade.roster.ConditionParameter result = new com.kikitrade.activity.facade.roster.ConditionParameter(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.roster.ConditionParameter result) {
      if (conditionBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          condition_ = java.util.Collections.unmodifiableList(condition_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.condition_ = condition_;
      } else {
        result.condition_ = conditionBuilder_.build();
      }
    }

    private void buildPartial0(com.kikitrade.activity.facade.roster.ConditionParameter result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.code_ = code_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.roster.ConditionParameter) {
        return mergeFrom((com.kikitrade.activity.facade.roster.ConditionParameter)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.roster.ConditionParameter other) {
      if (other == com.kikitrade.activity.facade.roster.ConditionParameter.getDefaultInstance()) return this;
      if (!other.getCode().isEmpty()) {
        code_ = other.code_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (conditionBuilder_ == null) {
        if (!other.condition_.isEmpty()) {
          if (condition_.isEmpty()) {
            condition_ = other.condition_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureConditionIsMutable();
            condition_.addAll(other.condition_);
          }
          onChanged();
        }
      } else {
        if (!other.condition_.isEmpty()) {
          if (conditionBuilder_.isEmpty()) {
            conditionBuilder_.dispose();
            conditionBuilder_ = null;
            condition_ = other.condition_;
            bitField0_ = (bitField0_ & ~0x00000002);
            conditionBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getConditionFieldBuilder() : null;
          } else {
            conditionBuilder_.addAllMessages(other.condition_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              code_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              com.kikitrade.activity.facade.roster.ConditionVO m =
                  input.readMessage(
                      com.kikitrade.activity.facade.roster.ConditionVO.parser(),
                      extensionRegistry);
              if (conditionBuilder_ == null) {
                ensureConditionIsMutable();
                condition_.add(m);
              } else {
                conditionBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object code_ = "";
    /**
     * <code>string code = 1;</code>
     * @return The code.
     */
    public java.lang.String getCode() {
      java.lang.Object ref = code_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        code_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string code = 1;</code>
     * @return The bytes for code.
     */
    public com.google.protobuf.ByteString
        getCodeBytes() {
      java.lang.Object ref = code_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        code_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string code = 1;</code>
     * @param value The code to set.
     * @return This builder for chaining.
     */
    public Builder setCode(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      code_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearCode() {
      code_ = getDefaultInstance().getCode();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string code = 1;</code>
     * @param value The bytes for code to set.
     * @return This builder for chaining.
     */
    public Builder setCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      code_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.util.List<com.kikitrade.activity.facade.roster.ConditionVO> condition_ =
      java.util.Collections.emptyList();
    private void ensureConditionIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        condition_ = new java.util.ArrayList<com.kikitrade.activity.facade.roster.ConditionVO>(condition_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.roster.ConditionVO, com.kikitrade.activity.facade.roster.ConditionVO.Builder, com.kikitrade.activity.facade.roster.ConditionVOOrBuilder> conditionBuilder_;

    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.roster.ConditionVO> getConditionList() {
      if (conditionBuilder_ == null) {
        return java.util.Collections.unmodifiableList(condition_);
      } else {
        return conditionBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public int getConditionCount() {
      if (conditionBuilder_ == null) {
        return condition_.size();
      } else {
        return conditionBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public com.kikitrade.activity.facade.roster.ConditionVO getCondition(int index) {
      if (conditionBuilder_ == null) {
        return condition_.get(index);
      } else {
        return conditionBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public Builder setCondition(
        int index, com.kikitrade.activity.facade.roster.ConditionVO value) {
      if (conditionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionIsMutable();
        condition_.set(index, value);
        onChanged();
      } else {
        conditionBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public Builder setCondition(
        int index, com.kikitrade.activity.facade.roster.ConditionVO.Builder builderForValue) {
      if (conditionBuilder_ == null) {
        ensureConditionIsMutable();
        condition_.set(index, builderForValue.build());
        onChanged();
      } else {
        conditionBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public Builder addCondition(com.kikitrade.activity.facade.roster.ConditionVO value) {
      if (conditionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionIsMutable();
        condition_.add(value);
        onChanged();
      } else {
        conditionBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public Builder addCondition(
        int index, com.kikitrade.activity.facade.roster.ConditionVO value) {
      if (conditionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionIsMutable();
        condition_.add(index, value);
        onChanged();
      } else {
        conditionBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public Builder addCondition(
        com.kikitrade.activity.facade.roster.ConditionVO.Builder builderForValue) {
      if (conditionBuilder_ == null) {
        ensureConditionIsMutable();
        condition_.add(builderForValue.build());
        onChanged();
      } else {
        conditionBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public Builder addCondition(
        int index, com.kikitrade.activity.facade.roster.ConditionVO.Builder builderForValue) {
      if (conditionBuilder_ == null) {
        ensureConditionIsMutable();
        condition_.add(index, builderForValue.build());
        onChanged();
      } else {
        conditionBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public Builder addAllCondition(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.roster.ConditionVO> values) {
      if (conditionBuilder_ == null) {
        ensureConditionIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, condition_);
        onChanged();
      } else {
        conditionBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public Builder clearCondition() {
      if (conditionBuilder_ == null) {
        condition_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        conditionBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public Builder removeCondition(int index) {
      if (conditionBuilder_ == null) {
        ensureConditionIsMutable();
        condition_.remove(index);
        onChanged();
      } else {
        conditionBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public com.kikitrade.activity.facade.roster.ConditionVO.Builder getConditionBuilder(
        int index) {
      return getConditionFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public com.kikitrade.activity.facade.roster.ConditionVOOrBuilder getConditionOrBuilder(
        int index) {
      if (conditionBuilder_ == null) {
        return condition_.get(index);  } else {
        return conditionBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.roster.ConditionVOOrBuilder> 
         getConditionOrBuilderList() {
      if (conditionBuilder_ != null) {
        return conditionBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(condition_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public com.kikitrade.activity.facade.roster.ConditionVO.Builder addConditionBuilder() {
      return getConditionFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.roster.ConditionVO.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public com.kikitrade.activity.facade.roster.ConditionVO.Builder addConditionBuilder(
        int index) {
      return getConditionFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.roster.ConditionVO.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.roster.ConditionVO.Builder> 
         getConditionBuilderList() {
      return getConditionFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.roster.ConditionVO, com.kikitrade.activity.facade.roster.ConditionVO.Builder, com.kikitrade.activity.facade.roster.ConditionVOOrBuilder> 
        getConditionFieldBuilder() {
      if (conditionBuilder_ == null) {
        conditionBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.roster.ConditionVO, com.kikitrade.activity.facade.roster.ConditionVO.Builder, com.kikitrade.activity.facade.roster.ConditionVOOrBuilder>(
                condition_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        condition_ = null;
      }
      return conditionBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.roster.ConditionParameter)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.roster.ConditionParameter)
  private static final com.kikitrade.activity.facade.roster.ConditionParameter DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.roster.ConditionParameter();
  }

  public static com.kikitrade.activity.facade.roster.ConditionParameter getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ConditionParameter>
      PARSER = new com.google.protobuf.AbstractParser<ConditionParameter>() {
    @java.lang.Override
    public ConditionParameter parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ConditionParameter> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ConditionParameter> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.roster.ConditionParameter getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

