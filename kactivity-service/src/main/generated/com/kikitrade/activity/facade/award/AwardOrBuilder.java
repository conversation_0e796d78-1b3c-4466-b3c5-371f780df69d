// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface AwardOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.Award)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string customerId = 2;</code>
   * @return The customerId.
   */
  java.lang.String getCustomerId();
  /**
   * <code>string customerId = 2;</code>
   * @return The bytes for customerId.
   */
  com.google.protobuf.ByteString
      getCustomerIdBytes();

  /**
   * <code>string phone = 3;</code>
   * @return The phone.
   */
  java.lang.String getPhone();
  /**
   * <code>string phone = 3;</code>
   * @return The bytes for phone.
   */
  com.google.protobuf.ByteString
      getPhoneBytes();

  /**
   * <code>string email = 4;</code>
   * @return The email.
   */
  java.lang.String getEmail();
  /**
   * <code>string email = 4;</code>
   * @return The bytes for email.
   */
  com.google.protobuf.ByteString
      getEmailBytes();

  /**
   * <pre>
   *预计发奖时间
   * </pre>
   *
   * <code>string awardTime = 5;</code>
   * @return The awardTime.
   */
  java.lang.String getAwardTime();
  /**
   * <pre>
   *预计发奖时间
   * </pre>
   *
   * <code>string awardTime = 5;</code>
   * @return The bytes for awardTime.
   */
  com.google.protobuf.ByteString
      getAwardTimeBytes();

  /**
   * <pre>
   *发奖状态 3 ～ 8
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.BatchStatusEnum awardStatus = 6;</code>
   * @return The enum numeric value on the wire for awardStatus.
   */
  int getAwardStatusValue();
  /**
   * <pre>
   *发奖状态 3 ～ 8
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.BatchStatusEnum awardStatus = 6;</code>
   * @return The awardStatus.
   */
  com.kikitrade.activity.facade.award.BatchStatusEnum getAwardStatus();

  /**
   * <code>string message = 7;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 7;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();
}
