// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Task.proto

package com.kikitrade.activity.facade.taskv2;

public interface RewardDTOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.taskv2.RewardDTO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *连续次数，如果ProgressType等于add，该值为0
   * </pre>
   *
   * <code>int32 consecutiveTimes = 1;</code>
   * @return The consecutiveTimes.
   */
  int getConsecutiveTimes();

  /**
   * <code>.com.kikitrade.activity.facade.taskv2.RewardType type = 2;</code>
   * @return The enum numeric value on the wire for type.
   */
  int getTypeValue();
  /**
   * <code>.com.kikitrade.activity.facade.taskv2.RewardType type = 2;</code>
   * @return The type.
   */
  com.kikitrade.activity.facade.taskv2.RewardType getType();

  /**
   * <pre>
   *方法数量
   * </pre>
   *
   * <code>string amount = 3;</code>
   * @return The amount.
   */
  java.lang.String getAmount();
  /**
   * <pre>
   *方法数量
   * </pre>
   *
   * <code>string amount = 3;</code>
   * @return The bytes for amount.
   */
  com.google.protobuf.ByteString
      getAmountBytes();

  /**
   * <pre>
   *奖励币种
   * </pre>
   *
   * <code>string currency = 4;</code>
   * @return The currency.
   */
  java.lang.String getCurrency();
  /**
   * <pre>
   *奖励币种
   * </pre>
   *
   * <code>string currency = 4;</code>
   * @return The bytes for currency.
   */
  com.google.protobuf.ByteString
      getCurrencyBytes();

  /**
   * <pre>
   *奖品图片
   * </pre>
   *
   * <code>string image = 5;</code>
   * @return The image.
   */
  java.lang.String getImage();
  /**
   * <pre>
   *奖品图片
   * </pre>
   *
   * <code>string image = 5;</code>
   * @return The bytes for image.
   */
  com.google.protobuf.ByteString
      getImageBytes();

  /**
   * <code>string desc = 6;</code>
   * @return The desc.
   */
  java.lang.String getDesc();
  /**
   * <code>string desc = 6;</code>
   * @return The bytes for desc.
   */
  com.google.protobuf.ByteString
      getDescBytes();

  /**
   * <code>.com.kikitrade.activity.facade.taskv2.VipLevel vipLevel = 7;</code>
   * @return The enum numeric value on the wire for vipLevel.
   */
  int getVipLevelValue();
  /**
   * <code>.com.kikitrade.activity.facade.taskv2.VipLevel vipLevel = 7;</code>
   * @return The vipLevel.
   */
  com.kikitrade.activity.facade.taskv2.VipLevel getVipLevel();
}
