// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Task.proto

package com.kikitrade.activity.facade.taskv2;

public interface TaskDTOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.taskv2.TaskDTO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *任务id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   *任务id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   *quest name
   * </pre>
   *
   * <code>string title = 2;</code>
   * @return The title.
   */
  java.lang.String getTitle();
  /**
   * <pre>
   *quest name
   * </pre>
   *
   * <code>string title = 2;</code>
   * @return The bytes for title.
   */
  com.google.protobuf.ByteString
      getTitleBytes();

  /**
   * <pre>
   *quest description
   * </pre>
   *
   * <code>string desc = 3;</code>
   * @return The desc.
   */
  java.lang.String getDesc();
  /**
   * <pre>
   *quest description
   * </pre>
   *
   * <code>string desc = 3;</code>
   * @return The bytes for desc.
   */
  com.google.protobuf.ByteString
      getDescBytes();

  /**
   * <pre>
   *label content
   * </pre>
   *
   * <code>string labelContent = 4;</code>
   * @return The labelContent.
   */
  java.lang.String getLabelContent();
  /**
   * <pre>
   *label content
   * </pre>
   *
   * <code>string labelContent = 4;</code>
   * @return The bytes for labelContent.
   */
  com.google.protobuf.ByteString
      getLabelContentBytes();

  /**
   * <pre>
   *label color
   * </pre>
   *
   * <code>string labelColor = 5;</code>
   * @return The labelColor.
   */
  java.lang.String getLabelColor();
  /**
   * <pre>
   *label color
   * </pre>
   *
   * <code>string labelColor = 5;</code>
   * @return The bytes for labelColor.
   */
  com.google.protobuf.ByteString
      getLabelColorBytes();

  /**
   * <pre>
   *start time
   * </pre>
   *
   * <code>string startTime = 6;</code>
   * @return The startTime.
   */
  java.lang.String getStartTime();
  /**
   * <pre>
   *start time
   * </pre>
   *
   * <code>string startTime = 6;</code>
   * @return The bytes for startTime.
   */
  com.google.protobuf.ByteString
      getStartTimeBytes();

  /**
   * <pre>
   * </pre>
   *
   * <code>string endTime = 7;</code>
   * @return The endTime.
   */
  java.lang.String getEndTime();
  /**
   * <pre>
   * </pre>
   *
   * <code>string endTime = 7;</code>
   * @return The bytes for endTime.
   */
  com.google.protobuf.ByteString
      getEndTimeBytes();

  /**
   * <code>string listImage = 8;</code>
   * @return The listImage.
   */
  java.lang.String getListImage();
  /**
   * <code>string listImage = 8;</code>
   * @return The bytes for listImage.
   */
  com.google.protobuf.ByteString
      getListImageBytes();

  /**
   * <code>string detailImage = 9;</code>
   * @return The detailImage.
   */
  java.lang.String getDetailImage();
  /**
   * <code>string detailImage = 9;</code>
   * @return The bytes for detailImage.
   */
  com.google.protobuf.ByteString
      getDetailImageBytes();

  /**
   * <code>string shareImage = 10;</code>
   * @return The shareImage.
   */
  java.lang.String getShareImage();
  /**
   * <code>string shareImage = 10;</code>
   * @return The bytes for shareImage.
   */
  com.google.protobuf.ByteString
      getShareImageBytes();

  /**
   * <pre>
   *分享文案
   * </pre>
   *
   * <code>string shareContent = 11;</code>
   * @return The shareContent.
   */
  java.lang.String getShareContent();
  /**
   * <pre>
   *分享文案
   * </pre>
   *
   * <code>string shareContent = 11;</code>
   * @return The bytes for shareContent.
   */
  com.google.protobuf.ByteString
      getShareContentBytes();

  /**
   * <pre>
   *quest duration type
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.TaskCycle cycle = 12;</code>
   * @return The enum numeric value on the wire for cycle.
   */
  int getCycleValue();
  /**
   * <pre>
   *quest duration type
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.TaskCycle cycle = 12;</code>
   * @return The cycle.
   */
  com.kikitrade.activity.facade.taskv2.TaskCycle getCycle();

  /**
   * <pre>
   *周期内完成任务次数上限，默认-1，表示不限制
   * </pre>
   *
   * <code>int32 limit = 13;</code>
   * @return The limit.
   */
  int getLimit();

  /**
   * <pre>
   *vip用户周期内完成任务次数上限，默认-1，表示不限制
   * </pre>
   *
   * <code>int32 limitVip = 14;</code>
   * @return The limitVip.
   */
  int getLimitVip();

  /**
   * <pre>
   *奖励频率，每rewardFrequency发次奖励
   * </pre>
   *
   * <code>int32 rewardFrequency = 15;</code>
   * @return The rewardFrequency.
   */
  int getRewardFrequency();

  /**
   * <pre>
   *任务进度计算方式
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.ProgressType progressType = 16;</code>
   * @return The enum numeric value on the wire for progressType.
   */
  int getProgressTypeValue();
  /**
   * <pre>
   *任务进度计算方式
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.ProgressType progressType = 16;</code>
   * @return The progressType.
   */
  com.kikitrade.activity.facade.taskv2.ProgressType getProgressType();

  /**
   * <pre>
   *奖品筛选方式
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.RewardForm rewardForm = 17;</code>
   * @return The enum numeric value on the wire for rewardForm.
   */
  int getRewardFormValue();
  /**
   * <pre>
   *奖品筛选方式
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.RewardForm rewardForm = 17;</code>
   * @return The rewardForm.
   */
  com.kikitrade.activity.facade.taskv2.RewardForm getRewardForm();

  /**
   * <pre>
   *发放方式
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.ProvideType provideType = 18;</code>
   * @return The enum numeric value on the wire for provideType.
   */
  int getProvideTypeValue();
  /**
   * <pre>
   *发放方式
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.ProvideType provideType = 18;</code>
   * @return The provideType.
   */
  com.kikitrade.activity.facade.taskv2.ProvideType getProvideType();

  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
   */
  java.util.List<com.kikitrade.activity.facade.taskv2.RewardDTO> 
      getRewardDTOList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
   */
  com.kikitrade.activity.facade.taskv2.RewardDTO getRewardDTO(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
   */
  int getRewardDTOCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.taskv2.RewardDTOOrBuilder> 
      getRewardDTOOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
   */
  com.kikitrade.activity.facade.taskv2.RewardDTOOrBuilder getRewardDTOOrBuilder(
      int index);

  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
   */
  java.util.List<com.kikitrade.activity.facade.taskv2.SubTaskDTO> 
      getSubTaskDTOList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
   */
  com.kikitrade.activity.facade.taskv2.SubTaskDTO getSubTaskDTO(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
   */
  int getSubTaskDTOCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.taskv2.SubTaskDTOOrBuilder> 
      getSubTaskDTOOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
   */
  com.kikitrade.activity.facade.taskv2.SubTaskDTOOrBuilder getSubTaskDTOOrBuilder(
      int index);

  /**
   * <code>.com.kikitrade.activity.facade.taskv2.CommonStatus status = 21;</code>
   * @return The enum numeric value on the wire for status.
   */
  int getStatusValue();
  /**
   * <code>.com.kikitrade.activity.facade.taskv2.CommonStatus status = 21;</code>
   * @return The status.
   */
  com.kikitrade.activity.facade.taskv2.CommonStatus getStatus();

  /**
   * <code>string saasId = 22;</code>
   * @return The saasId.
   */
  java.lang.String getSaasId();
  /**
   * <code>string saasId = 22;</code>
   * @return The bytes for saasId.
   */
  com.google.protobuf.ByteString
      getSaasIdBytes();

  /**
   * <code>string code = 23;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <code>string code = 23;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <code>string twitterSubject = 24;</code>
   * @return The twitterSubject.
   */
  java.lang.String getTwitterSubject();
  /**
   * <code>string twitterSubject = 24;</code>
   * @return The bytes for twitterSubject.
   */
  com.google.protobuf.ByteString
      getTwitterSubjectBytes();

  /**
   * <code>string twitterTo = 25;</code>
   * @return The twitterTo.
   */
  java.lang.String getTwitterTo();
  /**
   * <code>string twitterTo = 25;</code>
   * @return The bytes for twitterTo.
   */
  com.google.protobuf.ByteString
      getTwitterToBytes();
}
