// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * <pre>
 **
 *保存/修改批次请求实体
 * </pre>
 *
 * Protobuf type {@code com.kikitrade.activity.facade.award.ActivityBatchDTO}
 */
public final class ActivityBatchDTO extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.award.ActivityBatchDTO)
    ActivityBatchDTOOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ActivityBatchDTO.newBuilder() to construct.
  private ActivityBatchDTO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ActivityBatchDTO() {
    id_ = "";
    batchName_ = "";
    activityId_ = "";
    activityName_ = "";
    rewardType_ = "";
    amount_ = "";
    currency_ = "";
    remark_ = "";
    scheduledTime_ = "";
    amended_ = "";
    sourceOssUrl_ = "";
    rewardRule_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ActivityBatchDTO();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ActivityBatchDTO_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ActivityBatchDTO_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.award.ActivityBatchDTO.class, com.kikitrade.activity.facade.award.ActivityBatchDTO.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <pre>
   *batchId 新增空
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *batchId 新增空
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BATCHNAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object batchName_ = "";
  /**
   * <code>string batchName = 2;</code>
   * @return The batchName.
   */
  @java.lang.Override
  public java.lang.String getBatchName() {
    java.lang.Object ref = batchName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      batchName_ = s;
      return s;
    }
  }
  /**
   * <code>string batchName = 2;</code>
   * @return The bytes for batchName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBatchNameBytes() {
    java.lang.Object ref = batchName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      batchName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACTIVITYID_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object activityId_ = "";
  /**
   * <code>string activityId = 3;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public java.lang.String getActivityId() {
    java.lang.Object ref = activityId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      activityId_ = s;
      return s;
    }
  }
  /**
   * <code>string activityId = 3;</code>
   * @return The bytes for activityId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getActivityIdBytes() {
    java.lang.Object ref = activityId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      activityId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACTIVITYNAME_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object activityName_ = "";
  /**
   * <code>string activityName = 4;</code>
   * @return The activityName.
   */
  @java.lang.Override
  public java.lang.String getActivityName() {
    java.lang.Object ref = activityName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      activityName_ = s;
      return s;
    }
  }
  /**
   * <code>string activityName = 4;</code>
   * @return The bytes for activityName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getActivityNameBytes() {
    java.lang.Object ref = activityName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      activityName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REWARDTYPE_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object rewardType_ = "";
  /**
   * <pre>
   *数字货币、道具，code
   * </pre>
   *
   * <code>string rewardType = 5;</code>
   * @return The rewardType.
   */
  @java.lang.Override
  public java.lang.String getRewardType() {
    java.lang.Object ref = rewardType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      rewardType_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *数字货币、道具，code
   * </pre>
   *
   * <code>string rewardType = 5;</code>
   * @return The bytes for rewardType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardTypeBytes() {
    java.lang.Object ref = rewardType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      rewardType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AMOUNT_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object amount_ = "";
  /**
   * <pre>
   *货币金额
   * </pre>
   *
   * <code>string amount = 6;</code>
   * @return The amount.
   */
  @java.lang.Override
  public java.lang.String getAmount() {
    java.lang.Object ref = amount_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      amount_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *货币金额
   * </pre>
   *
   * <code>string amount = 6;</code>
   * @return The bytes for amount.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAmountBytes() {
    java.lang.Object ref = amount_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      amount_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CURRENCY_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object currency_ = "";
  /**
   * <pre>
   *货币单位
   * </pre>
   *
   * <code>string currency = 7;</code>
   * @return The currency.
   */
  @java.lang.Override
  public java.lang.String getCurrency() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      currency_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *货币单位
   * </pre>
   *
   * <code>string currency = 7;</code>
   * @return The bytes for currency.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCurrencyBytes() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      currency_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REMARK_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object remark_ = "";
  /**
   * <pre>
   *批次描述
   * </pre>
   *
   * <code>string remark = 8;</code>
   * @return The remark.
   */
  @java.lang.Override
  public java.lang.String getRemark() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      remark_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *批次描述
   * </pre>
   *
   * <code>string remark = 8;</code>
   * @return The bytes for remark.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRemarkBytes() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      remark_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SCHEDULED_FIELD_NUMBER = 9;
  private boolean scheduled_ = false;
  /**
   * <pre>
   *是否立即发奖
   * </pre>
   *
   * <code>bool scheduled = 9;</code>
   * @return The scheduled.
   */
  @java.lang.Override
  public boolean getScheduled() {
    return scheduled_;
  }

  public static final int SCHEDULEDTIME_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object scheduledTime_ = "";
  /**
   * <pre>
   *发奖时间
   * </pre>
   *
   * <code>string scheduledTime = 10;</code>
   * @return The scheduledTime.
   */
  @java.lang.Override
  public java.lang.String getScheduledTime() {
    java.lang.Object ref = scheduledTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      scheduledTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *发奖时间
   * </pre>
   *
   * <code>string scheduledTime = 10;</code>
   * @return The bytes for scheduledTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getScheduledTimeBytes() {
    java.lang.Object ref = scheduledTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      scheduledTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AMENDED_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private volatile java.lang.Object amended_ = "";
  /**
   * <pre>
   *最后修改人
   * </pre>
   *
   * <code>string amended = 11;</code>
   * @return The amended.
   */
  @java.lang.Override
  public java.lang.String getAmended() {
    java.lang.Object ref = amended_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      amended_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *最后修改人
   * </pre>
   *
   * <code>string amended = 11;</code>
   * @return The bytes for amended.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAmendedBytes() {
    java.lang.Object ref = amended_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      amended_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SOURCEOSSURL_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private volatile java.lang.Object sourceOssUrl_ = "";
  /**
   * <pre>
   *上传的csv文件
   * </pre>
   *
   * <code>string sourceOssUrl = 12;</code>
   * @return The sourceOssUrl.
   */
  @java.lang.Override
  public java.lang.String getSourceOssUrl() {
    java.lang.Object ref = sourceOssUrl_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      sourceOssUrl_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *上传的csv文件
   * </pre>
   *
   * <code>string sourceOssUrl = 12;</code>
   * @return The bytes for sourceOssUrl.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSourceOssUrlBytes() {
    java.lang.Object ref = sourceOssUrl_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      sourceOssUrl_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REWARDRULE_FIELD_NUMBER = 13;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.award.RewardRule> rewardRule_;
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.award.RewardRule> getRewardRuleList() {
    return rewardRule_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.award.RewardRuleOrBuilder> 
      getRewardRuleOrBuilderList() {
    return rewardRule_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
   */
  @java.lang.Override
  public int getRewardRuleCount() {
    return rewardRule_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.RewardRule getRewardRule(int index) {
    return rewardRule_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.RewardRuleOrBuilder getRewardRuleOrBuilder(
      int index) {
    return rewardRule_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(batchName_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, batchName_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, activityId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityName_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, activityName_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(rewardType_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, rewardType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amount_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, amount_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(currency_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, currency_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(remark_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, remark_);
    }
    if (scheduled_ != false) {
      output.writeBool(9, scheduled_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(scheduledTime_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, scheduledTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amended_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, amended_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sourceOssUrl_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 12, sourceOssUrl_);
    }
    for (int i = 0; i < rewardRule_.size(); i++) {
      output.writeMessage(13, rewardRule_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(batchName_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, batchName_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, activityId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityName_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, activityName_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(rewardType_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, rewardType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amount_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, amount_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(currency_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, currency_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(remark_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, remark_);
    }
    if (scheduled_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(9, scheduled_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(scheduledTime_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, scheduledTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amended_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, amended_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sourceOssUrl_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, sourceOssUrl_);
    }
    for (int i = 0; i < rewardRule_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(13, rewardRule_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.award.ActivityBatchDTO)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.award.ActivityBatchDTO other = (com.kikitrade.activity.facade.award.ActivityBatchDTO) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getBatchName()
        .equals(other.getBatchName())) return false;
    if (!getActivityId()
        .equals(other.getActivityId())) return false;
    if (!getActivityName()
        .equals(other.getActivityName())) return false;
    if (!getRewardType()
        .equals(other.getRewardType())) return false;
    if (!getAmount()
        .equals(other.getAmount())) return false;
    if (!getCurrency()
        .equals(other.getCurrency())) return false;
    if (!getRemark()
        .equals(other.getRemark())) return false;
    if (getScheduled()
        != other.getScheduled()) return false;
    if (!getScheduledTime()
        .equals(other.getScheduledTime())) return false;
    if (!getAmended()
        .equals(other.getAmended())) return false;
    if (!getSourceOssUrl()
        .equals(other.getSourceOssUrl())) return false;
    if (!getRewardRuleList()
        .equals(other.getRewardRuleList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + BATCHNAME_FIELD_NUMBER;
    hash = (53 * hash) + getBatchName().hashCode();
    hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
    hash = (53 * hash) + getActivityId().hashCode();
    hash = (37 * hash) + ACTIVITYNAME_FIELD_NUMBER;
    hash = (53 * hash) + getActivityName().hashCode();
    hash = (37 * hash) + REWARDTYPE_FIELD_NUMBER;
    hash = (53 * hash) + getRewardType().hashCode();
    hash = (37 * hash) + AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getAmount().hashCode();
    hash = (37 * hash) + CURRENCY_FIELD_NUMBER;
    hash = (53 * hash) + getCurrency().hashCode();
    hash = (37 * hash) + REMARK_FIELD_NUMBER;
    hash = (53 * hash) + getRemark().hashCode();
    hash = (37 * hash) + SCHEDULED_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getScheduled());
    hash = (37 * hash) + SCHEDULEDTIME_FIELD_NUMBER;
    hash = (53 * hash) + getScheduledTime().hashCode();
    hash = (37 * hash) + AMENDED_FIELD_NUMBER;
    hash = (53 * hash) + getAmended().hashCode();
    hash = (37 * hash) + SOURCEOSSURL_FIELD_NUMBER;
    hash = (53 * hash) + getSourceOssUrl().hashCode();
    if (getRewardRuleCount() > 0) {
      hash = (37 * hash) + REWARDRULE_FIELD_NUMBER;
      hash = (53 * hash) + getRewardRuleList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.award.ActivityBatchDTO parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatchDTO parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatchDTO parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatchDTO parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatchDTO parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatchDTO parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatchDTO parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatchDTO parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.award.ActivityBatchDTO parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.award.ActivityBatchDTO parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatchDTO parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatchDTO parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.award.ActivityBatchDTO prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   **
   *保存/修改批次请求实体
   * </pre>
   *
   * Protobuf type {@code com.kikitrade.activity.facade.award.ActivityBatchDTO}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.award.ActivityBatchDTO)
      com.kikitrade.activity.facade.award.ActivityBatchDTOOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ActivityBatchDTO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ActivityBatchDTO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.award.ActivityBatchDTO.class, com.kikitrade.activity.facade.award.ActivityBatchDTO.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.award.ActivityBatchDTO.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      batchName_ = "";
      activityId_ = "";
      activityName_ = "";
      rewardType_ = "";
      amount_ = "";
      currency_ = "";
      remark_ = "";
      scheduled_ = false;
      scheduledTime_ = "";
      amended_ = "";
      sourceOssUrl_ = "";
      if (rewardRuleBuilder_ == null) {
        rewardRule_ = java.util.Collections.emptyList();
      } else {
        rewardRule_ = null;
        rewardRuleBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00001000);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ActivityBatchDTO_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ActivityBatchDTO getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.award.ActivityBatchDTO.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ActivityBatchDTO build() {
      com.kikitrade.activity.facade.award.ActivityBatchDTO result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ActivityBatchDTO buildPartial() {
      com.kikitrade.activity.facade.award.ActivityBatchDTO result = new com.kikitrade.activity.facade.award.ActivityBatchDTO(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.award.ActivityBatchDTO result) {
      if (rewardRuleBuilder_ == null) {
        if (((bitField0_ & 0x00001000) != 0)) {
          rewardRule_ = java.util.Collections.unmodifiableList(rewardRule_);
          bitField0_ = (bitField0_ & ~0x00001000);
        }
        result.rewardRule_ = rewardRule_;
      } else {
        result.rewardRule_ = rewardRuleBuilder_.build();
      }
    }

    private void buildPartial0(com.kikitrade.activity.facade.award.ActivityBatchDTO result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.batchName_ = batchName_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.activityId_ = activityId_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.activityName_ = activityName_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.rewardType_ = rewardType_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.amount_ = amount_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.currency_ = currency_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.remark_ = remark_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.scheduled_ = scheduled_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.scheduledTime_ = scheduledTime_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.amended_ = amended_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.sourceOssUrl_ = sourceOssUrl_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.award.ActivityBatchDTO) {
        return mergeFrom((com.kikitrade.activity.facade.award.ActivityBatchDTO)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.award.ActivityBatchDTO other) {
      if (other == com.kikitrade.activity.facade.award.ActivityBatchDTO.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getBatchName().isEmpty()) {
        batchName_ = other.batchName_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getActivityId().isEmpty()) {
        activityId_ = other.activityId_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getActivityName().isEmpty()) {
        activityName_ = other.activityName_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (!other.getRewardType().isEmpty()) {
        rewardType_ = other.rewardType_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (!other.getAmount().isEmpty()) {
        amount_ = other.amount_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (!other.getCurrency().isEmpty()) {
        currency_ = other.currency_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (!other.getRemark().isEmpty()) {
        remark_ = other.remark_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (other.getScheduled() != false) {
        setScheduled(other.getScheduled());
      }
      if (!other.getScheduledTime().isEmpty()) {
        scheduledTime_ = other.scheduledTime_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (!other.getAmended().isEmpty()) {
        amended_ = other.amended_;
        bitField0_ |= 0x00000400;
        onChanged();
      }
      if (!other.getSourceOssUrl().isEmpty()) {
        sourceOssUrl_ = other.sourceOssUrl_;
        bitField0_ |= 0x00000800;
        onChanged();
      }
      if (rewardRuleBuilder_ == null) {
        if (!other.rewardRule_.isEmpty()) {
          if (rewardRule_.isEmpty()) {
            rewardRule_ = other.rewardRule_;
            bitField0_ = (bitField0_ & ~0x00001000);
          } else {
            ensureRewardRuleIsMutable();
            rewardRule_.addAll(other.rewardRule_);
          }
          onChanged();
        }
      } else {
        if (!other.rewardRule_.isEmpty()) {
          if (rewardRuleBuilder_.isEmpty()) {
            rewardRuleBuilder_.dispose();
            rewardRuleBuilder_ = null;
            rewardRule_ = other.rewardRule_;
            bitField0_ = (bitField0_ & ~0x00001000);
            rewardRuleBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getRewardRuleFieldBuilder() : null;
          } else {
            rewardRuleBuilder_.addAllMessages(other.rewardRule_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              batchName_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              activityId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              activityName_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              rewardType_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              amount_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              currency_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              remark_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 72: {
              scheduled_ = input.readBool();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 82: {
              scheduledTime_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 90: {
              amended_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 98: {
              sourceOssUrl_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000800;
              break;
            } // case 98
            case 106: {
              com.kikitrade.activity.facade.award.RewardRule m =
                  input.readMessage(
                      com.kikitrade.activity.facade.award.RewardRule.parser(),
                      extensionRegistry);
              if (rewardRuleBuilder_ == null) {
                ensureRewardRuleIsMutable();
                rewardRule_.add(m);
              } else {
                rewardRuleBuilder_.addMessage(m);
              }
              break;
            } // case 106
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <pre>
     *batchId 新增空
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *batchId 新增空
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *batchId 新增空
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *batchId 新增空
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *batchId 新增空
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object batchName_ = "";
    /**
     * <code>string batchName = 2;</code>
     * @return The batchName.
     */
    public java.lang.String getBatchName() {
      java.lang.Object ref = batchName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        batchName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string batchName = 2;</code>
     * @return The bytes for batchName.
     */
    public com.google.protobuf.ByteString
        getBatchNameBytes() {
      java.lang.Object ref = batchName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        batchName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string batchName = 2;</code>
     * @param value The batchName to set.
     * @return This builder for chaining.
     */
    public Builder setBatchName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      batchName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string batchName = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearBatchName() {
      batchName_ = getDefaultInstance().getBatchName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string batchName = 2;</code>
     * @param value The bytes for batchName to set.
     * @return This builder for chaining.
     */
    public Builder setBatchNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      batchName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object activityId_ = "";
    /**
     * <code>string activityId = 3;</code>
     * @return The activityId.
     */
    public java.lang.String getActivityId() {
      java.lang.Object ref = activityId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        activityId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string activityId = 3;</code>
     * @return The bytes for activityId.
     */
    public com.google.protobuf.ByteString
        getActivityIdBytes() {
      java.lang.Object ref = activityId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        activityId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string activityId = 3;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      activityId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>string activityId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      activityId_ = getDefaultInstance().getActivityId();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>string activityId = 3;</code>
     * @param value The bytes for activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      activityId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object activityName_ = "";
    /**
     * <code>string activityName = 4;</code>
     * @return The activityName.
     */
    public java.lang.String getActivityName() {
      java.lang.Object ref = activityName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        activityName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string activityName = 4;</code>
     * @return The bytes for activityName.
     */
    public com.google.protobuf.ByteString
        getActivityNameBytes() {
      java.lang.Object ref = activityName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        activityName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string activityName = 4;</code>
     * @param value The activityName to set.
     * @return This builder for chaining.
     */
    public Builder setActivityName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      activityName_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>string activityName = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityName() {
      activityName_ = getDefaultInstance().getActivityName();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>string activityName = 4;</code>
     * @param value The bytes for activityName to set.
     * @return This builder for chaining.
     */
    public Builder setActivityNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      activityName_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object rewardType_ = "";
    /**
     * <pre>
     *数字货币、道具，code
     * </pre>
     *
     * <code>string rewardType = 5;</code>
     * @return The rewardType.
     */
    public java.lang.String getRewardType() {
      java.lang.Object ref = rewardType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        rewardType_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *数字货币、道具，code
     * </pre>
     *
     * <code>string rewardType = 5;</code>
     * @return The bytes for rewardType.
     */
    public com.google.protobuf.ByteString
        getRewardTypeBytes() {
      java.lang.Object ref = rewardType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rewardType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *数字货币、道具，code
     * </pre>
     *
     * <code>string rewardType = 5;</code>
     * @param value The rewardType to set.
     * @return This builder for chaining.
     */
    public Builder setRewardType(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      rewardType_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *数字货币、道具，code
     * </pre>
     *
     * <code>string rewardType = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewardType() {
      rewardType_ = getDefaultInstance().getRewardType();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *数字货币、道具，code
     * </pre>
     *
     * <code>string rewardType = 5;</code>
     * @param value The bytes for rewardType to set.
     * @return This builder for chaining.
     */
    public Builder setRewardTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      rewardType_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private java.lang.Object amount_ = "";
    /**
     * <pre>
     *货币金额
     * </pre>
     *
     * <code>string amount = 6;</code>
     * @return The amount.
     */
    public java.lang.String getAmount() {
      java.lang.Object ref = amount_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        amount_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *货币金额
     * </pre>
     *
     * <code>string amount = 6;</code>
     * @return The bytes for amount.
     */
    public com.google.protobuf.ByteString
        getAmountBytes() {
      java.lang.Object ref = amount_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        amount_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *货币金额
     * </pre>
     *
     * <code>string amount = 6;</code>
     * @param value The amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmount(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      amount_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *货币金额
     * </pre>
     *
     * <code>string amount = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmount() {
      amount_ = getDefaultInstance().getAmount();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *货币金额
     * </pre>
     *
     * <code>string amount = 6;</code>
     * @param value The bytes for amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmountBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      amount_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object currency_ = "";
    /**
     * <pre>
     *货币单位
     * </pre>
     *
     * <code>string currency = 7;</code>
     * @return The currency.
     */
    public java.lang.String getCurrency() {
      java.lang.Object ref = currency_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        currency_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *货币单位
     * </pre>
     *
     * <code>string currency = 7;</code>
     * @return The bytes for currency.
     */
    public com.google.protobuf.ByteString
        getCurrencyBytes() {
      java.lang.Object ref = currency_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        currency_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *货币单位
     * </pre>
     *
     * <code>string currency = 7;</code>
     * @param value The currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrency(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      currency_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *货币单位
     * </pre>
     *
     * <code>string currency = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurrency() {
      currency_ = getDefaultInstance().getCurrency();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *货币单位
     * </pre>
     *
     * <code>string currency = 7;</code>
     * @param value The bytes for currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrencyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      currency_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object remark_ = "";
    /**
     * <pre>
     *批次描述
     * </pre>
     *
     * <code>string remark = 8;</code>
     * @return The remark.
     */
    public java.lang.String getRemark() {
      java.lang.Object ref = remark_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        remark_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *批次描述
     * </pre>
     *
     * <code>string remark = 8;</code>
     * @return The bytes for remark.
     */
    public com.google.protobuf.ByteString
        getRemarkBytes() {
      java.lang.Object ref = remark_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        remark_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *批次描述
     * </pre>
     *
     * <code>string remark = 8;</code>
     * @param value The remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemark(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      remark_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *批次描述
     * </pre>
     *
     * <code>string remark = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearRemark() {
      remark_ = getDefaultInstance().getRemark();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *批次描述
     * </pre>
     *
     * <code>string remark = 8;</code>
     * @param value The bytes for remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemarkBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      remark_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private boolean scheduled_ ;
    /**
     * <pre>
     *是否立即发奖
     * </pre>
     *
     * <code>bool scheduled = 9;</code>
     * @return The scheduled.
     */
    @java.lang.Override
    public boolean getScheduled() {
      return scheduled_;
    }
    /**
     * <pre>
     *是否立即发奖
     * </pre>
     *
     * <code>bool scheduled = 9;</code>
     * @param value The scheduled to set.
     * @return This builder for chaining.
     */
    public Builder setScheduled(boolean value) {

      scheduled_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *是否立即发奖
     * </pre>
     *
     * <code>bool scheduled = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearScheduled() {
      bitField0_ = (bitField0_ & ~0x00000100);
      scheduled_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object scheduledTime_ = "";
    /**
     * <pre>
     *发奖时间
     * </pre>
     *
     * <code>string scheduledTime = 10;</code>
     * @return The scheduledTime.
     */
    public java.lang.String getScheduledTime() {
      java.lang.Object ref = scheduledTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        scheduledTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *发奖时间
     * </pre>
     *
     * <code>string scheduledTime = 10;</code>
     * @return The bytes for scheduledTime.
     */
    public com.google.protobuf.ByteString
        getScheduledTimeBytes() {
      java.lang.Object ref = scheduledTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        scheduledTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *发奖时间
     * </pre>
     *
     * <code>string scheduledTime = 10;</code>
     * @param value The scheduledTime to set.
     * @return This builder for chaining.
     */
    public Builder setScheduledTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      scheduledTime_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *发奖时间
     * </pre>
     *
     * <code>string scheduledTime = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearScheduledTime() {
      scheduledTime_ = getDefaultInstance().getScheduledTime();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *发奖时间
     * </pre>
     *
     * <code>string scheduledTime = 10;</code>
     * @param value The bytes for scheduledTime to set.
     * @return This builder for chaining.
     */
    public Builder setScheduledTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      scheduledTime_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private java.lang.Object amended_ = "";
    /**
     * <pre>
     *最后修改人
     * </pre>
     *
     * <code>string amended = 11;</code>
     * @return The amended.
     */
    public java.lang.String getAmended() {
      java.lang.Object ref = amended_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        amended_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *最后修改人
     * </pre>
     *
     * <code>string amended = 11;</code>
     * @return The bytes for amended.
     */
    public com.google.protobuf.ByteString
        getAmendedBytes() {
      java.lang.Object ref = amended_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        amended_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *最后修改人
     * </pre>
     *
     * <code>string amended = 11;</code>
     * @param value The amended to set.
     * @return This builder for chaining.
     */
    public Builder setAmended(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      amended_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *最后修改人
     * </pre>
     *
     * <code>string amended = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmended() {
      amended_ = getDefaultInstance().getAmended();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *最后修改人
     * </pre>
     *
     * <code>string amended = 11;</code>
     * @param value The bytes for amended to set.
     * @return This builder for chaining.
     */
    public Builder setAmendedBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      amended_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }

    private java.lang.Object sourceOssUrl_ = "";
    /**
     * <pre>
     *上传的csv文件
     * </pre>
     *
     * <code>string sourceOssUrl = 12;</code>
     * @return The sourceOssUrl.
     */
    public java.lang.String getSourceOssUrl() {
      java.lang.Object ref = sourceOssUrl_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sourceOssUrl_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *上传的csv文件
     * </pre>
     *
     * <code>string sourceOssUrl = 12;</code>
     * @return The bytes for sourceOssUrl.
     */
    public com.google.protobuf.ByteString
        getSourceOssUrlBytes() {
      java.lang.Object ref = sourceOssUrl_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sourceOssUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *上传的csv文件
     * </pre>
     *
     * <code>string sourceOssUrl = 12;</code>
     * @param value The sourceOssUrl to set.
     * @return This builder for chaining.
     */
    public Builder setSourceOssUrl(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      sourceOssUrl_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *上传的csv文件
     * </pre>
     *
     * <code>string sourceOssUrl = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearSourceOssUrl() {
      sourceOssUrl_ = getDefaultInstance().getSourceOssUrl();
      bitField0_ = (bitField0_ & ~0x00000800);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *上传的csv文件
     * </pre>
     *
     * <code>string sourceOssUrl = 12;</code>
     * @param value The bytes for sourceOssUrl to set.
     * @return This builder for chaining.
     */
    public Builder setSourceOssUrlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      sourceOssUrl_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }

    private java.util.List<com.kikitrade.activity.facade.award.RewardRule> rewardRule_ =
      java.util.Collections.emptyList();
    private void ensureRewardRuleIsMutable() {
      if (!((bitField0_ & 0x00001000) != 0)) {
        rewardRule_ = new java.util.ArrayList<com.kikitrade.activity.facade.award.RewardRule>(rewardRule_);
        bitField0_ |= 0x00001000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.RewardRule, com.kikitrade.activity.facade.award.RewardRule.Builder, com.kikitrade.activity.facade.award.RewardRuleOrBuilder> rewardRuleBuilder_;

    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.RewardRule> getRewardRuleList() {
      if (rewardRuleBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rewardRule_);
      } else {
        return rewardRuleBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public int getRewardRuleCount() {
      if (rewardRuleBuilder_ == null) {
        return rewardRule_.size();
      } else {
        return rewardRuleBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public com.kikitrade.activity.facade.award.RewardRule getRewardRule(int index) {
      if (rewardRuleBuilder_ == null) {
        return rewardRule_.get(index);
      } else {
        return rewardRuleBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public Builder setRewardRule(
        int index, com.kikitrade.activity.facade.award.RewardRule value) {
      if (rewardRuleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardRuleIsMutable();
        rewardRule_.set(index, value);
        onChanged();
      } else {
        rewardRuleBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public Builder setRewardRule(
        int index, com.kikitrade.activity.facade.award.RewardRule.Builder builderForValue) {
      if (rewardRuleBuilder_ == null) {
        ensureRewardRuleIsMutable();
        rewardRule_.set(index, builderForValue.build());
        onChanged();
      } else {
        rewardRuleBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public Builder addRewardRule(com.kikitrade.activity.facade.award.RewardRule value) {
      if (rewardRuleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardRuleIsMutable();
        rewardRule_.add(value);
        onChanged();
      } else {
        rewardRuleBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public Builder addRewardRule(
        int index, com.kikitrade.activity.facade.award.RewardRule value) {
      if (rewardRuleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardRuleIsMutable();
        rewardRule_.add(index, value);
        onChanged();
      } else {
        rewardRuleBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public Builder addRewardRule(
        com.kikitrade.activity.facade.award.RewardRule.Builder builderForValue) {
      if (rewardRuleBuilder_ == null) {
        ensureRewardRuleIsMutable();
        rewardRule_.add(builderForValue.build());
        onChanged();
      } else {
        rewardRuleBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public Builder addRewardRule(
        int index, com.kikitrade.activity.facade.award.RewardRule.Builder builderForValue) {
      if (rewardRuleBuilder_ == null) {
        ensureRewardRuleIsMutable();
        rewardRule_.add(index, builderForValue.build());
        onChanged();
      } else {
        rewardRuleBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public Builder addAllRewardRule(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.award.RewardRule> values) {
      if (rewardRuleBuilder_ == null) {
        ensureRewardRuleIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rewardRule_);
        onChanged();
      } else {
        rewardRuleBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public Builder clearRewardRule() {
      if (rewardRuleBuilder_ == null) {
        rewardRule_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00001000);
        onChanged();
      } else {
        rewardRuleBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public Builder removeRewardRule(int index) {
      if (rewardRuleBuilder_ == null) {
        ensureRewardRuleIsMutable();
        rewardRule_.remove(index);
        onChanged();
      } else {
        rewardRuleBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public com.kikitrade.activity.facade.award.RewardRule.Builder getRewardRuleBuilder(
        int index) {
      return getRewardRuleFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public com.kikitrade.activity.facade.award.RewardRuleOrBuilder getRewardRuleOrBuilder(
        int index) {
      if (rewardRuleBuilder_ == null) {
        return rewardRule_.get(index);  } else {
        return rewardRuleBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.award.RewardRuleOrBuilder> 
         getRewardRuleOrBuilderList() {
      if (rewardRuleBuilder_ != null) {
        return rewardRuleBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rewardRule_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public com.kikitrade.activity.facade.award.RewardRule.Builder addRewardRuleBuilder() {
      return getRewardRuleFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.award.RewardRule.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public com.kikitrade.activity.facade.award.RewardRule.Builder addRewardRuleBuilder(
        int index) {
      return getRewardRuleFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.award.RewardRule.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.RewardRule.Builder> 
         getRewardRuleBuilderList() {
      return getRewardRuleFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.RewardRule, com.kikitrade.activity.facade.award.RewardRule.Builder, com.kikitrade.activity.facade.award.RewardRuleOrBuilder> 
        getRewardRuleFieldBuilder() {
      if (rewardRuleBuilder_ == null) {
        rewardRuleBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.award.RewardRule, com.kikitrade.activity.facade.award.RewardRule.Builder, com.kikitrade.activity.facade.award.RewardRuleOrBuilder>(
                rewardRule_,
                ((bitField0_ & 0x00001000) != 0),
                getParentForChildren(),
                isClean());
        rewardRule_ = null;
      }
      return rewardRuleBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.award.ActivityBatchDTO)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.award.ActivityBatchDTO)
  private static final com.kikitrade.activity.facade.award.ActivityBatchDTO DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.award.ActivityBatchDTO();
  }

  public static com.kikitrade.activity.facade.award.ActivityBatchDTO getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ActivityBatchDTO>
      PARSER = new com.google.protobuf.AbstractParser<ActivityBatchDTO>() {
    @java.lang.Override
    public ActivityBatchDTO parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ActivityBatchDTO> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ActivityBatchDTO> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.award.ActivityBatchDTO getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

