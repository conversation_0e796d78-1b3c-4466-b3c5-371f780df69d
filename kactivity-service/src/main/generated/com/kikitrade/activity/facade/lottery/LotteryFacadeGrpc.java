package com.kikitrade.activity.facade.lottery;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.58.0)",
    comments = "Source: Lottery.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class LotteryFacadeGrpc {

  private LotteryFacadeGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.kikitrade.activity.facade.lottery.LotteryFacade";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.lottery.LotteryDTO,
      com.kikitrade.activity.facade.lottery.LotterySaveResponse> getSaveMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "save",
      requestType = com.kikitrade.activity.facade.lottery.LotteryDTO.class,
      responseType = com.kikitrade.activity.facade.lottery.LotterySaveResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.lottery.LotteryDTO,
      com.kikitrade.activity.facade.lottery.LotterySaveResponse> getSaveMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.lottery.LotteryDTO, com.kikitrade.activity.facade.lottery.LotterySaveResponse> getSaveMethod;
    if ((getSaveMethod = LotteryFacadeGrpc.getSaveMethod) == null) {
      synchronized (LotteryFacadeGrpc.class) {
        if ((getSaveMethod = LotteryFacadeGrpc.getSaveMethod) == null) {
          LotteryFacadeGrpc.getSaveMethod = getSaveMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.lottery.LotteryDTO, com.kikitrade.activity.facade.lottery.LotterySaveResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "save"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.lottery.LotteryDTO.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.lottery.LotterySaveResponse.getDefaultInstance()))
              .setSchemaDescriptor(new LotteryFacadeMethodDescriptorSupplier("save"))
              .build();
        }
      }
    }
    return getSaveMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static LotteryFacadeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<LotteryFacadeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<LotteryFacadeStub>() {
        @java.lang.Override
        public LotteryFacadeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new LotteryFacadeStub(channel, callOptions);
        }
      };
    return LotteryFacadeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static LotteryFacadeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<LotteryFacadeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<LotteryFacadeBlockingStub>() {
        @java.lang.Override
        public LotteryFacadeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new LotteryFacadeBlockingStub(channel, callOptions);
        }
      };
    return LotteryFacadeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static LotteryFacadeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<LotteryFacadeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<LotteryFacadeFutureStub>() {
        @java.lang.Override
        public LotteryFacadeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new LotteryFacadeFutureStub(channel, callOptions);
        }
      };
    return LotteryFacadeFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     */
    default void save(com.kikitrade.activity.facade.lottery.LotteryDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.lottery.LotterySaveResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSaveMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service LotteryFacade.
   */
  public static abstract class LotteryFacadeImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return LotteryFacadeGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service LotteryFacade.
   */
  public static final class LotteryFacadeStub
      extends io.grpc.stub.AbstractAsyncStub<LotteryFacadeStub> {
    private LotteryFacadeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected LotteryFacadeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new LotteryFacadeStub(channel, callOptions);
    }

    /**
     */
    public void save(com.kikitrade.activity.facade.lottery.LotteryDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.lottery.LotterySaveResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSaveMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service LotteryFacade.
   */
  public static final class LotteryFacadeBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<LotteryFacadeBlockingStub> {
    private LotteryFacadeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected LotteryFacadeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new LotteryFacadeBlockingStub(channel, callOptions);
    }

    /**
     */
    public com.kikitrade.activity.facade.lottery.LotterySaveResponse save(com.kikitrade.activity.facade.lottery.LotteryDTO request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSaveMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service LotteryFacade.
   */
  public static final class LotteryFacadeFutureStub
      extends io.grpc.stub.AbstractFutureStub<LotteryFacadeFutureStub> {
    private LotteryFacadeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected LotteryFacadeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new LotteryFacadeFutureStub(channel, callOptions);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.lottery.LotterySaveResponse> save(
        com.kikitrade.activity.facade.lottery.LotteryDTO request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSaveMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_SAVE = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_SAVE:
          serviceImpl.save((com.kikitrade.activity.facade.lottery.LotteryDTO) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.lottery.LotterySaveResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getSaveMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.lottery.LotteryDTO,
              com.kikitrade.activity.facade.lottery.LotterySaveResponse>(
                service, METHODID_SAVE)))
        .build();
  }

  private static abstract class LotteryFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    LotteryFacadeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.kikitrade.activity.facade.lottery.LotteryFacadeOutClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("LotteryFacade");
    }
  }

  private static final class LotteryFacadeFileDescriptorSupplier
      extends LotteryFacadeBaseDescriptorSupplier {
    LotteryFacadeFileDescriptorSupplier() {}
  }

  private static final class LotteryFacadeMethodDescriptorSupplier
      extends LotteryFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    LotteryFacadeMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (LotteryFacadeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new LotteryFacadeFileDescriptorSupplier())
              .addMethod(getSaveMethod())
              .build();
        }
      }
    }
    return result;
  }
}
