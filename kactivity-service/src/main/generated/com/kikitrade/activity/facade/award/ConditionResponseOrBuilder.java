// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface ConditionResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.ConditionResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
   */
  java.util.List<com.kikitrade.activity.facade.award.ConditionVO> 
      getConditionList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
   */
  com.kikitrade.activity.facade.award.ConditionVO getCondition(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
   */
  int getConditionCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.award.ConditionVOOrBuilder> 
      getConditionOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
   */
  com.kikitrade.activity.facade.award.ConditionVOOrBuilder getConditionOrBuilder(
      int index);
}
