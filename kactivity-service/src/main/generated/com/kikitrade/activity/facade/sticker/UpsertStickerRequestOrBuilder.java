// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StickerFacade.proto

package com.kikitrade.activity.facade.sticker;

public interface UpsertStickerRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.sticker.UpsertStickerRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   * 所属艺人id
   * </pre>
   *
   * <code>string celebId = 2;</code>
   * @return The celebId.
   */
  java.lang.String getCelebId();
  /**
   * <pre>
   * 所属艺人id
   * </pre>
   *
   * <code>string celebId = 2;</code>
   * @return The bytes for celebId.
   */
  com.google.protobuf.ByteString
      getCelebIdBytes();

  /**
   * <pre>
   * 名称
   * </pre>
   *
   * <code>string name = 3;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   * 名称
   * </pre>
   *
   * <code>string name = 3;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * 图标
   * </pre>
   *
   * <code>string icon = 4;</code>
   * @return The icon.
   */
  java.lang.String getIcon();
  /**
   * <pre>
   * 图标
   * </pre>
   *
   * <code>string icon = 4;</code>
   * @return The bytes for icon.
   */
  com.google.protobuf.ByteString
      getIconBytes();

  /**
   * <pre>
   * 描述
   * </pre>
   *
   * <code>string description = 5;</code>
   * @return The description.
   */
  java.lang.String getDescription();
  /**
   * <pre>
   * 描述
   * </pre>
   *
   * <code>string description = 5;</code>
   * @return The bytes for description.
   */
  com.google.protobuf.ByteString
      getDescriptionBytes();

  /**
   * <pre>
   * 具体的表情包
   * </pre>
   *
   * <code>repeated string image = 6;</code>
   * @return A list containing the image.
   */
  java.util.List<java.lang.String>
      getImageList();
  /**
   * <pre>
   * 具体的表情包
   * </pre>
   *
   * <code>repeated string image = 6;</code>
   * @return The count of image.
   */
  int getImageCount();
  /**
   * <pre>
   * 具体的表情包
   * </pre>
   *
   * <code>repeated string image = 6;</code>
   * @param index The index of the element to return.
   * @return The image at the given index.
   */
  java.lang.String getImage(int index);
  /**
   * <pre>
   * 具体的表情包
   * </pre>
   *
   * <code>repeated string image = 6;</code>
   * @param index The index of the value to return.
   * @return The bytes of the image at the given index.
   */
  com.google.protobuf.ByteString
      getImageBytes(int index);

  /**
   * <pre>
   * 价格类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.PriceType priceType = 7;</code>
   * @return The enum numeric value on the wire for priceType.
   */
  int getPriceTypeValue();
  /**
   * <pre>
   * 价格类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.PriceType priceType = 7;</code>
   * @return The priceType.
   */
  com.kikitrade.activity.facade.sticker.PriceType getPriceType();

  /**
   * <pre>
   * 法币类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.CurrencyType currencyType = 8;</code>
   * @return The enum numeric value on the wire for currencyType.
   */
  int getCurrencyTypeValue();
  /**
   * <pre>
   * 法币类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.CurrencyType currencyType = 8;</code>
   * @return The currencyType.
   */
  com.kikitrade.activity.facade.sticker.CurrencyType getCurrencyType();

  /**
   * <pre>
   * 价格
   * </pre>
   *
   * <code>string price = 9;</code>
   * @return The price.
   */
  java.lang.String getPrice();
  /**
   * <pre>
   * 价格
   * </pre>
   *
   * <code>string price = 9;</code>
   * @return The bytes for price.
   */
  com.google.protobuf.ByteString
      getPriceBytes();

  /**
   * <pre>
   * 门槛类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.StickerGateType gateType = 10;</code>
   * @return The enum numeric value on the wire for gateType.
   */
  int getGateTypeValue();
  /**
   * <pre>
   * 门槛类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.StickerGateType gateType = 10;</code>
   * @return The gateType.
   */
  com.kikitrade.activity.facade.sticker.StickerGateType getGateType();

  /**
   * <pre>
   * 门槛值
   * </pre>
   *
   * <code>string gateValue = 11;</code>
   * @return The gateValue.
   */
  java.lang.String getGateValue();
  /**
   * <pre>
   * 门槛值
   * </pre>
   *
   * <code>string gateValue = 11;</code>
   * @return The bytes for gateValue.
   */
  com.google.protobuf.ByteString
      getGateValueBytes();

  /**
   * <pre>
   * 排序
   * </pre>
   *
   * <code>int32 sort = 12;</code>
   * @return The sort.
   */
  int getSort();

  /**
   * <pre>
   * 状态
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.StickerStatus status = 13;</code>
   * @return The enum numeric value on the wire for status.
   */
  int getStatusValue();
  /**
   * <pre>
   * 状态
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.StickerStatus status = 13;</code>
   * @return The status.
   */
  com.kikitrade.activity.facade.sticker.StickerStatus getStatus();
}
