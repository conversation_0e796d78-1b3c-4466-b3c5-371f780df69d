// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Lottery.proto

package com.kikitrade.activity.facade.lottery;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.lottery.LotteryDTO}
 */
public final class LotteryDTO extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.lottery.LotteryDTO)
    LotteryDTOOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LotteryDTO.newBuilder() to construct.
  private LotteryDTO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LotteryDTO() {
    amount_ = "";
    rewardVO_ = java.util.Collections.emptyList();
    saasId_ = "";
    id_ = "";
    status_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LotteryDTO();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.lottery.LotteryFacadeOutClass.internal_static_com_kikitrade_activity_facade_lottery_LotteryDTO_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.lottery.LotteryFacadeOutClass.internal_static_com_kikitrade_activity_facade_lottery_LotteryDTO_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.lottery.LotteryDTO.class, com.kikitrade.activity.facade.lottery.LotteryDTO.Builder.class);
  }

  public static final int AMOUNT_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object amount_ = "";
  /**
   * <pre>
   *每次抽奖消耗
   * </pre>
   *
   * <code>string amount = 1;</code>
   * @return The amount.
   */
  @java.lang.Override
  public java.lang.String getAmount() {
    java.lang.Object ref = amount_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      amount_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *每次抽奖消耗
   * </pre>
   *
   * <code>string amount = 1;</code>
   * @return The bytes for amount.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAmountBytes() {
    java.lang.Object ref = amount_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      amount_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LIMIT_FIELD_NUMBER = 2;
  private int limit_ = 0;
  /**
   * <pre>
   *每日抽奖次数上限，小于等于0，不限制
   * </pre>
   *
   * <code>int32 limit = 2;</code>
   * @return The limit.
   */
  @java.lang.Override
  public int getLimit() {
    return limit_;
  }

  public static final int REELS_FIELD_NUMBER = 3;
  private int reels_ = 0;
  /**
   * <pre>
   *转盘数
   * </pre>
   *
   * <code>int32 reels = 3;</code>
   * @return The reels.
   */
  @java.lang.Override
  public int getReels() {
    return reels_;
  }

  public static final int REWARDVO_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.lottery.RewardVO> rewardVO_;
  /**
   * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.lottery.RewardVO> getRewardVOList() {
    return rewardVO_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.lottery.RewardVOOrBuilder> 
      getRewardVOOrBuilderList() {
    return rewardVO_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
   */
  @java.lang.Override
  public int getRewardVOCount() {
    return rewardVO_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.lottery.RewardVO getRewardVO(int index) {
    return rewardVO_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.lottery.RewardVOOrBuilder getRewardVOOrBuilder(
      int index) {
    return rewardVO_.get(index);
  }

  public static final int SAASID_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object saasId_ = "";
  /**
   * <code>string saasId = 5;</code>
   * @return The saasId.
   */
  @java.lang.Override
  public java.lang.String getSaasId() {
    java.lang.Object ref = saasId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      saasId_ = s;
      return s;
    }
  }
  /**
   * <code>string saasId = 5;</code>
   * @return The bytes for saasId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSaasIdBytes() {
    java.lang.Object ref = saasId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      saasId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ID_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <code>string id = 6;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <code>string id = 6;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATUS_FIELD_NUMBER = 7;
  private int status_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.lottery.CommonStatus status = 7;</code>
   * @return The enum numeric value on the wire for status.
   */
  @java.lang.Override public int getStatusValue() {
    return status_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.lottery.CommonStatus status = 7;</code>
   * @return The status.
   */
  @java.lang.Override public com.kikitrade.activity.facade.lottery.CommonStatus getStatus() {
    com.kikitrade.activity.facade.lottery.CommonStatus result = com.kikitrade.activity.facade.lottery.CommonStatus.forNumber(status_);
    return result == null ? com.kikitrade.activity.facade.lottery.CommonStatus.UNRECOGNIZED : result;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amount_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, amount_);
    }
    if (limit_ != 0) {
      output.writeInt32(2, limit_);
    }
    if (reels_ != 0) {
      output.writeInt32(3, reels_);
    }
    for (int i = 0; i < rewardVO_.size(); i++) {
      output.writeMessage(4, rewardVO_.get(i));
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(saasId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, saasId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, id_);
    }
    if (status_ != com.kikitrade.activity.facade.lottery.CommonStatus.ACTIVE.getNumber()) {
      output.writeEnum(7, status_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amount_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, amount_);
    }
    if (limit_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, limit_);
    }
    if (reels_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, reels_);
    }
    for (int i = 0; i < rewardVO_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, rewardVO_.get(i));
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(saasId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, saasId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, id_);
    }
    if (status_ != com.kikitrade.activity.facade.lottery.CommonStatus.ACTIVE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(7, status_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.lottery.LotteryDTO)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.lottery.LotteryDTO other = (com.kikitrade.activity.facade.lottery.LotteryDTO) obj;

    if (!getAmount()
        .equals(other.getAmount())) return false;
    if (getLimit()
        != other.getLimit()) return false;
    if (getReels()
        != other.getReels()) return false;
    if (!getRewardVOList()
        .equals(other.getRewardVOList())) return false;
    if (!getSaasId()
        .equals(other.getSaasId())) return false;
    if (!getId()
        .equals(other.getId())) return false;
    if (status_ != other.status_) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getAmount().hashCode();
    hash = (37 * hash) + LIMIT_FIELD_NUMBER;
    hash = (53 * hash) + getLimit();
    hash = (37 * hash) + REELS_FIELD_NUMBER;
    hash = (53 * hash) + getReels();
    if (getRewardVOCount() > 0) {
      hash = (37 * hash) + REWARDVO_FIELD_NUMBER;
      hash = (53 * hash) + getRewardVOList().hashCode();
    }
    hash = (37 * hash) + SAASID_FIELD_NUMBER;
    hash = (53 * hash) + getSaasId().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + status_;
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.lottery.LotteryDTO parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.lottery.LotteryDTO parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.lottery.LotteryDTO parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.lottery.LotteryDTO parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.lottery.LotteryDTO parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.lottery.LotteryDTO parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.lottery.LotteryDTO parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.lottery.LotteryDTO parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.lottery.LotteryDTO parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.lottery.LotteryDTO parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.lottery.LotteryDTO parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.lottery.LotteryDTO parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.lottery.LotteryDTO prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.lottery.LotteryDTO}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.lottery.LotteryDTO)
      com.kikitrade.activity.facade.lottery.LotteryDTOOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.lottery.LotteryFacadeOutClass.internal_static_com_kikitrade_activity_facade_lottery_LotteryDTO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.lottery.LotteryFacadeOutClass.internal_static_com_kikitrade_activity_facade_lottery_LotteryDTO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.lottery.LotteryDTO.class, com.kikitrade.activity.facade.lottery.LotteryDTO.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.lottery.LotteryDTO.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      amount_ = "";
      limit_ = 0;
      reels_ = 0;
      if (rewardVOBuilder_ == null) {
        rewardVO_ = java.util.Collections.emptyList();
      } else {
        rewardVO_ = null;
        rewardVOBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      saasId_ = "";
      id_ = "";
      status_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.lottery.LotteryFacadeOutClass.internal_static_com_kikitrade_activity_facade_lottery_LotteryDTO_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.lottery.LotteryDTO getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.lottery.LotteryDTO.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.lottery.LotteryDTO build() {
      com.kikitrade.activity.facade.lottery.LotteryDTO result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.lottery.LotteryDTO buildPartial() {
      com.kikitrade.activity.facade.lottery.LotteryDTO result = new com.kikitrade.activity.facade.lottery.LotteryDTO(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.lottery.LotteryDTO result) {
      if (rewardVOBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          rewardVO_ = java.util.Collections.unmodifiableList(rewardVO_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.rewardVO_ = rewardVO_;
      } else {
        result.rewardVO_ = rewardVOBuilder_.build();
      }
    }

    private void buildPartial0(com.kikitrade.activity.facade.lottery.LotteryDTO result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.amount_ = amount_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.limit_ = limit_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.reels_ = reels_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.saasId_ = saasId_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.status_ = status_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.lottery.LotteryDTO) {
        return mergeFrom((com.kikitrade.activity.facade.lottery.LotteryDTO)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.lottery.LotteryDTO other) {
      if (other == com.kikitrade.activity.facade.lottery.LotteryDTO.getDefaultInstance()) return this;
      if (!other.getAmount().isEmpty()) {
        amount_ = other.amount_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.getLimit() != 0) {
        setLimit(other.getLimit());
      }
      if (other.getReels() != 0) {
        setReels(other.getReels());
      }
      if (rewardVOBuilder_ == null) {
        if (!other.rewardVO_.isEmpty()) {
          if (rewardVO_.isEmpty()) {
            rewardVO_ = other.rewardVO_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureRewardVOIsMutable();
            rewardVO_.addAll(other.rewardVO_);
          }
          onChanged();
        }
      } else {
        if (!other.rewardVO_.isEmpty()) {
          if (rewardVOBuilder_.isEmpty()) {
            rewardVOBuilder_.dispose();
            rewardVOBuilder_ = null;
            rewardVO_ = other.rewardVO_;
            bitField0_ = (bitField0_ & ~0x00000008);
            rewardVOBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getRewardVOFieldBuilder() : null;
          } else {
            rewardVOBuilder_.addAllMessages(other.rewardVO_);
          }
        }
      }
      if (!other.getSaasId().isEmpty()) {
        saasId_ = other.saasId_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (other.status_ != 0) {
        setStatusValue(other.getStatusValue());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              amount_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              limit_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              reels_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              com.kikitrade.activity.facade.lottery.RewardVO m =
                  input.readMessage(
                      com.kikitrade.activity.facade.lottery.RewardVO.parser(),
                      extensionRegistry);
              if (rewardVOBuilder_ == null) {
                ensureRewardVOIsMutable();
                rewardVO_.add(m);
              } else {
                rewardVOBuilder_.addMessage(m);
              }
              break;
            } // case 34
            case 42: {
              saasId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 56: {
              status_ = input.readEnum();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object amount_ = "";
    /**
     * <pre>
     *每次抽奖消耗
     * </pre>
     *
     * <code>string amount = 1;</code>
     * @return The amount.
     */
    public java.lang.String getAmount() {
      java.lang.Object ref = amount_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        amount_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *每次抽奖消耗
     * </pre>
     *
     * <code>string amount = 1;</code>
     * @return The bytes for amount.
     */
    public com.google.protobuf.ByteString
        getAmountBytes() {
      java.lang.Object ref = amount_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        amount_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *每次抽奖消耗
     * </pre>
     *
     * <code>string amount = 1;</code>
     * @param value The amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmount(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      amount_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *每次抽奖消耗
     * </pre>
     *
     * <code>string amount = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmount() {
      amount_ = getDefaultInstance().getAmount();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *每次抽奖消耗
     * </pre>
     *
     * <code>string amount = 1;</code>
     * @param value The bytes for amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmountBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      amount_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private int limit_ ;
    /**
     * <pre>
     *每日抽奖次数上限，小于等于0，不限制
     * </pre>
     *
     * <code>int32 limit = 2;</code>
     * @return The limit.
     */
    @java.lang.Override
    public int getLimit() {
      return limit_;
    }
    /**
     * <pre>
     *每日抽奖次数上限，小于等于0，不限制
     * </pre>
     *
     * <code>int32 limit = 2;</code>
     * @param value The limit to set.
     * @return This builder for chaining.
     */
    public Builder setLimit(int value) {

      limit_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *每日抽奖次数上限，小于等于0，不限制
     * </pre>
     *
     * <code>int32 limit = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearLimit() {
      bitField0_ = (bitField0_ & ~0x00000002);
      limit_ = 0;
      onChanged();
      return this;
    }

    private int reels_ ;
    /**
     * <pre>
     *转盘数
     * </pre>
     *
     * <code>int32 reels = 3;</code>
     * @return The reels.
     */
    @java.lang.Override
    public int getReels() {
      return reels_;
    }
    /**
     * <pre>
     *转盘数
     * </pre>
     *
     * <code>int32 reels = 3;</code>
     * @param value The reels to set.
     * @return This builder for chaining.
     */
    public Builder setReels(int value) {

      reels_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *转盘数
     * </pre>
     *
     * <code>int32 reels = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearReels() {
      bitField0_ = (bitField0_ & ~0x00000004);
      reels_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<com.kikitrade.activity.facade.lottery.RewardVO> rewardVO_ =
      java.util.Collections.emptyList();
    private void ensureRewardVOIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        rewardVO_ = new java.util.ArrayList<com.kikitrade.activity.facade.lottery.RewardVO>(rewardVO_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.lottery.RewardVO, com.kikitrade.activity.facade.lottery.RewardVO.Builder, com.kikitrade.activity.facade.lottery.RewardVOOrBuilder> rewardVOBuilder_;

    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.lottery.RewardVO> getRewardVOList() {
      if (rewardVOBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rewardVO_);
      } else {
        return rewardVOBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public int getRewardVOCount() {
      if (rewardVOBuilder_ == null) {
        return rewardVO_.size();
      } else {
        return rewardVOBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public com.kikitrade.activity.facade.lottery.RewardVO getRewardVO(int index) {
      if (rewardVOBuilder_ == null) {
        return rewardVO_.get(index);
      } else {
        return rewardVOBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public Builder setRewardVO(
        int index, com.kikitrade.activity.facade.lottery.RewardVO value) {
      if (rewardVOBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardVOIsMutable();
        rewardVO_.set(index, value);
        onChanged();
      } else {
        rewardVOBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public Builder setRewardVO(
        int index, com.kikitrade.activity.facade.lottery.RewardVO.Builder builderForValue) {
      if (rewardVOBuilder_ == null) {
        ensureRewardVOIsMutable();
        rewardVO_.set(index, builderForValue.build());
        onChanged();
      } else {
        rewardVOBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public Builder addRewardVO(com.kikitrade.activity.facade.lottery.RewardVO value) {
      if (rewardVOBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardVOIsMutable();
        rewardVO_.add(value);
        onChanged();
      } else {
        rewardVOBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public Builder addRewardVO(
        int index, com.kikitrade.activity.facade.lottery.RewardVO value) {
      if (rewardVOBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardVOIsMutable();
        rewardVO_.add(index, value);
        onChanged();
      } else {
        rewardVOBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public Builder addRewardVO(
        com.kikitrade.activity.facade.lottery.RewardVO.Builder builderForValue) {
      if (rewardVOBuilder_ == null) {
        ensureRewardVOIsMutable();
        rewardVO_.add(builderForValue.build());
        onChanged();
      } else {
        rewardVOBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public Builder addRewardVO(
        int index, com.kikitrade.activity.facade.lottery.RewardVO.Builder builderForValue) {
      if (rewardVOBuilder_ == null) {
        ensureRewardVOIsMutable();
        rewardVO_.add(index, builderForValue.build());
        onChanged();
      } else {
        rewardVOBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public Builder addAllRewardVO(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.lottery.RewardVO> values) {
      if (rewardVOBuilder_ == null) {
        ensureRewardVOIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rewardVO_);
        onChanged();
      } else {
        rewardVOBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public Builder clearRewardVO() {
      if (rewardVOBuilder_ == null) {
        rewardVO_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        rewardVOBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public Builder removeRewardVO(int index) {
      if (rewardVOBuilder_ == null) {
        ensureRewardVOIsMutable();
        rewardVO_.remove(index);
        onChanged();
      } else {
        rewardVOBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public com.kikitrade.activity.facade.lottery.RewardVO.Builder getRewardVOBuilder(
        int index) {
      return getRewardVOFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public com.kikitrade.activity.facade.lottery.RewardVOOrBuilder getRewardVOOrBuilder(
        int index) {
      if (rewardVOBuilder_ == null) {
        return rewardVO_.get(index);  } else {
        return rewardVOBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.lottery.RewardVOOrBuilder> 
         getRewardVOOrBuilderList() {
      if (rewardVOBuilder_ != null) {
        return rewardVOBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rewardVO_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public com.kikitrade.activity.facade.lottery.RewardVO.Builder addRewardVOBuilder() {
      return getRewardVOFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.lottery.RewardVO.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public com.kikitrade.activity.facade.lottery.RewardVO.Builder addRewardVOBuilder(
        int index) {
      return getRewardVOFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.lottery.RewardVO.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.lottery.RewardVO.Builder> 
         getRewardVOBuilderList() {
      return getRewardVOFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.lottery.RewardVO, com.kikitrade.activity.facade.lottery.RewardVO.Builder, com.kikitrade.activity.facade.lottery.RewardVOOrBuilder> 
        getRewardVOFieldBuilder() {
      if (rewardVOBuilder_ == null) {
        rewardVOBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.lottery.RewardVO, com.kikitrade.activity.facade.lottery.RewardVO.Builder, com.kikitrade.activity.facade.lottery.RewardVOOrBuilder>(
                rewardVO_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        rewardVO_ = null;
      }
      return rewardVOBuilder_;
    }

    private java.lang.Object saasId_ = "";
    /**
     * <code>string saasId = 5;</code>
     * @return The saasId.
     */
    public java.lang.String getSaasId() {
      java.lang.Object ref = saasId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        saasId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string saasId = 5;</code>
     * @return The bytes for saasId.
     */
    public com.google.protobuf.ByteString
        getSaasIdBytes() {
      java.lang.Object ref = saasId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        saasId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string saasId = 5;</code>
     * @param value The saasId to set.
     * @return This builder for chaining.
     */
    public Builder setSaasId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      saasId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>string saasId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearSaasId() {
      saasId_ = getDefaultInstance().getSaasId();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>string saasId = 5;</code>
     * @param value The bytes for saasId to set.
     * @return This builder for chaining.
     */
    public Builder setSaasIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      saasId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private java.lang.Object id_ = "";
    /**
     * <code>string id = 6;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string id = 6;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string id = 6;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>string id = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>string id = 6;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private int status_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.lottery.CommonStatus status = 7;</code>
     * @return The enum numeric value on the wire for status.
     */
    @java.lang.Override public int getStatusValue() {
      return status_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.lottery.CommonStatus status = 7;</code>
     * @param value The enum numeric value on the wire for status to set.
     * @return This builder for chaining.
     */
    public Builder setStatusValue(int value) {
      status_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.lottery.CommonStatus status = 7;</code>
     * @return The status.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.lottery.CommonStatus getStatus() {
      com.kikitrade.activity.facade.lottery.CommonStatus result = com.kikitrade.activity.facade.lottery.CommonStatus.forNumber(status_);
      return result == null ? com.kikitrade.activity.facade.lottery.CommonStatus.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.lottery.CommonStatus status = 7;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(com.kikitrade.activity.facade.lottery.CommonStatus value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000040;
      status_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.lottery.CommonStatus status = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      bitField0_ = (bitField0_ & ~0x00000040);
      status_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.lottery.LotteryDTO)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.lottery.LotteryDTO)
  private static final com.kikitrade.activity.facade.lottery.LotteryDTO DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.lottery.LotteryDTO();
  }

  public static com.kikitrade.activity.facade.lottery.LotteryDTO getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LotteryDTO>
      PARSER = new com.google.protobuf.AbstractParser<LotteryDTO>() {
    @java.lang.Override
    public LotteryDTO parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<LotteryDTO> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LotteryDTO> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.lottery.LotteryDTO getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

