// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: DiscountCouponFacade.proto

package com.kikitrade.activity.facade.coupon;

/**
 * <pre>
 **
 *优惠券实体类
 * </pre>
 *
 * Protobuf type {@code com.kikitrade.activity.facade.coupon.DiscountCouponRequest}
 */
public final class DiscountCouponRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.coupon.DiscountCouponRequest)
    DiscountCouponRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use DiscountCouponRequest.newBuilder() to construct.
  private DiscountCouponRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private DiscountCouponRequest() {
    id_ = "";
    name_ = "";
    image_ = "";
    shopifyDiscountCode_ = "";
    discountType_ = 0;
    applyTo_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new DiscountCouponRequest();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.coupon.DiscountCouponFacadeOuterClass.internal_static_com_kikitrade_activity_facade_coupon_DiscountCouponRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.coupon.DiscountCouponFacadeOuterClass.internal_static_com_kikitrade_activity_facade_coupon_DiscountCouponRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.coupon.DiscountCouponRequest.class, com.kikitrade.activity.facade.coupon.DiscountCouponRequest.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <pre>
   * id为空时新增，不为空时修改
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * id为空时新增，不为空时修改
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object name_ = "";
  /**
   * <pre>
   * 名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IMAGE_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object image_ = "";
  /**
   * <pre>
   * 图片
   * </pre>
   *
   * <code>string image = 3;</code>
   * @return The image.
   */
  @java.lang.Override
  public java.lang.String getImage() {
    java.lang.Object ref = image_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      image_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 图片
   * </pre>
   *
   * <code>string image = 3;</code>
   * @return The bytes for image.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getImageBytes() {
    java.lang.Object ref = image_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      image_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SHOPIFYDISCOUNTCODE_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object shopifyDiscountCode_ = "";
  /**
   * <pre>
   * shopify折扣码
   * </pre>
   *
   * <code>string shopifyDiscountCode = 4;</code>
   * @return The shopifyDiscountCode.
   */
  @java.lang.Override
  public java.lang.String getShopifyDiscountCode() {
    java.lang.Object ref = shopifyDiscountCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      shopifyDiscountCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * shopify折扣码
   * </pre>
   *
   * <code>string shopifyDiscountCode = 4;</code>
   * @return The bytes for shopifyDiscountCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getShopifyDiscountCodeBytes() {
    java.lang.Object ref = shopifyDiscountCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      shopifyDiscountCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int VALIDDAY_FIELD_NUMBER = 5;
  private int validDay_ = 0;
  /**
   * <pre>
   * 有效周期/日
   * </pre>
   *
   * <code>int32 validDay = 5;</code>
   * @return The validDay.
   */
  @java.lang.Override
  public int getValidDay() {
    return validDay_;
  }

  public static final int DISCOUNTTYPE_FIELD_NUMBER = 6;
  private int discountType_ = 0;
  /**
   * <pre>
   * 直减或折扣
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.coupon.DiscountType discountType = 6;</code>
   * @return The enum numeric value on the wire for discountType.
   */
  @java.lang.Override public int getDiscountTypeValue() {
    return discountType_;
  }
  /**
   * <pre>
   * 直减或折扣
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.coupon.DiscountType discountType = 6;</code>
   * @return The discountType.
   */
  @java.lang.Override public com.kikitrade.activity.facade.coupon.DiscountType getDiscountType() {
    com.kikitrade.activity.facade.coupon.DiscountType result = com.kikitrade.activity.facade.coupon.DiscountType.forNumber(discountType_);
    return result == null ? com.kikitrade.activity.facade.coupon.DiscountType.UNRECOGNIZED : result;
  }

  public static final int DISCOUNTVALUE_FIELD_NUMBER = 7;
  private double discountValue_ = 0D;
  /**
   * <pre>
   * 直减或折扣值
   * </pre>
   *
   * <code>double discountValue = 7;</code>
   * @return The discountValue.
   */
  @java.lang.Override
  public double getDiscountValue() {
    return discountValue_;
  }

  public static final int APPLYTO_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object applyTo_ = "";
  /**
   * <pre>
   * 使用范围 product id;product id;product id
   * </pre>
   *
   * <code>string applyTo = 8;</code>
   * @return The applyTo.
   */
  @java.lang.Override
  public java.lang.String getApplyTo() {
    java.lang.Object ref = applyTo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      applyTo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 使用范围 product id;product id;product id
   * </pre>
   *
   * <code>string applyTo = 8;</code>
   * @return The bytes for applyTo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getApplyToBytes() {
    java.lang.Object ref = applyTo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      applyTo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MINLIMITPRICE_FIELD_NUMBER = 9;
  private double minLimitPrice_ = 0D;
  /**
   * <pre>
   * 最小限制金额
   * </pre>
   *
   * <code>double minLimitPrice = 9;</code>
   * @return The minLimitPrice.
   */
  @java.lang.Override
  public double getMinLimitPrice() {
    return minLimitPrice_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(image_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, image_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(shopifyDiscountCode_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, shopifyDiscountCode_);
    }
    if (validDay_ != 0) {
      output.writeInt32(5, validDay_);
    }
    if (discountType_ != com.kikitrade.activity.facade.coupon.DiscountType.RATE.getNumber()) {
      output.writeEnum(6, discountType_);
    }
    if (java.lang.Double.doubleToRawLongBits(discountValue_) != 0) {
      output.writeDouble(7, discountValue_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(applyTo_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, applyTo_);
    }
    if (java.lang.Double.doubleToRawLongBits(minLimitPrice_) != 0) {
      output.writeDouble(9, minLimitPrice_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(image_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, image_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(shopifyDiscountCode_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, shopifyDiscountCode_);
    }
    if (validDay_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, validDay_);
    }
    if (discountType_ != com.kikitrade.activity.facade.coupon.DiscountType.RATE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(6, discountType_);
    }
    if (java.lang.Double.doubleToRawLongBits(discountValue_) != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(7, discountValue_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(applyTo_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, applyTo_);
    }
    if (java.lang.Double.doubleToRawLongBits(minLimitPrice_) != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(9, minLimitPrice_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.coupon.DiscountCouponRequest)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.coupon.DiscountCouponRequest other = (com.kikitrade.activity.facade.coupon.DiscountCouponRequest) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (!getImage()
        .equals(other.getImage())) return false;
    if (!getShopifyDiscountCode()
        .equals(other.getShopifyDiscountCode())) return false;
    if (getValidDay()
        != other.getValidDay()) return false;
    if (discountType_ != other.discountType_) return false;
    if (java.lang.Double.doubleToLongBits(getDiscountValue())
        != java.lang.Double.doubleToLongBits(
            other.getDiscountValue())) return false;
    if (!getApplyTo()
        .equals(other.getApplyTo())) return false;
    if (java.lang.Double.doubleToLongBits(getMinLimitPrice())
        != java.lang.Double.doubleToLongBits(
            other.getMinLimitPrice())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + IMAGE_FIELD_NUMBER;
    hash = (53 * hash) + getImage().hashCode();
    hash = (37 * hash) + SHOPIFYDISCOUNTCODE_FIELD_NUMBER;
    hash = (53 * hash) + getShopifyDiscountCode().hashCode();
    hash = (37 * hash) + VALIDDAY_FIELD_NUMBER;
    hash = (53 * hash) + getValidDay();
    hash = (37 * hash) + DISCOUNTTYPE_FIELD_NUMBER;
    hash = (53 * hash) + discountType_;
    hash = (37 * hash) + DISCOUNTVALUE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getDiscountValue()));
    hash = (37 * hash) + APPLYTO_FIELD_NUMBER;
    hash = (53 * hash) + getApplyTo().hashCode();
    hash = (37 * hash) + MINLIMITPRICE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getMinLimitPrice()));
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.coupon.DiscountCouponRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.coupon.DiscountCouponRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.coupon.DiscountCouponRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.coupon.DiscountCouponRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.coupon.DiscountCouponRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.coupon.DiscountCouponRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.coupon.DiscountCouponRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.coupon.DiscountCouponRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.coupon.DiscountCouponRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.coupon.DiscountCouponRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.coupon.DiscountCouponRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.coupon.DiscountCouponRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.coupon.DiscountCouponRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   **
   *优惠券实体类
   * </pre>
   *
   * Protobuf type {@code com.kikitrade.activity.facade.coupon.DiscountCouponRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.coupon.DiscountCouponRequest)
      com.kikitrade.activity.facade.coupon.DiscountCouponRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.coupon.DiscountCouponFacadeOuterClass.internal_static_com_kikitrade_activity_facade_coupon_DiscountCouponRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.coupon.DiscountCouponFacadeOuterClass.internal_static_com_kikitrade_activity_facade_coupon_DiscountCouponRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.coupon.DiscountCouponRequest.class, com.kikitrade.activity.facade.coupon.DiscountCouponRequest.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.coupon.DiscountCouponRequest.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      name_ = "";
      image_ = "";
      shopifyDiscountCode_ = "";
      validDay_ = 0;
      discountType_ = 0;
      discountValue_ = 0D;
      applyTo_ = "";
      minLimitPrice_ = 0D;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.coupon.DiscountCouponFacadeOuterClass.internal_static_com_kikitrade_activity_facade_coupon_DiscountCouponRequest_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.coupon.DiscountCouponRequest getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.coupon.DiscountCouponRequest.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.coupon.DiscountCouponRequest build() {
      com.kikitrade.activity.facade.coupon.DiscountCouponRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.coupon.DiscountCouponRequest buildPartial() {
      com.kikitrade.activity.facade.coupon.DiscountCouponRequest result = new com.kikitrade.activity.facade.coupon.DiscountCouponRequest(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.kikitrade.activity.facade.coupon.DiscountCouponRequest result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.name_ = name_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.image_ = image_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.shopifyDiscountCode_ = shopifyDiscountCode_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.validDay_ = validDay_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.discountType_ = discountType_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.discountValue_ = discountValue_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.applyTo_ = applyTo_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.minLimitPrice_ = minLimitPrice_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.coupon.DiscountCouponRequest) {
        return mergeFrom((com.kikitrade.activity.facade.coupon.DiscountCouponRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.coupon.DiscountCouponRequest other) {
      if (other == com.kikitrade.activity.facade.coupon.DiscountCouponRequest.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getImage().isEmpty()) {
        image_ = other.image_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getShopifyDiscountCode().isEmpty()) {
        shopifyDiscountCode_ = other.shopifyDiscountCode_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.getValidDay() != 0) {
        setValidDay(other.getValidDay());
      }
      if (other.discountType_ != 0) {
        setDiscountTypeValue(other.getDiscountTypeValue());
      }
      if (other.getDiscountValue() != 0D) {
        setDiscountValue(other.getDiscountValue());
      }
      if (!other.getApplyTo().isEmpty()) {
        applyTo_ = other.applyTo_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (other.getMinLimitPrice() != 0D) {
        setMinLimitPrice(other.getMinLimitPrice());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              name_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              image_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              shopifyDiscountCode_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              validDay_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              discountType_ = input.readEnum();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 57: {
              discountValue_ = input.readDouble();
              bitField0_ |= 0x00000040;
              break;
            } // case 57
            case 66: {
              applyTo_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 73: {
              minLimitPrice_ = input.readDouble();
              bitField0_ |= 0x00000100;
              break;
            } // case 73
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <pre>
     * id为空时新增，不为空时修改
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * id为空时新增，不为空时修改
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * id为空时新增，不为空时修改
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * id为空时新增，不为空时修改
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * id为空时新增，不为空时修改
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <pre>
     * 名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      name_ = getDefaultInstance().getName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      name_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object image_ = "";
    /**
     * <pre>
     * 图片
     * </pre>
     *
     * <code>string image = 3;</code>
     * @return The image.
     */
    public java.lang.String getImage() {
      java.lang.Object ref = image_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        image_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 图片
     * </pre>
     *
     * <code>string image = 3;</code>
     * @return The bytes for image.
     */
    public com.google.protobuf.ByteString
        getImageBytes() {
      java.lang.Object ref = image_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        image_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 图片
     * </pre>
     *
     * <code>string image = 3;</code>
     * @param value The image to set.
     * @return This builder for chaining.
     */
    public Builder setImage(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      image_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 图片
     * </pre>
     *
     * <code>string image = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearImage() {
      image_ = getDefaultInstance().getImage();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 图片
     * </pre>
     *
     * <code>string image = 3;</code>
     * @param value The bytes for image to set.
     * @return This builder for chaining.
     */
    public Builder setImageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      image_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object shopifyDiscountCode_ = "";
    /**
     * <pre>
     * shopify折扣码
     * </pre>
     *
     * <code>string shopifyDiscountCode = 4;</code>
     * @return The shopifyDiscountCode.
     */
    public java.lang.String getShopifyDiscountCode() {
      java.lang.Object ref = shopifyDiscountCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        shopifyDiscountCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * shopify折扣码
     * </pre>
     *
     * <code>string shopifyDiscountCode = 4;</code>
     * @return The bytes for shopifyDiscountCode.
     */
    public com.google.protobuf.ByteString
        getShopifyDiscountCodeBytes() {
      java.lang.Object ref = shopifyDiscountCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        shopifyDiscountCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * shopify折扣码
     * </pre>
     *
     * <code>string shopifyDiscountCode = 4;</code>
     * @param value The shopifyDiscountCode to set.
     * @return This builder for chaining.
     */
    public Builder setShopifyDiscountCode(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      shopifyDiscountCode_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * shopify折扣码
     * </pre>
     *
     * <code>string shopifyDiscountCode = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearShopifyDiscountCode() {
      shopifyDiscountCode_ = getDefaultInstance().getShopifyDiscountCode();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * shopify折扣码
     * </pre>
     *
     * <code>string shopifyDiscountCode = 4;</code>
     * @param value The bytes for shopifyDiscountCode to set.
     * @return This builder for chaining.
     */
    public Builder setShopifyDiscountCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      shopifyDiscountCode_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private int validDay_ ;
    /**
     * <pre>
     * 有效周期/日
     * </pre>
     *
     * <code>int32 validDay = 5;</code>
     * @return The validDay.
     */
    @java.lang.Override
    public int getValidDay() {
      return validDay_;
    }
    /**
     * <pre>
     * 有效周期/日
     * </pre>
     *
     * <code>int32 validDay = 5;</code>
     * @param value The validDay to set.
     * @return This builder for chaining.
     */
    public Builder setValidDay(int value) {

      validDay_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 有效周期/日
     * </pre>
     *
     * <code>int32 validDay = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearValidDay() {
      bitField0_ = (bitField0_ & ~0x00000010);
      validDay_ = 0;
      onChanged();
      return this;
    }

    private int discountType_ = 0;
    /**
     * <pre>
     * 直减或折扣
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.coupon.DiscountType discountType = 6;</code>
     * @return The enum numeric value on the wire for discountType.
     */
    @java.lang.Override public int getDiscountTypeValue() {
      return discountType_;
    }
    /**
     * <pre>
     * 直减或折扣
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.coupon.DiscountType discountType = 6;</code>
     * @param value The enum numeric value on the wire for discountType to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountTypeValue(int value) {
      discountType_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 直减或折扣
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.coupon.DiscountType discountType = 6;</code>
     * @return The discountType.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.coupon.DiscountType getDiscountType() {
      com.kikitrade.activity.facade.coupon.DiscountType result = com.kikitrade.activity.facade.coupon.DiscountType.forNumber(discountType_);
      return result == null ? com.kikitrade.activity.facade.coupon.DiscountType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 直减或折扣
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.coupon.DiscountType discountType = 6;</code>
     * @param value The discountType to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountType(com.kikitrade.activity.facade.coupon.DiscountType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000020;
      discountType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 直减或折扣
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.coupon.DiscountType discountType = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountType() {
      bitField0_ = (bitField0_ & ~0x00000020);
      discountType_ = 0;
      onChanged();
      return this;
    }

    private double discountValue_ ;
    /**
     * <pre>
     * 直减或折扣值
     * </pre>
     *
     * <code>double discountValue = 7;</code>
     * @return The discountValue.
     */
    @java.lang.Override
    public double getDiscountValue() {
      return discountValue_;
    }
    /**
     * <pre>
     * 直减或折扣值
     * </pre>
     *
     * <code>double discountValue = 7;</code>
     * @param value The discountValue to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountValue(double value) {

      discountValue_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 直减或折扣值
     * </pre>
     *
     * <code>double discountValue = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountValue() {
      bitField0_ = (bitField0_ & ~0x00000040);
      discountValue_ = 0D;
      onChanged();
      return this;
    }

    private java.lang.Object applyTo_ = "";
    /**
     * <pre>
     * 使用范围 product id;product id;product id
     * </pre>
     *
     * <code>string applyTo = 8;</code>
     * @return The applyTo.
     */
    public java.lang.String getApplyTo() {
      java.lang.Object ref = applyTo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        applyTo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 使用范围 product id;product id;product id
     * </pre>
     *
     * <code>string applyTo = 8;</code>
     * @return The bytes for applyTo.
     */
    public com.google.protobuf.ByteString
        getApplyToBytes() {
      java.lang.Object ref = applyTo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        applyTo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 使用范围 product id;product id;product id
     * </pre>
     *
     * <code>string applyTo = 8;</code>
     * @param value The applyTo to set.
     * @return This builder for chaining.
     */
    public Builder setApplyTo(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      applyTo_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 使用范围 product id;product id;product id
     * </pre>
     *
     * <code>string applyTo = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearApplyTo() {
      applyTo_ = getDefaultInstance().getApplyTo();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 使用范围 product id;product id;product id
     * </pre>
     *
     * <code>string applyTo = 8;</code>
     * @param value The bytes for applyTo to set.
     * @return This builder for chaining.
     */
    public Builder setApplyToBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      applyTo_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private double minLimitPrice_ ;
    /**
     * <pre>
     * 最小限制金额
     * </pre>
     *
     * <code>double minLimitPrice = 9;</code>
     * @return The minLimitPrice.
     */
    @java.lang.Override
    public double getMinLimitPrice() {
      return minLimitPrice_;
    }
    /**
     * <pre>
     * 最小限制金额
     * </pre>
     *
     * <code>double minLimitPrice = 9;</code>
     * @param value The minLimitPrice to set.
     * @return This builder for chaining.
     */
    public Builder setMinLimitPrice(double value) {

      minLimitPrice_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 最小限制金额
     * </pre>
     *
     * <code>double minLimitPrice = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearMinLimitPrice() {
      bitField0_ = (bitField0_ & ~0x00000100);
      minLimitPrice_ = 0D;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.coupon.DiscountCouponRequest)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.coupon.DiscountCouponRequest)
  private static final com.kikitrade.activity.facade.coupon.DiscountCouponRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.coupon.DiscountCouponRequest();
  }

  public static com.kikitrade.activity.facade.coupon.DiscountCouponRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DiscountCouponRequest>
      PARSER = new com.google.protobuf.AbstractParser<DiscountCouponRequest>() {
    @java.lang.Override
    public DiscountCouponRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DiscountCouponRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DiscountCouponRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.coupon.DiscountCouponRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

