// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: TaskFacade.proto

package com.kikitrade.activity.facade.task;

public interface TaskDTOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.task.TaskDTO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string taskNameCn = 2;</code>
   * @return The taskNameCn.
   */
  java.lang.String getTaskNameCn();
  /**
   * <code>string taskNameCn = 2;</code>
   * @return The bytes for taskNameCn.
   */
  com.google.protobuf.ByteString
      getTaskNameCnBytes();

  /**
   * <code>string taskNameEn = 3;</code>
   * @return The taskNameEn.
   */
  java.lang.String getTaskNameEn();
  /**
   * <code>string taskNameEn = 3;</code>
   * @return The bytes for taskNameEn.
   */
  com.google.protobuf.ByteString
      getTaskNameEnBytes();

  /**
   * <code>string taskNameHk = 4;</code>
   * @return The taskNameHk.
   */
  java.lang.String getTaskNameHk();
  /**
   * <code>string taskNameHk = 4;</code>
   * @return The bytes for taskNameHk.
   */
  com.google.protobuf.ByteString
      getTaskNameHkBytes();

  /**
   * <code>.com.kikitrade.activity.facade.task.Status status = 5;</code>
   * @return The enum numeric value on the wire for status.
   */
  int getStatusValue();
  /**
   * <code>.com.kikitrade.activity.facade.task.Status status = 5;</code>
   * @return The status.
   */
  com.kikitrade.activity.facade.task.Status getStatus();

  /**
   * <code>string descCn = 6;</code>
   * @return The descCn.
   */
  java.lang.String getDescCn();
  /**
   * <code>string descCn = 6;</code>
   * @return The bytes for descCn.
   */
  com.google.protobuf.ByteString
      getDescCnBytes();

  /**
   * <code>string descEn = 7;</code>
   * @return The descEn.
   */
  java.lang.String getDescEn();
  /**
   * <code>string descEn = 7;</code>
   * @return The bytes for descEn.
   */
  com.google.protobuf.ByteString
      getDescEnBytes();

  /**
   * <code>string descHk = 8;</code>
   * @return The descHk.
   */
  java.lang.String getDescHk();
  /**
   * <code>string descHk = 8;</code>
   * @return The bytes for descHk.
   */
  com.google.protobuf.ByteString
      getDescHkBytes();

  /**
   * <code>string startTime = 9;</code>
   * @return The startTime.
   */
  java.lang.String getStartTime();
  /**
   * <code>string startTime = 9;</code>
   * @return The bytes for startTime.
   */
  com.google.protobuf.ByteString
      getStartTimeBytes();

  /**
   * <code>string endTime = 10;</code>
   * @return The endTime.
   */
  java.lang.String getEndTime();
  /**
   * <code>string endTime = 10;</code>
   * @return The bytes for endTime.
   */
  com.google.protobuf.ByteString
      getEndTimeBytes();

  /**
   * <code>string vipLevel = 11;</code>
   * @return The vipLevel.
   */
  java.lang.String getVipLevel();
  /**
   * <code>string vipLevel = 11;</code>
   * @return The bytes for vipLevel.
   */
  com.google.protobuf.ByteString
      getVipLevelBytes();

  /**
   * <code>.com.kikitrade.activity.facade.task.TaskLevelType type = 12;</code>
   * @return The enum numeric value on the wire for type.
   */
  int getTypeValue();
  /**
   * <code>.com.kikitrade.activity.facade.task.TaskLevelType type = 12;</code>
   * @return The type.
   */
  com.kikitrade.activity.facade.task.TaskLevelType getType();

  /**
   * <code>.com.kikitrade.activity.facade.task.CycleType cycleType = 13;</code>
   * @return The enum numeric value on the wire for cycleType.
   */
  int getCycleTypeValue();
  /**
   * <code>.com.kikitrade.activity.facade.task.CycleType cycleType = 13;</code>
   * @return The cycleType.
   */
  com.kikitrade.activity.facade.task.CycleType getCycleType();

  /**
   * <code>.com.kikitrade.activity.facade.task.TaskCycle cycle = 14;</code>
   * @return The enum numeric value on the wire for cycle.
   */
  int getCycleValue();
  /**
   * <code>.com.kikitrade.activity.facade.task.TaskCycle cycle = 14;</code>
   * @return The cycle.
   */
  com.kikitrade.activity.facade.task.TaskCycle getCycle();

  /**
   * <code>.com.kikitrade.activity.facade.task.EventCode event = 15;</code>
   * @return The enum numeric value on the wire for event.
   */
  int getEventValue();
  /**
   * <code>.com.kikitrade.activity.facade.task.EventCode event = 15;</code>
   * @return The event.
   */
  com.kikitrade.activity.facade.task.EventCode getEvent();

  /**
   * <code>string completeThreshold = 16;</code>
   * @return The completeThreshold.
   */
  java.lang.String getCompleteThreshold();
  /**
   * <code>string completeThreshold = 16;</code>
   * @return The bytes for completeThreshold.
   */
  com.google.protobuf.ByteString
      getCompleteThresholdBytes();

  /**
   * <code>string award = 17;</code>
   * @return The award.
   */
  java.lang.String getAward();
  /**
   * <code>string award = 17;</code>
   * @return The bytes for award.
   */
  com.google.protobuf.ByteString
      getAwardBytes();

  /**
   * <code>string url = 18;</code>
   * @return The url.
   */
  java.lang.String getUrl();
  /**
   * <code>string url = 18;</code>
   * @return The bytes for url.
   */
  com.google.protobuf.ByteString
      getUrlBytes();

  /**
   * <code>string icon = 19;</code>
   * @return The icon.
   */
  java.lang.String getIcon();
  /**
   * <code>string icon = 19;</code>
   * @return The bytes for icon.
   */
  com.google.protobuf.ByteString
      getIconBytes();

  /**
   * <code>.com.kikitrade.activity.facade.task.AwardTimeType awardTimeType = 20;</code>
   * @return The enum numeric value on the wire for awardTimeType.
   */
  int getAwardTimeTypeValue();
  /**
   * <code>.com.kikitrade.activity.facade.task.AwardTimeType awardTimeType = 20;</code>
   * @return The awardTimeType.
   */
  com.kikitrade.activity.facade.task.AwardTimeType getAwardTimeType();

  /**
   * <pre>
   *可完成次数
   * </pre>
   *
   * <code>string completeTimes = 21;</code>
   * @return The completeTimes.
   */
  java.lang.String getCompleteTimes();
  /**
   * <pre>
   *可完成次数
   * </pre>
   *
   * <code>string completeTimes = 21;</code>
   * @return The bytes for completeTimes.
   */
  com.google.protobuf.ByteString
      getCompleteTimesBytes();
}
