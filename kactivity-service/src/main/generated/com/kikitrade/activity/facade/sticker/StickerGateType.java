// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StickerFacade.proto

package com.kikitrade.activity.facade.sticker;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.sticker.StickerGateType}
 */
public enum StickerGateType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>all = 0;</code>
   */
  all(0),
  /**
   * <pre>
   *会员
   * </pre>
   *
   * <code>membership = 1;</code>
   */
  membership(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>all = 0;</code>
   */
  public static final int all_VALUE = 0;
  /**
   * <pre>
   *会员
   * </pre>
   *
   * <code>membership = 1;</code>
   */
  public static final int membership_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static StickerGateType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static StickerGateType forNumber(int value) {
    switch (value) {
      case 0: return all;
      case 1: return membership;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<StickerGateType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      StickerGateType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<StickerGateType>() {
          public StickerGateType findValueByNumber(int number) {
            return StickerGateType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.sticker.StickerFacadeOuterClass.getDescriptor().getEnumTypes().get(3);
  }

  private static final StickerGateType[] VALUES = values();

  public static StickerGateType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private StickerGateType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.sticker.StickerGateType)
}

