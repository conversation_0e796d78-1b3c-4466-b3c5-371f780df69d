// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: TaskFacade.proto

package com.kikitrade.activity.facade.task;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.task.TaskLevelType}
 */
public enum TaskLevelType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *日常任务
   * </pre>
   *
   * <code>NORMAL = 0;</code>
   */
  NORMAL(0),
  /**
   * <pre>
   *进阶任务
   * </pre>
   *
   * <code>ADVANCED = 1;</code>
   */
  ADVANCED(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *日常任务
   * </pre>
   *
   * <code>NORMAL = 0;</code>
   */
  public static final int NORMAL_VALUE = 0;
  /**
   * <pre>
   *进阶任务
   * </pre>
   *
   * <code>ADVANCED = 1;</code>
   */
  public static final int ADVANCED_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static TaskLevelType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static TaskLevelType forNumber(int value) {
    switch (value) {
      case 0: return NORMAL;
      case 1: return ADVANCED;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<TaskLevelType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      TaskLevelType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<TaskLevelType>() {
          public TaskLevelType findValueByNumber(int number) {
            return TaskLevelType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.task.ActivityTaskFacadeOutClass.getDescriptor().getEnumTypes().get(2);
  }

  private static final TaskLevelType[] VALUES = values();

  public static TaskLevelType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private TaskLevelType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.task.TaskLevelType)
}

