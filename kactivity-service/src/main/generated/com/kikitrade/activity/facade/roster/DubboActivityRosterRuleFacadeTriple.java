/*
* Licensed to the Apache Software Foundation (ASF) under one or more
* contributor license agreements.  See the NOTICE file distributed with
* this work for additional information regarding copyright ownership.
* The ASF licenses this file to You under the Apache License, Version 2.0
* (the "License"); you may not use this file except in compliance with
* the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

    package com.kikitrade.activity.facade.roster;

import org.apache.dubbo.common.stream.StreamObserver;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.PathResolver;
import org.apache.dubbo.rpc.RpcException;
import org.apache.dubbo.rpc.ServerService;
import org.apache.dubbo.rpc.TriRpcStatus;
import org.apache.dubbo.rpc.model.MethodDescriptor;
import org.apache.dubbo.rpc.model.ServiceDescriptor;
import org.apache.dubbo.rpc.model.StubMethodDescriptor;
import org.apache.dubbo.rpc.model.StubServiceDescriptor;
import org.apache.dubbo.rpc.stub.BiStreamMethodHandler;
import org.apache.dubbo.rpc.stub.ServerStreamMethodHandler;
import org.apache.dubbo.rpc.stub.StubInvocationUtil;
import org.apache.dubbo.rpc.stub.StubInvoker;
import org.apache.dubbo.rpc.stub.StubMethodHandler;
import org.apache.dubbo.rpc.stub.StubSuppliers;
import org.apache.dubbo.rpc.stub.UnaryStubMethodHandler;

import com.google.protobuf.Message;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.concurrent.CompletableFuture;

public final class DubboActivityRosterRuleFacadeTriple {

    public static final String SERVICE_NAME = ActivityRosterRuleFacade.SERVICE_NAME;

    private static final StubServiceDescriptor serviceDescriptor = new StubServiceDescriptor(SERVICE_NAME,ActivityRosterRuleFacade.class);

    static {
        org.apache.dubbo.rpc.protocol.tri.service.SchemaDescriptorRegistry.addSchemaDescriptor(SERVICE_NAME,ActivityRosterRuleFacadeOutClass.getDescriptor());
        StubSuppliers.addSupplier(SERVICE_NAME, DubboActivityRosterRuleFacadeTriple::newStub);
        StubSuppliers.addSupplier(ActivityRosterRuleFacade.JAVA_SERVICE_NAME,  DubboActivityRosterRuleFacadeTriple::newStub);
        StubSuppliers.addDescriptor(SERVICE_NAME, serviceDescriptor);
        StubSuppliers.addDescriptor(ActivityRosterRuleFacade.JAVA_SERVICE_NAME, serviceDescriptor);
    }

    @SuppressWarnings("all")
    public static ActivityRosterRuleFacade newStub(Invoker<?> invoker) {
        return new ActivityRosterRuleFacadeStub((Invoker<ActivityRosterRuleFacade>)invoker);
    }

    private static final StubMethodDescriptor getConditionNamesMethod = new StubMethodDescriptor("getConditionNames",
    com.kikitrade.activity.facade.roster.EmptyRequest.class, com.kikitrade.activity.facade.roster.ConditionParameterResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.roster.EmptyRequest::parseFrom,
    com.kikitrade.activity.facade.roster.ConditionParameterResponse::parseFrom);

    private static final StubMethodDescriptor getConditionNamesAsyncMethod = new StubMethodDescriptor("getConditionNames",
    com.kikitrade.activity.facade.roster.EmptyRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.roster.EmptyRequest::parseFrom,
    com.kikitrade.activity.facade.roster.ConditionParameterResponse::parseFrom);

    private static final StubMethodDescriptor getConditionNamesProxyAsyncMethod = new StubMethodDescriptor("getConditionNamesAsync",
    com.kikitrade.activity.facade.roster.EmptyRequest.class, com.kikitrade.activity.facade.roster.ConditionParameterResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.roster.EmptyRequest::parseFrom,
    com.kikitrade.activity.facade.roster.ConditionParameterResponse::parseFrom);

    private static final StubMethodDescriptor saveConditionMethod = new StubMethodDescriptor("saveCondition",
    com.kikitrade.activity.facade.roster.ConditionDTO.class, com.kikitrade.activity.facade.roster.CommonResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.roster.ConditionDTO::parseFrom,
    com.kikitrade.activity.facade.roster.CommonResponse::parseFrom);

    private static final StubMethodDescriptor saveConditionAsyncMethod = new StubMethodDescriptor("saveCondition",
    com.kikitrade.activity.facade.roster.ConditionDTO.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.roster.ConditionDTO::parseFrom,
    com.kikitrade.activity.facade.roster.CommonResponse::parseFrom);

    private static final StubMethodDescriptor saveConditionProxyAsyncMethod = new StubMethodDescriptor("saveConditionAsync",
    com.kikitrade.activity.facade.roster.ConditionDTO.class, com.kikitrade.activity.facade.roster.CommonResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.roster.ConditionDTO::parseFrom,
    com.kikitrade.activity.facade.roster.CommonResponse::parseFrom);





    public static class ActivityRosterRuleFacadeStub implements ActivityRosterRuleFacade{
        private final Invoker<ActivityRosterRuleFacade> invoker;

        public ActivityRosterRuleFacadeStub(Invoker<ActivityRosterRuleFacade> invoker) {
            this.invoker = invoker;
        }

        @Override
        public com.kikitrade.activity.facade.roster.ConditionParameterResponse getConditionNames(com.kikitrade.activity.facade.roster.EmptyRequest request){
            return StubInvocationUtil.unaryCall(invoker, getConditionNamesMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.roster.ConditionParameterResponse> getConditionNamesAsync(com.kikitrade.activity.facade.roster.EmptyRequest request){
            return StubInvocationUtil.unaryCall(invoker, getConditionNamesAsyncMethod, request);
        }

        @Override
        public void getConditionNames(com.kikitrade.activity.facade.roster.EmptyRequest request, StreamObserver<com.kikitrade.activity.facade.roster.ConditionParameterResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, getConditionNamesMethod , request, responseObserver);
        }
        @Override
        public com.kikitrade.activity.facade.roster.CommonResponse saveCondition(com.kikitrade.activity.facade.roster.ConditionDTO request){
            return StubInvocationUtil.unaryCall(invoker, saveConditionMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.roster.CommonResponse> saveConditionAsync(com.kikitrade.activity.facade.roster.ConditionDTO request){
            return StubInvocationUtil.unaryCall(invoker, saveConditionAsyncMethod, request);
        }

        @Override
        public void saveCondition(com.kikitrade.activity.facade.roster.ConditionDTO request, StreamObserver<com.kikitrade.activity.facade.roster.CommonResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, saveConditionMethod , request, responseObserver);
        }



    }

    public static abstract class ActivityRosterRuleFacadeImplBase implements ActivityRosterRuleFacade, ServerService<ActivityRosterRuleFacade> {

        private <T, R> BiConsumer<T, StreamObserver<R>> syncToAsync(java.util.function.Function<T, R> syncFun) {
            return new BiConsumer<T, StreamObserver<R>>() {
                @Override
                public void accept(T t, StreamObserver<R> observer) {
                    try {
                        R ret = syncFun.apply(t);
                        observer.onNext(ret);
                        observer.onCompleted();
                    } catch (Throwable e) {
                        observer.onError(e);
                    }
                }
            };
        }

        @Override
        public final Invoker<ActivityRosterRuleFacade> getInvoker(URL url) {
            PathResolver pathResolver = url.getOrDefaultFrameworkModel()
            .getExtensionLoader(PathResolver.class)
            .getDefaultExtension();
            Map<String,StubMethodHandler<?, ?>> handlers = new HashMap<>();

            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/getConditionNames" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/getConditionNamesAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/saveCondition" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/saveConditionAsync" );

            BiConsumer<com.kikitrade.activity.facade.roster.EmptyRequest, StreamObserver<com.kikitrade.activity.facade.roster.ConditionParameterResponse>> getConditionNamesFunc = this::getConditionNames;
            handlers.put(getConditionNamesMethod.getMethodName(), new UnaryStubMethodHandler<>(getConditionNamesFunc));
            BiConsumer<com.kikitrade.activity.facade.roster.EmptyRequest, StreamObserver<com.kikitrade.activity.facade.roster.ConditionParameterResponse>> getConditionNamesAsyncFunc = syncToAsync(this::getConditionNames);
            handlers.put(getConditionNamesProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(getConditionNamesAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.roster.ConditionDTO, StreamObserver<com.kikitrade.activity.facade.roster.CommonResponse>> saveConditionFunc = this::saveCondition;
            handlers.put(saveConditionMethod.getMethodName(), new UnaryStubMethodHandler<>(saveConditionFunc));
            BiConsumer<com.kikitrade.activity.facade.roster.ConditionDTO, StreamObserver<com.kikitrade.activity.facade.roster.CommonResponse>> saveConditionAsyncFunc = syncToAsync(this::saveCondition);
            handlers.put(saveConditionProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(saveConditionAsyncFunc));




            return new StubInvoker<>(this, url, ActivityRosterRuleFacade.class, handlers);
        }


        @Override
        public com.kikitrade.activity.facade.roster.ConditionParameterResponse getConditionNames(com.kikitrade.activity.facade.roster.EmptyRequest request){
            throw unimplementedMethodException(getConditionNamesMethod);
        }

        @Override
        public com.kikitrade.activity.facade.roster.CommonResponse saveCondition(com.kikitrade.activity.facade.roster.ConditionDTO request){
            throw unimplementedMethodException(saveConditionMethod);
        }





        @Override
        public final ServiceDescriptor getServiceDescriptor() {
            return serviceDescriptor;
        }
        private RpcException unimplementedMethodException(StubMethodDescriptor methodDescriptor) {
            return TriRpcStatus.UNIMPLEMENTED.withDescription(String.format("Method %s is unimplemented",
                "/" + serviceDescriptor.getInterfaceName() + "/" + methodDescriptor.getMethodName())).asException();
        }
    }

}
