// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface ModifyAwardResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.ModifyAwardResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
   */
  java.util.List<com.kikitrade.activity.facade.award.ModifyDetail> 
      getDetailList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
   */
  com.kikitrade.activity.facade.award.ModifyDetail getDetail(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
   */
  int getDetailCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.award.ModifyDetailOrBuilder> 
      getDetailOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
   */
  com.kikitrade.activity.facade.award.ModifyDetailOrBuilder getDetailOrBuilder(
      int index);
}
