// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Goods.proto

package com.kikitrade.activity.facade.goods;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.goods.GoodsStatus}
 */
public enum GoodsStatus
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *上架
   * </pre>
   *
   * <code>ONLINE = 0;</code>
   */
  ONLINE(0),
  /**
   * <pre>
   *下架
   * </pre>
   *
   * <code>OFFLINE = 1;</code>
   */
  OFFLINE(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *上架
   * </pre>
   *
   * <code>ONLINE = 0;</code>
   */
  public static final int ONLINE_VALUE = 0;
  /**
   * <pre>
   *下架
   * </pre>
   *
   * <code>OFFLINE = 1;</code>
   */
  public static final int OFFLINE_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static GoodsStatus valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static GoodsStatus forNumber(int value) {
    switch (value) {
      case 0: return ONLINE;
      case 1: return OFFLINE;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<GoodsStatus>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      GoodsStatus> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<GoodsStatus>() {
          public GoodsStatus findValueByNumber(int number) {
            return GoodsStatus.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.goods.GoodFacadeOutClass.getDescriptor().getEnumTypes().get(0);
  }

  private static final GoodsStatus[] VALUES = values();

  public static GoodsStatus valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private GoodsStatus(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.goods.GoodsStatus)
}

