/*
* Licensed to the Apache Software Foundation (ASF) under one or more
* contributor license agreements.  See the NOTICE file distributed with
* this work for additional information regarding copyright ownership.
* The ASF licenses this file to You under the Apache License, Version 2.0
* (the "License"); you may not use this file except in compliance with
* the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

    package com.kikitrade.activity.facade.award;

import org.apache.dubbo.common.stream.StreamObserver;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.PathResolver;
import org.apache.dubbo.rpc.RpcException;
import org.apache.dubbo.rpc.ServerService;
import org.apache.dubbo.rpc.TriRpcStatus;
import org.apache.dubbo.rpc.model.MethodDescriptor;
import org.apache.dubbo.rpc.model.ServiceDescriptor;
import org.apache.dubbo.rpc.model.StubMethodDescriptor;
import org.apache.dubbo.rpc.model.StubServiceDescriptor;
import org.apache.dubbo.rpc.stub.BiStreamMethodHandler;
import org.apache.dubbo.rpc.stub.ServerStreamMethodHandler;
import org.apache.dubbo.rpc.stub.StubInvocationUtil;
import org.apache.dubbo.rpc.stub.StubInvoker;
import org.apache.dubbo.rpc.stub.StubMethodHandler;
import org.apache.dubbo.rpc.stub.StubSuppliers;
import org.apache.dubbo.rpc.stub.UnaryStubMethodHandler;

import com.google.protobuf.Message;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.concurrent.CompletableFuture;

public final class DubboActivityFacadeTriple {

    public static final String SERVICE_NAME = ActivityFacade.SERVICE_NAME;

    private static final StubServiceDescriptor serviceDescriptor = new StubServiceDescriptor(SERVICE_NAME,ActivityFacade.class);

    static {
        org.apache.dubbo.rpc.protocol.tri.service.SchemaDescriptorRegistry.addSchemaDescriptor(SERVICE_NAME,ActivityFacadeOuterClass.getDescriptor());
        StubSuppliers.addSupplier(SERVICE_NAME, DubboActivityFacadeTriple::newStub);
        StubSuppliers.addSupplier(ActivityFacade.JAVA_SERVICE_NAME,  DubboActivityFacadeTriple::newStub);
        StubSuppliers.addDescriptor(SERVICE_NAME, serviceDescriptor);
        StubSuppliers.addDescriptor(ActivityFacade.JAVA_SERVICE_NAME, serviceDescriptor);
    }

    @SuppressWarnings("all")
    public static ActivityFacade newStub(Invoker<?> invoker) {
        return new ActivityFacadeStub((Invoker<ActivityFacade>)invoker);
    }

    private static final StubMethodDescriptor saveOrUpdateActivityMethod = new StubMethodDescriptor("saveOrUpdateActivity",
    com.kikitrade.activity.facade.award.ActivityDTO.class, com.kikitrade.activity.facade.award.ActivityResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ActivityDTO::parseFrom,
    com.kikitrade.activity.facade.award.ActivityResponse::parseFrom);

    private static final StubMethodDescriptor saveOrUpdateActivityAsyncMethod = new StubMethodDescriptor("saveOrUpdateActivity",
    com.kikitrade.activity.facade.award.ActivityDTO.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ActivityDTO::parseFrom,
    com.kikitrade.activity.facade.award.ActivityResponse::parseFrom);

    private static final StubMethodDescriptor saveOrUpdateActivityProxyAsyncMethod = new StubMethodDescriptor("saveOrUpdateActivityAsync",
    com.kikitrade.activity.facade.award.ActivityDTO.class, com.kikitrade.activity.facade.award.ActivityResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ActivityDTO::parseFrom,
    com.kikitrade.activity.facade.award.ActivityResponse::parseFrom);

    private static final StubMethodDescriptor saveOrUpdateBatchMethod = new StubMethodDescriptor("saveOrUpdateBatch",
    com.kikitrade.activity.facade.award.ActivityBatchDTO.class, com.kikitrade.activity.facade.award.ActivityBatchResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ActivityBatchDTO::parseFrom,
    com.kikitrade.activity.facade.award.ActivityBatchResponse::parseFrom);

    private static final StubMethodDescriptor saveOrUpdateBatchAsyncMethod = new StubMethodDescriptor("saveOrUpdateBatch",
    com.kikitrade.activity.facade.award.ActivityBatchDTO.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ActivityBatchDTO::parseFrom,
    com.kikitrade.activity.facade.award.ActivityBatchResponse::parseFrom);

    private static final StubMethodDescriptor saveOrUpdateBatchProxyAsyncMethod = new StubMethodDescriptor("saveOrUpdateBatchAsync",
    com.kikitrade.activity.facade.award.ActivityBatchDTO.class, com.kikitrade.activity.facade.award.ActivityBatchResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ActivityBatchDTO::parseFrom,
    com.kikitrade.activity.facade.award.ActivityBatchResponse::parseFrom);

    /**
         * <pre>
         * 自定义
         * </pre>
         */
    private static final StubMethodDescriptor deleteBatchMethod = new StubMethodDescriptor("deleteBatch",
    com.kikitrade.activity.facade.award.ActivityBatchDTO.class, com.kikitrade.activity.facade.award.ActivityBatchResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ActivityBatchDTO::parseFrom,
    com.kikitrade.activity.facade.award.ActivityBatchResponse::parseFrom);

    private static final StubMethodDescriptor deleteBatchAsyncMethod = new StubMethodDescriptor("deleteBatch",
    com.kikitrade.activity.facade.award.ActivityBatchDTO.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ActivityBatchDTO::parseFrom,
    com.kikitrade.activity.facade.award.ActivityBatchResponse::parseFrom);

    private static final StubMethodDescriptor deleteBatchProxyAsyncMethod = new StubMethodDescriptor("deleteBatchAsync",
    com.kikitrade.activity.facade.award.ActivityBatchDTO.class, com.kikitrade.activity.facade.award.ActivityBatchResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ActivityBatchDTO::parseFrom,
    com.kikitrade.activity.facade.award.ActivityBatchResponse::parseFrom);

    /**
         * <pre>
         * 普通
         * </pre>
         */
    private static final StubMethodDescriptor queryBatchForListMethod = new StubMethodDescriptor("queryBatchForList",
    com.kikitrade.activity.facade.award.ActivityBatchRequest.class, com.kikitrade.activity.facade.award.ActivityBatchListResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ActivityBatchRequest::parseFrom,
    com.kikitrade.activity.facade.award.ActivityBatchListResponse::parseFrom);

    private static final StubMethodDescriptor queryBatchForListAsyncMethod = new StubMethodDescriptor("queryBatchForList",
    com.kikitrade.activity.facade.award.ActivityBatchRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ActivityBatchRequest::parseFrom,
    com.kikitrade.activity.facade.award.ActivityBatchListResponse::parseFrom);

    private static final StubMethodDescriptor queryBatchForListProxyAsyncMethod = new StubMethodDescriptor("queryBatchForListAsync",
    com.kikitrade.activity.facade.award.ActivityBatchRequest.class, com.kikitrade.activity.facade.award.ActivityBatchListResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ActivityBatchRequest::parseFrom,
    com.kikitrade.activity.facade.award.ActivityBatchListResponse::parseFrom);

    private static final StubMethodDescriptor queryDetailMethod = new StubMethodDescriptor("queryDetail",
    com.kikitrade.activity.facade.award.ActivityBatchDetailRequest.class, com.kikitrade.activity.facade.award.ActivityBatch.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ActivityBatchDetailRequest::parseFrom,
    com.kikitrade.activity.facade.award.ActivityBatch::parseFrom);

    private static final StubMethodDescriptor queryDetailAsyncMethod = new StubMethodDescriptor("queryDetail",
    com.kikitrade.activity.facade.award.ActivityBatchDetailRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ActivityBatchDetailRequest::parseFrom,
    com.kikitrade.activity.facade.award.ActivityBatch::parseFrom);

    private static final StubMethodDescriptor queryDetailProxyAsyncMethod = new StubMethodDescriptor("queryDetailAsync",
    com.kikitrade.activity.facade.award.ActivityBatchDetailRequest.class, com.kikitrade.activity.facade.award.ActivityBatch.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ActivityBatchDetailRequest::parseFrom,
    com.kikitrade.activity.facade.award.ActivityBatch::parseFrom);

    private static final StubMethodDescriptor auditMethod = new StubMethodDescriptor("audit",
    com.kikitrade.activity.facade.award.AuditRequest.class, com.kikitrade.activity.facade.award.AuditResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.AuditRequest::parseFrom,
    com.kikitrade.activity.facade.award.AuditResponse::parseFrom);

    private static final StubMethodDescriptor auditAsyncMethod = new StubMethodDescriptor("audit",
    com.kikitrade.activity.facade.award.AuditRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.AuditRequest::parseFrom,
    com.kikitrade.activity.facade.award.AuditResponse::parseFrom);

    private static final StubMethodDescriptor auditProxyAsyncMethod = new StubMethodDescriptor("auditAsync",
    com.kikitrade.activity.facade.award.AuditRequest.class, com.kikitrade.activity.facade.award.AuditResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.AuditRequest::parseFrom,
    com.kikitrade.activity.facade.award.AuditResponse::parseFrom);

    private static final StubMethodDescriptor queryRewardListMethod = new StubMethodDescriptor("queryRewardList",
    com.kikitrade.activity.facade.award.AwardRequest.class, com.kikitrade.activity.facade.award.AwardListResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.AwardRequest::parseFrom,
    com.kikitrade.activity.facade.award.AwardListResponse::parseFrom);

    private static final StubMethodDescriptor queryRewardListAsyncMethod = new StubMethodDescriptor("queryRewardList",
    com.kikitrade.activity.facade.award.AwardRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.AwardRequest::parseFrom,
    com.kikitrade.activity.facade.award.AwardListResponse::parseFrom);

    private static final StubMethodDescriptor queryRewardListProxyAsyncMethod = new StubMethodDescriptor("queryRewardListAsync",
    com.kikitrade.activity.facade.award.AwardRequest.class, com.kikitrade.activity.facade.award.AwardListResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.AwardRequest::parseFrom,
    com.kikitrade.activity.facade.award.AwardListResponse::parseFrom);

    private static final StubMethodDescriptor deleteRewardMethod = new StubMethodDescriptor("deleteReward",
    com.kikitrade.activity.facade.award.AwardDTO.class, com.kikitrade.activity.facade.award.ModifyAwardResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.AwardDTO::parseFrom,
    com.kikitrade.activity.facade.award.ModifyAwardResponse::parseFrom);

    private static final StubMethodDescriptor deleteRewardAsyncMethod = new StubMethodDescriptor("deleteReward",
    com.kikitrade.activity.facade.award.AwardDTO.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.AwardDTO::parseFrom,
    com.kikitrade.activity.facade.award.ModifyAwardResponse::parseFrom);

    private static final StubMethodDescriptor deleteRewardProxyAsyncMethod = new StubMethodDescriptor("deleteRewardAsync",
    com.kikitrade.activity.facade.award.AwardDTO.class, com.kikitrade.activity.facade.award.ModifyAwardResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.AwardDTO::parseFrom,
    com.kikitrade.activity.facade.award.ModifyAwardResponse::parseFrom);

    private static final StubMethodDescriptor uploadFileMethod = new StubMethodDescriptor("uploadFile",
    com.kikitrade.activity.facade.award.UploadRequest.class, com.kikitrade.activity.facade.award.UploadResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.UploadRequest::parseFrom,
    com.kikitrade.activity.facade.award.UploadResponse::parseFrom);

    private static final StubMethodDescriptor uploadFileAsyncMethod = new StubMethodDescriptor("uploadFile",
    com.kikitrade.activity.facade.award.UploadRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.UploadRequest::parseFrom,
    com.kikitrade.activity.facade.award.UploadResponse::parseFrom);

    private static final StubMethodDescriptor uploadFileProxyAsyncMethod = new StubMethodDescriptor("uploadFileAsync",
    com.kikitrade.activity.facade.award.UploadRequest.class, com.kikitrade.activity.facade.award.UploadResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.UploadRequest::parseFrom,
    com.kikitrade.activity.facade.award.UploadResponse::parseFrom);

    private static final StubMethodDescriptor exportDataMethod = new StubMethodDescriptor("exportData",
    com.kikitrade.activity.facade.award.ExportDataRequest.class, com.kikitrade.activity.facade.award.ExportDataResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ExportDataRequest::parseFrom,
    com.kikitrade.activity.facade.award.ExportDataResponse::parseFrom);

    private static final StubMethodDescriptor exportDataAsyncMethod = new StubMethodDescriptor("exportData",
    com.kikitrade.activity.facade.award.ExportDataRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ExportDataRequest::parseFrom,
    com.kikitrade.activity.facade.award.ExportDataResponse::parseFrom);

    private static final StubMethodDescriptor exportDataProxyAsyncMethod = new StubMethodDescriptor("exportDataAsync",
    com.kikitrade.activity.facade.award.ExportDataRequest.class, com.kikitrade.activity.facade.award.ExportDataResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ExportDataRequest::parseFrom,
    com.kikitrade.activity.facade.award.ExportDataResponse::parseFrom);

    private static final StubMethodDescriptor importDataMethod = new StubMethodDescriptor("importData",
    com.kikitrade.activity.facade.award.ImportDataRequest.class, com.kikitrade.activity.facade.award.ImportDataResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ImportDataRequest::parseFrom,
    com.kikitrade.activity.facade.award.ImportDataResponse::parseFrom);

    private static final StubMethodDescriptor importDataAsyncMethod = new StubMethodDescriptor("importData",
    com.kikitrade.activity.facade.award.ImportDataRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ImportDataRequest::parseFrom,
    com.kikitrade.activity.facade.award.ImportDataResponse::parseFrom);

    private static final StubMethodDescriptor importDataProxyAsyncMethod = new StubMethodDescriptor("importDataAsync",
    com.kikitrade.activity.facade.award.ImportDataRequest.class, com.kikitrade.activity.facade.award.ImportDataResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ImportDataRequest::parseFrom,
    com.kikitrade.activity.facade.award.ImportDataResponse::parseFrom);

    private static final StubMethodDescriptor saveLotteryMethod = new StubMethodDescriptor("saveLottery",
    com.kikitrade.activity.facade.award.LotteryDTO.class, com.kikitrade.activity.facade.award.LotteryResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.LotteryDTO::parseFrom,
    com.kikitrade.activity.facade.award.LotteryResponse::parseFrom);

    private static final StubMethodDescriptor saveLotteryAsyncMethod = new StubMethodDescriptor("saveLottery",
    com.kikitrade.activity.facade.award.LotteryDTO.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.LotteryDTO::parseFrom,
    com.kikitrade.activity.facade.award.LotteryResponse::parseFrom);

    private static final StubMethodDescriptor saveLotteryProxyAsyncMethod = new StubMethodDescriptor("saveLotteryAsync",
    com.kikitrade.activity.facade.award.LotteryDTO.class, com.kikitrade.activity.facade.award.LotteryResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.LotteryDTO::parseFrom,
    com.kikitrade.activity.facade.award.LotteryResponse::parseFrom);

    private static final StubMethodDescriptor deleteLotteryMethod = new StubMethodDescriptor("deleteLottery",
    com.kikitrade.activity.facade.award.LotteryDeleteDTO.class, com.kikitrade.activity.facade.award.LotteryResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.LotteryDeleteDTO::parseFrom,
    com.kikitrade.activity.facade.award.LotteryResponse::parseFrom);

    private static final StubMethodDescriptor deleteLotteryAsyncMethod = new StubMethodDescriptor("deleteLottery",
    com.kikitrade.activity.facade.award.LotteryDeleteDTO.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.LotteryDeleteDTO::parseFrom,
    com.kikitrade.activity.facade.award.LotteryResponse::parseFrom);

    private static final StubMethodDescriptor deleteLotteryProxyAsyncMethod = new StubMethodDescriptor("deleteLotteryAsync",
    com.kikitrade.activity.facade.award.LotteryDeleteDTO.class, com.kikitrade.activity.facade.award.LotteryResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.LotteryDeleteDTO::parseFrom,
    com.kikitrade.activity.facade.award.LotteryResponse::parseFrom);

    private static final StubMethodDescriptor lotteryListMethod = new StubMethodDescriptor("lotteryList",
    com.kikitrade.activity.facade.award.LotteryRequest.class, com.kikitrade.activity.facade.award.LotteryListResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.LotteryRequest::parseFrom,
    com.kikitrade.activity.facade.award.LotteryListResponse::parseFrom);

    private static final StubMethodDescriptor lotteryListAsyncMethod = new StubMethodDescriptor("lotteryList",
    com.kikitrade.activity.facade.award.LotteryRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.LotteryRequest::parseFrom,
    com.kikitrade.activity.facade.award.LotteryListResponse::parseFrom);

    private static final StubMethodDescriptor lotteryListProxyAsyncMethod = new StubMethodDescriptor("lotteryListAsync",
    com.kikitrade.activity.facade.award.LotteryRequest.class, com.kikitrade.activity.facade.award.LotteryListResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.LotteryRequest::parseFrom,
    com.kikitrade.activity.facade.award.LotteryListResponse::parseFrom);

    private static final StubMethodDescriptor lotteryDetailMethod = new StubMethodDescriptor("lotteryDetail",
    com.kikitrade.activity.facade.award.LotteryDetailRequest.class, com.kikitrade.activity.facade.award.LotteryVO.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.LotteryDetailRequest::parseFrom,
    com.kikitrade.activity.facade.award.LotteryVO::parseFrom);

    private static final StubMethodDescriptor lotteryDetailAsyncMethod = new StubMethodDescriptor("lotteryDetail",
    com.kikitrade.activity.facade.award.LotteryDetailRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.LotteryDetailRequest::parseFrom,
    com.kikitrade.activity.facade.award.LotteryVO::parseFrom);

    private static final StubMethodDescriptor lotteryDetailProxyAsyncMethod = new StubMethodDescriptor("lotteryDetailAsync",
    com.kikitrade.activity.facade.award.LotteryDetailRequest.class, com.kikitrade.activity.facade.award.LotteryVO.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.LotteryDetailRequest::parseFrom,
    com.kikitrade.activity.facade.award.LotteryVO::parseFrom);

    /**
         * <pre>
         *  子活动类型
         * </pre>
         */
    private static final StubMethodDescriptor getConditionCodesMethod = new StubMethodDescriptor("getConditionCodes",
    com.kikitrade.activity.facade.award.EmptyRequest.class, com.kikitrade.activity.facade.award.ConditionCode.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.EmptyRequest::parseFrom,
    com.kikitrade.activity.facade.award.ConditionCode::parseFrom);

    private static final StubMethodDescriptor getConditionCodesAsyncMethod = new StubMethodDescriptor("getConditionCodes",
    com.kikitrade.activity.facade.award.EmptyRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.EmptyRequest::parseFrom,
    com.kikitrade.activity.facade.award.ConditionCode::parseFrom);

    private static final StubMethodDescriptor getConditionCodesProxyAsyncMethod = new StubMethodDescriptor("getConditionCodesAsync",
    com.kikitrade.activity.facade.award.EmptyRequest.class, com.kikitrade.activity.facade.award.ConditionCode.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.EmptyRequest::parseFrom,
    com.kikitrade.activity.facade.award.ConditionCode::parseFrom);

    /**
         * <pre>
         * 下载地址
         * </pre>
         */
    private static final StubMethodDescriptor getConditionMethod = new StubMethodDescriptor("getCondition",
    com.kikitrade.activity.facade.award.ConditionRequest.class, com.kikitrade.activity.facade.award.ConditionResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ConditionRequest::parseFrom,
    com.kikitrade.activity.facade.award.ConditionResponse::parseFrom);

    private static final StubMethodDescriptor getConditionAsyncMethod = new StubMethodDescriptor("getCondition",
    com.kikitrade.activity.facade.award.ConditionRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ConditionRequest::parseFrom,
    com.kikitrade.activity.facade.award.ConditionResponse::parseFrom);

    private static final StubMethodDescriptor getConditionProxyAsyncMethod = new StubMethodDescriptor("getConditionAsync",
    com.kikitrade.activity.facade.award.ConditionRequest.class, com.kikitrade.activity.facade.award.ConditionResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.award.ConditionRequest::parseFrom,
    com.kikitrade.activity.facade.award.ConditionResponse::parseFrom);





    public static class ActivityFacadeStub implements ActivityFacade{
        private final Invoker<ActivityFacade> invoker;

        public ActivityFacadeStub(Invoker<ActivityFacade> invoker) {
            this.invoker = invoker;
        }

        @Override
        public com.kikitrade.activity.facade.award.ActivityResponse saveOrUpdateActivity(com.kikitrade.activity.facade.award.ActivityDTO request){
            return StubInvocationUtil.unaryCall(invoker, saveOrUpdateActivityMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.ActivityResponse> saveOrUpdateActivityAsync(com.kikitrade.activity.facade.award.ActivityDTO request){
            return StubInvocationUtil.unaryCall(invoker, saveOrUpdateActivityAsyncMethod, request);
        }

        @Override
        public void saveOrUpdateActivity(com.kikitrade.activity.facade.award.ActivityDTO request, StreamObserver<com.kikitrade.activity.facade.award.ActivityResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, saveOrUpdateActivityMethod , request, responseObserver);
        }
        @Override
        public com.kikitrade.activity.facade.award.ActivityBatchResponse saveOrUpdateBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request){
            return StubInvocationUtil.unaryCall(invoker, saveOrUpdateBatchMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.ActivityBatchResponse> saveOrUpdateBatchAsync(com.kikitrade.activity.facade.award.ActivityBatchDTO request){
            return StubInvocationUtil.unaryCall(invoker, saveOrUpdateBatchAsyncMethod, request);
        }

        @Override
        public void saveOrUpdateBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, saveOrUpdateBatchMethod , request, responseObserver);
        }
            /**
         * <pre>
         * 自定义
         * </pre>
         */
        @Override
        public com.kikitrade.activity.facade.award.ActivityBatchResponse deleteBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request){
            return StubInvocationUtil.unaryCall(invoker, deleteBatchMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.ActivityBatchResponse> deleteBatchAsync(com.kikitrade.activity.facade.award.ActivityBatchDTO request){
            return StubInvocationUtil.unaryCall(invoker, deleteBatchAsyncMethod, request);
        }

            /**
         * <pre>
         * 自定义
         * </pre>
         */
        @Override
        public void deleteBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, deleteBatchMethod , request, responseObserver);
        }
            /**
         * <pre>
         * 普通
         * </pre>
         */
        @Override
        public com.kikitrade.activity.facade.award.ActivityBatchListResponse queryBatchForList(com.kikitrade.activity.facade.award.ActivityBatchRequest request){
            return StubInvocationUtil.unaryCall(invoker, queryBatchForListMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.ActivityBatchListResponse> queryBatchForListAsync(com.kikitrade.activity.facade.award.ActivityBatchRequest request){
            return StubInvocationUtil.unaryCall(invoker, queryBatchForListAsyncMethod, request);
        }

            /**
         * <pre>
         * 普通
         * </pre>
         */
        @Override
        public void queryBatchForList(com.kikitrade.activity.facade.award.ActivityBatchRequest request, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchListResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, queryBatchForListMethod , request, responseObserver);
        }
        @Override
        public com.kikitrade.activity.facade.award.ActivityBatch queryDetail(com.kikitrade.activity.facade.award.ActivityBatchDetailRequest request){
            return StubInvocationUtil.unaryCall(invoker, queryDetailMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.ActivityBatch> queryDetailAsync(com.kikitrade.activity.facade.award.ActivityBatchDetailRequest request){
            return StubInvocationUtil.unaryCall(invoker, queryDetailAsyncMethod, request);
        }

        @Override
        public void queryDetail(com.kikitrade.activity.facade.award.ActivityBatchDetailRequest request, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatch> responseObserver){
            StubInvocationUtil.unaryCall(invoker, queryDetailMethod , request, responseObserver);
        }
        @Override
        public com.kikitrade.activity.facade.award.AuditResponse audit(com.kikitrade.activity.facade.award.AuditRequest request){
            return StubInvocationUtil.unaryCall(invoker, auditMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.AuditResponse> auditAsync(com.kikitrade.activity.facade.award.AuditRequest request){
            return StubInvocationUtil.unaryCall(invoker, auditAsyncMethod, request);
        }

        @Override
        public void audit(com.kikitrade.activity.facade.award.AuditRequest request, StreamObserver<com.kikitrade.activity.facade.award.AuditResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, auditMethod , request, responseObserver);
        }
        @Override
        public com.kikitrade.activity.facade.award.AwardListResponse queryRewardList(com.kikitrade.activity.facade.award.AwardRequest request){
            return StubInvocationUtil.unaryCall(invoker, queryRewardListMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.AwardListResponse> queryRewardListAsync(com.kikitrade.activity.facade.award.AwardRequest request){
            return StubInvocationUtil.unaryCall(invoker, queryRewardListAsyncMethod, request);
        }

        @Override
        public void queryRewardList(com.kikitrade.activity.facade.award.AwardRequest request, StreamObserver<com.kikitrade.activity.facade.award.AwardListResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, queryRewardListMethod , request, responseObserver);
        }
        @Override
        public com.kikitrade.activity.facade.award.ModifyAwardResponse deleteReward(com.kikitrade.activity.facade.award.AwardDTO request){
            return StubInvocationUtil.unaryCall(invoker, deleteRewardMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.ModifyAwardResponse> deleteRewardAsync(com.kikitrade.activity.facade.award.AwardDTO request){
            return StubInvocationUtil.unaryCall(invoker, deleteRewardAsyncMethod, request);
        }

        @Override
        public void deleteReward(com.kikitrade.activity.facade.award.AwardDTO request, StreamObserver<com.kikitrade.activity.facade.award.ModifyAwardResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, deleteRewardMethod , request, responseObserver);
        }
        @Override
        public com.kikitrade.activity.facade.award.UploadResponse uploadFile(com.kikitrade.activity.facade.award.UploadRequest request){
            return StubInvocationUtil.unaryCall(invoker, uploadFileMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.UploadResponse> uploadFileAsync(com.kikitrade.activity.facade.award.UploadRequest request){
            return StubInvocationUtil.unaryCall(invoker, uploadFileAsyncMethod, request);
        }

        @Override
        public void uploadFile(com.kikitrade.activity.facade.award.UploadRequest request, StreamObserver<com.kikitrade.activity.facade.award.UploadResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, uploadFileMethod , request, responseObserver);
        }
        @Override
        public com.kikitrade.activity.facade.award.ExportDataResponse exportData(com.kikitrade.activity.facade.award.ExportDataRequest request){
            return StubInvocationUtil.unaryCall(invoker, exportDataMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.ExportDataResponse> exportDataAsync(com.kikitrade.activity.facade.award.ExportDataRequest request){
            return StubInvocationUtil.unaryCall(invoker, exportDataAsyncMethod, request);
        }

        @Override
        public void exportData(com.kikitrade.activity.facade.award.ExportDataRequest request, StreamObserver<com.kikitrade.activity.facade.award.ExportDataResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, exportDataMethod , request, responseObserver);
        }
        @Override
        public com.kikitrade.activity.facade.award.ImportDataResponse importData(com.kikitrade.activity.facade.award.ImportDataRequest request){
            return StubInvocationUtil.unaryCall(invoker, importDataMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.ImportDataResponse> importDataAsync(com.kikitrade.activity.facade.award.ImportDataRequest request){
            return StubInvocationUtil.unaryCall(invoker, importDataAsyncMethod, request);
        }

        @Override
        public void importData(com.kikitrade.activity.facade.award.ImportDataRequest request, StreamObserver<com.kikitrade.activity.facade.award.ImportDataResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, importDataMethod , request, responseObserver);
        }
        @Override
        public com.kikitrade.activity.facade.award.LotteryResponse saveLottery(com.kikitrade.activity.facade.award.LotteryDTO request){
            return StubInvocationUtil.unaryCall(invoker, saveLotteryMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.LotteryResponse> saveLotteryAsync(com.kikitrade.activity.facade.award.LotteryDTO request){
            return StubInvocationUtil.unaryCall(invoker, saveLotteryAsyncMethod, request);
        }

        @Override
        public void saveLottery(com.kikitrade.activity.facade.award.LotteryDTO request, StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, saveLotteryMethod , request, responseObserver);
        }
        @Override
        public com.kikitrade.activity.facade.award.LotteryResponse deleteLottery(com.kikitrade.activity.facade.award.LotteryDeleteDTO request){
            return StubInvocationUtil.unaryCall(invoker, deleteLotteryMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.LotteryResponse> deleteLotteryAsync(com.kikitrade.activity.facade.award.LotteryDeleteDTO request){
            return StubInvocationUtil.unaryCall(invoker, deleteLotteryAsyncMethod, request);
        }

        @Override
        public void deleteLottery(com.kikitrade.activity.facade.award.LotteryDeleteDTO request, StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, deleteLotteryMethod , request, responseObserver);
        }
        @Override
        public com.kikitrade.activity.facade.award.LotteryListResponse lotteryList(com.kikitrade.activity.facade.award.LotteryRequest request){
            return StubInvocationUtil.unaryCall(invoker, lotteryListMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.LotteryListResponse> lotteryListAsync(com.kikitrade.activity.facade.award.LotteryRequest request){
            return StubInvocationUtil.unaryCall(invoker, lotteryListAsyncMethod, request);
        }

        @Override
        public void lotteryList(com.kikitrade.activity.facade.award.LotteryRequest request, StreamObserver<com.kikitrade.activity.facade.award.LotteryListResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, lotteryListMethod , request, responseObserver);
        }
        @Override
        public com.kikitrade.activity.facade.award.LotteryVO lotteryDetail(com.kikitrade.activity.facade.award.LotteryDetailRequest request){
            return StubInvocationUtil.unaryCall(invoker, lotteryDetailMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.LotteryVO> lotteryDetailAsync(com.kikitrade.activity.facade.award.LotteryDetailRequest request){
            return StubInvocationUtil.unaryCall(invoker, lotteryDetailAsyncMethod, request);
        }

        @Override
        public void lotteryDetail(com.kikitrade.activity.facade.award.LotteryDetailRequest request, StreamObserver<com.kikitrade.activity.facade.award.LotteryVO> responseObserver){
            StubInvocationUtil.unaryCall(invoker, lotteryDetailMethod , request, responseObserver);
        }
            /**
         * <pre>
         *  子活动类型
         * </pre>
         */
        @Override
        public com.kikitrade.activity.facade.award.ConditionCode getConditionCodes(com.kikitrade.activity.facade.award.EmptyRequest request){
            return StubInvocationUtil.unaryCall(invoker, getConditionCodesMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.ConditionCode> getConditionCodesAsync(com.kikitrade.activity.facade.award.EmptyRequest request){
            return StubInvocationUtil.unaryCall(invoker, getConditionCodesAsyncMethod, request);
        }

            /**
         * <pre>
         *  子活动类型
         * </pre>
         */
        @Override
        public void getConditionCodes(com.kikitrade.activity.facade.award.EmptyRequest request, StreamObserver<com.kikitrade.activity.facade.award.ConditionCode> responseObserver){
            StubInvocationUtil.unaryCall(invoker, getConditionCodesMethod , request, responseObserver);
        }
            /**
         * <pre>
         * 下载地址
         * </pre>
         */
        @Override
        public com.kikitrade.activity.facade.award.ConditionResponse getCondition(com.kikitrade.activity.facade.award.ConditionRequest request){
            return StubInvocationUtil.unaryCall(invoker, getConditionMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.award.ConditionResponse> getConditionAsync(com.kikitrade.activity.facade.award.ConditionRequest request){
            return StubInvocationUtil.unaryCall(invoker, getConditionAsyncMethod, request);
        }

            /**
         * <pre>
         * 下载地址
         * </pre>
         */
        @Override
        public void getCondition(com.kikitrade.activity.facade.award.ConditionRequest request, StreamObserver<com.kikitrade.activity.facade.award.ConditionResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, getConditionMethod , request, responseObserver);
        }



    }

    public static abstract class ActivityFacadeImplBase implements ActivityFacade, ServerService<ActivityFacade> {

        private <T, R> BiConsumer<T, StreamObserver<R>> syncToAsync(java.util.function.Function<T, R> syncFun) {
            return new BiConsumer<T, StreamObserver<R>>() {
                @Override
                public void accept(T t, StreamObserver<R> observer) {
                    try {
                        R ret = syncFun.apply(t);
                        observer.onNext(ret);
                        observer.onCompleted();
                    } catch (Throwable e) {
                        observer.onError(e);
                    }
                }
            };
        }

        @Override
        public final Invoker<ActivityFacade> getInvoker(URL url) {
            PathResolver pathResolver = url.getOrDefaultFrameworkModel()
            .getExtensionLoader(PathResolver.class)
            .getDefaultExtension();
            Map<String,StubMethodHandler<?, ?>> handlers = new HashMap<>();

            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/saveOrUpdateActivity" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/saveOrUpdateActivityAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/saveOrUpdateBatch" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/saveOrUpdateBatchAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/deleteBatch" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/deleteBatchAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/queryBatchForList" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/queryBatchForListAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/queryDetail" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/queryDetailAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/audit" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/auditAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/queryRewardList" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/queryRewardListAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/deleteReward" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/deleteRewardAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/uploadFile" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/uploadFileAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/exportData" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/exportDataAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/importData" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/importDataAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/saveLottery" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/saveLotteryAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/deleteLottery" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/deleteLotteryAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/lotteryList" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/lotteryListAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/lotteryDetail" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/lotteryDetailAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/getConditionCodes" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/getConditionCodesAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/getCondition" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/getConditionAsync" );

            BiConsumer<com.kikitrade.activity.facade.award.ActivityDTO, StreamObserver<com.kikitrade.activity.facade.award.ActivityResponse>> saveOrUpdateActivityFunc = this::saveOrUpdateActivity;
            handlers.put(saveOrUpdateActivityMethod.getMethodName(), new UnaryStubMethodHandler<>(saveOrUpdateActivityFunc));
            BiConsumer<com.kikitrade.activity.facade.award.ActivityDTO, StreamObserver<com.kikitrade.activity.facade.award.ActivityResponse>> saveOrUpdateActivityAsyncFunc = syncToAsync(this::saveOrUpdateActivity);
            handlers.put(saveOrUpdateActivityProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(saveOrUpdateActivityAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.ActivityBatchDTO, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse>> saveOrUpdateBatchFunc = this::saveOrUpdateBatch;
            handlers.put(saveOrUpdateBatchMethod.getMethodName(), new UnaryStubMethodHandler<>(saveOrUpdateBatchFunc));
            BiConsumer<com.kikitrade.activity.facade.award.ActivityBatchDTO, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse>> saveOrUpdateBatchAsyncFunc = syncToAsync(this::saveOrUpdateBatch);
            handlers.put(saveOrUpdateBatchProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(saveOrUpdateBatchAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.ActivityBatchDTO, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse>> deleteBatchFunc = this::deleteBatch;
            handlers.put(deleteBatchMethod.getMethodName(), new UnaryStubMethodHandler<>(deleteBatchFunc));
            BiConsumer<com.kikitrade.activity.facade.award.ActivityBatchDTO, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse>> deleteBatchAsyncFunc = syncToAsync(this::deleteBatch);
            handlers.put(deleteBatchProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(deleteBatchAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.ActivityBatchRequest, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchListResponse>> queryBatchForListFunc = this::queryBatchForList;
            handlers.put(queryBatchForListMethod.getMethodName(), new UnaryStubMethodHandler<>(queryBatchForListFunc));
            BiConsumer<com.kikitrade.activity.facade.award.ActivityBatchRequest, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchListResponse>> queryBatchForListAsyncFunc = syncToAsync(this::queryBatchForList);
            handlers.put(queryBatchForListProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(queryBatchForListAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.ActivityBatchDetailRequest, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatch>> queryDetailFunc = this::queryDetail;
            handlers.put(queryDetailMethod.getMethodName(), new UnaryStubMethodHandler<>(queryDetailFunc));
            BiConsumer<com.kikitrade.activity.facade.award.ActivityBatchDetailRequest, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatch>> queryDetailAsyncFunc = syncToAsync(this::queryDetail);
            handlers.put(queryDetailProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(queryDetailAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.AuditRequest, StreamObserver<com.kikitrade.activity.facade.award.AuditResponse>> auditFunc = this::audit;
            handlers.put(auditMethod.getMethodName(), new UnaryStubMethodHandler<>(auditFunc));
            BiConsumer<com.kikitrade.activity.facade.award.AuditRequest, StreamObserver<com.kikitrade.activity.facade.award.AuditResponse>> auditAsyncFunc = syncToAsync(this::audit);
            handlers.put(auditProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(auditAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.AwardRequest, StreamObserver<com.kikitrade.activity.facade.award.AwardListResponse>> queryRewardListFunc = this::queryRewardList;
            handlers.put(queryRewardListMethod.getMethodName(), new UnaryStubMethodHandler<>(queryRewardListFunc));
            BiConsumer<com.kikitrade.activity.facade.award.AwardRequest, StreamObserver<com.kikitrade.activity.facade.award.AwardListResponse>> queryRewardListAsyncFunc = syncToAsync(this::queryRewardList);
            handlers.put(queryRewardListProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(queryRewardListAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.AwardDTO, StreamObserver<com.kikitrade.activity.facade.award.ModifyAwardResponse>> deleteRewardFunc = this::deleteReward;
            handlers.put(deleteRewardMethod.getMethodName(), new UnaryStubMethodHandler<>(deleteRewardFunc));
            BiConsumer<com.kikitrade.activity.facade.award.AwardDTO, StreamObserver<com.kikitrade.activity.facade.award.ModifyAwardResponse>> deleteRewardAsyncFunc = syncToAsync(this::deleteReward);
            handlers.put(deleteRewardProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(deleteRewardAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.UploadRequest, StreamObserver<com.kikitrade.activity.facade.award.UploadResponse>> uploadFileFunc = this::uploadFile;
            handlers.put(uploadFileMethod.getMethodName(), new UnaryStubMethodHandler<>(uploadFileFunc));
            BiConsumer<com.kikitrade.activity.facade.award.UploadRequest, StreamObserver<com.kikitrade.activity.facade.award.UploadResponse>> uploadFileAsyncFunc = syncToAsync(this::uploadFile);
            handlers.put(uploadFileProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(uploadFileAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.ExportDataRequest, StreamObserver<com.kikitrade.activity.facade.award.ExportDataResponse>> exportDataFunc = this::exportData;
            handlers.put(exportDataMethod.getMethodName(), new UnaryStubMethodHandler<>(exportDataFunc));
            BiConsumer<com.kikitrade.activity.facade.award.ExportDataRequest, StreamObserver<com.kikitrade.activity.facade.award.ExportDataResponse>> exportDataAsyncFunc = syncToAsync(this::exportData);
            handlers.put(exportDataProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(exportDataAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.ImportDataRequest, StreamObserver<com.kikitrade.activity.facade.award.ImportDataResponse>> importDataFunc = this::importData;
            handlers.put(importDataMethod.getMethodName(), new UnaryStubMethodHandler<>(importDataFunc));
            BiConsumer<com.kikitrade.activity.facade.award.ImportDataRequest, StreamObserver<com.kikitrade.activity.facade.award.ImportDataResponse>> importDataAsyncFunc = syncToAsync(this::importData);
            handlers.put(importDataProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(importDataAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.LotteryDTO, StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse>> saveLotteryFunc = this::saveLottery;
            handlers.put(saveLotteryMethod.getMethodName(), new UnaryStubMethodHandler<>(saveLotteryFunc));
            BiConsumer<com.kikitrade.activity.facade.award.LotteryDTO, StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse>> saveLotteryAsyncFunc = syncToAsync(this::saveLottery);
            handlers.put(saveLotteryProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(saveLotteryAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.LotteryDeleteDTO, StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse>> deleteLotteryFunc = this::deleteLottery;
            handlers.put(deleteLotteryMethod.getMethodName(), new UnaryStubMethodHandler<>(deleteLotteryFunc));
            BiConsumer<com.kikitrade.activity.facade.award.LotteryDeleteDTO, StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse>> deleteLotteryAsyncFunc = syncToAsync(this::deleteLottery);
            handlers.put(deleteLotteryProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(deleteLotteryAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.LotteryRequest, StreamObserver<com.kikitrade.activity.facade.award.LotteryListResponse>> lotteryListFunc = this::lotteryList;
            handlers.put(lotteryListMethod.getMethodName(), new UnaryStubMethodHandler<>(lotteryListFunc));
            BiConsumer<com.kikitrade.activity.facade.award.LotteryRequest, StreamObserver<com.kikitrade.activity.facade.award.LotteryListResponse>> lotteryListAsyncFunc = syncToAsync(this::lotteryList);
            handlers.put(lotteryListProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(lotteryListAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.LotteryDetailRequest, StreamObserver<com.kikitrade.activity.facade.award.LotteryVO>> lotteryDetailFunc = this::lotteryDetail;
            handlers.put(lotteryDetailMethod.getMethodName(), new UnaryStubMethodHandler<>(lotteryDetailFunc));
            BiConsumer<com.kikitrade.activity.facade.award.LotteryDetailRequest, StreamObserver<com.kikitrade.activity.facade.award.LotteryVO>> lotteryDetailAsyncFunc = syncToAsync(this::lotteryDetail);
            handlers.put(lotteryDetailProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(lotteryDetailAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.EmptyRequest, StreamObserver<com.kikitrade.activity.facade.award.ConditionCode>> getConditionCodesFunc = this::getConditionCodes;
            handlers.put(getConditionCodesMethod.getMethodName(), new UnaryStubMethodHandler<>(getConditionCodesFunc));
            BiConsumer<com.kikitrade.activity.facade.award.EmptyRequest, StreamObserver<com.kikitrade.activity.facade.award.ConditionCode>> getConditionCodesAsyncFunc = syncToAsync(this::getConditionCodes);
            handlers.put(getConditionCodesProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(getConditionCodesAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.award.ConditionRequest, StreamObserver<com.kikitrade.activity.facade.award.ConditionResponse>> getConditionFunc = this::getCondition;
            handlers.put(getConditionMethod.getMethodName(), new UnaryStubMethodHandler<>(getConditionFunc));
            BiConsumer<com.kikitrade.activity.facade.award.ConditionRequest, StreamObserver<com.kikitrade.activity.facade.award.ConditionResponse>> getConditionAsyncFunc = syncToAsync(this::getCondition);
            handlers.put(getConditionProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(getConditionAsyncFunc));




            return new StubInvoker<>(this, url, ActivityFacade.class, handlers);
        }


        @Override
        public com.kikitrade.activity.facade.award.ActivityResponse saveOrUpdateActivity(com.kikitrade.activity.facade.award.ActivityDTO request){
            throw unimplementedMethodException(saveOrUpdateActivityMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.ActivityBatchResponse saveOrUpdateBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request){
            throw unimplementedMethodException(saveOrUpdateBatchMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.ActivityBatchResponse deleteBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request){
            throw unimplementedMethodException(deleteBatchMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.ActivityBatchListResponse queryBatchForList(com.kikitrade.activity.facade.award.ActivityBatchRequest request){
            throw unimplementedMethodException(queryBatchForListMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.ActivityBatch queryDetail(com.kikitrade.activity.facade.award.ActivityBatchDetailRequest request){
            throw unimplementedMethodException(queryDetailMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.AuditResponse audit(com.kikitrade.activity.facade.award.AuditRequest request){
            throw unimplementedMethodException(auditMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.AwardListResponse queryRewardList(com.kikitrade.activity.facade.award.AwardRequest request){
            throw unimplementedMethodException(queryRewardListMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.ModifyAwardResponse deleteReward(com.kikitrade.activity.facade.award.AwardDTO request){
            throw unimplementedMethodException(deleteRewardMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.UploadResponse uploadFile(com.kikitrade.activity.facade.award.UploadRequest request){
            throw unimplementedMethodException(uploadFileMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.ExportDataResponse exportData(com.kikitrade.activity.facade.award.ExportDataRequest request){
            throw unimplementedMethodException(exportDataMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.ImportDataResponse importData(com.kikitrade.activity.facade.award.ImportDataRequest request){
            throw unimplementedMethodException(importDataMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.LotteryResponse saveLottery(com.kikitrade.activity.facade.award.LotteryDTO request){
            throw unimplementedMethodException(saveLotteryMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.LotteryResponse deleteLottery(com.kikitrade.activity.facade.award.LotteryDeleteDTO request){
            throw unimplementedMethodException(deleteLotteryMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.LotteryListResponse lotteryList(com.kikitrade.activity.facade.award.LotteryRequest request){
            throw unimplementedMethodException(lotteryListMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.LotteryVO lotteryDetail(com.kikitrade.activity.facade.award.LotteryDetailRequest request){
            throw unimplementedMethodException(lotteryDetailMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.ConditionCode getConditionCodes(com.kikitrade.activity.facade.award.EmptyRequest request){
            throw unimplementedMethodException(getConditionCodesMethod);
        }

        @Override
        public com.kikitrade.activity.facade.award.ConditionResponse getCondition(com.kikitrade.activity.facade.award.ConditionRequest request){
            throw unimplementedMethodException(getConditionMethod);
        }





        @Override
        public final ServiceDescriptor getServiceDescriptor() {
            return serviceDescriptor;
        }
        private RpcException unimplementedMethodException(StubMethodDescriptor methodDescriptor) {
            return TriRpcStatus.UNIMPLEMENTED.withDescription(String.format("Method %s is unimplemented",
                "/" + serviceDescriptor.getInterfaceName() + "/" + methodDescriptor.getMethodName())).asException();
        }
    }

}
