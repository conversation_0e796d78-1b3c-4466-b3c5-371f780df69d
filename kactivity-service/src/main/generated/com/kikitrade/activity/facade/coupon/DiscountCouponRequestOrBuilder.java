// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: DiscountCouponFacade.proto

package com.kikitrade.activity.facade.coupon;

public interface DiscountCouponRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.coupon.DiscountCouponRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * id为空时新增，不为空时修改
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   * id为空时新增，不为空时修改
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   * 名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   * 名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * 图片
   * </pre>
   *
   * <code>string image = 3;</code>
   * @return The image.
   */
  java.lang.String getImage();
  /**
   * <pre>
   * 图片
   * </pre>
   *
   * <code>string image = 3;</code>
   * @return The bytes for image.
   */
  com.google.protobuf.ByteString
      getImageBytes();

  /**
   * <pre>
   * shopify折扣码
   * </pre>
   *
   * <code>string shopifyDiscountCode = 4;</code>
   * @return The shopifyDiscountCode.
   */
  java.lang.String getShopifyDiscountCode();
  /**
   * <pre>
   * shopify折扣码
   * </pre>
   *
   * <code>string shopifyDiscountCode = 4;</code>
   * @return The bytes for shopifyDiscountCode.
   */
  com.google.protobuf.ByteString
      getShopifyDiscountCodeBytes();

  /**
   * <pre>
   * 有效周期/日
   * </pre>
   *
   * <code>int32 validDay = 5;</code>
   * @return The validDay.
   */
  int getValidDay();

  /**
   * <pre>
   * 直减或折扣
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.coupon.DiscountType discountType = 6;</code>
   * @return The enum numeric value on the wire for discountType.
   */
  int getDiscountTypeValue();
  /**
   * <pre>
   * 直减或折扣
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.coupon.DiscountType discountType = 6;</code>
   * @return The discountType.
   */
  com.kikitrade.activity.facade.coupon.DiscountType getDiscountType();

  /**
   * <pre>
   * 直减或折扣值
   * </pre>
   *
   * <code>double discountValue = 7;</code>
   * @return The discountValue.
   */
  double getDiscountValue();

  /**
   * <pre>
   * 使用范围 product id;product id;product id
   * </pre>
   *
   * <code>string applyTo = 8;</code>
   * @return The applyTo.
   */
  java.lang.String getApplyTo();
  /**
   * <pre>
   * 使用范围 product id;product id;product id
   * </pre>
   *
   * <code>string applyTo = 8;</code>
   * @return The bytes for applyTo.
   */
  com.google.protobuf.ByteString
      getApplyToBytes();

  /**
   * <pre>
   * 最小限制金额
   * </pre>
   *
   * <code>double minLimitPrice = 9;</code>
   * @return The minLimitPrice.
   */
  double getMinLimitPrice();
}
