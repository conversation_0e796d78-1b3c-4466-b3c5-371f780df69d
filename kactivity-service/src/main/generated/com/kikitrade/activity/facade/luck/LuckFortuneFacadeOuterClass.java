// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: LuckFortuneFacade.proto

package com.kikitrade.activity.facade.luck;

public final class LuckFortuneFacadeOuterClass {
  private LuckFortuneFacadeOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_luck_LuckFortuneRuleDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_luck_LuckFortuneRuleDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_luck_LuckCommonResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_luck_LuckCommonResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\027LuckFortuneFacade.proto\022\"com.kikitrade" +
      ".activity.facade.luck\032\037google/protobuf/t" +
      "imestamp.proto\"\255\003\n\022LuckFortuneRuleDTO\022>\n" +
      "\010kycLevel\030\001 \001(\0162,.com.kikitrade.activity" +
      ".facade.luck.KycLevel\022>\n\010userType\030\002 \001(\0162" +
      ",.com.kikitrade.activity.facade.luck.Use" +
      "rType\022\022\n\nreleaseMin\030\003 \001(\t\022\022\n\nreleaseMax\030" +
      "\004 \001(\t\022\025\n\rreleaseNumMax\030\005 \001(\005\022\032\n\022releaseA" +
      "ccountDays\030\006 \001(\t\022\034\n\024releaseAccountMonths" +
      "\030\007 \001(\t\022\022\n\nreceiveMin\030\010 \001(\t\022\022\n\nreceiveMax" +
      "\030\t \001(\t\022\032\n\022receiveAccountDays\030\n \001(\t\022\034\n\024re" +
      "ceiveAccountMonths\030\013 \001(\t\022\026\n\016receiveNumDa" +
      "ys\030\014 \001(\005\022\030\n\020receiveNumMonths\030\r \001(\005\022\n\n\002id" +
      "\030\016 \001(\t\"B\n\022LuckCommonResponse\022\017\n\007success\030" +
      "\001 \001(\010\022\017\n\007message\030\002 \001(\t\022\n\n\002id\030\003 \001(\t*\"\n\010Ky" +
      "cLevel\022\006\n\002L0\020\000\022\006\n\002L1\020\003\022\006\n\002L2\020\006*<\n\010UserTy" +
      "pe\022\n\n\006COMMON\020\000\022\007\n\003KOL\020\002\022\t\n\005MEDIA\020\003\022\020\n\014OR" +
      "GANIZATION\020\0042\213\001\n\021LuckFortuneFacade\022v\n\004sa" +
      "ve\0226.com.kikitrade.activity.facade.luck." +
      "LuckFortuneRuleDTO\0326.com.kikitrade.activ" +
      "ity.facade.luck.LuckCommonResponseB&\n\"co" +
      "m.kikitrade.activity.facade.luckP\001b\006prot" +
      "o3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.TimestampProto.getDescriptor(),
        });
    internal_static_com_kikitrade_activity_facade_luck_LuckFortuneRuleDTO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_activity_facade_luck_LuckFortuneRuleDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_luck_LuckFortuneRuleDTO_descriptor,
        new java.lang.String[] { "KycLevel", "UserType", "ReleaseMin", "ReleaseMax", "ReleaseNumMax", "ReleaseAccountDays", "ReleaseAccountMonths", "ReceiveMin", "ReceiveMax", "ReceiveAccountDays", "ReceiveAccountMonths", "ReceiveNumDays", "ReceiveNumMonths", "Id", });
    internal_static_com_kikitrade_activity_facade_luck_LuckCommonResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_activity_facade_luck_LuckCommonResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_luck_LuckCommonResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "Id", });
    com.google.protobuf.TimestampProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
