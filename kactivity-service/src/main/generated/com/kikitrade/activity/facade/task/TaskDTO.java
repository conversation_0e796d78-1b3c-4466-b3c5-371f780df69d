// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: TaskFacade.proto

package com.kikitrade.activity.facade.task;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.task.TaskDTO}
 */
public final class TaskDTO extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.task.TaskDTO)
    TaskDTOOrBuilder {
private static final long serialVersionUID = 0L;
  // Use TaskDTO.newBuilder() to construct.
  private TaskDTO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private TaskDTO() {
    id_ = "";
    taskNameCn_ = "";
    taskNameEn_ = "";
    taskNameHk_ = "";
    status_ = 0;
    descCn_ = "";
    descEn_ = "";
    descHk_ = "";
    startTime_ = "";
    endTime_ = "";
    vipLevel_ = "";
    type_ = 0;
    cycleType_ = 0;
    cycle_ = 0;
    event_ = 0;
    completeThreshold_ = "";
    award_ = "";
    url_ = "";
    icon_ = "";
    awardTimeType_ = 0;
    completeTimes_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new TaskDTO();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.task.ActivityTaskFacadeOutClass.internal_static_com_kikitrade_activity_facade_task_TaskDTO_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.task.ActivityTaskFacadeOutClass.internal_static_com_kikitrade_activity_facade_task_TaskDTO_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.task.TaskDTO.class, com.kikitrade.activity.facade.task.TaskDTO.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TASKNAMECN_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object taskNameCn_ = "";
  /**
   * <code>string taskNameCn = 2;</code>
   * @return The taskNameCn.
   */
  @java.lang.Override
  public java.lang.String getTaskNameCn() {
    java.lang.Object ref = taskNameCn_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      taskNameCn_ = s;
      return s;
    }
  }
  /**
   * <code>string taskNameCn = 2;</code>
   * @return The bytes for taskNameCn.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTaskNameCnBytes() {
    java.lang.Object ref = taskNameCn_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      taskNameCn_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TASKNAMEEN_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object taskNameEn_ = "";
  /**
   * <code>string taskNameEn = 3;</code>
   * @return The taskNameEn.
   */
  @java.lang.Override
  public java.lang.String getTaskNameEn() {
    java.lang.Object ref = taskNameEn_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      taskNameEn_ = s;
      return s;
    }
  }
  /**
   * <code>string taskNameEn = 3;</code>
   * @return The bytes for taskNameEn.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTaskNameEnBytes() {
    java.lang.Object ref = taskNameEn_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      taskNameEn_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TASKNAMEHK_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object taskNameHk_ = "";
  /**
   * <code>string taskNameHk = 4;</code>
   * @return The taskNameHk.
   */
  @java.lang.Override
  public java.lang.String getTaskNameHk() {
    java.lang.Object ref = taskNameHk_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      taskNameHk_ = s;
      return s;
    }
  }
  /**
   * <code>string taskNameHk = 4;</code>
   * @return The bytes for taskNameHk.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTaskNameHkBytes() {
    java.lang.Object ref = taskNameHk_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      taskNameHk_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATUS_FIELD_NUMBER = 5;
  private int status_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.task.Status status = 5;</code>
   * @return The enum numeric value on the wire for status.
   */
  @java.lang.Override public int getStatusValue() {
    return status_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.task.Status status = 5;</code>
   * @return The status.
   */
  @java.lang.Override public com.kikitrade.activity.facade.task.Status getStatus() {
    com.kikitrade.activity.facade.task.Status result = com.kikitrade.activity.facade.task.Status.forNumber(status_);
    return result == null ? com.kikitrade.activity.facade.task.Status.UNRECOGNIZED : result;
  }

  public static final int DESCCN_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object descCn_ = "";
  /**
   * <code>string descCn = 6;</code>
   * @return The descCn.
   */
  @java.lang.Override
  public java.lang.String getDescCn() {
    java.lang.Object ref = descCn_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      descCn_ = s;
      return s;
    }
  }
  /**
   * <code>string descCn = 6;</code>
   * @return The bytes for descCn.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescCnBytes() {
    java.lang.Object ref = descCn_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      descCn_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DESCEN_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object descEn_ = "";
  /**
   * <code>string descEn = 7;</code>
   * @return The descEn.
   */
  @java.lang.Override
  public java.lang.String getDescEn() {
    java.lang.Object ref = descEn_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      descEn_ = s;
      return s;
    }
  }
  /**
   * <code>string descEn = 7;</code>
   * @return The bytes for descEn.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescEnBytes() {
    java.lang.Object ref = descEn_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      descEn_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DESCHK_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object descHk_ = "";
  /**
   * <code>string descHk = 8;</code>
   * @return The descHk.
   */
  @java.lang.Override
  public java.lang.String getDescHk() {
    java.lang.Object ref = descHk_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      descHk_ = s;
      return s;
    }
  }
  /**
   * <code>string descHk = 8;</code>
   * @return The bytes for descHk.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescHkBytes() {
    java.lang.Object ref = descHk_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      descHk_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STARTTIME_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object startTime_ = "";
  /**
   * <code>string startTime = 9;</code>
   * @return The startTime.
   */
  @java.lang.Override
  public java.lang.String getStartTime() {
    java.lang.Object ref = startTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      startTime_ = s;
      return s;
    }
  }
  /**
   * <code>string startTime = 9;</code>
   * @return The bytes for startTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStartTimeBytes() {
    java.lang.Object ref = startTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      startTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ENDTIME_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object endTime_ = "";
  /**
   * <code>string endTime = 10;</code>
   * @return The endTime.
   */
  @java.lang.Override
  public java.lang.String getEndTime() {
    java.lang.Object ref = endTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      endTime_ = s;
      return s;
    }
  }
  /**
   * <code>string endTime = 10;</code>
   * @return The bytes for endTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getEndTimeBytes() {
    java.lang.Object ref = endTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      endTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int VIPLEVEL_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private volatile java.lang.Object vipLevel_ = "";
  /**
   * <code>string vipLevel = 11;</code>
   * @return The vipLevel.
   */
  @java.lang.Override
  public java.lang.String getVipLevel() {
    java.lang.Object ref = vipLevel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      vipLevel_ = s;
      return s;
    }
  }
  /**
   * <code>string vipLevel = 11;</code>
   * @return The bytes for vipLevel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getVipLevelBytes() {
    java.lang.Object ref = vipLevel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      vipLevel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TYPE_FIELD_NUMBER = 12;
  private int type_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.task.TaskLevelType type = 12;</code>
   * @return The enum numeric value on the wire for type.
   */
  @java.lang.Override public int getTypeValue() {
    return type_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.task.TaskLevelType type = 12;</code>
   * @return The type.
   */
  @java.lang.Override public com.kikitrade.activity.facade.task.TaskLevelType getType() {
    com.kikitrade.activity.facade.task.TaskLevelType result = com.kikitrade.activity.facade.task.TaskLevelType.forNumber(type_);
    return result == null ? com.kikitrade.activity.facade.task.TaskLevelType.UNRECOGNIZED : result;
  }

  public static final int CYCLETYPE_FIELD_NUMBER = 13;
  private int cycleType_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.task.CycleType cycleType = 13;</code>
   * @return The enum numeric value on the wire for cycleType.
   */
  @java.lang.Override public int getCycleTypeValue() {
    return cycleType_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.task.CycleType cycleType = 13;</code>
   * @return The cycleType.
   */
  @java.lang.Override public com.kikitrade.activity.facade.task.CycleType getCycleType() {
    com.kikitrade.activity.facade.task.CycleType result = com.kikitrade.activity.facade.task.CycleType.forNumber(cycleType_);
    return result == null ? com.kikitrade.activity.facade.task.CycleType.UNRECOGNIZED : result;
  }

  public static final int CYCLE_FIELD_NUMBER = 14;
  private int cycle_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.task.TaskCycle cycle = 14;</code>
   * @return The enum numeric value on the wire for cycle.
   */
  @java.lang.Override public int getCycleValue() {
    return cycle_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.task.TaskCycle cycle = 14;</code>
   * @return The cycle.
   */
  @java.lang.Override public com.kikitrade.activity.facade.task.TaskCycle getCycle() {
    com.kikitrade.activity.facade.task.TaskCycle result = com.kikitrade.activity.facade.task.TaskCycle.forNumber(cycle_);
    return result == null ? com.kikitrade.activity.facade.task.TaskCycle.UNRECOGNIZED : result;
  }

  public static final int EVENT_FIELD_NUMBER = 15;
  private int event_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.task.EventCode event = 15;</code>
   * @return The enum numeric value on the wire for event.
   */
  @java.lang.Override public int getEventValue() {
    return event_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.task.EventCode event = 15;</code>
   * @return The event.
   */
  @java.lang.Override public com.kikitrade.activity.facade.task.EventCode getEvent() {
    com.kikitrade.activity.facade.task.EventCode result = com.kikitrade.activity.facade.task.EventCode.forNumber(event_);
    return result == null ? com.kikitrade.activity.facade.task.EventCode.UNRECOGNIZED : result;
  }

  public static final int COMPLETETHRESHOLD_FIELD_NUMBER = 16;
  @SuppressWarnings("serial")
  private volatile java.lang.Object completeThreshold_ = "";
  /**
   * <code>string completeThreshold = 16;</code>
   * @return The completeThreshold.
   */
  @java.lang.Override
  public java.lang.String getCompleteThreshold() {
    java.lang.Object ref = completeThreshold_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      completeThreshold_ = s;
      return s;
    }
  }
  /**
   * <code>string completeThreshold = 16;</code>
   * @return The bytes for completeThreshold.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCompleteThresholdBytes() {
    java.lang.Object ref = completeThreshold_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      completeThreshold_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AWARD_FIELD_NUMBER = 17;
  @SuppressWarnings("serial")
  private volatile java.lang.Object award_ = "";
  /**
   * <code>string award = 17;</code>
   * @return The award.
   */
  @java.lang.Override
  public java.lang.String getAward() {
    java.lang.Object ref = award_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      award_ = s;
      return s;
    }
  }
  /**
   * <code>string award = 17;</code>
   * @return The bytes for award.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAwardBytes() {
    java.lang.Object ref = award_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      award_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int URL_FIELD_NUMBER = 18;
  @SuppressWarnings("serial")
  private volatile java.lang.Object url_ = "";
  /**
   * <code>string url = 18;</code>
   * @return The url.
   */
  @java.lang.Override
  public java.lang.String getUrl() {
    java.lang.Object ref = url_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      url_ = s;
      return s;
    }
  }
  /**
   * <code>string url = 18;</code>
   * @return The bytes for url.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUrlBytes() {
    java.lang.Object ref = url_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      url_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ICON_FIELD_NUMBER = 19;
  @SuppressWarnings("serial")
  private volatile java.lang.Object icon_ = "";
  /**
   * <code>string icon = 19;</code>
   * @return The icon.
   */
  @java.lang.Override
  public java.lang.String getIcon() {
    java.lang.Object ref = icon_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      icon_ = s;
      return s;
    }
  }
  /**
   * <code>string icon = 19;</code>
   * @return The bytes for icon.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIconBytes() {
    java.lang.Object ref = icon_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      icon_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AWARDTIMETYPE_FIELD_NUMBER = 20;
  private int awardTimeType_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.task.AwardTimeType awardTimeType = 20;</code>
   * @return The enum numeric value on the wire for awardTimeType.
   */
  @java.lang.Override public int getAwardTimeTypeValue() {
    return awardTimeType_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.task.AwardTimeType awardTimeType = 20;</code>
   * @return The awardTimeType.
   */
  @java.lang.Override public com.kikitrade.activity.facade.task.AwardTimeType getAwardTimeType() {
    com.kikitrade.activity.facade.task.AwardTimeType result = com.kikitrade.activity.facade.task.AwardTimeType.forNumber(awardTimeType_);
    return result == null ? com.kikitrade.activity.facade.task.AwardTimeType.UNRECOGNIZED : result;
  }

  public static final int COMPLETETIMES_FIELD_NUMBER = 21;
  @SuppressWarnings("serial")
  private volatile java.lang.Object completeTimes_ = "";
  /**
   * <pre>
   *可完成次数
   * </pre>
   *
   * <code>string completeTimes = 21;</code>
   * @return The completeTimes.
   */
  @java.lang.Override
  public java.lang.String getCompleteTimes() {
    java.lang.Object ref = completeTimes_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      completeTimes_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可完成次数
   * </pre>
   *
   * <code>string completeTimes = 21;</code>
   * @return The bytes for completeTimes.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCompleteTimesBytes() {
    java.lang.Object ref = completeTimes_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      completeTimes_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(taskNameCn_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, taskNameCn_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(taskNameEn_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, taskNameEn_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(taskNameHk_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, taskNameHk_);
    }
    if (status_ != com.kikitrade.activity.facade.task.Status.ACTIVE.getNumber()) {
      output.writeEnum(5, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(descCn_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, descCn_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(descEn_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, descEn_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(descHk_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, descHk_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(startTime_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, startTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(endTime_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, endTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(vipLevel_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, vipLevel_);
    }
    if (type_ != com.kikitrade.activity.facade.task.TaskLevelType.NORMAL.getNumber()) {
      output.writeEnum(12, type_);
    }
    if (cycleType_ != com.kikitrade.activity.facade.task.CycleType.CYCLE.getNumber()) {
      output.writeEnum(13, cycleType_);
    }
    if (cycle_ != com.kikitrade.activity.facade.task.TaskCycle.DAILY.getNumber()) {
      output.writeEnum(14, cycle_);
    }
    if (event_ != com.kikitrade.activity.facade.task.EventCode.NONE.getNumber()) {
      output.writeEnum(15, event_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(completeThreshold_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 16, completeThreshold_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(award_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 17, award_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(url_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 18, url_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(icon_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 19, icon_);
    }
    if (awardTimeType_ != com.kikitrade.activity.facade.task.AwardTimeType.REALTIME.getNumber()) {
      output.writeEnum(20, awardTimeType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(completeTimes_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 21, completeTimes_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(taskNameCn_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, taskNameCn_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(taskNameEn_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, taskNameEn_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(taskNameHk_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, taskNameHk_);
    }
    if (status_ != com.kikitrade.activity.facade.task.Status.ACTIVE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(5, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(descCn_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, descCn_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(descEn_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, descEn_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(descHk_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, descHk_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(startTime_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, startTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(endTime_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, endTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(vipLevel_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, vipLevel_);
    }
    if (type_ != com.kikitrade.activity.facade.task.TaskLevelType.NORMAL.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(12, type_);
    }
    if (cycleType_ != com.kikitrade.activity.facade.task.CycleType.CYCLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(13, cycleType_);
    }
    if (cycle_ != com.kikitrade.activity.facade.task.TaskCycle.DAILY.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(14, cycle_);
    }
    if (event_ != com.kikitrade.activity.facade.task.EventCode.NONE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(15, event_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(completeThreshold_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, completeThreshold_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(award_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, award_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(url_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(18, url_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(icon_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(19, icon_);
    }
    if (awardTimeType_ != com.kikitrade.activity.facade.task.AwardTimeType.REALTIME.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(20, awardTimeType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(completeTimes_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(21, completeTimes_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.task.TaskDTO)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.task.TaskDTO other = (com.kikitrade.activity.facade.task.TaskDTO) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getTaskNameCn()
        .equals(other.getTaskNameCn())) return false;
    if (!getTaskNameEn()
        .equals(other.getTaskNameEn())) return false;
    if (!getTaskNameHk()
        .equals(other.getTaskNameHk())) return false;
    if (status_ != other.status_) return false;
    if (!getDescCn()
        .equals(other.getDescCn())) return false;
    if (!getDescEn()
        .equals(other.getDescEn())) return false;
    if (!getDescHk()
        .equals(other.getDescHk())) return false;
    if (!getStartTime()
        .equals(other.getStartTime())) return false;
    if (!getEndTime()
        .equals(other.getEndTime())) return false;
    if (!getVipLevel()
        .equals(other.getVipLevel())) return false;
    if (type_ != other.type_) return false;
    if (cycleType_ != other.cycleType_) return false;
    if (cycle_ != other.cycle_) return false;
    if (event_ != other.event_) return false;
    if (!getCompleteThreshold()
        .equals(other.getCompleteThreshold())) return false;
    if (!getAward()
        .equals(other.getAward())) return false;
    if (!getUrl()
        .equals(other.getUrl())) return false;
    if (!getIcon()
        .equals(other.getIcon())) return false;
    if (awardTimeType_ != other.awardTimeType_) return false;
    if (!getCompleteTimes()
        .equals(other.getCompleteTimes())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + TASKNAMECN_FIELD_NUMBER;
    hash = (53 * hash) + getTaskNameCn().hashCode();
    hash = (37 * hash) + TASKNAMEEN_FIELD_NUMBER;
    hash = (53 * hash) + getTaskNameEn().hashCode();
    hash = (37 * hash) + TASKNAMEHK_FIELD_NUMBER;
    hash = (53 * hash) + getTaskNameHk().hashCode();
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + status_;
    hash = (37 * hash) + DESCCN_FIELD_NUMBER;
    hash = (53 * hash) + getDescCn().hashCode();
    hash = (37 * hash) + DESCEN_FIELD_NUMBER;
    hash = (53 * hash) + getDescEn().hashCode();
    hash = (37 * hash) + DESCHK_FIELD_NUMBER;
    hash = (53 * hash) + getDescHk().hashCode();
    hash = (37 * hash) + STARTTIME_FIELD_NUMBER;
    hash = (53 * hash) + getStartTime().hashCode();
    hash = (37 * hash) + ENDTIME_FIELD_NUMBER;
    hash = (53 * hash) + getEndTime().hashCode();
    hash = (37 * hash) + VIPLEVEL_FIELD_NUMBER;
    hash = (53 * hash) + getVipLevel().hashCode();
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + type_;
    hash = (37 * hash) + CYCLETYPE_FIELD_NUMBER;
    hash = (53 * hash) + cycleType_;
    hash = (37 * hash) + CYCLE_FIELD_NUMBER;
    hash = (53 * hash) + cycle_;
    hash = (37 * hash) + EVENT_FIELD_NUMBER;
    hash = (53 * hash) + event_;
    hash = (37 * hash) + COMPLETETHRESHOLD_FIELD_NUMBER;
    hash = (53 * hash) + getCompleteThreshold().hashCode();
    hash = (37 * hash) + AWARD_FIELD_NUMBER;
    hash = (53 * hash) + getAward().hashCode();
    hash = (37 * hash) + URL_FIELD_NUMBER;
    hash = (53 * hash) + getUrl().hashCode();
    hash = (37 * hash) + ICON_FIELD_NUMBER;
    hash = (53 * hash) + getIcon().hashCode();
    hash = (37 * hash) + AWARDTIMETYPE_FIELD_NUMBER;
    hash = (53 * hash) + awardTimeType_;
    hash = (37 * hash) + COMPLETETIMES_FIELD_NUMBER;
    hash = (53 * hash) + getCompleteTimes().hashCode();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.task.TaskDTO parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.task.TaskDTO parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.task.TaskDTO parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.task.TaskDTO parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.task.TaskDTO parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.task.TaskDTO parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.task.TaskDTO parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.task.TaskDTO parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.task.TaskDTO parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.task.TaskDTO parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.task.TaskDTO parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.task.TaskDTO parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.task.TaskDTO prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.task.TaskDTO}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.task.TaskDTO)
      com.kikitrade.activity.facade.task.TaskDTOOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.task.ActivityTaskFacadeOutClass.internal_static_com_kikitrade_activity_facade_task_TaskDTO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.task.ActivityTaskFacadeOutClass.internal_static_com_kikitrade_activity_facade_task_TaskDTO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.task.TaskDTO.class, com.kikitrade.activity.facade.task.TaskDTO.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.task.TaskDTO.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      taskNameCn_ = "";
      taskNameEn_ = "";
      taskNameHk_ = "";
      status_ = 0;
      descCn_ = "";
      descEn_ = "";
      descHk_ = "";
      startTime_ = "";
      endTime_ = "";
      vipLevel_ = "";
      type_ = 0;
      cycleType_ = 0;
      cycle_ = 0;
      event_ = 0;
      completeThreshold_ = "";
      award_ = "";
      url_ = "";
      icon_ = "";
      awardTimeType_ = 0;
      completeTimes_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.task.ActivityTaskFacadeOutClass.internal_static_com_kikitrade_activity_facade_task_TaskDTO_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.task.TaskDTO getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.task.TaskDTO.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.task.TaskDTO build() {
      com.kikitrade.activity.facade.task.TaskDTO result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.task.TaskDTO buildPartial() {
      com.kikitrade.activity.facade.task.TaskDTO result = new com.kikitrade.activity.facade.task.TaskDTO(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.kikitrade.activity.facade.task.TaskDTO result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.taskNameCn_ = taskNameCn_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.taskNameEn_ = taskNameEn_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.taskNameHk_ = taskNameHk_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.status_ = status_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.descCn_ = descCn_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.descEn_ = descEn_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.descHk_ = descHk_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.startTime_ = startTime_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.endTime_ = endTime_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.vipLevel_ = vipLevel_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.type_ = type_;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.cycleType_ = cycleType_;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.cycle_ = cycle_;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.event_ = event_;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.completeThreshold_ = completeThreshold_;
      }
      if (((from_bitField0_ & 0x00010000) != 0)) {
        result.award_ = award_;
      }
      if (((from_bitField0_ & 0x00020000) != 0)) {
        result.url_ = url_;
      }
      if (((from_bitField0_ & 0x00040000) != 0)) {
        result.icon_ = icon_;
      }
      if (((from_bitField0_ & 0x00080000) != 0)) {
        result.awardTimeType_ = awardTimeType_;
      }
      if (((from_bitField0_ & 0x00100000) != 0)) {
        result.completeTimes_ = completeTimes_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.task.TaskDTO) {
        return mergeFrom((com.kikitrade.activity.facade.task.TaskDTO)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.task.TaskDTO other) {
      if (other == com.kikitrade.activity.facade.task.TaskDTO.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getTaskNameCn().isEmpty()) {
        taskNameCn_ = other.taskNameCn_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getTaskNameEn().isEmpty()) {
        taskNameEn_ = other.taskNameEn_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getTaskNameHk().isEmpty()) {
        taskNameHk_ = other.taskNameHk_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.status_ != 0) {
        setStatusValue(other.getStatusValue());
      }
      if (!other.getDescCn().isEmpty()) {
        descCn_ = other.descCn_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (!other.getDescEn().isEmpty()) {
        descEn_ = other.descEn_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (!other.getDescHk().isEmpty()) {
        descHk_ = other.descHk_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (!other.getStartTime().isEmpty()) {
        startTime_ = other.startTime_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (!other.getEndTime().isEmpty()) {
        endTime_ = other.endTime_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (!other.getVipLevel().isEmpty()) {
        vipLevel_ = other.vipLevel_;
        bitField0_ |= 0x00000400;
        onChanged();
      }
      if (other.type_ != 0) {
        setTypeValue(other.getTypeValue());
      }
      if (other.cycleType_ != 0) {
        setCycleTypeValue(other.getCycleTypeValue());
      }
      if (other.cycle_ != 0) {
        setCycleValue(other.getCycleValue());
      }
      if (other.event_ != 0) {
        setEventValue(other.getEventValue());
      }
      if (!other.getCompleteThreshold().isEmpty()) {
        completeThreshold_ = other.completeThreshold_;
        bitField0_ |= 0x00008000;
        onChanged();
      }
      if (!other.getAward().isEmpty()) {
        award_ = other.award_;
        bitField0_ |= 0x00010000;
        onChanged();
      }
      if (!other.getUrl().isEmpty()) {
        url_ = other.url_;
        bitField0_ |= 0x00020000;
        onChanged();
      }
      if (!other.getIcon().isEmpty()) {
        icon_ = other.icon_;
        bitField0_ |= 0x00040000;
        onChanged();
      }
      if (other.awardTimeType_ != 0) {
        setAwardTimeTypeValue(other.getAwardTimeTypeValue());
      }
      if (!other.getCompleteTimes().isEmpty()) {
        completeTimes_ = other.completeTimes_;
        bitField0_ |= 0x00100000;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              taskNameCn_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              taskNameEn_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              taskNameHk_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              status_ = input.readEnum();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              descCn_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              descEn_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              descHk_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 74: {
              startTime_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 82: {
              endTime_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 90: {
              vipLevel_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 96: {
              type_ = input.readEnum();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            case 104: {
              cycleType_ = input.readEnum();
              bitField0_ |= 0x00001000;
              break;
            } // case 104
            case 112: {
              cycle_ = input.readEnum();
              bitField0_ |= 0x00002000;
              break;
            } // case 112
            case 120: {
              event_ = input.readEnum();
              bitField0_ |= 0x00004000;
              break;
            } // case 120
            case 130: {
              completeThreshold_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00008000;
              break;
            } // case 130
            case 138: {
              award_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00010000;
              break;
            } // case 138
            case 146: {
              url_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00020000;
              break;
            } // case 146
            case 154: {
              icon_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00040000;
              break;
            } // case 154
            case 160: {
              awardTimeType_ = input.readEnum();
              bitField0_ |= 0x00080000;
              break;
            } // case 160
            case 170: {
              completeTimes_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00100000;
              break;
            } // case 170
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object taskNameCn_ = "";
    /**
     * <code>string taskNameCn = 2;</code>
     * @return The taskNameCn.
     */
    public java.lang.String getTaskNameCn() {
      java.lang.Object ref = taskNameCn_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        taskNameCn_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string taskNameCn = 2;</code>
     * @return The bytes for taskNameCn.
     */
    public com.google.protobuf.ByteString
        getTaskNameCnBytes() {
      java.lang.Object ref = taskNameCn_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        taskNameCn_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string taskNameCn = 2;</code>
     * @param value The taskNameCn to set.
     * @return This builder for chaining.
     */
    public Builder setTaskNameCn(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      taskNameCn_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string taskNameCn = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearTaskNameCn() {
      taskNameCn_ = getDefaultInstance().getTaskNameCn();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string taskNameCn = 2;</code>
     * @param value The bytes for taskNameCn to set.
     * @return This builder for chaining.
     */
    public Builder setTaskNameCnBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      taskNameCn_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object taskNameEn_ = "";
    /**
     * <code>string taskNameEn = 3;</code>
     * @return The taskNameEn.
     */
    public java.lang.String getTaskNameEn() {
      java.lang.Object ref = taskNameEn_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        taskNameEn_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string taskNameEn = 3;</code>
     * @return The bytes for taskNameEn.
     */
    public com.google.protobuf.ByteString
        getTaskNameEnBytes() {
      java.lang.Object ref = taskNameEn_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        taskNameEn_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string taskNameEn = 3;</code>
     * @param value The taskNameEn to set.
     * @return This builder for chaining.
     */
    public Builder setTaskNameEn(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      taskNameEn_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>string taskNameEn = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearTaskNameEn() {
      taskNameEn_ = getDefaultInstance().getTaskNameEn();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>string taskNameEn = 3;</code>
     * @param value The bytes for taskNameEn to set.
     * @return This builder for chaining.
     */
    public Builder setTaskNameEnBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      taskNameEn_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object taskNameHk_ = "";
    /**
     * <code>string taskNameHk = 4;</code>
     * @return The taskNameHk.
     */
    public java.lang.String getTaskNameHk() {
      java.lang.Object ref = taskNameHk_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        taskNameHk_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string taskNameHk = 4;</code>
     * @return The bytes for taskNameHk.
     */
    public com.google.protobuf.ByteString
        getTaskNameHkBytes() {
      java.lang.Object ref = taskNameHk_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        taskNameHk_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string taskNameHk = 4;</code>
     * @param value The taskNameHk to set.
     * @return This builder for chaining.
     */
    public Builder setTaskNameHk(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      taskNameHk_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>string taskNameHk = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearTaskNameHk() {
      taskNameHk_ = getDefaultInstance().getTaskNameHk();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>string taskNameHk = 4;</code>
     * @param value The bytes for taskNameHk to set.
     * @return This builder for chaining.
     */
    public Builder setTaskNameHkBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      taskNameHk_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private int status_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.task.Status status = 5;</code>
     * @return The enum numeric value on the wire for status.
     */
    @java.lang.Override public int getStatusValue() {
      return status_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.Status status = 5;</code>
     * @param value The enum numeric value on the wire for status to set.
     * @return This builder for chaining.
     */
    public Builder setStatusValue(int value) {
      status_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.Status status = 5;</code>
     * @return The status.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.task.Status getStatus() {
      com.kikitrade.activity.facade.task.Status result = com.kikitrade.activity.facade.task.Status.forNumber(status_);
      return result == null ? com.kikitrade.activity.facade.task.Status.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.Status status = 5;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(com.kikitrade.activity.facade.task.Status value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000010;
      status_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.Status status = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      bitField0_ = (bitField0_ & ~0x00000010);
      status_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object descCn_ = "";
    /**
     * <code>string descCn = 6;</code>
     * @return The descCn.
     */
    public java.lang.String getDescCn() {
      java.lang.Object ref = descCn_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        descCn_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string descCn = 6;</code>
     * @return The bytes for descCn.
     */
    public com.google.protobuf.ByteString
        getDescCnBytes() {
      java.lang.Object ref = descCn_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        descCn_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string descCn = 6;</code>
     * @param value The descCn to set.
     * @return This builder for chaining.
     */
    public Builder setDescCn(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      descCn_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>string descCn = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearDescCn() {
      descCn_ = getDefaultInstance().getDescCn();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>string descCn = 6;</code>
     * @param value The bytes for descCn to set.
     * @return This builder for chaining.
     */
    public Builder setDescCnBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      descCn_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object descEn_ = "";
    /**
     * <code>string descEn = 7;</code>
     * @return The descEn.
     */
    public java.lang.String getDescEn() {
      java.lang.Object ref = descEn_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        descEn_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string descEn = 7;</code>
     * @return The bytes for descEn.
     */
    public com.google.protobuf.ByteString
        getDescEnBytes() {
      java.lang.Object ref = descEn_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        descEn_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string descEn = 7;</code>
     * @param value The descEn to set.
     * @return This builder for chaining.
     */
    public Builder setDescEn(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      descEn_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>string descEn = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearDescEn() {
      descEn_ = getDefaultInstance().getDescEn();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <code>string descEn = 7;</code>
     * @param value The bytes for descEn to set.
     * @return This builder for chaining.
     */
    public Builder setDescEnBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      descEn_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object descHk_ = "";
    /**
     * <code>string descHk = 8;</code>
     * @return The descHk.
     */
    public java.lang.String getDescHk() {
      java.lang.Object ref = descHk_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        descHk_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string descHk = 8;</code>
     * @return The bytes for descHk.
     */
    public com.google.protobuf.ByteString
        getDescHkBytes() {
      java.lang.Object ref = descHk_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        descHk_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string descHk = 8;</code>
     * @param value The descHk to set.
     * @return This builder for chaining.
     */
    public Builder setDescHk(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      descHk_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>string descHk = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearDescHk() {
      descHk_ = getDefaultInstance().getDescHk();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <code>string descHk = 8;</code>
     * @param value The bytes for descHk to set.
     * @return This builder for chaining.
     */
    public Builder setDescHkBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      descHk_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private java.lang.Object startTime_ = "";
    /**
     * <code>string startTime = 9;</code>
     * @return The startTime.
     */
    public java.lang.String getStartTime() {
      java.lang.Object ref = startTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        startTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string startTime = 9;</code>
     * @return The bytes for startTime.
     */
    public com.google.protobuf.ByteString
        getStartTimeBytes() {
      java.lang.Object ref = startTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        startTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string startTime = 9;</code>
     * @param value The startTime to set.
     * @return This builder for chaining.
     */
    public Builder setStartTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      startTime_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>string startTime = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearStartTime() {
      startTime_ = getDefaultInstance().getStartTime();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <code>string startTime = 9;</code>
     * @param value The bytes for startTime to set.
     * @return This builder for chaining.
     */
    public Builder setStartTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      startTime_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private java.lang.Object endTime_ = "";
    /**
     * <code>string endTime = 10;</code>
     * @return The endTime.
     */
    public java.lang.String getEndTime() {
      java.lang.Object ref = endTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        endTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string endTime = 10;</code>
     * @return The bytes for endTime.
     */
    public com.google.protobuf.ByteString
        getEndTimeBytes() {
      java.lang.Object ref = endTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        endTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string endTime = 10;</code>
     * @param value The endTime to set.
     * @return This builder for chaining.
     */
    public Builder setEndTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      endTime_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>string endTime = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearEndTime() {
      endTime_ = getDefaultInstance().getEndTime();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <code>string endTime = 10;</code>
     * @param value The bytes for endTime to set.
     * @return This builder for chaining.
     */
    public Builder setEndTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      endTime_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private java.lang.Object vipLevel_ = "";
    /**
     * <code>string vipLevel = 11;</code>
     * @return The vipLevel.
     */
    public java.lang.String getVipLevel() {
      java.lang.Object ref = vipLevel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        vipLevel_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string vipLevel = 11;</code>
     * @return The bytes for vipLevel.
     */
    public com.google.protobuf.ByteString
        getVipLevelBytes() {
      java.lang.Object ref = vipLevel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        vipLevel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string vipLevel = 11;</code>
     * @param value The vipLevel to set.
     * @return This builder for chaining.
     */
    public Builder setVipLevel(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      vipLevel_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>string vipLevel = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearVipLevel() {
      vipLevel_ = getDefaultInstance().getVipLevel();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }
    /**
     * <code>string vipLevel = 11;</code>
     * @param value The bytes for vipLevel to set.
     * @return This builder for chaining.
     */
    public Builder setVipLevelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      vipLevel_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }

    private int type_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.task.TaskLevelType type = 12;</code>
     * @return The enum numeric value on the wire for type.
     */
    @java.lang.Override public int getTypeValue() {
      return type_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.TaskLevelType type = 12;</code>
     * @param value The enum numeric value on the wire for type to set.
     * @return This builder for chaining.
     */
    public Builder setTypeValue(int value) {
      type_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.TaskLevelType type = 12;</code>
     * @return The type.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.task.TaskLevelType getType() {
      com.kikitrade.activity.facade.task.TaskLevelType result = com.kikitrade.activity.facade.task.TaskLevelType.forNumber(type_);
      return result == null ? com.kikitrade.activity.facade.task.TaskLevelType.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.TaskLevelType type = 12;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(com.kikitrade.activity.facade.task.TaskLevelType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000800;
      type_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.TaskLevelType type = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000800);
      type_ = 0;
      onChanged();
      return this;
    }

    private int cycleType_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.task.CycleType cycleType = 13;</code>
     * @return The enum numeric value on the wire for cycleType.
     */
    @java.lang.Override public int getCycleTypeValue() {
      return cycleType_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.CycleType cycleType = 13;</code>
     * @param value The enum numeric value on the wire for cycleType to set.
     * @return This builder for chaining.
     */
    public Builder setCycleTypeValue(int value) {
      cycleType_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.CycleType cycleType = 13;</code>
     * @return The cycleType.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.task.CycleType getCycleType() {
      com.kikitrade.activity.facade.task.CycleType result = com.kikitrade.activity.facade.task.CycleType.forNumber(cycleType_);
      return result == null ? com.kikitrade.activity.facade.task.CycleType.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.CycleType cycleType = 13;</code>
     * @param value The cycleType to set.
     * @return This builder for chaining.
     */
    public Builder setCycleType(com.kikitrade.activity.facade.task.CycleType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00001000;
      cycleType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.CycleType cycleType = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearCycleType() {
      bitField0_ = (bitField0_ & ~0x00001000);
      cycleType_ = 0;
      onChanged();
      return this;
    }

    private int cycle_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.task.TaskCycle cycle = 14;</code>
     * @return The enum numeric value on the wire for cycle.
     */
    @java.lang.Override public int getCycleValue() {
      return cycle_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.TaskCycle cycle = 14;</code>
     * @param value The enum numeric value on the wire for cycle to set.
     * @return This builder for chaining.
     */
    public Builder setCycleValue(int value) {
      cycle_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.TaskCycle cycle = 14;</code>
     * @return The cycle.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.task.TaskCycle getCycle() {
      com.kikitrade.activity.facade.task.TaskCycle result = com.kikitrade.activity.facade.task.TaskCycle.forNumber(cycle_);
      return result == null ? com.kikitrade.activity.facade.task.TaskCycle.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.TaskCycle cycle = 14;</code>
     * @param value The cycle to set.
     * @return This builder for chaining.
     */
    public Builder setCycle(com.kikitrade.activity.facade.task.TaskCycle value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00002000;
      cycle_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.TaskCycle cycle = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearCycle() {
      bitField0_ = (bitField0_ & ~0x00002000);
      cycle_ = 0;
      onChanged();
      return this;
    }

    private int event_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.task.EventCode event = 15;</code>
     * @return The enum numeric value on the wire for event.
     */
    @java.lang.Override public int getEventValue() {
      return event_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.EventCode event = 15;</code>
     * @param value The enum numeric value on the wire for event to set.
     * @return This builder for chaining.
     */
    public Builder setEventValue(int value) {
      event_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.EventCode event = 15;</code>
     * @return The event.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.task.EventCode getEvent() {
      com.kikitrade.activity.facade.task.EventCode result = com.kikitrade.activity.facade.task.EventCode.forNumber(event_);
      return result == null ? com.kikitrade.activity.facade.task.EventCode.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.EventCode event = 15;</code>
     * @param value The event to set.
     * @return This builder for chaining.
     */
    public Builder setEvent(com.kikitrade.activity.facade.task.EventCode value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00004000;
      event_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.EventCode event = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearEvent() {
      bitField0_ = (bitField0_ & ~0x00004000);
      event_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object completeThreshold_ = "";
    /**
     * <code>string completeThreshold = 16;</code>
     * @return The completeThreshold.
     */
    public java.lang.String getCompleteThreshold() {
      java.lang.Object ref = completeThreshold_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        completeThreshold_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string completeThreshold = 16;</code>
     * @return The bytes for completeThreshold.
     */
    public com.google.protobuf.ByteString
        getCompleteThresholdBytes() {
      java.lang.Object ref = completeThreshold_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        completeThreshold_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string completeThreshold = 16;</code>
     * @param value The completeThreshold to set.
     * @return This builder for chaining.
     */
    public Builder setCompleteThreshold(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      completeThreshold_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <code>string completeThreshold = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearCompleteThreshold() {
      completeThreshold_ = getDefaultInstance().getCompleteThreshold();
      bitField0_ = (bitField0_ & ~0x00008000);
      onChanged();
      return this;
    }
    /**
     * <code>string completeThreshold = 16;</code>
     * @param value The bytes for completeThreshold to set.
     * @return This builder for chaining.
     */
    public Builder setCompleteThresholdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      completeThreshold_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }

    private java.lang.Object award_ = "";
    /**
     * <code>string award = 17;</code>
     * @return The award.
     */
    public java.lang.String getAward() {
      java.lang.Object ref = award_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        award_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string award = 17;</code>
     * @return The bytes for award.
     */
    public com.google.protobuf.ByteString
        getAwardBytes() {
      java.lang.Object ref = award_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        award_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string award = 17;</code>
     * @param value The award to set.
     * @return This builder for chaining.
     */
    public Builder setAward(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      award_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <code>string award = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearAward() {
      award_ = getDefaultInstance().getAward();
      bitField0_ = (bitField0_ & ~0x00010000);
      onChanged();
      return this;
    }
    /**
     * <code>string award = 17;</code>
     * @param value The bytes for award to set.
     * @return This builder for chaining.
     */
    public Builder setAwardBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      award_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }

    private java.lang.Object url_ = "";
    /**
     * <code>string url = 18;</code>
     * @return The url.
     */
    public java.lang.String getUrl() {
      java.lang.Object ref = url_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        url_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string url = 18;</code>
     * @return The bytes for url.
     */
    public com.google.protobuf.ByteString
        getUrlBytes() {
      java.lang.Object ref = url_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        url_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string url = 18;</code>
     * @param value The url to set.
     * @return This builder for chaining.
     */
    public Builder setUrl(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      url_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <code>string url = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearUrl() {
      url_ = getDefaultInstance().getUrl();
      bitField0_ = (bitField0_ & ~0x00020000);
      onChanged();
      return this;
    }
    /**
     * <code>string url = 18;</code>
     * @param value The bytes for url to set.
     * @return This builder for chaining.
     */
    public Builder setUrlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      url_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }

    private java.lang.Object icon_ = "";
    /**
     * <code>string icon = 19;</code>
     * @return The icon.
     */
    public java.lang.String getIcon() {
      java.lang.Object ref = icon_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        icon_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string icon = 19;</code>
     * @return The bytes for icon.
     */
    public com.google.protobuf.ByteString
        getIconBytes() {
      java.lang.Object ref = icon_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        icon_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string icon = 19;</code>
     * @param value The icon to set.
     * @return This builder for chaining.
     */
    public Builder setIcon(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      icon_ = value;
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <code>string icon = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearIcon() {
      icon_ = getDefaultInstance().getIcon();
      bitField0_ = (bitField0_ & ~0x00040000);
      onChanged();
      return this;
    }
    /**
     * <code>string icon = 19;</code>
     * @param value The bytes for icon to set.
     * @return This builder for chaining.
     */
    public Builder setIconBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      icon_ = value;
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }

    private int awardTimeType_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.task.AwardTimeType awardTimeType = 20;</code>
     * @return The enum numeric value on the wire for awardTimeType.
     */
    @java.lang.Override public int getAwardTimeTypeValue() {
      return awardTimeType_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.AwardTimeType awardTimeType = 20;</code>
     * @param value The enum numeric value on the wire for awardTimeType to set.
     * @return This builder for chaining.
     */
    public Builder setAwardTimeTypeValue(int value) {
      awardTimeType_ = value;
      bitField0_ |= 0x00080000;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.AwardTimeType awardTimeType = 20;</code>
     * @return The awardTimeType.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.task.AwardTimeType getAwardTimeType() {
      com.kikitrade.activity.facade.task.AwardTimeType result = com.kikitrade.activity.facade.task.AwardTimeType.forNumber(awardTimeType_);
      return result == null ? com.kikitrade.activity.facade.task.AwardTimeType.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.AwardTimeType awardTimeType = 20;</code>
     * @param value The awardTimeType to set.
     * @return This builder for chaining.
     */
    public Builder setAwardTimeType(com.kikitrade.activity.facade.task.AwardTimeType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00080000;
      awardTimeType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.task.AwardTimeType awardTimeType = 20;</code>
     * @return This builder for chaining.
     */
    public Builder clearAwardTimeType() {
      bitField0_ = (bitField0_ & ~0x00080000);
      awardTimeType_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object completeTimes_ = "";
    /**
     * <pre>
     *可完成次数
     * </pre>
     *
     * <code>string completeTimes = 21;</code>
     * @return The completeTimes.
     */
    public java.lang.String getCompleteTimes() {
      java.lang.Object ref = completeTimes_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        completeTimes_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可完成次数
     * </pre>
     *
     * <code>string completeTimes = 21;</code>
     * @return The bytes for completeTimes.
     */
    public com.google.protobuf.ByteString
        getCompleteTimesBytes() {
      java.lang.Object ref = completeTimes_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        completeTimes_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可完成次数
     * </pre>
     *
     * <code>string completeTimes = 21;</code>
     * @param value The completeTimes to set.
     * @return This builder for chaining.
     */
    public Builder setCompleteTimes(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      completeTimes_ = value;
      bitField0_ |= 0x00100000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可完成次数
     * </pre>
     *
     * <code>string completeTimes = 21;</code>
     * @return This builder for chaining.
     */
    public Builder clearCompleteTimes() {
      completeTimes_ = getDefaultInstance().getCompleteTimes();
      bitField0_ = (bitField0_ & ~0x00100000);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可完成次数
     * </pre>
     *
     * <code>string completeTimes = 21;</code>
     * @param value The bytes for completeTimes to set.
     * @return This builder for chaining.
     */
    public Builder setCompleteTimesBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      completeTimes_ = value;
      bitField0_ |= 0x00100000;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.task.TaskDTO)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.task.TaskDTO)
  private static final com.kikitrade.activity.facade.task.TaskDTO DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.task.TaskDTO();
  }

  public static com.kikitrade.activity.facade.task.TaskDTO getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TaskDTO>
      PARSER = new com.google.protobuf.AbstractParser<TaskDTO>() {
    @java.lang.Override
    public TaskDTO parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<TaskDTO> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TaskDTO> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.task.TaskDTO getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

