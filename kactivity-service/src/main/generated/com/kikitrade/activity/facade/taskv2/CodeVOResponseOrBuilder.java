// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Task.proto

package com.kikitrade.activity.facade.taskv2;

public interface CodeVOResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.taskv2.CodeVOResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.CodeVO data = 3;</code>
   */
  java.util.List<com.kikitrade.activity.facade.taskv2.CodeVO> 
      getDataList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.CodeVO data = 3;</code>
   */
  com.kikitrade.activity.facade.taskv2.CodeVO getData(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.CodeVO data = 3;</code>
   */
  int getDataCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.CodeVO data = 3;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.taskv2.CodeVOOrBuilder> 
      getDataOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.CodeVO data = 3;</code>
   */
  com.kikitrade.activity.facade.taskv2.CodeVOOrBuilder getDataOrBuilder(
      int index);
}
