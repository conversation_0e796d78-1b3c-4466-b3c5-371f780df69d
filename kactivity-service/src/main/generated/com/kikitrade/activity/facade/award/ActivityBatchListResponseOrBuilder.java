// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface ActivityBatchListResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.ActivityBatchListResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ActivityBatch activityBatch = 3;</code>
   */
  java.util.List<com.kikitrade.activity.facade.award.ActivityBatch> 
      getActivityBatchList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ActivityBatch activityBatch = 3;</code>
   */
  com.kikitrade.activity.facade.award.ActivityBatch getActivityBatch(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ActivityBatch activityBatch = 3;</code>
   */
  int getActivityBatchCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ActivityBatch activityBatch = 3;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.award.ActivityBatchOrBuilder> 
      getActivityBatchOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ActivityBatch activityBatch = 3;</code>
   */
  com.kikitrade.activity.facade.award.ActivityBatchOrBuilder getActivityBatchOrBuilder(
      int index);
}
