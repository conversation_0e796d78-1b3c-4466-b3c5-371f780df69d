// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: TaskFacade.proto

package com.kikitrade.activity.facade.task;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.task.EventCode}
 */
public enum EventCode
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>NONE = 0;</code>
   */
  NONE(0),
  /**
   * <pre>
   *签到
   * </pre>
   *
   * <code>SIGN_IN = 1;</code>
   */
  SIGN_IN(1),
  /**
   * <pre>
   *关注
   * </pre>
   *
   * <code>FOLLOW = 2;</code>
   */
  FOLLOW(2),
  /**
   * <pre>
   *评论
   * </pre>
   *
   * <code>COMMENT = 3;</code>
   */
  COMMENT(3),
  /**
   * <pre>
   *赞
   * </pre>
   *
   * <code>LIKE = 4;</code>
   */
  LIKE(4),
  /**
   * <pre>
   *发帖
   * </pre>
   *
   * <code>POST = 5;</code>
   */
  POST(5),
  /**
   * <pre>
   *分享帖子
   * </pre>
   *
   * <code>SHARE_POST = 6;</code>
   */
  SHARE_POST(6),
  /**
   * <pre>
   *分享新闻
   * </pre>
   *
   * <code>SHARE_NEWS = 7;</code>
   */
  SHARE_NEWS(7),
  /**
   * <pre>
   *设置精品帖子
   * </pre>
   *
   * <code>POST_SPLENDID = 8;</code>
   */
  POST_SPLENDID(8),
  /**
   * <pre>
   *kyc认证
   * </pre>
   *
   * <code>KYC = 9;</code>
   */
  KYC(9),
  /**
   * <pre>
   *kycL1认证
   * </pre>
   *
   * <code>KYC_L1 = 10;</code>
   */
  KYC_L1(10),
  /**
   * <pre>
   *kycL2认证
   * </pre>
   *
   * <code>KYC_L2 = 11;</code>
   */
  KYC_L2(11),
  /**
   * <pre>
   *交易分析
   * </pre>
   *
   * <code>TRANSACTION_ANALYSIS = 12;</code>
   */
  TRANSACTION_ANALYSIS(12),
  /**
   * <pre>
   *资产分析
   * </pre>
   *
   * <code>ASSET_ANALYSIS = 13;</code>
   */
  ASSET_ANALYSIS(13),
  /**
   * <pre>
   *交易下单
   * </pre>
   *
   * <code>ORDER_CREATED = 14;</code>
   */
  ORDER_CREATED(14),
  /**
   * <pre>
   *交易成交
   * </pre>
   *
   * <code>ORDER_MATCHED = 15;</code>
   */
  ORDER_MATCHED(15),
  /**
   * <code>FOLLOWED = 16;</code>
   */
  FOLLOWED(16),
  /**
   * <code>LIKED = 17;</code>
   */
  LIKED(17),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>NONE = 0;</code>
   */
  public static final int NONE_VALUE = 0;
  /**
   * <pre>
   *签到
   * </pre>
   *
   * <code>SIGN_IN = 1;</code>
   */
  public static final int SIGN_IN_VALUE = 1;
  /**
   * <pre>
   *关注
   * </pre>
   *
   * <code>FOLLOW = 2;</code>
   */
  public static final int FOLLOW_VALUE = 2;
  /**
   * <pre>
   *评论
   * </pre>
   *
   * <code>COMMENT = 3;</code>
   */
  public static final int COMMENT_VALUE = 3;
  /**
   * <pre>
   *赞
   * </pre>
   *
   * <code>LIKE = 4;</code>
   */
  public static final int LIKE_VALUE = 4;
  /**
   * <pre>
   *发帖
   * </pre>
   *
   * <code>POST = 5;</code>
   */
  public static final int POST_VALUE = 5;
  /**
   * <pre>
   *分享帖子
   * </pre>
   *
   * <code>SHARE_POST = 6;</code>
   */
  public static final int SHARE_POST_VALUE = 6;
  /**
   * <pre>
   *分享新闻
   * </pre>
   *
   * <code>SHARE_NEWS = 7;</code>
   */
  public static final int SHARE_NEWS_VALUE = 7;
  /**
   * <pre>
   *设置精品帖子
   * </pre>
   *
   * <code>POST_SPLENDID = 8;</code>
   */
  public static final int POST_SPLENDID_VALUE = 8;
  /**
   * <pre>
   *kyc认证
   * </pre>
   *
   * <code>KYC = 9;</code>
   */
  public static final int KYC_VALUE = 9;
  /**
   * <pre>
   *kycL1认证
   * </pre>
   *
   * <code>KYC_L1 = 10;</code>
   */
  public static final int KYC_L1_VALUE = 10;
  /**
   * <pre>
   *kycL2认证
   * </pre>
   *
   * <code>KYC_L2 = 11;</code>
   */
  public static final int KYC_L2_VALUE = 11;
  /**
   * <pre>
   *交易分析
   * </pre>
   *
   * <code>TRANSACTION_ANALYSIS = 12;</code>
   */
  public static final int TRANSACTION_ANALYSIS_VALUE = 12;
  /**
   * <pre>
   *资产分析
   * </pre>
   *
   * <code>ASSET_ANALYSIS = 13;</code>
   */
  public static final int ASSET_ANALYSIS_VALUE = 13;
  /**
   * <pre>
   *交易下单
   * </pre>
   *
   * <code>ORDER_CREATED = 14;</code>
   */
  public static final int ORDER_CREATED_VALUE = 14;
  /**
   * <pre>
   *交易成交
   * </pre>
   *
   * <code>ORDER_MATCHED = 15;</code>
   */
  public static final int ORDER_MATCHED_VALUE = 15;
  /**
   * <code>FOLLOWED = 16;</code>
   */
  public static final int FOLLOWED_VALUE = 16;
  /**
   * <code>LIKED = 17;</code>
   */
  public static final int LIKED_VALUE = 17;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static EventCode valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static EventCode forNumber(int value) {
    switch (value) {
      case 0: return NONE;
      case 1: return SIGN_IN;
      case 2: return FOLLOW;
      case 3: return COMMENT;
      case 4: return LIKE;
      case 5: return POST;
      case 6: return SHARE_POST;
      case 7: return SHARE_NEWS;
      case 8: return POST_SPLENDID;
      case 9: return KYC;
      case 10: return KYC_L1;
      case 11: return KYC_L2;
      case 12: return TRANSACTION_ANALYSIS;
      case 13: return ASSET_ANALYSIS;
      case 14: return ORDER_CREATED;
      case 15: return ORDER_MATCHED;
      case 16: return FOLLOWED;
      case 17: return LIKED;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<EventCode>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      EventCode> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<EventCode>() {
          public EventCode findValueByNumber(int number) {
            return EventCode.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.task.ActivityTaskFacadeOutClass.getDescriptor().getEnumTypes().get(3);
  }

  private static final EventCode[] VALUES = values();

  public static EventCode valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private EventCode(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.task.EventCode)
}

