// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityRosterRule.proto

package com.kikitrade.activity.facade.roster;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.roster.ConditionVO}
 */
public final class ConditionVO extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.roster.ConditionVO)
    ConditionVOOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ConditionVO.newBuilder() to construct.
  private ConditionVO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ConditionVO() {
    name_ = "";
    filterTypes_ = java.util.Collections.emptyList();
    alisa_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ConditionVO();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionVO_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionVO_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.roster.ConditionVO.class, com.kikitrade.activity.facade.roster.ConditionVO.Builder.class);
  }

  public static final int NAME_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object name_ = "";
  /**
   * <code>string name = 1;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <code>string name = 1;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int FILTERTYPES_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<java.lang.Integer> filterTypes_;
  private static final com.google.protobuf.Internal.ListAdapter.Converter<
      java.lang.Integer, com.kikitrade.activity.facade.roster.FilterType> filterTypes_converter_ =
          new com.google.protobuf.Internal.ListAdapter.Converter<
              java.lang.Integer, com.kikitrade.activity.facade.roster.FilterType>() {
            public com.kikitrade.activity.facade.roster.FilterType convert(java.lang.Integer from) {
              com.kikitrade.activity.facade.roster.FilterType result = com.kikitrade.activity.facade.roster.FilterType.forNumber(from);
              return result == null ? com.kikitrade.activity.facade.roster.FilterType.UNRECOGNIZED : result;
            }
          };
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
   * @return A list containing the filterTypes.
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.roster.FilterType> getFilterTypesList() {
    return new com.google.protobuf.Internal.ListAdapter<
        java.lang.Integer, com.kikitrade.activity.facade.roster.FilterType>(filterTypes_, filterTypes_converter_);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
   * @return The count of filterTypes.
   */
  @java.lang.Override
  public int getFilterTypesCount() {
    return filterTypes_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
   * @param index The index of the element to return.
   * @return The filterTypes at the given index.
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.roster.FilterType getFilterTypes(int index) {
    return filterTypes_converter_.convert(filterTypes_.get(index));
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
   * @return A list containing the enum numeric values on the wire for filterTypes.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
  getFilterTypesValueList() {
    return filterTypes_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
   * @param index The index of the value to return.
   * @return The enum numeric value on the wire of filterTypes at the given index.
   */
  @java.lang.Override
  public int getFilterTypesValue(int index) {
    return filterTypes_.get(index);
  }
  private int filterTypesMemoizedSerializedSize;

  public static final int ALISA_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object alisa_ = "";
  /**
   * <code>string alisa = 3;</code>
   * @return The alisa.
   */
  @java.lang.Override
  public java.lang.String getAlisa() {
    java.lang.Object ref = alisa_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      alisa_ = s;
      return s;
    }
  }
  /**
   * <code>string alisa = 3;</code>
   * @return The bytes for alisa.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAlisaBytes() {
    java.lang.Object ref = alisa_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      alisa_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DYNAMIC_FIELD_NUMBER = 4;
  private boolean dynamic_ = false;
  /**
   * <code>bool dynamic = 4;</code>
   * @return The dynamic.
   */
  @java.lang.Override
  public boolean getDynamic() {
    return dynamic_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    getSerializedSize();
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
    }
    if (getFilterTypesList().size() > 0) {
      output.writeUInt32NoTag(18);
      output.writeUInt32NoTag(filterTypesMemoizedSerializedSize);
    }
    for (int i = 0; i < filterTypes_.size(); i++) {
      output.writeEnumNoTag(filterTypes_.get(i));
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(alisa_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, alisa_);
    }
    if (dynamic_ != false) {
      output.writeBool(4, dynamic_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < filterTypes_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeEnumSizeNoTag(filterTypes_.get(i));
      }
      size += dataSize;
      if (!getFilterTypesList().isEmpty()) {  size += 1;
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32SizeNoTag(dataSize);
      }filterTypesMemoizedSerializedSize = dataSize;
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(alisa_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, alisa_);
    }
    if (dynamic_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(4, dynamic_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.roster.ConditionVO)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.roster.ConditionVO other = (com.kikitrade.activity.facade.roster.ConditionVO) obj;

    if (!getName()
        .equals(other.getName())) return false;
    if (!filterTypes_.equals(other.filterTypes_)) return false;
    if (!getAlisa()
        .equals(other.getAlisa())) return false;
    if (getDynamic()
        != other.getDynamic()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    if (getFilterTypesCount() > 0) {
      hash = (37 * hash) + FILTERTYPES_FIELD_NUMBER;
      hash = (53 * hash) + filterTypes_.hashCode();
    }
    hash = (37 * hash) + ALISA_FIELD_NUMBER;
    hash = (53 * hash) + getAlisa().hashCode();
    hash = (37 * hash) + DYNAMIC_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getDynamic());
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.roster.ConditionVO parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.roster.ConditionVO parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionVO parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.roster.ConditionVO parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionVO parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.roster.ConditionVO parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionVO parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.roster.ConditionVO parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.roster.ConditionVO parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.roster.ConditionVO parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionVO parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.roster.ConditionVO parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.roster.ConditionVO prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.roster.ConditionVO}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.roster.ConditionVO)
      com.kikitrade.activity.facade.roster.ConditionVOOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionVO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionVO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.roster.ConditionVO.class, com.kikitrade.activity.facade.roster.ConditionVO.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.roster.ConditionVO.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      name_ = "";
      filterTypes_ = java.util.Collections.emptyList();
      bitField0_ = (bitField0_ & ~0x00000002);
      alisa_ = "";
      dynamic_ = false;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionVO_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.roster.ConditionVO getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.roster.ConditionVO.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.roster.ConditionVO build() {
      com.kikitrade.activity.facade.roster.ConditionVO result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.roster.ConditionVO buildPartial() {
      com.kikitrade.activity.facade.roster.ConditionVO result = new com.kikitrade.activity.facade.roster.ConditionVO(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.roster.ConditionVO result) {
      if (((bitField0_ & 0x00000002) != 0)) {
        filterTypes_ = java.util.Collections.unmodifiableList(filterTypes_);
        bitField0_ = (bitField0_ & ~0x00000002);
      }
      result.filterTypes_ = filterTypes_;
    }

    private void buildPartial0(com.kikitrade.activity.facade.roster.ConditionVO result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.name_ = name_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.alisa_ = alisa_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.dynamic_ = dynamic_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.roster.ConditionVO) {
        return mergeFrom((com.kikitrade.activity.facade.roster.ConditionVO)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.roster.ConditionVO other) {
      if (other == com.kikitrade.activity.facade.roster.ConditionVO.getDefaultInstance()) return this;
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.filterTypes_.isEmpty()) {
        if (filterTypes_.isEmpty()) {
          filterTypes_ = other.filterTypes_;
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          ensureFilterTypesIsMutable();
          filterTypes_.addAll(other.filterTypes_);
        }
        onChanged();
      }
      if (!other.getAlisa().isEmpty()) {
        alisa_ = other.alisa_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.getDynamic() != false) {
        setDynamic(other.getDynamic());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              name_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              int tmpRaw = input.readEnum();
              ensureFilterTypesIsMutable();
              filterTypes_.add(tmpRaw);
              break;
            } // case 16
            case 18: {
              int length = input.readRawVarint32();
              int oldLimit = input.pushLimit(length);
              while(input.getBytesUntilLimit() > 0) {
                int tmpRaw = input.readEnum();
                ensureFilterTypesIsMutable();
                filterTypes_.add(tmpRaw);
              }
              input.popLimit(oldLimit);
              break;
            } // case 18
            case 26: {
              alisa_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              dynamic_ = input.readBool();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object name_ = "";
    /**
     * <code>string name = 1;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string name = 1;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string name = 1;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string name = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      name_ = getDefaultInstance().getName();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string name = 1;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      name_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.util.List<java.lang.Integer> filterTypes_ =
      java.util.Collections.emptyList();
    private void ensureFilterTypesIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        filterTypes_ = new java.util.ArrayList<java.lang.Integer>(filterTypes_);
        bitField0_ |= 0x00000002;
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
     * @return A list containing the filterTypes.
     */
    public java.util.List<com.kikitrade.activity.facade.roster.FilterType> getFilterTypesList() {
      return new com.google.protobuf.Internal.ListAdapter<
          java.lang.Integer, com.kikitrade.activity.facade.roster.FilterType>(filterTypes_, filterTypes_converter_);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
     * @return The count of filterTypes.
     */
    public int getFilterTypesCount() {
      return filterTypes_.size();
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
     * @param index The index of the element to return.
     * @return The filterTypes at the given index.
     */
    public com.kikitrade.activity.facade.roster.FilterType getFilterTypes(int index) {
      return filterTypes_converter_.convert(filterTypes_.get(index));
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
     * @param index The index to set the value at.
     * @param value The filterTypes to set.
     * @return This builder for chaining.
     */
    public Builder setFilterTypes(
        int index, com.kikitrade.activity.facade.roster.FilterType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      ensureFilterTypesIsMutable();
      filterTypes_.set(index, value.getNumber());
      onChanged();
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
     * @param value The filterTypes to add.
     * @return This builder for chaining.
     */
    public Builder addFilterTypes(com.kikitrade.activity.facade.roster.FilterType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      ensureFilterTypesIsMutable();
      filterTypes_.add(value.getNumber());
      onChanged();
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
     * @param values The filterTypes to add.
     * @return This builder for chaining.
     */
    public Builder addAllFilterTypes(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.roster.FilterType> values) {
      ensureFilterTypesIsMutable();
      for (com.kikitrade.activity.facade.roster.FilterType value : values) {
        filterTypes_.add(value.getNumber());
      }
      onChanged();
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearFilterTypes() {
      filterTypes_ = java.util.Collections.emptyList();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
     * @return A list containing the enum numeric values on the wire for filterTypes.
     */
    public java.util.List<java.lang.Integer>
    getFilterTypesValueList() {
      return java.util.Collections.unmodifiableList(filterTypes_);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of filterTypes at the given index.
     */
    public int getFilterTypesValue(int index) {
      return filterTypes_.get(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
     * @param index The index to set the value at.
     * @param value The enum numeric value on the wire for filterTypes to set.
     * @return This builder for chaining.
     */
    public Builder setFilterTypesValue(
        int index, int value) {
      ensureFilterTypesIsMutable();
      filterTypes_.set(index, value);
      onChanged();
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
     * @param value The enum numeric value on the wire for filterTypes to add.
     * @return This builder for chaining.
     */
    public Builder addFilterTypesValue(int value) {
      ensureFilterTypesIsMutable();
      filterTypes_.add(value);
      onChanged();
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
     * @param values The enum numeric values on the wire for filterTypes to add.
     * @return This builder for chaining.
     */
    public Builder addAllFilterTypesValue(
        java.lang.Iterable<java.lang.Integer> values) {
      ensureFilterTypesIsMutable();
      for (int value : values) {
        filterTypes_.add(value);
      }
      onChanged();
      return this;
    }

    private java.lang.Object alisa_ = "";
    /**
     * <code>string alisa = 3;</code>
     * @return The alisa.
     */
    public java.lang.String getAlisa() {
      java.lang.Object ref = alisa_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        alisa_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string alisa = 3;</code>
     * @return The bytes for alisa.
     */
    public com.google.protobuf.ByteString
        getAlisaBytes() {
      java.lang.Object ref = alisa_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        alisa_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string alisa = 3;</code>
     * @param value The alisa to set.
     * @return This builder for chaining.
     */
    public Builder setAlisa(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      alisa_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>string alisa = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearAlisa() {
      alisa_ = getDefaultInstance().getAlisa();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>string alisa = 3;</code>
     * @param value The bytes for alisa to set.
     * @return This builder for chaining.
     */
    public Builder setAlisaBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      alisa_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private boolean dynamic_ ;
    /**
     * <code>bool dynamic = 4;</code>
     * @return The dynamic.
     */
    @java.lang.Override
    public boolean getDynamic() {
      return dynamic_;
    }
    /**
     * <code>bool dynamic = 4;</code>
     * @param value The dynamic to set.
     * @return This builder for chaining.
     */
    public Builder setDynamic(boolean value) {

      dynamic_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>bool dynamic = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearDynamic() {
      bitField0_ = (bitField0_ & ~0x00000008);
      dynamic_ = false;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.roster.ConditionVO)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.roster.ConditionVO)
  private static final com.kikitrade.activity.facade.roster.ConditionVO DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.roster.ConditionVO();
  }

  public static com.kikitrade.activity.facade.roster.ConditionVO getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ConditionVO>
      PARSER = new com.google.protobuf.AbstractParser<ConditionVO>() {
    @java.lang.Override
    public ConditionVO parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ConditionVO> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ConditionVO> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.roster.ConditionVO getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

