// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.award.BatchFrequency}
 */
public enum BatchFrequency
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>EVERY_DAY = 0;</code>
   */
  EVERY_DAY(0),
  /**
   * <code>FINISH = 1;</code>
   */
  FINISH(1),
  /**
   * <code>EVERY_MONTH = 2;</code>
   */
  EVERY_MONTH(2),
  /**
   * <pre>
   * 每周
   * </pre>
   *
   * <code>EVERY_WEEK = 3;</code>
   */
  EVERY_WEEK(3),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>EVERY_DAY = 0;</code>
   */
  public static final int EVERY_DAY_VALUE = 0;
  /**
   * <code>FINISH = 1;</code>
   */
  public static final int FINISH_VALUE = 1;
  /**
   * <code>EVERY_MONTH = 2;</code>
   */
  public static final int EVERY_MONTH_VALUE = 2;
  /**
   * <pre>
   * 每周
   * </pre>
   *
   * <code>EVERY_WEEK = 3;</code>
   */
  public static final int EVERY_WEEK_VALUE = 3;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static BatchFrequency valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static BatchFrequency forNumber(int value) {
    switch (value) {
      case 0: return EVERY_DAY;
      case 1: return FINISH;
      case 2: return EVERY_MONTH;
      case 3: return EVERY_WEEK;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<BatchFrequency>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      BatchFrequency> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<BatchFrequency>() {
          public BatchFrequency findValueByNumber(int number) {
            return BatchFrequency.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.getDescriptor().getEnumTypes().get(6);
  }

  private static final BatchFrequency[] VALUES = values();

  public static BatchFrequency valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private BatchFrequency(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.award.BatchFrequency)
}

