/*
* Licensed to the Apache Software Foundation (ASF) under one or more
* contributor license agreements.  See the NOTICE file distributed with
* this work for additional information regarding copyright ownership.
* The ASF licenses this file to You under the Apache License, Version 2.0
* (the "License"); you may not use this file except in compliance with
* the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

    package com.kikitrade.activity.facade.reward;

import org.apache.dubbo.common.stream.StreamObserver;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.PathResolver;
import org.apache.dubbo.rpc.RpcException;
import org.apache.dubbo.rpc.ServerService;
import org.apache.dubbo.rpc.TriRpcStatus;
import org.apache.dubbo.rpc.model.MethodDescriptor;
import org.apache.dubbo.rpc.model.ServiceDescriptor;
import org.apache.dubbo.rpc.model.StubMethodDescriptor;
import org.apache.dubbo.rpc.model.StubServiceDescriptor;
import org.apache.dubbo.rpc.stub.BiStreamMethodHandler;
import org.apache.dubbo.rpc.stub.ServerStreamMethodHandler;
import org.apache.dubbo.rpc.stub.StubInvocationUtil;
import org.apache.dubbo.rpc.stub.StubInvoker;
import org.apache.dubbo.rpc.stub.StubMethodHandler;
import org.apache.dubbo.rpc.stub.StubSuppliers;
import org.apache.dubbo.rpc.stub.UnaryStubMethodHandler;

import com.google.protobuf.Message;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.concurrent.CompletableFuture;

public final class DubboActivityRewardFacadeTriple {

    public static final String SERVICE_NAME = ActivityRewardFacade.SERVICE_NAME;

    private static final StubServiceDescriptor serviceDescriptor = new StubServiceDescriptor(SERVICE_NAME,ActivityRewardFacade.class);

    static {
        org.apache.dubbo.rpc.protocol.tri.service.SchemaDescriptorRegistry.addSchemaDescriptor(SERVICE_NAME,ActivityRewardFacadeOuterClass.getDescriptor());
        StubSuppliers.addSupplier(SERVICE_NAME, DubboActivityRewardFacadeTriple::newStub);
        StubSuppliers.addSupplier(ActivityRewardFacade.JAVA_SERVICE_NAME,  DubboActivityRewardFacadeTriple::newStub);
        StubSuppliers.addDescriptor(SERVICE_NAME, serviceDescriptor);
        StubSuppliers.addDescriptor(ActivityRewardFacade.JAVA_SERVICE_NAME, serviceDescriptor);
    }

    @SuppressWarnings("all")
    public static ActivityRewardFacade newStub(Invoker<?> invoker) {
        return new ActivityRewardFacadeStub((Invoker<ActivityRewardFacade>)invoker);
    }

    private static final StubMethodDescriptor manualRewardMethod = new StubMethodDescriptor("manualReward",
    com.kikitrade.activity.facade.reward.ManualRewardRequest.class, com.kikitrade.activity.facade.reward.ManualRewardResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.reward.ManualRewardRequest::parseFrom,
    com.kikitrade.activity.facade.reward.ManualRewardResponse::parseFrom);

    private static final StubMethodDescriptor manualRewardAsyncMethod = new StubMethodDescriptor("manualReward",
    com.kikitrade.activity.facade.reward.ManualRewardRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.reward.ManualRewardRequest::parseFrom,
    com.kikitrade.activity.facade.reward.ManualRewardResponse::parseFrom);

    private static final StubMethodDescriptor manualRewardProxyAsyncMethod = new StubMethodDescriptor("manualRewardAsync",
    com.kikitrade.activity.facade.reward.ManualRewardRequest.class, com.kikitrade.activity.facade.reward.ManualRewardResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.reward.ManualRewardRequest::parseFrom,
    com.kikitrade.activity.facade.reward.ManualRewardResponse::parseFrom);





    public static class ActivityRewardFacadeStub implements ActivityRewardFacade{
        private final Invoker<ActivityRewardFacade> invoker;

        public ActivityRewardFacadeStub(Invoker<ActivityRewardFacade> invoker) {
            this.invoker = invoker;
        }

        @Override
        public com.kikitrade.activity.facade.reward.ManualRewardResponse manualReward(com.kikitrade.activity.facade.reward.ManualRewardRequest request){
            return StubInvocationUtil.unaryCall(invoker, manualRewardMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.reward.ManualRewardResponse> manualRewardAsync(com.kikitrade.activity.facade.reward.ManualRewardRequest request){
            return StubInvocationUtil.unaryCall(invoker, manualRewardAsyncMethod, request);
        }

        @Override
        public void manualReward(com.kikitrade.activity.facade.reward.ManualRewardRequest request, StreamObserver<com.kikitrade.activity.facade.reward.ManualRewardResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, manualRewardMethod , request, responseObserver);
        }



    }

    public static abstract class ActivityRewardFacadeImplBase implements ActivityRewardFacade, ServerService<ActivityRewardFacade> {

        private <T, R> BiConsumer<T, StreamObserver<R>> syncToAsync(java.util.function.Function<T, R> syncFun) {
            return new BiConsumer<T, StreamObserver<R>>() {
                @Override
                public void accept(T t, StreamObserver<R> observer) {
                    try {
                        R ret = syncFun.apply(t);
                        observer.onNext(ret);
                        observer.onCompleted();
                    } catch (Throwable e) {
                        observer.onError(e);
                    }
                }
            };
        }

        @Override
        public final Invoker<ActivityRewardFacade> getInvoker(URL url) {
            PathResolver pathResolver = url.getOrDefaultFrameworkModel()
            .getExtensionLoader(PathResolver.class)
            .getDefaultExtension();
            Map<String,StubMethodHandler<?, ?>> handlers = new HashMap<>();

            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/manualReward" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/manualRewardAsync" );

            BiConsumer<com.kikitrade.activity.facade.reward.ManualRewardRequest, StreamObserver<com.kikitrade.activity.facade.reward.ManualRewardResponse>> manualRewardFunc = this::manualReward;
            handlers.put(manualRewardMethod.getMethodName(), new UnaryStubMethodHandler<>(manualRewardFunc));
            BiConsumer<com.kikitrade.activity.facade.reward.ManualRewardRequest, StreamObserver<com.kikitrade.activity.facade.reward.ManualRewardResponse>> manualRewardAsyncFunc = syncToAsync(this::manualReward);
            handlers.put(manualRewardProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(manualRewardAsyncFunc));




            return new StubInvoker<>(this, url, ActivityRewardFacade.class, handlers);
        }


        @Override
        public com.kikitrade.activity.facade.reward.ManualRewardResponse manualReward(com.kikitrade.activity.facade.reward.ManualRewardRequest request){
            throw unimplementedMethodException(manualRewardMethod);
        }





        @Override
        public final ServiceDescriptor getServiceDescriptor() {
            return serviceDescriptor;
        }
        private RpcException unimplementedMethodException(StubMethodDescriptor methodDescriptor) {
            return TriRpcStatus.UNIMPLEMENTED.withDescription(String.format("Method %s is unimplemented",
                "/" + serviceDescriptor.getInterfaceName() + "/" + methodDescriptor.getMethodName())).asException();
        }
    }

}
