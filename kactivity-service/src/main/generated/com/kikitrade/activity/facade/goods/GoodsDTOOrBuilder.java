// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Goods.proto

package com.kikitrade.activity.facade.goods;

public interface GoodsDTOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.goods.GoodsDTO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *商品id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   *商品id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   *商品名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   *商品名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   *商品状态
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.goods.GoodsStatus status = 3;</code>
   * @return The enum numeric value on the wire for status.
   */
  int getStatusValue();
  /**
   * <pre>
   *商品状态
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.goods.GoodsStatus status = 3;</code>
   * @return The status.
   */
  com.kikitrade.activity.facade.goods.GoodsStatus getStatus();

  /**
   * <pre>
   *商品描述
   * </pre>
   *
   * <code>string desc = 4;</code>
   * @return The desc.
   */
  java.lang.String getDesc();
  /**
   * <pre>
   *商品描述
   * </pre>
   *
   * <code>string desc = 4;</code>
   * @return The bytes for desc.
   */
  com.google.protobuf.ByteString
      getDescBytes();

  /**
   * <code>.com.kikitrade.activity.facade.goods.GoodsType type = 5;</code>
   * @return The enum numeric value on the wire for type.
   */
  int getTypeValue();
  /**
   * <code>.com.kikitrade.activity.facade.goods.GoodsType type = 5;</code>
   * @return The type.
   */
  com.kikitrade.activity.facade.goods.GoodsType getType();

  /**
   * <pre>
   *商品售卖时间
   * </pre>
   *
   * <code>string startTime = 6;</code>
   * @return The startTime.
   */
  java.lang.String getStartTime();
  /**
   * <pre>
   *商品售卖时间
   * </pre>
   *
   * <code>string startTime = 6;</code>
   * @return The bytes for startTime.
   */
  com.google.protobuf.ByteString
      getStartTimeBytes();

  /**
   * <pre>
   *商品售卖时间
   * </pre>
   *
   * <code>string endTime = 7;</code>
   * @return The endTime.
   */
  java.lang.String getEndTime();
  /**
   * <pre>
   *商品售卖时间
   * </pre>
   *
   * <code>string endTime = 7;</code>
   * @return The bytes for endTime.
   */
  com.google.protobuf.ByteString
      getEndTimeBytes();

  /**
   * <pre>
   *商品所属链
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.goods.BlockChain chain = 8;</code>
   * @return The enum numeric value on the wire for chain.
   */
  int getChainValue();
  /**
   * <pre>
   *商品所属链
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.goods.BlockChain chain = 8;</code>
   * @return The chain.
   */
  com.kikitrade.activity.facade.goods.BlockChain getChain();

  /**
   * <pre>
   *商品价格
   * </pre>
   *
   * <code>string price = 9;</code>
   * @return The price.
   */
  java.lang.String getPrice();
  /**
   * <pre>
   *商品价格
   * </pre>
   *
   * <code>string price = 9;</code>
   * @return The bytes for price.
   */
  com.google.protobuf.ByteString
      getPriceBytes();

  /**
   * <pre>
   *库存
   * </pre>
   *
   * <code>int32 stock = 10;</code>
   * @return The stock.
   */
  int getStock();

  /**
   * <pre>
   *列表图片
   * </pre>
   *
   * <code>string listImage = 11;</code>
   * @return The listImage.
   */
  java.lang.String getListImage();
  /**
   * <pre>
   *列表图片
   * </pre>
   *
   * <code>string listImage = 11;</code>
   * @return The bytes for listImage.
   */
  com.google.protobuf.ByteString
      getListImageBytes();

  /**
   * <pre>
   *详情图片
   * </pre>
   *
   * <code>string detailImage = 12;</code>
   * @return The detailImage.
   */
  java.lang.String getDetailImage();
  /**
   * <pre>
   *详情图片
   * </pre>
   *
   * <code>string detailImage = 12;</code>
   * @return The bytes for detailImage.
   */
  com.google.protobuf.ByteString
      getDetailImageBytes();

  /**
   * <pre>
   *分享图片
   * </pre>
   *
   * <code>string shareImage = 13;</code>
   * @return The shareImage.
   */
  java.lang.String getShareImage();
  /**
   * <pre>
   *分享图片
   * </pre>
   *
   * <code>string shareImage = 13;</code>
   * @return The bytes for shareImage.
   */
  com.google.protobuf.ByteString
      getShareImageBytes();

  /**
   * <pre>
   *标签名称
   * </pre>
   *
   * <code>string labelName = 14;</code>
   * @return The labelName.
   */
  java.lang.String getLabelName();
  /**
   * <pre>
   *标签名称
   * </pre>
   *
   * <code>string labelName = 14;</code>
   * @return The bytes for labelName.
   */
  com.google.protobuf.ByteString
      getLabelNameBytes();

  /**
   * <pre>
   *标签颜色
   * </pre>
   *
   * <code>string labelColor = 15;</code>
   * @return The labelColor.
   */
  java.lang.String getLabelColor();
  /**
   * <pre>
   *标签颜色
   * </pre>
   *
   * <code>string labelColor = 15;</code>
   * @return The bytes for labelColor.
   */
  com.google.protobuf.ByteString
      getLabelColorBytes();

  /**
   * <pre>
   *外部商品id
   * </pre>
   *
   * <code>string outId = 16;</code>
   * @return The outId.
   */
  java.lang.String getOutId();
  /**
   * <pre>
   *外部商品id
   * </pre>
   *
   * <code>string outId = 16;</code>
   * @return The bytes for outId.
   */
  com.google.protobuf.ByteString
      getOutIdBytes();

  /**
   * <pre>
   *前置任务id
   * </pre>
   *
   * <code>string preQuestId = 17;</code>
   * @return The preQuestId.
   */
  java.lang.String getPreQuestId();
  /**
   * <pre>
   *前置任务id
   * </pre>
   *
   * <code>string preQuestId = 17;</code>
   * @return The bytes for preQuestId.
   */
  com.google.protobuf.ByteString
      getPreQuestIdBytes();

  /**
   * <pre>
   *saasId
   * </pre>
   *
   * <code>string saasId = 18;</code>
   * @return The saasId.
   */
  java.lang.String getSaasId();
  /**
   * <pre>
   *saasId
   * </pre>
   *
   * <code>string saasId = 18;</code>
   * @return The bytes for saasId.
   */
  com.google.protobuf.ByteString
      getSaasIdBytes();

  /**
   * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
   * @return A list containing the test.
   */
  java.util.List<com.kikitrade.activity.facade.goods.GoodsType> getTestList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
   * @return The count of test.
   */
  int getTestCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
   * @param index The index of the element to return.
   * @return The test at the given index.
   */
  com.kikitrade.activity.facade.goods.GoodsType getTest(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
   * @return A list containing the enum numeric values on the wire for test.
   */
  java.util.List<java.lang.Integer>
  getTestValueList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.goods.GoodsType test = 19;</code>
   * @param index The index of the value to return.
   * @return The enum numeric value on the wire of test at the given index.
   */
  int getTestValue(int index);
}
