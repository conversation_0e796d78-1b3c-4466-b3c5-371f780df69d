// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Task.proto

package com.kikitrade.activity.facade.taskv2;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.taskv2.VipLevel}
 */
public enum VipLevel
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>NORMAL = 0;</code>
   */
  NORMAL(0),
  /**
   * <code>VIP = 1;</code>
   */
  VIP(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>NORMAL = 0;</code>
   */
  public static final int NORMAL_VALUE = 0;
  /**
   * <code>VIP = 1;</code>
   */
  public static final int VIP_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static VipLevel valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static VipLevel forNumber(int value) {
    switch (value) {
      case 0: return NORMAL;
      case 1: return VIP;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<VipLevel>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      VipLevel> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<VipLevel>() {
          public VipLevel findValueByNumber(int number) {
            return VipLevel.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.getDescriptor().getEnumTypes().get(0);
  }

  private static final VipLevel[] VALUES = values();

  public static VipLevel valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private VipLevel(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.taskv2.VipLevel)
}

