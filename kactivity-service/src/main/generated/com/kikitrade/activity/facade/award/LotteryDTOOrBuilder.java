// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface LotteryDTOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.LotteryDTO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *奖池id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   *奖池id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   *valid
   * </pre>
   *
   * <code>string valid = 2;</code>
   * @return The valid.
   */
  java.lang.String getValid();
  /**
   * <pre>
   *valid
   * </pre>
   *
   * <code>string valid = 2;</code>
   * @return The bytes for valid.
   */
  com.google.protobuf.ByteString
      getValidBytes();

  /**
   * <pre>
   *remark
   * </pre>
   *
   * <code>string remark = 3;</code>
   * @return The remark.
   */
  java.lang.String getRemark();
  /**
   * <pre>
   *remark
   * </pre>
   *
   * <code>string remark = 3;</code>
   * @return The bytes for remark.
   */
  com.google.protobuf.ByteString
      getRemarkBytes();

  /**
   * <pre>
   *status
   * </pre>
   *
   * <code>string status = 4;</code>
   * @return The status.
   */
  java.lang.String getStatus();
  /**
   * <pre>
   *status
   * </pre>
   *
   * <code>string status = 4;</code>
   * @return The bytes for status.
   */
  com.google.protobuf.ByteString
      getStatusBytes();

  /**
   * <pre>
   *规则vip等级
   * </pre>
   *
   * <code>string vipLevel = 5;</code>
   * @return The vipLevel.
   */
  java.lang.String getVipLevel();
  /**
   * <pre>
   *规则vip等级
   * </pre>
   *
   * <code>string vipLevel = 5;</code>
   * @return The bytes for vipLevel.
   */
  com.google.protobuf.ByteString
      getVipLevelBytes();

  /**
   * <pre>
   *消耗燃料（次）
   * </pre>
   *
   * <code>string amount = 6;</code>
   * @return The amount.
   */
  java.lang.String getAmount();
  /**
   * <pre>
   *消耗燃料（次）
   * </pre>
   *
   * <code>string amount = 6;</code>
   * @return The bytes for amount.
   */
  com.google.protobuf.ByteString
      getAmountBytes();

  /**
   * <pre>
   *参与次数
   * </pre>
   *
   * <code>string timesLimit = 7;</code>
   * @return The timesLimit.
   */
  java.lang.String getTimesLimit();
  /**
   * <pre>
   *参与次数
   * </pre>
   *
   * <code>string timesLimit = 7;</code>
   * @return The bytes for timesLimit.
   */
  com.google.protobuf.ByteString
      getTimesLimitBytes();

  /**
   * <pre>
   *奖励金额上限
   * </pre>
   *
   * <code>string rewardLimit = 8;</code>
   * @return The rewardLimit.
   */
  java.lang.String getRewardLimit();
  /**
   * <pre>
   *奖励金额上限
   * </pre>
   *
   * <code>string rewardLimit = 8;</code>
   * @return The bytes for rewardLimit.
   */
  com.google.protobuf.ByteString
      getRewardLimitBytes();

  /**
   * <pre>
   *奖品url
   * </pre>
   *
   * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 9;</code>
   */
  java.util.List<com.kikitrade.activity.facade.award.LotteryItem> 
      getItemList();
  /**
   * <pre>
   *奖品url
   * </pre>
   *
   * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 9;</code>
   */
  com.kikitrade.activity.facade.award.LotteryItem getItem(int index);
  /**
   * <pre>
   *奖品url
   * </pre>
   *
   * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 9;</code>
   */
  int getItemCount();
  /**
   * <pre>
   *奖品url
   * </pre>
   *
   * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 9;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.award.LotteryItemOrBuilder> 
      getItemOrBuilderList();
  /**
   * <pre>
   *奖品url
   * </pre>
   *
   * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 9;</code>
   */
  com.kikitrade.activity.facade.award.LotteryItemOrBuilder getItemOrBuilder(
      int index);
}
