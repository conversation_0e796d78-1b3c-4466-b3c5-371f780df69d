// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface UploadRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.UploadRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string batchId = 1;</code>
   * @return The batchId.
   */
  java.lang.String getBatchId();
  /**
   * <code>string batchId = 1;</code>
   * @return The bytes for batchId.
   */
  com.google.protobuf.ByteString
      getBatchIdBytes();

  /**
   * <code>string fileName = 2;</code>
   * @return The fileName.
   */
  java.lang.String getFileName();
  /**
   * <code>string fileName = 2;</code>
   * @return The bytes for fileName.
   */
  com.google.protobuf.ByteString
      getFileNameBytes();
}
