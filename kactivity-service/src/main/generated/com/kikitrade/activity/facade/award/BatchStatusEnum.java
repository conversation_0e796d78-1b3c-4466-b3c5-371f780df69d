// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.award.BatchStatusEnum}
 */
public enum BatchStatusEnum
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>ALL = 0;</code>
   */
  ALL(0),
  /**
   * <code>NOT_IMPORTED = 1;</code>
   */
  NOT_IMPORTED(1),
  /**
   * <code>IMPORTING = 2;</code>
   */
  IMPORTING(2),
  /**
   * <code>IMPORT_FAILED = 3;</code>
   */
  IMPORT_FAILED(3),
  /**
   * <code>UNAUDITED = 4;</code>
   */
  UNAUDITED(4),
  /**
   * <code>REJECTED = 5;</code>
   */
  REJECTED(5),
  /**
   * <code>APPROVED = 6;</code>
   */
  APPROVED(6),
  /**
   * <code>AWARDING = 7;</code>
   */
  AWARDING(7),
  /**
   * <code>AWARD_FAILED = 8;</code>
   */
  AWARD_FAILED(8),
  /**
   * <code>AWARD_SUCCESS = 9;</code>
   */
  AWARD_SUCCESS(9),
  /**
   * <code>ODPS_RUNNING = 10;</code>
   */
  ODPS_RUNNING(10),
  /**
   * <code>ODPS_FAILED = 11;</code>
   */
  ODPS_FAILED(11),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>ALL = 0;</code>
   */
  public static final int ALL_VALUE = 0;
  /**
   * <code>NOT_IMPORTED = 1;</code>
   */
  public static final int NOT_IMPORTED_VALUE = 1;
  /**
   * <code>IMPORTING = 2;</code>
   */
  public static final int IMPORTING_VALUE = 2;
  /**
   * <code>IMPORT_FAILED = 3;</code>
   */
  public static final int IMPORT_FAILED_VALUE = 3;
  /**
   * <code>UNAUDITED = 4;</code>
   */
  public static final int UNAUDITED_VALUE = 4;
  /**
   * <code>REJECTED = 5;</code>
   */
  public static final int REJECTED_VALUE = 5;
  /**
   * <code>APPROVED = 6;</code>
   */
  public static final int APPROVED_VALUE = 6;
  /**
   * <code>AWARDING = 7;</code>
   */
  public static final int AWARDING_VALUE = 7;
  /**
   * <code>AWARD_FAILED = 8;</code>
   */
  public static final int AWARD_FAILED_VALUE = 8;
  /**
   * <code>AWARD_SUCCESS = 9;</code>
   */
  public static final int AWARD_SUCCESS_VALUE = 9;
  /**
   * <code>ODPS_RUNNING = 10;</code>
   */
  public static final int ODPS_RUNNING_VALUE = 10;
  /**
   * <code>ODPS_FAILED = 11;</code>
   */
  public static final int ODPS_FAILED_VALUE = 11;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static BatchStatusEnum valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static BatchStatusEnum forNumber(int value) {
    switch (value) {
      case 0: return ALL;
      case 1: return NOT_IMPORTED;
      case 2: return IMPORTING;
      case 3: return IMPORT_FAILED;
      case 4: return UNAUDITED;
      case 5: return REJECTED;
      case 6: return APPROVED;
      case 7: return AWARDING;
      case 8: return AWARD_FAILED;
      case 9: return AWARD_SUCCESS;
      case 10: return ODPS_RUNNING;
      case 11: return ODPS_FAILED;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<BatchStatusEnum>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      BatchStatusEnum> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<BatchStatusEnum>() {
          public BatchStatusEnum findValueByNumber(int number) {
            return BatchStatusEnum.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.getDescriptor().getEnumTypes().get(4);
  }

  private static final BatchStatusEnum[] VALUES = values();

  public static BatchStatusEnum valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private BatchStatusEnum(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.award.BatchStatusEnum)
}

