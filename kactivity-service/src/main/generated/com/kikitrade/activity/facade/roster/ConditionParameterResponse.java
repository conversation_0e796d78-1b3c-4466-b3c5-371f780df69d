// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityRosterRule.proto

package com.kikitrade.activity.facade.roster;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.roster.ConditionParameterResponse}
 */
public final class ConditionParameterResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.roster.ConditionParameterResponse)
    ConditionParameterResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ConditionParameterResponse.newBuilder() to construct.
  private ConditionParameterResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ConditionParameterResponse() {
    conditionParameter_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ConditionParameterResponse();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionParameterResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionParameterResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.roster.ConditionParameterResponse.class, com.kikitrade.activity.facade.roster.ConditionParameterResponse.Builder.class);
  }

  public static final int CONDITIONPARAMETER_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.roster.ConditionParameter> conditionParameter_;
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.roster.ConditionParameter> getConditionParameterList() {
    return conditionParameter_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.roster.ConditionParameterOrBuilder> 
      getConditionParameterOrBuilderList() {
    return conditionParameter_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
   */
  @java.lang.Override
  public int getConditionParameterCount() {
    return conditionParameter_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.roster.ConditionParameter getConditionParameter(int index) {
    return conditionParameter_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.roster.ConditionParameterOrBuilder getConditionParameterOrBuilder(
      int index) {
    return conditionParameter_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < conditionParameter_.size(); i++) {
      output.writeMessage(1, conditionParameter_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < conditionParameter_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, conditionParameter_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.roster.ConditionParameterResponse)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.roster.ConditionParameterResponse other = (com.kikitrade.activity.facade.roster.ConditionParameterResponse) obj;

    if (!getConditionParameterList()
        .equals(other.getConditionParameterList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getConditionParameterCount() > 0) {
      hash = (37 * hash) + CONDITIONPARAMETER_FIELD_NUMBER;
      hash = (53 * hash) + getConditionParameterList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.roster.ConditionParameterResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameterResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameterResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameterResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameterResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameterResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameterResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameterResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.roster.ConditionParameterResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.roster.ConditionParameterResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameterResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.roster.ConditionParameterResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.roster.ConditionParameterResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.roster.ConditionParameterResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.roster.ConditionParameterResponse)
      com.kikitrade.activity.facade.roster.ConditionParameterResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionParameterResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionParameterResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.roster.ConditionParameterResponse.class, com.kikitrade.activity.facade.roster.ConditionParameterResponse.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.roster.ConditionParameterResponse.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (conditionParameterBuilder_ == null) {
        conditionParameter_ = java.util.Collections.emptyList();
      } else {
        conditionParameter_ = null;
        conditionParameterBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionParameterResponse_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.roster.ConditionParameterResponse getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.roster.ConditionParameterResponse.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.roster.ConditionParameterResponse build() {
      com.kikitrade.activity.facade.roster.ConditionParameterResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.roster.ConditionParameterResponse buildPartial() {
      com.kikitrade.activity.facade.roster.ConditionParameterResponse result = new com.kikitrade.activity.facade.roster.ConditionParameterResponse(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.roster.ConditionParameterResponse result) {
      if (conditionParameterBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          conditionParameter_ = java.util.Collections.unmodifiableList(conditionParameter_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.conditionParameter_ = conditionParameter_;
      } else {
        result.conditionParameter_ = conditionParameterBuilder_.build();
      }
    }

    private void buildPartial0(com.kikitrade.activity.facade.roster.ConditionParameterResponse result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.roster.ConditionParameterResponse) {
        return mergeFrom((com.kikitrade.activity.facade.roster.ConditionParameterResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.roster.ConditionParameterResponse other) {
      if (other == com.kikitrade.activity.facade.roster.ConditionParameterResponse.getDefaultInstance()) return this;
      if (conditionParameterBuilder_ == null) {
        if (!other.conditionParameter_.isEmpty()) {
          if (conditionParameter_.isEmpty()) {
            conditionParameter_ = other.conditionParameter_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureConditionParameterIsMutable();
            conditionParameter_.addAll(other.conditionParameter_);
          }
          onChanged();
        }
      } else {
        if (!other.conditionParameter_.isEmpty()) {
          if (conditionParameterBuilder_.isEmpty()) {
            conditionParameterBuilder_.dispose();
            conditionParameterBuilder_ = null;
            conditionParameter_ = other.conditionParameter_;
            bitField0_ = (bitField0_ & ~0x00000001);
            conditionParameterBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getConditionParameterFieldBuilder() : null;
          } else {
            conditionParameterBuilder_.addAllMessages(other.conditionParameter_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.kikitrade.activity.facade.roster.ConditionParameter m =
                  input.readMessage(
                      com.kikitrade.activity.facade.roster.ConditionParameter.parser(),
                      extensionRegistry);
              if (conditionParameterBuilder_ == null) {
                ensureConditionParameterIsMutable();
                conditionParameter_.add(m);
              } else {
                conditionParameterBuilder_.addMessage(m);
              }
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<com.kikitrade.activity.facade.roster.ConditionParameter> conditionParameter_ =
      java.util.Collections.emptyList();
    private void ensureConditionParameterIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        conditionParameter_ = new java.util.ArrayList<com.kikitrade.activity.facade.roster.ConditionParameter>(conditionParameter_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.roster.ConditionParameter, com.kikitrade.activity.facade.roster.ConditionParameter.Builder, com.kikitrade.activity.facade.roster.ConditionParameterOrBuilder> conditionParameterBuilder_;

    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.roster.ConditionParameter> getConditionParameterList() {
      if (conditionParameterBuilder_ == null) {
        return java.util.Collections.unmodifiableList(conditionParameter_);
      } else {
        return conditionParameterBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public int getConditionParameterCount() {
      if (conditionParameterBuilder_ == null) {
        return conditionParameter_.size();
      } else {
        return conditionParameterBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public com.kikitrade.activity.facade.roster.ConditionParameter getConditionParameter(int index) {
      if (conditionParameterBuilder_ == null) {
        return conditionParameter_.get(index);
      } else {
        return conditionParameterBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public Builder setConditionParameter(
        int index, com.kikitrade.activity.facade.roster.ConditionParameter value) {
      if (conditionParameterBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionParameterIsMutable();
        conditionParameter_.set(index, value);
        onChanged();
      } else {
        conditionParameterBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public Builder setConditionParameter(
        int index, com.kikitrade.activity.facade.roster.ConditionParameter.Builder builderForValue) {
      if (conditionParameterBuilder_ == null) {
        ensureConditionParameterIsMutable();
        conditionParameter_.set(index, builderForValue.build());
        onChanged();
      } else {
        conditionParameterBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public Builder addConditionParameter(com.kikitrade.activity.facade.roster.ConditionParameter value) {
      if (conditionParameterBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionParameterIsMutable();
        conditionParameter_.add(value);
        onChanged();
      } else {
        conditionParameterBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public Builder addConditionParameter(
        int index, com.kikitrade.activity.facade.roster.ConditionParameter value) {
      if (conditionParameterBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionParameterIsMutable();
        conditionParameter_.add(index, value);
        onChanged();
      } else {
        conditionParameterBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public Builder addConditionParameter(
        com.kikitrade.activity.facade.roster.ConditionParameter.Builder builderForValue) {
      if (conditionParameterBuilder_ == null) {
        ensureConditionParameterIsMutable();
        conditionParameter_.add(builderForValue.build());
        onChanged();
      } else {
        conditionParameterBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public Builder addConditionParameter(
        int index, com.kikitrade.activity.facade.roster.ConditionParameter.Builder builderForValue) {
      if (conditionParameterBuilder_ == null) {
        ensureConditionParameterIsMutable();
        conditionParameter_.add(index, builderForValue.build());
        onChanged();
      } else {
        conditionParameterBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public Builder addAllConditionParameter(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.roster.ConditionParameter> values) {
      if (conditionParameterBuilder_ == null) {
        ensureConditionParameterIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, conditionParameter_);
        onChanged();
      } else {
        conditionParameterBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public Builder clearConditionParameter() {
      if (conditionParameterBuilder_ == null) {
        conditionParameter_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        conditionParameterBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public Builder removeConditionParameter(int index) {
      if (conditionParameterBuilder_ == null) {
        ensureConditionParameterIsMutable();
        conditionParameter_.remove(index);
        onChanged();
      } else {
        conditionParameterBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public com.kikitrade.activity.facade.roster.ConditionParameter.Builder getConditionParameterBuilder(
        int index) {
      return getConditionParameterFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public com.kikitrade.activity.facade.roster.ConditionParameterOrBuilder getConditionParameterOrBuilder(
        int index) {
      if (conditionParameterBuilder_ == null) {
        return conditionParameter_.get(index);  } else {
        return conditionParameterBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.roster.ConditionParameterOrBuilder> 
         getConditionParameterOrBuilderList() {
      if (conditionParameterBuilder_ != null) {
        return conditionParameterBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(conditionParameter_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public com.kikitrade.activity.facade.roster.ConditionParameter.Builder addConditionParameterBuilder() {
      return getConditionParameterFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.roster.ConditionParameter.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public com.kikitrade.activity.facade.roster.ConditionParameter.Builder addConditionParameterBuilder(
        int index) {
      return getConditionParameterFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.roster.ConditionParameter.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.roster.ConditionParameter.Builder> 
         getConditionParameterBuilderList() {
      return getConditionParameterFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.roster.ConditionParameter, com.kikitrade.activity.facade.roster.ConditionParameter.Builder, com.kikitrade.activity.facade.roster.ConditionParameterOrBuilder> 
        getConditionParameterFieldBuilder() {
      if (conditionParameterBuilder_ == null) {
        conditionParameterBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.roster.ConditionParameter, com.kikitrade.activity.facade.roster.ConditionParameter.Builder, com.kikitrade.activity.facade.roster.ConditionParameterOrBuilder>(
                conditionParameter_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        conditionParameter_ = null;
      }
      return conditionParameterBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.roster.ConditionParameterResponse)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.roster.ConditionParameterResponse)
  private static final com.kikitrade.activity.facade.roster.ConditionParameterResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.roster.ConditionParameterResponse();
  }

  public static com.kikitrade.activity.facade.roster.ConditionParameterResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ConditionParameterResponse>
      PARSER = new com.google.protobuf.AbstractParser<ConditionParameterResponse>() {
    @java.lang.Override
    public ConditionParameterResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ConditionParameterResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ConditionParameterResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.roster.ConditionParameterResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

