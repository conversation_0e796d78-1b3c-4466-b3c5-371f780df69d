// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityTemplate.proto

package com.kikitrade.activity.facade.template;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.template.ActivityMaterialDTO}
 */
public final class ActivityMaterialDTO extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.template.ActivityMaterialDTO)
    ActivityMaterialDTOOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ActivityMaterialDTO.newBuilder() to construct.
  private ActivityMaterialDTO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ActivityMaterialDTO() {
    activityId_ = "";
    templateStatus_ = "";
    material_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ActivityMaterialDTO();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.template.ActivityTemplateFacadeOutClass.internal_static_com_kikitrade_activity_facade_template_ActivityMaterialDTO_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.template.ActivityTemplateFacadeOutClass.internal_static_com_kikitrade_activity_facade_template_ActivityMaterialDTO_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.template.ActivityMaterialDTO.class, com.kikitrade.activity.facade.template.ActivityMaterialDTO.Builder.class);
  }

  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object activityId_ = "";
  /**
   * <code>string activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public java.lang.String getActivityId() {
    java.lang.Object ref = activityId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      activityId_ = s;
      return s;
    }
  }
  /**
   * <code>string activityId = 1;</code>
   * @return The bytes for activityId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getActivityIdBytes() {
    java.lang.Object ref = activityId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      activityId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TEMPLATESTATUS_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object templateStatus_ = "";
  /**
   * <code>string templateStatus = 2;</code>
   * @return The templateStatus.
   */
  @java.lang.Override
  public java.lang.String getTemplateStatus() {
    java.lang.Object ref = templateStatus_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      templateStatus_ = s;
      return s;
    }
  }
  /**
   * <code>string templateStatus = 2;</code>
   * @return The bytes for templateStatus.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTemplateStatusBytes() {
    java.lang.Object ref = templateStatus_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      templateStatus_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MATERIAL_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.template.ActivityMaterial> material_;
  /**
   * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.template.ActivityMaterial> getMaterialList() {
    return material_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.template.ActivityMaterialOrBuilder> 
      getMaterialOrBuilderList() {
    return material_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
   */
  @java.lang.Override
  public int getMaterialCount() {
    return material_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.template.ActivityMaterial getMaterial(int index) {
    return material_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.template.ActivityMaterialOrBuilder getMaterialOrBuilder(
      int index) {
    return material_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, activityId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(templateStatus_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, templateStatus_);
    }
    for (int i = 0; i < material_.size(); i++) {
      output.writeMessage(3, material_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, activityId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(templateStatus_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, templateStatus_);
    }
    for (int i = 0; i < material_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, material_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.template.ActivityMaterialDTO)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.template.ActivityMaterialDTO other = (com.kikitrade.activity.facade.template.ActivityMaterialDTO) obj;

    if (!getActivityId()
        .equals(other.getActivityId())) return false;
    if (!getTemplateStatus()
        .equals(other.getTemplateStatus())) return false;
    if (!getMaterialList()
        .equals(other.getMaterialList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
    hash = (53 * hash) + getActivityId().hashCode();
    hash = (37 * hash) + TEMPLATESTATUS_FIELD_NUMBER;
    hash = (53 * hash) + getTemplateStatus().hashCode();
    if (getMaterialCount() > 0) {
      hash = (37 * hash) + MATERIAL_FIELD_NUMBER;
      hash = (53 * hash) + getMaterialList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.template.ActivityMaterialDTO parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.template.ActivityMaterialDTO parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.template.ActivityMaterialDTO parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.template.ActivityMaterialDTO parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.template.ActivityMaterialDTO parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.template.ActivityMaterialDTO parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.template.ActivityMaterialDTO parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.template.ActivityMaterialDTO parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.template.ActivityMaterialDTO parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.template.ActivityMaterialDTO parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.template.ActivityMaterialDTO parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.template.ActivityMaterialDTO parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.template.ActivityMaterialDTO prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.template.ActivityMaterialDTO}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.template.ActivityMaterialDTO)
      com.kikitrade.activity.facade.template.ActivityMaterialDTOOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.template.ActivityTemplateFacadeOutClass.internal_static_com_kikitrade_activity_facade_template_ActivityMaterialDTO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.template.ActivityTemplateFacadeOutClass.internal_static_com_kikitrade_activity_facade_template_ActivityMaterialDTO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.template.ActivityMaterialDTO.class, com.kikitrade.activity.facade.template.ActivityMaterialDTO.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.template.ActivityMaterialDTO.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = "";
      templateStatus_ = "";
      if (materialBuilder_ == null) {
        material_ = java.util.Collections.emptyList();
      } else {
        material_ = null;
        materialBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.template.ActivityTemplateFacadeOutClass.internal_static_com_kikitrade_activity_facade_template_ActivityMaterialDTO_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.template.ActivityMaterialDTO getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.template.ActivityMaterialDTO.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.template.ActivityMaterialDTO build() {
      com.kikitrade.activity.facade.template.ActivityMaterialDTO result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.template.ActivityMaterialDTO buildPartial() {
      com.kikitrade.activity.facade.template.ActivityMaterialDTO result = new com.kikitrade.activity.facade.template.ActivityMaterialDTO(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.template.ActivityMaterialDTO result) {
      if (materialBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          material_ = java.util.Collections.unmodifiableList(material_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.material_ = material_;
      } else {
        result.material_ = materialBuilder_.build();
      }
    }

    private void buildPartial0(com.kikitrade.activity.facade.template.ActivityMaterialDTO result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.templateStatus_ = templateStatus_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.template.ActivityMaterialDTO) {
        return mergeFrom((com.kikitrade.activity.facade.template.ActivityMaterialDTO)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.template.ActivityMaterialDTO other) {
      if (other == com.kikitrade.activity.facade.template.ActivityMaterialDTO.getDefaultInstance()) return this;
      if (!other.getActivityId().isEmpty()) {
        activityId_ = other.activityId_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getTemplateStatus().isEmpty()) {
        templateStatus_ = other.templateStatus_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (materialBuilder_ == null) {
        if (!other.material_.isEmpty()) {
          if (material_.isEmpty()) {
            material_ = other.material_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureMaterialIsMutable();
            material_.addAll(other.material_);
          }
          onChanged();
        }
      } else {
        if (!other.material_.isEmpty()) {
          if (materialBuilder_.isEmpty()) {
            materialBuilder_.dispose();
            materialBuilder_ = null;
            material_ = other.material_;
            bitField0_ = (bitField0_ & ~0x00000004);
            materialBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getMaterialFieldBuilder() : null;
          } else {
            materialBuilder_.addAllMessages(other.material_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              activityId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              templateStatus_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              com.kikitrade.activity.facade.template.ActivityMaterial m =
                  input.readMessage(
                      com.kikitrade.activity.facade.template.ActivityMaterial.parser(),
                      extensionRegistry);
              if (materialBuilder_ == null) {
                ensureMaterialIsMutable();
                material_.add(m);
              } else {
                materialBuilder_.addMessage(m);
              }
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object activityId_ = "";
    /**
     * <code>string activityId = 1;</code>
     * @return The activityId.
     */
    public java.lang.String getActivityId() {
      java.lang.Object ref = activityId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        activityId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string activityId = 1;</code>
     * @return The bytes for activityId.
     */
    public com.google.protobuf.ByteString
        getActivityIdBytes() {
      java.lang.Object ref = activityId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        activityId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      activityId_ = getDefaultInstance().getActivityId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string activityId = 1;</code>
     * @param value The bytes for activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object templateStatus_ = "";
    /**
     * <code>string templateStatus = 2;</code>
     * @return The templateStatus.
     */
    public java.lang.String getTemplateStatus() {
      java.lang.Object ref = templateStatus_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        templateStatus_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string templateStatus = 2;</code>
     * @return The bytes for templateStatus.
     */
    public com.google.protobuf.ByteString
        getTemplateStatusBytes() {
      java.lang.Object ref = templateStatus_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        templateStatus_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string templateStatus = 2;</code>
     * @param value The templateStatus to set.
     * @return This builder for chaining.
     */
    public Builder setTemplateStatus(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      templateStatus_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string templateStatus = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearTemplateStatus() {
      templateStatus_ = getDefaultInstance().getTemplateStatus();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string templateStatus = 2;</code>
     * @param value The bytes for templateStatus to set.
     * @return This builder for chaining.
     */
    public Builder setTemplateStatusBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      templateStatus_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.util.List<com.kikitrade.activity.facade.template.ActivityMaterial> material_ =
      java.util.Collections.emptyList();
    private void ensureMaterialIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        material_ = new java.util.ArrayList<com.kikitrade.activity.facade.template.ActivityMaterial>(material_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.template.ActivityMaterial, com.kikitrade.activity.facade.template.ActivityMaterial.Builder, com.kikitrade.activity.facade.template.ActivityMaterialOrBuilder> materialBuilder_;

    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.template.ActivityMaterial> getMaterialList() {
      if (materialBuilder_ == null) {
        return java.util.Collections.unmodifiableList(material_);
      } else {
        return materialBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public int getMaterialCount() {
      if (materialBuilder_ == null) {
        return material_.size();
      } else {
        return materialBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public com.kikitrade.activity.facade.template.ActivityMaterial getMaterial(int index) {
      if (materialBuilder_ == null) {
        return material_.get(index);
      } else {
        return materialBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public Builder setMaterial(
        int index, com.kikitrade.activity.facade.template.ActivityMaterial value) {
      if (materialBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMaterialIsMutable();
        material_.set(index, value);
        onChanged();
      } else {
        materialBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public Builder setMaterial(
        int index, com.kikitrade.activity.facade.template.ActivityMaterial.Builder builderForValue) {
      if (materialBuilder_ == null) {
        ensureMaterialIsMutable();
        material_.set(index, builderForValue.build());
        onChanged();
      } else {
        materialBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public Builder addMaterial(com.kikitrade.activity.facade.template.ActivityMaterial value) {
      if (materialBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMaterialIsMutable();
        material_.add(value);
        onChanged();
      } else {
        materialBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public Builder addMaterial(
        int index, com.kikitrade.activity.facade.template.ActivityMaterial value) {
      if (materialBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMaterialIsMutable();
        material_.add(index, value);
        onChanged();
      } else {
        materialBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public Builder addMaterial(
        com.kikitrade.activity.facade.template.ActivityMaterial.Builder builderForValue) {
      if (materialBuilder_ == null) {
        ensureMaterialIsMutable();
        material_.add(builderForValue.build());
        onChanged();
      } else {
        materialBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public Builder addMaterial(
        int index, com.kikitrade.activity.facade.template.ActivityMaterial.Builder builderForValue) {
      if (materialBuilder_ == null) {
        ensureMaterialIsMutable();
        material_.add(index, builderForValue.build());
        onChanged();
      } else {
        materialBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public Builder addAllMaterial(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.template.ActivityMaterial> values) {
      if (materialBuilder_ == null) {
        ensureMaterialIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, material_);
        onChanged();
      } else {
        materialBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public Builder clearMaterial() {
      if (materialBuilder_ == null) {
        material_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        materialBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public Builder removeMaterial(int index) {
      if (materialBuilder_ == null) {
        ensureMaterialIsMutable();
        material_.remove(index);
        onChanged();
      } else {
        materialBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public com.kikitrade.activity.facade.template.ActivityMaterial.Builder getMaterialBuilder(
        int index) {
      return getMaterialFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public com.kikitrade.activity.facade.template.ActivityMaterialOrBuilder getMaterialOrBuilder(
        int index) {
      if (materialBuilder_ == null) {
        return material_.get(index);  } else {
        return materialBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.template.ActivityMaterialOrBuilder> 
         getMaterialOrBuilderList() {
      if (materialBuilder_ != null) {
        return materialBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(material_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public com.kikitrade.activity.facade.template.ActivityMaterial.Builder addMaterialBuilder() {
      return getMaterialFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.template.ActivityMaterial.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public com.kikitrade.activity.facade.template.ActivityMaterial.Builder addMaterialBuilder(
        int index) {
      return getMaterialFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.template.ActivityMaterial.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.template.ActivityMaterial.Builder> 
         getMaterialBuilderList() {
      return getMaterialFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.template.ActivityMaterial, com.kikitrade.activity.facade.template.ActivityMaterial.Builder, com.kikitrade.activity.facade.template.ActivityMaterialOrBuilder> 
        getMaterialFieldBuilder() {
      if (materialBuilder_ == null) {
        materialBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.template.ActivityMaterial, com.kikitrade.activity.facade.template.ActivityMaterial.Builder, com.kikitrade.activity.facade.template.ActivityMaterialOrBuilder>(
                material_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        material_ = null;
      }
      return materialBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.template.ActivityMaterialDTO)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.template.ActivityMaterialDTO)
  private static final com.kikitrade.activity.facade.template.ActivityMaterialDTO DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.template.ActivityMaterialDTO();
  }

  public static com.kikitrade.activity.facade.template.ActivityMaterialDTO getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ActivityMaterialDTO>
      PARSER = new com.google.protobuf.AbstractParser<ActivityMaterialDTO>() {
    @java.lang.Override
    public ActivityMaterialDTO parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ActivityMaterialDTO> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ActivityMaterialDTO> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.template.ActivityMaterialDTO getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

