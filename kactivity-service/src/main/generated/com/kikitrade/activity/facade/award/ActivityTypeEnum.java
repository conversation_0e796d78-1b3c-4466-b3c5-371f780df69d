// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.award.ActivityTypeEnum}
 */
public enum ActivityTypeEnum
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *层级发奖
   * </pre>
   *
   * <code>HIERARCHY = 0;</code>
   */
  HIERARCHY(0),
  /**
   * <pre>
   *邀请发奖
   * </pre>
   *
   * <code>INVITE = 1;</code>
   */
  INVITE(1),
  /**
   * <pre>
   *自定义
   * </pre>
   *
   * <code>CUSTOMIZE = 2;</code>
   */
  CUSTOMIZE(2),
  /**
   * <pre>
   *普通
   * </pre>
   *
   * <code>NORMAL = 3;</code>
   */
  NORMAL(3),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *层级发奖
   * </pre>
   *
   * <code>HIERARCHY = 0;</code>
   */
  public static final int HIERARCHY_VALUE = 0;
  /**
   * <pre>
   *邀请发奖
   * </pre>
   *
   * <code>INVITE = 1;</code>
   */
  public static final int INVITE_VALUE = 1;
  /**
   * <pre>
   *自定义
   * </pre>
   *
   * <code>CUSTOMIZE = 2;</code>
   */
  public static final int CUSTOMIZE_VALUE = 2;
  /**
   * <pre>
   *普通
   * </pre>
   *
   * <code>NORMAL = 3;</code>
   */
  public static final int NORMAL_VALUE = 3;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static ActivityTypeEnum valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static ActivityTypeEnum forNumber(int value) {
    switch (value) {
      case 0: return HIERARCHY;
      case 1: return INVITE;
      case 2: return CUSTOMIZE;
      case 3: return NORMAL;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<ActivityTypeEnum>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      ActivityTypeEnum> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<ActivityTypeEnum>() {
          public ActivityTypeEnum findValueByNumber(int number) {
            return ActivityTypeEnum.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.getDescriptor().getEnumTypes().get(1);
  }

  private static final ActivityTypeEnum[] VALUES = values();

  public static ActivityTypeEnum valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private ActivityTypeEnum(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.award.ActivityTypeEnum)
}

