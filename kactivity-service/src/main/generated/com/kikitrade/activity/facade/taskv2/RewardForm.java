// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Task.proto

package com.kikitrade.activity.facade.taskv2;

/**
 * <pre>
 **
 *奖品过滤方式
 * </pre>
 *
 * Protobuf enum {@code com.kikitrade.activity.facade.taskv2.RewardForm}
 */
public enum RewardForm
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *不奖励，默认值
   * </pre>
   *
   * <code>none = 0;</code>
   */
  none(0),
  /**
   * <pre>
   *固定奖励
   * </pre>
   *
   * <code>fixed = 1;</code>
   */
  fixed(1),
  /**
   * <pre>
   *从固定的奖励中随机一个奖品
   * </pre>
   *
   * <code>fixed_random = 2;</code>
   */
  fixed_random(2),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *不奖励，默认值
   * </pre>
   *
   * <code>none = 0;</code>
   */
  public static final int none_VALUE = 0;
  /**
   * <pre>
   *固定奖励
   * </pre>
   *
   * <code>fixed = 1;</code>
   */
  public static final int fixed_VALUE = 1;
  /**
   * <pre>
   *从固定的奖励中随机一个奖品
   * </pre>
   *
   * <code>fixed_random = 2;</code>
   */
  public static final int fixed_random_VALUE = 2;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static RewardForm valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static RewardForm forNumber(int value) {
    switch (value) {
      case 0: return none;
      case 1: return fixed;
      case 2: return fixed_random;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<RewardForm>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      RewardForm> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<RewardForm>() {
          public RewardForm findValueByNumber(int number) {
            return RewardForm.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.getDescriptor().getEnumTypes().get(3);
  }

  private static final RewardForm[] VALUES = values();

  public static RewardForm valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private RewardForm(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.taskv2.RewardForm)
}

