// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Task.proto

package com.kikitrade.activity.facade.taskv2;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.taskv2.RewardDTO}
 */
public final class RewardDTO extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.taskv2.RewardDTO)
    RewardDTOOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RewardDTO.newBuilder() to construct.
  private RewardDTO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RewardDTO() {
    type_ = 0;
    amount_ = "";
    currency_ = "";
    image_ = "";
    desc_ = "";
    vipLevel_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new RewardDTO();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.internal_static_com_kikitrade_activity_facade_taskv2_RewardDTO_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.internal_static_com_kikitrade_activity_facade_taskv2_RewardDTO_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.taskv2.RewardDTO.class, com.kikitrade.activity.facade.taskv2.RewardDTO.Builder.class);
  }

  public static final int CONSECUTIVETIMES_FIELD_NUMBER = 1;
  private int consecutiveTimes_ = 0;
  /**
   * <pre>
   *连续次数，如果ProgressType等于add，该值为0
   * </pre>
   *
   * <code>int32 consecutiveTimes = 1;</code>
   * @return The consecutiveTimes.
   */
  @java.lang.Override
  public int getConsecutiveTimes() {
    return consecutiveTimes_;
  }

  public static final int TYPE_FIELD_NUMBER = 2;
  private int type_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.taskv2.RewardType type = 2;</code>
   * @return The enum numeric value on the wire for type.
   */
  @java.lang.Override public int getTypeValue() {
    return type_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.taskv2.RewardType type = 2;</code>
   * @return The type.
   */
  @java.lang.Override public com.kikitrade.activity.facade.taskv2.RewardType getType() {
    com.kikitrade.activity.facade.taskv2.RewardType result = com.kikitrade.activity.facade.taskv2.RewardType.forNumber(type_);
    return result == null ? com.kikitrade.activity.facade.taskv2.RewardType.UNRECOGNIZED : result;
  }

  public static final int AMOUNT_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object amount_ = "";
  /**
   * <pre>
   *方法数量
   * </pre>
   *
   * <code>string amount = 3;</code>
   * @return The amount.
   */
  @java.lang.Override
  public java.lang.String getAmount() {
    java.lang.Object ref = amount_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      amount_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *方法数量
   * </pre>
   *
   * <code>string amount = 3;</code>
   * @return The bytes for amount.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAmountBytes() {
    java.lang.Object ref = amount_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      amount_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CURRENCY_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object currency_ = "";
  /**
   * <pre>
   *奖励币种
   * </pre>
   *
   * <code>string currency = 4;</code>
   * @return The currency.
   */
  @java.lang.Override
  public java.lang.String getCurrency() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      currency_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *奖励币种
   * </pre>
   *
   * <code>string currency = 4;</code>
   * @return The bytes for currency.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCurrencyBytes() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      currency_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IMAGE_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object image_ = "";
  /**
   * <pre>
   *奖品图片
   * </pre>
   *
   * <code>string image = 5;</code>
   * @return The image.
   */
  @java.lang.Override
  public java.lang.String getImage() {
    java.lang.Object ref = image_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      image_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *奖品图片
   * </pre>
   *
   * <code>string image = 5;</code>
   * @return The bytes for image.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getImageBytes() {
    java.lang.Object ref = image_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      image_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DESC_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object desc_ = "";
  /**
   * <code>string desc = 6;</code>
   * @return The desc.
   */
  @java.lang.Override
  public java.lang.String getDesc() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      desc_ = s;
      return s;
    }
  }
  /**
   * <code>string desc = 6;</code>
   * @return The bytes for desc.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescBytes() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      desc_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int VIPLEVEL_FIELD_NUMBER = 7;
  private int vipLevel_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.taskv2.VipLevel vipLevel = 7;</code>
   * @return The enum numeric value on the wire for vipLevel.
   */
  @java.lang.Override public int getVipLevelValue() {
    return vipLevel_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.taskv2.VipLevel vipLevel = 7;</code>
   * @return The vipLevel.
   */
  @java.lang.Override public com.kikitrade.activity.facade.taskv2.VipLevel getVipLevel() {
    com.kikitrade.activity.facade.taskv2.VipLevel result = com.kikitrade.activity.facade.taskv2.VipLevel.forNumber(vipLevel_);
    return result == null ? com.kikitrade.activity.facade.taskv2.VipLevel.UNRECOGNIZED : result;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (consecutiveTimes_ != 0) {
      output.writeInt32(1, consecutiveTimes_);
    }
    if (type_ != com.kikitrade.activity.facade.taskv2.RewardType.POINT.getNumber()) {
      output.writeEnum(2, type_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amount_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, amount_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(currency_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, currency_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(image_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, image_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(desc_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, desc_);
    }
    if (vipLevel_ != com.kikitrade.activity.facade.taskv2.VipLevel.NORMAL.getNumber()) {
      output.writeEnum(7, vipLevel_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (consecutiveTimes_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, consecutiveTimes_);
    }
    if (type_ != com.kikitrade.activity.facade.taskv2.RewardType.POINT.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, type_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amount_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, amount_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(currency_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, currency_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(image_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, image_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(desc_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, desc_);
    }
    if (vipLevel_ != com.kikitrade.activity.facade.taskv2.VipLevel.NORMAL.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(7, vipLevel_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.taskv2.RewardDTO)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.taskv2.RewardDTO other = (com.kikitrade.activity.facade.taskv2.RewardDTO) obj;

    if (getConsecutiveTimes()
        != other.getConsecutiveTimes()) return false;
    if (type_ != other.type_) return false;
    if (!getAmount()
        .equals(other.getAmount())) return false;
    if (!getCurrency()
        .equals(other.getCurrency())) return false;
    if (!getImage()
        .equals(other.getImage())) return false;
    if (!getDesc()
        .equals(other.getDesc())) return false;
    if (vipLevel_ != other.vipLevel_) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CONSECUTIVETIMES_FIELD_NUMBER;
    hash = (53 * hash) + getConsecutiveTimes();
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + type_;
    hash = (37 * hash) + AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getAmount().hashCode();
    hash = (37 * hash) + CURRENCY_FIELD_NUMBER;
    hash = (53 * hash) + getCurrency().hashCode();
    hash = (37 * hash) + IMAGE_FIELD_NUMBER;
    hash = (53 * hash) + getImage().hashCode();
    hash = (37 * hash) + DESC_FIELD_NUMBER;
    hash = (53 * hash) + getDesc().hashCode();
    hash = (37 * hash) + VIPLEVEL_FIELD_NUMBER;
    hash = (53 * hash) + vipLevel_;
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.taskv2.RewardDTO parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.taskv2.RewardDTO parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.taskv2.RewardDTO parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.taskv2.RewardDTO parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.taskv2.RewardDTO parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.taskv2.RewardDTO parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.taskv2.RewardDTO parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.taskv2.RewardDTO parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.taskv2.RewardDTO parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.taskv2.RewardDTO parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.taskv2.RewardDTO parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.taskv2.RewardDTO parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.taskv2.RewardDTO prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.taskv2.RewardDTO}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.taskv2.RewardDTO)
      com.kikitrade.activity.facade.taskv2.RewardDTOOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.internal_static_com_kikitrade_activity_facade_taskv2_RewardDTO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.internal_static_com_kikitrade_activity_facade_taskv2_RewardDTO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.taskv2.RewardDTO.class, com.kikitrade.activity.facade.taskv2.RewardDTO.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.taskv2.RewardDTO.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      consecutiveTimes_ = 0;
      type_ = 0;
      amount_ = "";
      currency_ = "";
      image_ = "";
      desc_ = "";
      vipLevel_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.internal_static_com_kikitrade_activity_facade_taskv2_RewardDTO_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.taskv2.RewardDTO getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.taskv2.RewardDTO.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.taskv2.RewardDTO build() {
      com.kikitrade.activity.facade.taskv2.RewardDTO result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.taskv2.RewardDTO buildPartial() {
      com.kikitrade.activity.facade.taskv2.RewardDTO result = new com.kikitrade.activity.facade.taskv2.RewardDTO(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.kikitrade.activity.facade.taskv2.RewardDTO result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.consecutiveTimes_ = consecutiveTimes_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.type_ = type_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.amount_ = amount_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.currency_ = currency_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.image_ = image_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.desc_ = desc_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.vipLevel_ = vipLevel_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.taskv2.RewardDTO) {
        return mergeFrom((com.kikitrade.activity.facade.taskv2.RewardDTO)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.taskv2.RewardDTO other) {
      if (other == com.kikitrade.activity.facade.taskv2.RewardDTO.getDefaultInstance()) return this;
      if (other.getConsecutiveTimes() != 0) {
        setConsecutiveTimes(other.getConsecutiveTimes());
      }
      if (other.type_ != 0) {
        setTypeValue(other.getTypeValue());
      }
      if (!other.getAmount().isEmpty()) {
        amount_ = other.amount_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getCurrency().isEmpty()) {
        currency_ = other.currency_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (!other.getImage().isEmpty()) {
        image_ = other.image_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (!other.getDesc().isEmpty()) {
        desc_ = other.desc_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (other.vipLevel_ != 0) {
        setVipLevelValue(other.getVipLevelValue());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              consecutiveTimes_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              type_ = input.readEnum();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              amount_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              currency_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              image_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              desc_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 56: {
              vipLevel_ = input.readEnum();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int consecutiveTimes_ ;
    /**
     * <pre>
     *连续次数，如果ProgressType等于add，该值为0
     * </pre>
     *
     * <code>int32 consecutiveTimes = 1;</code>
     * @return The consecutiveTimes.
     */
    @java.lang.Override
    public int getConsecutiveTimes() {
      return consecutiveTimes_;
    }
    /**
     * <pre>
     *连续次数，如果ProgressType等于add，该值为0
     * </pre>
     *
     * <code>int32 consecutiveTimes = 1;</code>
     * @param value The consecutiveTimes to set.
     * @return This builder for chaining.
     */
    public Builder setConsecutiveTimes(int value) {

      consecutiveTimes_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *连续次数，如果ProgressType等于add，该值为0
     * </pre>
     *
     * <code>int32 consecutiveTimes = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearConsecutiveTimes() {
      bitField0_ = (bitField0_ & ~0x00000001);
      consecutiveTimes_ = 0;
      onChanged();
      return this;
    }

    private int type_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.taskv2.RewardType type = 2;</code>
     * @return The enum numeric value on the wire for type.
     */
    @java.lang.Override public int getTypeValue() {
      return type_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.taskv2.RewardType type = 2;</code>
     * @param value The enum numeric value on the wire for type to set.
     * @return This builder for chaining.
     */
    public Builder setTypeValue(int value) {
      type_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.taskv2.RewardType type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.taskv2.RewardType getType() {
      com.kikitrade.activity.facade.taskv2.RewardType result = com.kikitrade.activity.facade.taskv2.RewardType.forNumber(type_);
      return result == null ? com.kikitrade.activity.facade.taskv2.RewardType.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.taskv2.RewardType type = 2;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(com.kikitrade.activity.facade.taskv2.RewardType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000002;
      type_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.taskv2.RewardType type = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000002);
      type_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object amount_ = "";
    /**
     * <pre>
     *方法数量
     * </pre>
     *
     * <code>string amount = 3;</code>
     * @return The amount.
     */
    public java.lang.String getAmount() {
      java.lang.Object ref = amount_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        amount_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *方法数量
     * </pre>
     *
     * <code>string amount = 3;</code>
     * @return The bytes for amount.
     */
    public com.google.protobuf.ByteString
        getAmountBytes() {
      java.lang.Object ref = amount_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        amount_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *方法数量
     * </pre>
     *
     * <code>string amount = 3;</code>
     * @param value The amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmount(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      amount_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *方法数量
     * </pre>
     *
     * <code>string amount = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmount() {
      amount_ = getDefaultInstance().getAmount();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *方法数量
     * </pre>
     *
     * <code>string amount = 3;</code>
     * @param value The bytes for amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmountBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      amount_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object currency_ = "";
    /**
     * <pre>
     *奖励币种
     * </pre>
     *
     * <code>string currency = 4;</code>
     * @return The currency.
     */
    public java.lang.String getCurrency() {
      java.lang.Object ref = currency_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        currency_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *奖励币种
     * </pre>
     *
     * <code>string currency = 4;</code>
     * @return The bytes for currency.
     */
    public com.google.protobuf.ByteString
        getCurrencyBytes() {
      java.lang.Object ref = currency_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        currency_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *奖励币种
     * </pre>
     *
     * <code>string currency = 4;</code>
     * @param value The currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrency(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      currency_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖励币种
     * </pre>
     *
     * <code>string currency = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurrency() {
      currency_ = getDefaultInstance().getCurrency();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖励币种
     * </pre>
     *
     * <code>string currency = 4;</code>
     * @param value The bytes for currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrencyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      currency_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object image_ = "";
    /**
     * <pre>
     *奖品图片
     * </pre>
     *
     * <code>string image = 5;</code>
     * @return The image.
     */
    public java.lang.String getImage() {
      java.lang.Object ref = image_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        image_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *奖品图片
     * </pre>
     *
     * <code>string image = 5;</code>
     * @return The bytes for image.
     */
    public com.google.protobuf.ByteString
        getImageBytes() {
      java.lang.Object ref = image_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        image_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *奖品图片
     * </pre>
     *
     * <code>string image = 5;</code>
     * @param value The image to set.
     * @return This builder for chaining.
     */
    public Builder setImage(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      image_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖品图片
     * </pre>
     *
     * <code>string image = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearImage() {
      image_ = getDefaultInstance().getImage();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖品图片
     * </pre>
     *
     * <code>string image = 5;</code>
     * @param value The bytes for image to set.
     * @return This builder for chaining.
     */
    public Builder setImageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      image_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private java.lang.Object desc_ = "";
    /**
     * <code>string desc = 6;</code>
     * @return The desc.
     */
    public java.lang.String getDesc() {
      java.lang.Object ref = desc_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        desc_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string desc = 6;</code>
     * @return The bytes for desc.
     */
    public com.google.protobuf.ByteString
        getDescBytes() {
      java.lang.Object ref = desc_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        desc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string desc = 6;</code>
     * @param value The desc to set.
     * @return This builder for chaining.
     */
    public Builder setDesc(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      desc_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>string desc = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearDesc() {
      desc_ = getDefaultInstance().getDesc();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>string desc = 6;</code>
     * @param value The bytes for desc to set.
     * @return This builder for chaining.
     */
    public Builder setDescBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      desc_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private int vipLevel_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.taskv2.VipLevel vipLevel = 7;</code>
     * @return The enum numeric value on the wire for vipLevel.
     */
    @java.lang.Override public int getVipLevelValue() {
      return vipLevel_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.taskv2.VipLevel vipLevel = 7;</code>
     * @param value The enum numeric value on the wire for vipLevel to set.
     * @return This builder for chaining.
     */
    public Builder setVipLevelValue(int value) {
      vipLevel_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.taskv2.VipLevel vipLevel = 7;</code>
     * @return The vipLevel.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.taskv2.VipLevel getVipLevel() {
      com.kikitrade.activity.facade.taskv2.VipLevel result = com.kikitrade.activity.facade.taskv2.VipLevel.forNumber(vipLevel_);
      return result == null ? com.kikitrade.activity.facade.taskv2.VipLevel.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.taskv2.VipLevel vipLevel = 7;</code>
     * @param value The vipLevel to set.
     * @return This builder for chaining.
     */
    public Builder setVipLevel(com.kikitrade.activity.facade.taskv2.VipLevel value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000040;
      vipLevel_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.taskv2.VipLevel vipLevel = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearVipLevel() {
      bitField0_ = (bitField0_ & ~0x00000040);
      vipLevel_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.taskv2.RewardDTO)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.taskv2.RewardDTO)
  private static final com.kikitrade.activity.facade.taskv2.RewardDTO DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.taskv2.RewardDTO();
  }

  public static com.kikitrade.activity.facade.taskv2.RewardDTO getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RewardDTO>
      PARSER = new com.google.protobuf.AbstractParser<RewardDTO>() {
    @java.lang.Override
    public RewardDTO parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<RewardDTO> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RewardDTO> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.taskv2.RewardDTO getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

