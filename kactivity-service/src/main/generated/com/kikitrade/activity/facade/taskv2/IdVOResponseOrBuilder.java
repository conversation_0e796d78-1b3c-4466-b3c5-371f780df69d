// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Task.proto

package com.kikitrade.activity.facade.taskv2;

public interface IdVOResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.taskv2.IdVOResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>string id = 3;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 3;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>repeated string subId = 4;</code>
   * @return A list containing the subId.
   */
  java.util.List<java.lang.String>
      getSubIdList();
  /**
   * <code>repeated string subId = 4;</code>
   * @return The count of subId.
   */
  int getSubIdCount();
  /**
   * <code>repeated string subId = 4;</code>
   * @param index The index of the element to return.
   * @return The subId at the given index.
   */
  java.lang.String getSubId(int index);
  /**
   * <code>repeated string subId = 4;</code>
   * @param index The index of the value to return.
   * @return The bytes of the subId at the given index.
   */
  com.google.protobuf.ByteString
      getSubIdBytes(int index);
}
