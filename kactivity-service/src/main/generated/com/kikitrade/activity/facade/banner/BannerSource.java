// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Banner.proto

package com.kikitrade.activity.facade.banner;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.banner.BannerSource}
 */
public enum BannerSource
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>TASK = 0;</code>
   */
  TASK(0),
  /**
   * <code>GOODS = 1;</code>
   */
  GOODS(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>TASK = 0;</code>
   */
  public static final int TASK_VALUE = 0;
  /**
   * <code>GOODS = 1;</code>
   */
  public static final int GOODS_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static BannerSource valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static BannerSource forNumber(int value) {
    switch (value) {
      case 0: return TASK;
      case 1: return GOODS;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<BannerSource>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      BannerSource> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<BannerSource>() {
          public BannerSource findValueByNumber(int number) {
            return BannerSource.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.banner.BannerFacadeOutClass.getDescriptor().getEnumTypes().get(0);
  }

  private static final BannerSource[] VALUES = values();

  public static BannerSource valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private BannerSource(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.banner.BannerSource)
}

