// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface ActivityBatchDTOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.ActivityBatchDTO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *batchId 新增空
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   *batchId 新增空
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string batchName = 2;</code>
   * @return The batchName.
   */
  java.lang.String getBatchName();
  /**
   * <code>string batchName = 2;</code>
   * @return The bytes for batchName.
   */
  com.google.protobuf.ByteString
      getBatchNameBytes();

  /**
   * <code>string activityId = 3;</code>
   * @return The activityId.
   */
  java.lang.String getActivityId();
  /**
   * <code>string activityId = 3;</code>
   * @return The bytes for activityId.
   */
  com.google.protobuf.ByteString
      getActivityIdBytes();

  /**
   * <code>string activityName = 4;</code>
   * @return The activityName.
   */
  java.lang.String getActivityName();
  /**
   * <code>string activityName = 4;</code>
   * @return The bytes for activityName.
   */
  com.google.protobuf.ByteString
      getActivityNameBytes();

  /**
   * <pre>
   *数字货币、道具，code
   * </pre>
   *
   * <code>string rewardType = 5;</code>
   * @return The rewardType.
   */
  java.lang.String getRewardType();
  /**
   * <pre>
   *数字货币、道具，code
   * </pre>
   *
   * <code>string rewardType = 5;</code>
   * @return The bytes for rewardType.
   */
  com.google.protobuf.ByteString
      getRewardTypeBytes();

  /**
   * <pre>
   *货币金额
   * </pre>
   *
   * <code>string amount = 6;</code>
   * @return The amount.
   */
  java.lang.String getAmount();
  /**
   * <pre>
   *货币金额
   * </pre>
   *
   * <code>string amount = 6;</code>
   * @return The bytes for amount.
   */
  com.google.protobuf.ByteString
      getAmountBytes();

  /**
   * <pre>
   *货币单位
   * </pre>
   *
   * <code>string currency = 7;</code>
   * @return The currency.
   */
  java.lang.String getCurrency();
  /**
   * <pre>
   *货币单位
   * </pre>
   *
   * <code>string currency = 7;</code>
   * @return The bytes for currency.
   */
  com.google.protobuf.ByteString
      getCurrencyBytes();

  /**
   * <pre>
   *批次描述
   * </pre>
   *
   * <code>string remark = 8;</code>
   * @return The remark.
   */
  java.lang.String getRemark();
  /**
   * <pre>
   *批次描述
   * </pre>
   *
   * <code>string remark = 8;</code>
   * @return The bytes for remark.
   */
  com.google.protobuf.ByteString
      getRemarkBytes();

  /**
   * <pre>
   *是否立即发奖
   * </pre>
   *
   * <code>bool scheduled = 9;</code>
   * @return The scheduled.
   */
  boolean getScheduled();

  /**
   * <pre>
   *发奖时间
   * </pre>
   *
   * <code>string scheduledTime = 10;</code>
   * @return The scheduledTime.
   */
  java.lang.String getScheduledTime();
  /**
   * <pre>
   *发奖时间
   * </pre>
   *
   * <code>string scheduledTime = 10;</code>
   * @return The bytes for scheduledTime.
   */
  com.google.protobuf.ByteString
      getScheduledTimeBytes();

  /**
   * <pre>
   *最后修改人
   * </pre>
   *
   * <code>string amended = 11;</code>
   * @return The amended.
   */
  java.lang.String getAmended();
  /**
   * <pre>
   *最后修改人
   * </pre>
   *
   * <code>string amended = 11;</code>
   * @return The bytes for amended.
   */
  com.google.protobuf.ByteString
      getAmendedBytes();

  /**
   * <pre>
   *上传的csv文件
   * </pre>
   *
   * <code>string sourceOssUrl = 12;</code>
   * @return The sourceOssUrl.
   */
  java.lang.String getSourceOssUrl();
  /**
   * <pre>
   *上传的csv文件
   * </pre>
   *
   * <code>string sourceOssUrl = 12;</code>
   * @return The bytes for sourceOssUrl.
   */
  com.google.protobuf.ByteString
      getSourceOssUrlBytes();

  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
   */
  java.util.List<com.kikitrade.activity.facade.award.RewardRule> 
      getRewardRuleList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
   */
  com.kikitrade.activity.facade.award.RewardRule getRewardRule(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
   */
  int getRewardRuleCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.award.RewardRuleOrBuilder> 
      getRewardRuleOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 13;</code>
   */
  com.kikitrade.activity.facade.award.RewardRuleOrBuilder getRewardRuleOrBuilder(
      int index);
}
