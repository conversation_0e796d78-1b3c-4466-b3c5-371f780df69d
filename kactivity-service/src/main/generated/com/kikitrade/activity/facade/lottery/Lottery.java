// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Lottery.proto

package com.kikitrade.activity.facade.lottery;

public final class Lottery {
  private Lottery() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_lottery_LotteryDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_lottery_LotteryDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_lottery_RewardVO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_lottery_RewardVO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_lottery_LotterySaveResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_lottery_LotterySaveResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rLottery.proto\022%com.kikitrade.activity." +
      "facade.lottery\"\215\001\n\nLotteryDTO\022\016\n\006amount\030" +
      "\001 \001(\t\022\r\n\005limit\030\002 \001(\005\022\r\n\005reels\030\003 \001(\005\022A\n\010r" +
      "ewardVO\030\004 \003(\0132/.com.kikitrade.activity.f" +
      "acade.lottery.RewardVO\022\016\n\006saasId\030\005 \001(\t\"*" +
      "\n\010RewardVO\022\014\n\004name\030\001 \001(\t\022\020\n\010multiple\030\002 \001" +
      "(\005\"7\n\023LotterySaveResponse\022\017\n\007success\030\001 \001" +
      "(\010\022\017\n\007message\030\002 \001(\t2\206\001\n\rLotteryFacade\022u\n" +
      "\004save\0221.com.kikitrade.activity.facade.lo" +
      "ttery.LotteryDTO\032:.com.kikitrade.activit" +
      "y.facade.lottery.LotterySaveResponseB)\n%" +
      "com.kikitrade.activity.facade.lotteryP\001b" +
      "\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_kikitrade_activity_facade_lottery_LotteryDTO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_activity_facade_lottery_LotteryDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_lottery_LotteryDTO_descriptor,
        new java.lang.String[] { "Amount", "Limit", "Reels", "RewardVO", "SaasId", });
    internal_static_com_kikitrade_activity_facade_lottery_RewardVO_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_activity_facade_lottery_RewardVO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_lottery_RewardVO_descriptor,
        new java.lang.String[] { "Name", "Multiple", });
    internal_static_com_kikitrade_activity_facade_lottery_LotterySaveResponse_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_kikitrade_activity_facade_lottery_LotterySaveResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_lottery_LotterySaveResponse_descriptor,
        new java.lang.String[] { "Success", "Message", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
