package com.kikitrade.activity.facade.sticker;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.58.0)",
    comments = "Source: StickerFacade.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class StickerFacadeGrpc {

  private StickerFacadeGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.kikitrade.activity.facade.sticker.StickerFacade";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.sticker.UpsertStickerRequest,
      com.kikitrade.activity.facade.sticker.UpsertStickerResponse> getUpsertStickerMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "upsertSticker",
      requestType = com.kikitrade.activity.facade.sticker.UpsertStickerRequest.class,
      responseType = com.kikitrade.activity.facade.sticker.UpsertStickerResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.sticker.UpsertStickerRequest,
      com.kikitrade.activity.facade.sticker.UpsertStickerResponse> getUpsertStickerMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.sticker.UpsertStickerRequest, com.kikitrade.activity.facade.sticker.UpsertStickerResponse> getUpsertStickerMethod;
    if ((getUpsertStickerMethod = StickerFacadeGrpc.getUpsertStickerMethod) == null) {
      synchronized (StickerFacadeGrpc.class) {
        if ((getUpsertStickerMethod = StickerFacadeGrpc.getUpsertStickerMethod) == null) {
          StickerFacadeGrpc.getUpsertStickerMethod = getUpsertStickerMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.sticker.UpsertStickerRequest, com.kikitrade.activity.facade.sticker.UpsertStickerResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "upsertSticker"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.sticker.UpsertStickerRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.sticker.UpsertStickerResponse.getDefaultInstance()))
              .setSchemaDescriptor(new StickerFacadeMethodDescriptorSupplier("upsertSticker"))
              .build();
        }
      }
    }
    return getUpsertStickerMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static StickerFacadeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<StickerFacadeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<StickerFacadeStub>() {
        @java.lang.Override
        public StickerFacadeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new StickerFacadeStub(channel, callOptions);
        }
      };
    return StickerFacadeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static StickerFacadeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<StickerFacadeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<StickerFacadeBlockingStub>() {
        @java.lang.Override
        public StickerFacadeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new StickerFacadeBlockingStub(channel, callOptions);
        }
      };
    return StickerFacadeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static StickerFacadeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<StickerFacadeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<StickerFacadeFutureStub>() {
        @java.lang.Override
        public StickerFacadeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new StickerFacadeFutureStub(channel, callOptions);
        }
      };
    return StickerFacadeFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     * <pre>
     **
     *新增/保存 sticker
     * </pre>
     */
    default void upsertSticker(com.kikitrade.activity.facade.sticker.UpsertStickerRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.sticker.UpsertStickerResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUpsertStickerMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service StickerFacade.
   */
  public static abstract class StickerFacadeImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return StickerFacadeGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service StickerFacade.
   */
  public static final class StickerFacadeStub
      extends io.grpc.stub.AbstractAsyncStub<StickerFacadeStub> {
    private StickerFacadeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected StickerFacadeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new StickerFacadeStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *新增/保存 sticker
     * </pre>
     */
    public void upsertSticker(com.kikitrade.activity.facade.sticker.UpsertStickerRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.sticker.UpsertStickerResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUpsertStickerMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service StickerFacade.
   */
  public static final class StickerFacadeBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<StickerFacadeBlockingStub> {
    private StickerFacadeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected StickerFacadeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new StickerFacadeBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *新增/保存 sticker
     * </pre>
     */
    public com.kikitrade.activity.facade.sticker.UpsertStickerResponse upsertSticker(com.kikitrade.activity.facade.sticker.UpsertStickerRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUpsertStickerMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service StickerFacade.
   */
  public static final class StickerFacadeFutureStub
      extends io.grpc.stub.AbstractFutureStub<StickerFacadeFutureStub> {
    private StickerFacadeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected StickerFacadeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new StickerFacadeFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *新增/保存 sticker
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.sticker.UpsertStickerResponse> upsertSticker(
        com.kikitrade.activity.facade.sticker.UpsertStickerRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUpsertStickerMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_UPSERT_STICKER = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_UPSERT_STICKER:
          serviceImpl.upsertSticker((com.kikitrade.activity.facade.sticker.UpsertStickerRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.sticker.UpsertStickerResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getUpsertStickerMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.sticker.UpsertStickerRequest,
              com.kikitrade.activity.facade.sticker.UpsertStickerResponse>(
                service, METHODID_UPSERT_STICKER)))
        .build();
  }

  private static abstract class StickerFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    StickerFacadeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.kikitrade.activity.facade.sticker.StickerFacadeOuterClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("StickerFacade");
    }
  }

  private static final class StickerFacadeFileDescriptorSupplier
      extends StickerFacadeBaseDescriptorSupplier {
    StickerFacadeFileDescriptorSupplier() {}
  }

  private static final class StickerFacadeMethodDescriptorSupplier
      extends StickerFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    StickerFacadeMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (StickerFacadeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new StickerFacadeFileDescriptorSupplier())
              .addMethod(getUpsertStickerMethod())
              .build();
        }
      }
    }
    return result;
  }
}
