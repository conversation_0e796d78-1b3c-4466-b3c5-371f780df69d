// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface ActivityBatchResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.ActivityBatchResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>.com.kikitrade.activity.facade.award.ActivityBatch activityBatch = 3;</code>
   * @return Whether the activityBatch field is set.
   */
  boolean hasActivityBatch();
  /**
   * <code>.com.kikitrade.activity.facade.award.ActivityBatch activityBatch = 3;</code>
   * @return The activityBatch.
   */
  com.kikitrade.activity.facade.award.ActivityBatch getActivityBatch();
  /**
   * <code>.com.kikitrade.activity.facade.award.ActivityBatch activityBatch = 3;</code>
   */
  com.kikitrade.activity.facade.award.ActivityBatchOrBuilder getActivityBatchOrBuilder();
}
