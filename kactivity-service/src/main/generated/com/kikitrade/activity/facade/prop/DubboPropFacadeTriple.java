/*
* Licensed to the Apache Software Foundation (ASF) under one or more
* contributor license agreements.  See the NOTICE file distributed with
* this work for additional information regarding copyright ownership.
* The ASF licenses this file to You under the Apache License, Version 2.0
* (the "License"); you may not use this file except in compliance with
* the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

    package com.kikitrade.activity.facade.prop;

import org.apache.dubbo.common.stream.StreamObserver;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.PathResolver;
import org.apache.dubbo.rpc.RpcException;
import org.apache.dubbo.rpc.ServerService;
import org.apache.dubbo.rpc.TriRpcStatus;
import org.apache.dubbo.rpc.model.MethodDescriptor;
import org.apache.dubbo.rpc.model.ServiceDescriptor;
import org.apache.dubbo.rpc.model.StubMethodDescriptor;
import org.apache.dubbo.rpc.model.StubServiceDescriptor;
import org.apache.dubbo.rpc.stub.BiStreamMethodHandler;
import org.apache.dubbo.rpc.stub.ServerStreamMethodHandler;
import org.apache.dubbo.rpc.stub.StubInvocationUtil;
import org.apache.dubbo.rpc.stub.StubInvoker;
import org.apache.dubbo.rpc.stub.StubMethodHandler;
import org.apache.dubbo.rpc.stub.StubSuppliers;
import org.apache.dubbo.rpc.stub.UnaryStubMethodHandler;

import com.google.protobuf.Message;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.concurrent.CompletableFuture;

public final class DubboPropFacadeTriple {

    public static final String SERVICE_NAME = PropFacade.SERVICE_NAME;

    private static final StubServiceDescriptor serviceDescriptor = new StubServiceDescriptor(SERVICE_NAME,PropFacade.class);

    static {
        org.apache.dubbo.rpc.protocol.tri.service.SchemaDescriptorRegistry.addSchemaDescriptor(SERVICE_NAME,PropFacadeOuterClass.getDescriptor());
        StubSuppliers.addSupplier(SERVICE_NAME, DubboPropFacadeTriple::newStub);
        StubSuppliers.addSupplier(PropFacade.JAVA_SERVICE_NAME,  DubboPropFacadeTriple::newStub);
        StubSuppliers.addDescriptor(SERVICE_NAME, serviceDescriptor);
        StubSuppliers.addDescriptor(PropFacade.JAVA_SERVICE_NAME, serviceDescriptor);
    }

    @SuppressWarnings("all")
    public static PropFacade newStub(Invoker<?> invoker) {
        return new PropFacadeStub((Invoker<PropFacade>)invoker);
    }

    private static final StubMethodDescriptor upsertPropMethod = new StubMethodDescriptor("upsertProp",
    com.kikitrade.activity.facade.prop.UpsertPropRequest.class, com.kikitrade.activity.facade.prop.UpsertPropReply.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.prop.UpsertPropRequest::parseFrom,
    com.kikitrade.activity.facade.prop.UpsertPropReply::parseFrom);

    private static final StubMethodDescriptor upsertPropAsyncMethod = new StubMethodDescriptor("upsertProp",
    com.kikitrade.activity.facade.prop.UpsertPropRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.prop.UpsertPropRequest::parseFrom,
    com.kikitrade.activity.facade.prop.UpsertPropReply::parseFrom);

    private static final StubMethodDescriptor upsertPropProxyAsyncMethod = new StubMethodDescriptor("upsertPropAsync",
    com.kikitrade.activity.facade.prop.UpsertPropRequest.class, com.kikitrade.activity.facade.prop.UpsertPropReply.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.prop.UpsertPropRequest::parseFrom,
    com.kikitrade.activity.facade.prop.UpsertPropReply::parseFrom);

    private static final StubMethodDescriptor queryPropsMethod = new StubMethodDescriptor("queryProps",
    com.kikitrade.activity.facade.prop.PropsQueryRequest.class, com.kikitrade.activity.facade.prop.PropsQueryReply.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.prop.PropsQueryRequest::parseFrom,
    com.kikitrade.activity.facade.prop.PropsQueryReply::parseFrom);

    private static final StubMethodDescriptor queryPropsAsyncMethod = new StubMethodDescriptor("queryProps",
    com.kikitrade.activity.facade.prop.PropsQueryRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.prop.PropsQueryRequest::parseFrom,
    com.kikitrade.activity.facade.prop.PropsQueryReply::parseFrom);

    private static final StubMethodDescriptor queryPropsProxyAsyncMethod = new StubMethodDescriptor("queryPropsAsync",
    com.kikitrade.activity.facade.prop.PropsQueryRequest.class, com.kikitrade.activity.facade.prop.PropsQueryReply.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.prop.PropsQueryRequest::parseFrom,
    com.kikitrade.activity.facade.prop.PropsQueryReply::parseFrom);

    private static final StubMethodDescriptor getPropMethod = new StubMethodDescriptor("getProp",
    com.kikitrade.activity.facade.prop.GetPropRequest.class, com.kikitrade.activity.facade.prop.PropGetReply.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.prop.GetPropRequest::parseFrom,
    com.kikitrade.activity.facade.prop.PropGetReply::parseFrom);

    private static final StubMethodDescriptor getPropAsyncMethod = new StubMethodDescriptor("getProp",
    com.kikitrade.activity.facade.prop.GetPropRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.prop.GetPropRequest::parseFrom,
    com.kikitrade.activity.facade.prop.PropGetReply::parseFrom);

    private static final StubMethodDescriptor getPropProxyAsyncMethod = new StubMethodDescriptor("getPropAsync",
    com.kikitrade.activity.facade.prop.GetPropRequest.class, com.kikitrade.activity.facade.prop.PropGetReply.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.prop.GetPropRequest::parseFrom,
    com.kikitrade.activity.facade.prop.PropGetReply::parseFrom);





    public static class PropFacadeStub implements PropFacade{
        private final Invoker<PropFacade> invoker;

        public PropFacadeStub(Invoker<PropFacade> invoker) {
            this.invoker = invoker;
        }

        @Override
        public com.kikitrade.activity.facade.prop.UpsertPropReply upsertProp(com.kikitrade.activity.facade.prop.UpsertPropRequest request){
            return StubInvocationUtil.unaryCall(invoker, upsertPropMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.prop.UpsertPropReply> upsertPropAsync(com.kikitrade.activity.facade.prop.UpsertPropRequest request){
            return StubInvocationUtil.unaryCall(invoker, upsertPropAsyncMethod, request);
        }

        @Override
        public void upsertProp(com.kikitrade.activity.facade.prop.UpsertPropRequest request, StreamObserver<com.kikitrade.activity.facade.prop.UpsertPropReply> responseObserver){
            StubInvocationUtil.unaryCall(invoker, upsertPropMethod , request, responseObserver);
        }
        @Override
        public com.kikitrade.activity.facade.prop.PropsQueryReply queryProps(com.kikitrade.activity.facade.prop.PropsQueryRequest request){
            return StubInvocationUtil.unaryCall(invoker, queryPropsMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.prop.PropsQueryReply> queryPropsAsync(com.kikitrade.activity.facade.prop.PropsQueryRequest request){
            return StubInvocationUtil.unaryCall(invoker, queryPropsAsyncMethod, request);
        }

        @Override
        public void queryProps(com.kikitrade.activity.facade.prop.PropsQueryRequest request, StreamObserver<com.kikitrade.activity.facade.prop.PropsQueryReply> responseObserver){
            StubInvocationUtil.unaryCall(invoker, queryPropsMethod , request, responseObserver);
        }
        @Override
        public com.kikitrade.activity.facade.prop.PropGetReply getProp(com.kikitrade.activity.facade.prop.GetPropRequest request){
            return StubInvocationUtil.unaryCall(invoker, getPropMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.prop.PropGetReply> getPropAsync(com.kikitrade.activity.facade.prop.GetPropRequest request){
            return StubInvocationUtil.unaryCall(invoker, getPropAsyncMethod, request);
        }

        @Override
        public void getProp(com.kikitrade.activity.facade.prop.GetPropRequest request, StreamObserver<com.kikitrade.activity.facade.prop.PropGetReply> responseObserver){
            StubInvocationUtil.unaryCall(invoker, getPropMethod , request, responseObserver);
        }



    }

    public static abstract class PropFacadeImplBase implements PropFacade, ServerService<PropFacade> {

        private <T, R> BiConsumer<T, StreamObserver<R>> syncToAsync(java.util.function.Function<T, R> syncFun) {
            return new BiConsumer<T, StreamObserver<R>>() {
                @Override
                public void accept(T t, StreamObserver<R> observer) {
                    try {
                        R ret = syncFun.apply(t);
                        observer.onNext(ret);
                        observer.onCompleted();
                    } catch (Throwable e) {
                        observer.onError(e);
                    }
                }
            };
        }

        @Override
        public final Invoker<PropFacade> getInvoker(URL url) {
            PathResolver pathResolver = url.getOrDefaultFrameworkModel()
            .getExtensionLoader(PathResolver.class)
            .getDefaultExtension();
            Map<String,StubMethodHandler<?, ?>> handlers = new HashMap<>();

            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/upsertProp" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/upsertPropAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/queryProps" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/queryPropsAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/getProp" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/getPropAsync" );

            BiConsumer<com.kikitrade.activity.facade.prop.UpsertPropRequest, StreamObserver<com.kikitrade.activity.facade.prop.UpsertPropReply>> upsertPropFunc = this::upsertProp;
            handlers.put(upsertPropMethod.getMethodName(), new UnaryStubMethodHandler<>(upsertPropFunc));
            BiConsumer<com.kikitrade.activity.facade.prop.UpsertPropRequest, StreamObserver<com.kikitrade.activity.facade.prop.UpsertPropReply>> upsertPropAsyncFunc = syncToAsync(this::upsertProp);
            handlers.put(upsertPropProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(upsertPropAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.prop.PropsQueryRequest, StreamObserver<com.kikitrade.activity.facade.prop.PropsQueryReply>> queryPropsFunc = this::queryProps;
            handlers.put(queryPropsMethod.getMethodName(), new UnaryStubMethodHandler<>(queryPropsFunc));
            BiConsumer<com.kikitrade.activity.facade.prop.PropsQueryRequest, StreamObserver<com.kikitrade.activity.facade.prop.PropsQueryReply>> queryPropsAsyncFunc = syncToAsync(this::queryProps);
            handlers.put(queryPropsProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(queryPropsAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.prop.GetPropRequest, StreamObserver<com.kikitrade.activity.facade.prop.PropGetReply>> getPropFunc = this::getProp;
            handlers.put(getPropMethod.getMethodName(), new UnaryStubMethodHandler<>(getPropFunc));
            BiConsumer<com.kikitrade.activity.facade.prop.GetPropRequest, StreamObserver<com.kikitrade.activity.facade.prop.PropGetReply>> getPropAsyncFunc = syncToAsync(this::getProp);
            handlers.put(getPropProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(getPropAsyncFunc));




            return new StubInvoker<>(this, url, PropFacade.class, handlers);
        }


        @Override
        public com.kikitrade.activity.facade.prop.UpsertPropReply upsertProp(com.kikitrade.activity.facade.prop.UpsertPropRequest request){
            throw unimplementedMethodException(upsertPropMethod);
        }

        @Override
        public com.kikitrade.activity.facade.prop.PropsQueryReply queryProps(com.kikitrade.activity.facade.prop.PropsQueryRequest request){
            throw unimplementedMethodException(queryPropsMethod);
        }

        @Override
        public com.kikitrade.activity.facade.prop.PropGetReply getProp(com.kikitrade.activity.facade.prop.GetPropRequest request){
            throw unimplementedMethodException(getPropMethod);
        }





        @Override
        public final ServiceDescriptor getServiceDescriptor() {
            return serviceDescriptor;
        }
        private RpcException unimplementedMethodException(StubMethodDescriptor methodDescriptor) {
            return TriRpcStatus.UNIMPLEMENTED.withDescription(String.format("Method %s is unimplemented",
                "/" + serviceDescriptor.getInterfaceName() + "/" + methodDescriptor.getMethodName())).asException();
        }
    }

}
