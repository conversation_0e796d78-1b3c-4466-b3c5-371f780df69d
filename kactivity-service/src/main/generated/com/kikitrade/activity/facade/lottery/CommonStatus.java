// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Lottery.proto

package com.kikitrade.activity.facade.lottery;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.lottery.CommonStatus}
 */
public enum CommonStatus
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>ACTIVE = 0;</code>
   */
  ACTIVE(0),
  /**
   * <code>DISABLE = 1;</code>
   */
  DISABLE(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>ACTIVE = 0;</code>
   */
  public static final int ACTIVE_VALUE = 0;
  /**
   * <code>DISABLE = 1;</code>
   */
  public static final int DISABLE_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static CommonStatus valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static CommonStatus forNumber(int value) {
    switch (value) {
      case 0: return ACTIVE;
      case 1: return DISABLE;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<CommonStatus>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      CommonStatus> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<CommonStatus>() {
          public CommonStatus findValueByNumber(int number) {
            return CommonStatus.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.lottery.LotteryFacadeOutClass.getDescriptor().getEnumTypes().get(0);
  }

  private static final CommonStatus[] VALUES = values();

  public static CommonStatus valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private CommonStatus(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.lottery.CommonStatus)
}

