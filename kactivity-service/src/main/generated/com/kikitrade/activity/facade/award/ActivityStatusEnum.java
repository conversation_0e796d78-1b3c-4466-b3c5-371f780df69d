// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.award.ActivityStatusEnum}
 */
public enum ActivityStatusEnum
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>DRAFT = 0;</code>
   */
  DRAFT(0),
  /**
   * <code>ACTIVE = 1;</code>
   */
  ACTIVE(1),
  /**
   * <code>PAUSE = 2;</code>
   */
  PAUSE(2),
  /**
   * <code>END = 3;</code>
   */
  END(3),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>DRAFT = 0;</code>
   */
  public static final int DRAFT_VALUE = 0;
  /**
   * <code>ACTIVE = 1;</code>
   */
  public static final int ACTIVE_VALUE = 1;
  /**
   * <code>PAUSE = 2;</code>
   */
  public static final int PAUSE_VALUE = 2;
  /**
   * <code>END = 3;</code>
   */
  public static final int END_VALUE = 3;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static ActivityStatusEnum valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static ActivityStatusEnum forNumber(int value) {
    switch (value) {
      case 0: return DRAFT;
      case 1: return ACTIVE;
      case 2: return PAUSE;
      case 3: return END;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<ActivityStatusEnum>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      ActivityStatusEnum> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<ActivityStatusEnum>() {
          public ActivityStatusEnum findValueByNumber(int number) {
            return ActivityStatusEnum.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.getDescriptor().getEnumTypes().get(3);
  }

  private static final ActivityStatusEnum[] VALUES = values();

  public static ActivityStatusEnum valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private ActivityStatusEnum(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.award.ActivityStatusEnum)
}

