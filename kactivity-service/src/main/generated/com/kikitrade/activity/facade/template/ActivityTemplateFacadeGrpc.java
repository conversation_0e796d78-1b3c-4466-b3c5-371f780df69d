package com.kikitrade.activity.facade.template;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.58.0)",
    comments = "Source: ActivityTemplate.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class ActivityTemplateFacadeGrpc {

  private ActivityTemplateFacadeGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.kikitrade.activity.facade.template.ActivityTemplateFacade";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.template.ActivityMaterialDTO,
      com.kikitrade.activity.facade.template.CommonResponse> getSaveMaterialMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "saveMaterial",
      requestType = com.kikitrade.activity.facade.template.ActivityMaterialDTO.class,
      responseType = com.kikitrade.activity.facade.template.CommonResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.template.ActivityMaterialDTO,
      com.kikitrade.activity.facade.template.CommonResponse> getSaveMaterialMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.template.ActivityMaterialDTO, com.kikitrade.activity.facade.template.CommonResponse> getSaveMaterialMethod;
    if ((getSaveMaterialMethod = ActivityTemplateFacadeGrpc.getSaveMaterialMethod) == null) {
      synchronized (ActivityTemplateFacadeGrpc.class) {
        if ((getSaveMaterialMethod = ActivityTemplateFacadeGrpc.getSaveMaterialMethod) == null) {
          ActivityTemplateFacadeGrpc.getSaveMaterialMethod = getSaveMaterialMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.template.ActivityMaterialDTO, com.kikitrade.activity.facade.template.CommonResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "saveMaterial"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.template.ActivityMaterialDTO.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.template.CommonResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityTemplateFacadeMethodDescriptorSupplier("saveMaterial"))
              .build();
        }
      }
    }
    return getSaveMaterialMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static ActivityTemplateFacadeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ActivityTemplateFacadeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ActivityTemplateFacadeStub>() {
        @java.lang.Override
        public ActivityTemplateFacadeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ActivityTemplateFacadeStub(channel, callOptions);
        }
      };
    return ActivityTemplateFacadeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static ActivityTemplateFacadeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ActivityTemplateFacadeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ActivityTemplateFacadeBlockingStub>() {
        @java.lang.Override
        public ActivityTemplateFacadeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ActivityTemplateFacadeBlockingStub(channel, callOptions);
        }
      };
    return ActivityTemplateFacadeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static ActivityTemplateFacadeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ActivityTemplateFacadeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ActivityTemplateFacadeFutureStub>() {
        @java.lang.Override
        public ActivityTemplateFacadeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ActivityTemplateFacadeFutureStub(channel, callOptions);
        }
      };
    return ActivityTemplateFacadeFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     * <pre>
     **
     *保存物料
     * </pre>
     */
    default void saveMaterial(com.kikitrade.activity.facade.template.ActivityMaterialDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.template.CommonResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSaveMaterialMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service ActivityTemplateFacade.
   */
  public static abstract class ActivityTemplateFacadeImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return ActivityTemplateFacadeGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service ActivityTemplateFacade.
   */
  public static final class ActivityTemplateFacadeStub
      extends io.grpc.stub.AbstractAsyncStub<ActivityTemplateFacadeStub> {
    private ActivityTemplateFacadeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ActivityTemplateFacadeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ActivityTemplateFacadeStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *保存物料
     * </pre>
     */
    public void saveMaterial(com.kikitrade.activity.facade.template.ActivityMaterialDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.template.CommonResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSaveMaterialMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service ActivityTemplateFacade.
   */
  public static final class ActivityTemplateFacadeBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<ActivityTemplateFacadeBlockingStub> {
    private ActivityTemplateFacadeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ActivityTemplateFacadeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ActivityTemplateFacadeBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *保存物料
     * </pre>
     */
    public com.kikitrade.activity.facade.template.CommonResponse saveMaterial(com.kikitrade.activity.facade.template.ActivityMaterialDTO request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSaveMaterialMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service ActivityTemplateFacade.
   */
  public static final class ActivityTemplateFacadeFutureStub
      extends io.grpc.stub.AbstractFutureStub<ActivityTemplateFacadeFutureStub> {
    private ActivityTemplateFacadeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ActivityTemplateFacadeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ActivityTemplateFacadeFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *保存物料
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.template.CommonResponse> saveMaterial(
        com.kikitrade.activity.facade.template.ActivityMaterialDTO request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSaveMaterialMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_SAVE_MATERIAL = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_SAVE_MATERIAL:
          serviceImpl.saveMaterial((com.kikitrade.activity.facade.template.ActivityMaterialDTO) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.template.CommonResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getSaveMaterialMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.template.ActivityMaterialDTO,
              com.kikitrade.activity.facade.template.CommonResponse>(
                service, METHODID_SAVE_MATERIAL)))
        .build();
  }

  private static abstract class ActivityTemplateFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    ActivityTemplateFacadeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.kikitrade.activity.facade.template.ActivityTemplateFacadeOutClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("ActivityTemplateFacade");
    }
  }

  private static final class ActivityTemplateFacadeFileDescriptorSupplier
      extends ActivityTemplateFacadeBaseDescriptorSupplier {
    ActivityTemplateFacadeFileDescriptorSupplier() {}
  }

  private static final class ActivityTemplateFacadeMethodDescriptorSupplier
      extends ActivityTemplateFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    ActivityTemplateFacadeMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (ActivityTemplateFacadeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new ActivityTemplateFacadeFileDescriptorSupplier())
              .addMethod(getSaveMaterialMethod())
              .build();
        }
      }
    }
    return result;
  }
}
