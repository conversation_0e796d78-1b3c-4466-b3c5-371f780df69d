package com.kikitrade.activity.facade.coupon;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.58.0)",
    comments = "Source: DiscountCouponFacade.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class DiscountCouponFacadeGrpc {

  private DiscountCouponFacadeGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.kikitrade.activity.facade.coupon.DiscountCouponFacade";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.coupon.DiscountCouponRequest,
      com.kikitrade.activity.facade.coupon.DiscountCouponResponse> getUpsertDiscountCouponMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "upsertDiscountCoupon",
      requestType = com.kikitrade.activity.facade.coupon.DiscountCouponRequest.class,
      responseType = com.kikitrade.activity.facade.coupon.DiscountCouponResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.coupon.DiscountCouponRequest,
      com.kikitrade.activity.facade.coupon.DiscountCouponResponse> getUpsertDiscountCouponMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.coupon.DiscountCouponRequest, com.kikitrade.activity.facade.coupon.DiscountCouponResponse> getUpsertDiscountCouponMethod;
    if ((getUpsertDiscountCouponMethod = DiscountCouponFacadeGrpc.getUpsertDiscountCouponMethod) == null) {
      synchronized (DiscountCouponFacadeGrpc.class) {
        if ((getUpsertDiscountCouponMethod = DiscountCouponFacadeGrpc.getUpsertDiscountCouponMethod) == null) {
          DiscountCouponFacadeGrpc.getUpsertDiscountCouponMethod = getUpsertDiscountCouponMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.coupon.DiscountCouponRequest, com.kikitrade.activity.facade.coupon.DiscountCouponResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "upsertDiscountCoupon"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.coupon.DiscountCouponRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.coupon.DiscountCouponResponse.getDefaultInstance()))
              .setSchemaDescriptor(new DiscountCouponFacadeMethodDescriptorSupplier("upsertDiscountCoupon"))
              .build();
        }
      }
    }
    return getUpsertDiscountCouponMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static DiscountCouponFacadeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<DiscountCouponFacadeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<DiscountCouponFacadeStub>() {
        @java.lang.Override
        public DiscountCouponFacadeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new DiscountCouponFacadeStub(channel, callOptions);
        }
      };
    return DiscountCouponFacadeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static DiscountCouponFacadeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<DiscountCouponFacadeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<DiscountCouponFacadeBlockingStub>() {
        @java.lang.Override
        public DiscountCouponFacadeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new DiscountCouponFacadeBlockingStub(channel, callOptions);
        }
      };
    return DiscountCouponFacadeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static DiscountCouponFacadeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<DiscountCouponFacadeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<DiscountCouponFacadeFutureStub>() {
        @java.lang.Override
        public DiscountCouponFacadeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new DiscountCouponFacadeFutureStub(channel, callOptions);
        }
      };
    return DiscountCouponFacadeFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     * <pre>
     **
     *保存或修改优惠券
     * </pre>
     */
    default void upsertDiscountCoupon(com.kikitrade.activity.facade.coupon.DiscountCouponRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.coupon.DiscountCouponResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUpsertDiscountCouponMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service DiscountCouponFacade.
   */
  public static abstract class DiscountCouponFacadeImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return DiscountCouponFacadeGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service DiscountCouponFacade.
   */
  public static final class DiscountCouponFacadeStub
      extends io.grpc.stub.AbstractAsyncStub<DiscountCouponFacadeStub> {
    private DiscountCouponFacadeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected DiscountCouponFacadeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new DiscountCouponFacadeStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *保存或修改优惠券
     * </pre>
     */
    public void upsertDiscountCoupon(com.kikitrade.activity.facade.coupon.DiscountCouponRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.coupon.DiscountCouponResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUpsertDiscountCouponMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service DiscountCouponFacade.
   */
  public static final class DiscountCouponFacadeBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<DiscountCouponFacadeBlockingStub> {
    private DiscountCouponFacadeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected DiscountCouponFacadeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new DiscountCouponFacadeBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *保存或修改优惠券
     * </pre>
     */
    public com.kikitrade.activity.facade.coupon.DiscountCouponResponse upsertDiscountCoupon(com.kikitrade.activity.facade.coupon.DiscountCouponRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUpsertDiscountCouponMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service DiscountCouponFacade.
   */
  public static final class DiscountCouponFacadeFutureStub
      extends io.grpc.stub.AbstractFutureStub<DiscountCouponFacadeFutureStub> {
    private DiscountCouponFacadeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected DiscountCouponFacadeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new DiscountCouponFacadeFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *保存或修改优惠券
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.coupon.DiscountCouponResponse> upsertDiscountCoupon(
        com.kikitrade.activity.facade.coupon.DiscountCouponRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUpsertDiscountCouponMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_UPSERT_DISCOUNT_COUPON = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_UPSERT_DISCOUNT_COUPON:
          serviceImpl.upsertDiscountCoupon((com.kikitrade.activity.facade.coupon.DiscountCouponRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.coupon.DiscountCouponResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getUpsertDiscountCouponMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.coupon.DiscountCouponRequest,
              com.kikitrade.activity.facade.coupon.DiscountCouponResponse>(
                service, METHODID_UPSERT_DISCOUNT_COUPON)))
        .build();
  }

  private static abstract class DiscountCouponFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    DiscountCouponFacadeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.kikitrade.activity.facade.coupon.DiscountCouponFacadeOuterClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("DiscountCouponFacade");
    }
  }

  private static final class DiscountCouponFacadeFileDescriptorSupplier
      extends DiscountCouponFacadeBaseDescriptorSupplier {
    DiscountCouponFacadeFileDescriptorSupplier() {}
  }

  private static final class DiscountCouponFacadeMethodDescriptorSupplier
      extends DiscountCouponFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    DiscountCouponFacadeMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (DiscountCouponFacadeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new DiscountCouponFacadeFileDescriptorSupplier())
              .addMethod(getUpsertDiscountCouponMethod())
              .build();
        }
      }
    }
    return result;
  }
}
