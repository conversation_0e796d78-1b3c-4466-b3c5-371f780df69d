// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Banner.proto

package com.kikitrade.activity.facade.banner;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.banner.BannerDTO}
 */
public final class BannerDTO extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.banner.BannerDTO)
    BannerDTOOrBuilder {
private static final long serialVersionUID = 0L;
  // Use BannerDTO.newBuilder() to construct.
  private BannerDTO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private BannerDTO() {
    id_ = "";
    image_ = "";
    name_ = "";
    desc_ = "";
    url_ = "";
    source_ = 0;
    sourceId_ = "";
    channel_ = 0;
    local_ = "";
    saasId_ = "";
    status_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new BannerDTO();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.banner.BannerFacadeOutClass.internal_static_com_kikitrade_activity_facade_banner_BannerDTO_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.banner.BannerFacadeOutClass.internal_static_com_kikitrade_activity_facade_banner_BannerDTO_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.banner.BannerDTO.class, com.kikitrade.activity.facade.banner.BannerDTO.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IMAGE_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object image_ = "";
  /**
   * <code>string image = 2;</code>
   * @return The image.
   */
  @java.lang.Override
  public java.lang.String getImage() {
    java.lang.Object ref = image_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      image_ = s;
      return s;
    }
  }
  /**
   * <code>string image = 2;</code>
   * @return The bytes for image.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getImageBytes() {
    java.lang.Object ref = image_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      image_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object name_ = "";
  /**
   * <code>string name = 3;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <code>string name = 3;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DESC_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object desc_ = "";
  /**
   * <code>string desc = 4;</code>
   * @return The desc.
   */
  @java.lang.Override
  public java.lang.String getDesc() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      desc_ = s;
      return s;
    }
  }
  /**
   * <code>string desc = 4;</code>
   * @return The bytes for desc.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescBytes() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      desc_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int URL_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object url_ = "";
  /**
   * <pre>
   *banner跳转地址
   * </pre>
   *
   * <code>string url = 5;</code>
   * @return The url.
   */
  @java.lang.Override
  public java.lang.String getUrl() {
    java.lang.Object ref = url_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      url_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *banner跳转地址
   * </pre>
   *
   * <code>string url = 5;</code>
   * @return The bytes for url.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUrlBytes() {
    java.lang.Object ref = url_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      url_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SOURCE_FIELD_NUMBER = 6;
  private int source_ = 0;
  /**
   * <pre>
   *banner来源
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.banner.BannerSource source = 6;</code>
   * @return The enum numeric value on the wire for source.
   */
  @java.lang.Override public int getSourceValue() {
    return source_;
  }
  /**
   * <pre>
   *banner来源
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.banner.BannerSource source = 6;</code>
   * @return The source.
   */
  @java.lang.Override public com.kikitrade.activity.facade.banner.BannerSource getSource() {
    com.kikitrade.activity.facade.banner.BannerSource result = com.kikitrade.activity.facade.banner.BannerSource.forNumber(source_);
    return result == null ? com.kikitrade.activity.facade.banner.BannerSource.UNRECOGNIZED : result;
  }

  public static final int SOURCEID_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object sourceId_ = "";
  /**
   * <pre>
   *source来源id
   * </pre>
   *
   * <code>string sourceId = 7;</code>
   * @return The sourceId.
   */
  @java.lang.Override
  public java.lang.String getSourceId() {
    java.lang.Object ref = sourceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      sourceId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *source来源id
   * </pre>
   *
   * <code>string sourceId = 7;</code>
   * @return The bytes for sourceId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSourceIdBytes() {
    java.lang.Object ref = sourceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      sourceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ORDER_FIELD_NUMBER = 8;
  private int order_ = 0;
  /**
   * <pre>
   *bannerId
   * </pre>
   *
   * <code>int32 order = 8;</code>
   * @return The order.
   */
  @java.lang.Override
  public int getOrder() {
    return order_;
  }

  public static final int CHANNEL_FIELD_NUMBER = 9;
  private int channel_ = 0;
  /**
   * <pre>
   *banner显示在什么终端
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.banner.BannerChannel channel = 9;</code>
   * @return The enum numeric value on the wire for channel.
   */
  @java.lang.Override public int getChannelValue() {
    return channel_;
  }
  /**
   * <pre>
   *banner显示在什么终端
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.banner.BannerChannel channel = 9;</code>
   * @return The channel.
   */
  @java.lang.Override public com.kikitrade.activity.facade.banner.BannerChannel getChannel() {
    com.kikitrade.activity.facade.banner.BannerChannel result = com.kikitrade.activity.facade.banner.BannerChannel.forNumber(channel_);
    return result == null ? com.kikitrade.activity.facade.banner.BannerChannel.UNRECOGNIZED : result;
  }

  public static final int LOCAL_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object local_ = "";
  /**
   * <pre>
   *显示在页面的什么位置，home、detail
   * </pre>
   *
   * <code>string local = 10;</code>
   * @return The local.
   */
  @java.lang.Override
  public java.lang.String getLocal() {
    java.lang.Object ref = local_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      local_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *显示在页面的什么位置，home、detail
   * </pre>
   *
   * <code>string local = 10;</code>
   * @return The bytes for local.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLocalBytes() {
    java.lang.Object ref = local_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      local_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SAASID_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private volatile java.lang.Object saasId_ = "";
  /**
   * <code>string saasId = 11;</code>
   * @return The saasId.
   */
  @java.lang.Override
  public java.lang.String getSaasId() {
    java.lang.Object ref = saasId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      saasId_ = s;
      return s;
    }
  }
  /**
   * <code>string saasId = 11;</code>
   * @return The bytes for saasId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSaasIdBytes() {
    java.lang.Object ref = saasId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      saasId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATUS_FIELD_NUMBER = 12;
  private int status_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.banner.CommonStatus status = 12;</code>
   * @return The enum numeric value on the wire for status.
   */
  @java.lang.Override public int getStatusValue() {
    return status_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.banner.CommonStatus status = 12;</code>
   * @return The status.
   */
  @java.lang.Override public com.kikitrade.activity.facade.banner.CommonStatus getStatus() {
    com.kikitrade.activity.facade.banner.CommonStatus result = com.kikitrade.activity.facade.banner.CommonStatus.forNumber(status_);
    return result == null ? com.kikitrade.activity.facade.banner.CommonStatus.UNRECOGNIZED : result;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(image_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, image_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, name_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(desc_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, desc_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(url_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, url_);
    }
    if (source_ != com.kikitrade.activity.facade.banner.BannerSource.TASK.getNumber()) {
      output.writeEnum(6, source_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sourceId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, sourceId_);
    }
    if (order_ != 0) {
      output.writeInt32(8, order_);
    }
    if (channel_ != com.kikitrade.activity.facade.banner.BannerChannel.APP.getNumber()) {
      output.writeEnum(9, channel_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(local_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, local_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(saasId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, saasId_);
    }
    if (status_ != com.kikitrade.activity.facade.banner.CommonStatus.ACTIVE.getNumber()) {
      output.writeEnum(12, status_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(image_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, image_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, name_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(desc_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, desc_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(url_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, url_);
    }
    if (source_ != com.kikitrade.activity.facade.banner.BannerSource.TASK.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(6, source_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sourceId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, sourceId_);
    }
    if (order_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, order_);
    }
    if (channel_ != com.kikitrade.activity.facade.banner.BannerChannel.APP.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(9, channel_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(local_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, local_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(saasId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, saasId_);
    }
    if (status_ != com.kikitrade.activity.facade.banner.CommonStatus.ACTIVE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(12, status_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.banner.BannerDTO)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.banner.BannerDTO other = (com.kikitrade.activity.facade.banner.BannerDTO) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getImage()
        .equals(other.getImage())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (!getDesc()
        .equals(other.getDesc())) return false;
    if (!getUrl()
        .equals(other.getUrl())) return false;
    if (source_ != other.source_) return false;
    if (!getSourceId()
        .equals(other.getSourceId())) return false;
    if (getOrder()
        != other.getOrder()) return false;
    if (channel_ != other.channel_) return false;
    if (!getLocal()
        .equals(other.getLocal())) return false;
    if (!getSaasId()
        .equals(other.getSaasId())) return false;
    if (status_ != other.status_) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + IMAGE_FIELD_NUMBER;
    hash = (53 * hash) + getImage().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + DESC_FIELD_NUMBER;
    hash = (53 * hash) + getDesc().hashCode();
    hash = (37 * hash) + URL_FIELD_NUMBER;
    hash = (53 * hash) + getUrl().hashCode();
    hash = (37 * hash) + SOURCE_FIELD_NUMBER;
    hash = (53 * hash) + source_;
    hash = (37 * hash) + SOURCEID_FIELD_NUMBER;
    hash = (53 * hash) + getSourceId().hashCode();
    hash = (37 * hash) + ORDER_FIELD_NUMBER;
    hash = (53 * hash) + getOrder();
    hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
    hash = (53 * hash) + channel_;
    hash = (37 * hash) + LOCAL_FIELD_NUMBER;
    hash = (53 * hash) + getLocal().hashCode();
    hash = (37 * hash) + SAASID_FIELD_NUMBER;
    hash = (53 * hash) + getSaasId().hashCode();
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + status_;
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.banner.BannerDTO parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.banner.BannerDTO parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.banner.BannerDTO parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.banner.BannerDTO parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.banner.BannerDTO parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.banner.BannerDTO parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.banner.BannerDTO parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.banner.BannerDTO parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.banner.BannerDTO parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.banner.BannerDTO parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.banner.BannerDTO parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.banner.BannerDTO parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.banner.BannerDTO prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.banner.BannerDTO}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.banner.BannerDTO)
      com.kikitrade.activity.facade.banner.BannerDTOOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.banner.BannerFacadeOutClass.internal_static_com_kikitrade_activity_facade_banner_BannerDTO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.banner.BannerFacadeOutClass.internal_static_com_kikitrade_activity_facade_banner_BannerDTO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.banner.BannerDTO.class, com.kikitrade.activity.facade.banner.BannerDTO.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.banner.BannerDTO.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      image_ = "";
      name_ = "";
      desc_ = "";
      url_ = "";
      source_ = 0;
      sourceId_ = "";
      order_ = 0;
      channel_ = 0;
      local_ = "";
      saasId_ = "";
      status_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.banner.BannerFacadeOutClass.internal_static_com_kikitrade_activity_facade_banner_BannerDTO_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.banner.BannerDTO getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.banner.BannerDTO.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.banner.BannerDTO build() {
      com.kikitrade.activity.facade.banner.BannerDTO result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.banner.BannerDTO buildPartial() {
      com.kikitrade.activity.facade.banner.BannerDTO result = new com.kikitrade.activity.facade.banner.BannerDTO(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.kikitrade.activity.facade.banner.BannerDTO result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.image_ = image_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.name_ = name_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.desc_ = desc_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.url_ = url_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.source_ = source_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.sourceId_ = sourceId_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.order_ = order_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.channel_ = channel_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.local_ = local_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.saasId_ = saasId_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.status_ = status_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.banner.BannerDTO) {
        return mergeFrom((com.kikitrade.activity.facade.banner.BannerDTO)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.banner.BannerDTO other) {
      if (other == com.kikitrade.activity.facade.banner.BannerDTO.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getImage().isEmpty()) {
        image_ = other.image_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getDesc().isEmpty()) {
        desc_ = other.desc_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (!other.getUrl().isEmpty()) {
        url_ = other.url_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (other.source_ != 0) {
        setSourceValue(other.getSourceValue());
      }
      if (!other.getSourceId().isEmpty()) {
        sourceId_ = other.sourceId_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (other.getOrder() != 0) {
        setOrder(other.getOrder());
      }
      if (other.channel_ != 0) {
        setChannelValue(other.getChannelValue());
      }
      if (!other.getLocal().isEmpty()) {
        local_ = other.local_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (!other.getSaasId().isEmpty()) {
        saasId_ = other.saasId_;
        bitField0_ |= 0x00000400;
        onChanged();
      }
      if (other.status_ != 0) {
        setStatusValue(other.getStatusValue());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              image_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              name_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              desc_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              url_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 48: {
              source_ = input.readEnum();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 58: {
              sourceId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 64: {
              order_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              channel_ = input.readEnum();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 82: {
              local_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 90: {
              saasId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 96: {
              status_ = input.readEnum();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object image_ = "";
    /**
     * <code>string image = 2;</code>
     * @return The image.
     */
    public java.lang.String getImage() {
      java.lang.Object ref = image_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        image_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string image = 2;</code>
     * @return The bytes for image.
     */
    public com.google.protobuf.ByteString
        getImageBytes() {
      java.lang.Object ref = image_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        image_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string image = 2;</code>
     * @param value The image to set.
     * @return This builder for chaining.
     */
    public Builder setImage(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      image_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string image = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearImage() {
      image_ = getDefaultInstance().getImage();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string image = 2;</code>
     * @param value The bytes for image to set.
     * @return This builder for chaining.
     */
    public Builder setImageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      image_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <code>string name = 3;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string name = 3;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string name = 3;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>string name = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      name_ = getDefaultInstance().getName();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>string name = 3;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      name_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object desc_ = "";
    /**
     * <code>string desc = 4;</code>
     * @return The desc.
     */
    public java.lang.String getDesc() {
      java.lang.Object ref = desc_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        desc_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string desc = 4;</code>
     * @return The bytes for desc.
     */
    public com.google.protobuf.ByteString
        getDescBytes() {
      java.lang.Object ref = desc_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        desc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string desc = 4;</code>
     * @param value The desc to set.
     * @return This builder for chaining.
     */
    public Builder setDesc(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      desc_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>string desc = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearDesc() {
      desc_ = getDefaultInstance().getDesc();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>string desc = 4;</code>
     * @param value The bytes for desc to set.
     * @return This builder for chaining.
     */
    public Builder setDescBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      desc_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object url_ = "";
    /**
     * <pre>
     *banner跳转地址
     * </pre>
     *
     * <code>string url = 5;</code>
     * @return The url.
     */
    public java.lang.String getUrl() {
      java.lang.Object ref = url_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        url_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *banner跳转地址
     * </pre>
     *
     * <code>string url = 5;</code>
     * @return The bytes for url.
     */
    public com.google.protobuf.ByteString
        getUrlBytes() {
      java.lang.Object ref = url_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        url_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *banner跳转地址
     * </pre>
     *
     * <code>string url = 5;</code>
     * @param value The url to set.
     * @return This builder for chaining.
     */
    public Builder setUrl(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      url_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *banner跳转地址
     * </pre>
     *
     * <code>string url = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearUrl() {
      url_ = getDefaultInstance().getUrl();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *banner跳转地址
     * </pre>
     *
     * <code>string url = 5;</code>
     * @param value The bytes for url to set.
     * @return This builder for chaining.
     */
    public Builder setUrlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      url_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private int source_ = 0;
    /**
     * <pre>
     *banner来源
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.banner.BannerSource source = 6;</code>
     * @return The enum numeric value on the wire for source.
     */
    @java.lang.Override public int getSourceValue() {
      return source_;
    }
    /**
     * <pre>
     *banner来源
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.banner.BannerSource source = 6;</code>
     * @param value The enum numeric value on the wire for source to set.
     * @return This builder for chaining.
     */
    public Builder setSourceValue(int value) {
      source_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *banner来源
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.banner.BannerSource source = 6;</code>
     * @return The source.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.banner.BannerSource getSource() {
      com.kikitrade.activity.facade.banner.BannerSource result = com.kikitrade.activity.facade.banner.BannerSource.forNumber(source_);
      return result == null ? com.kikitrade.activity.facade.banner.BannerSource.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *banner来源
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.banner.BannerSource source = 6;</code>
     * @param value The source to set.
     * @return This builder for chaining.
     */
    public Builder setSource(com.kikitrade.activity.facade.banner.BannerSource value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000020;
      source_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *banner来源
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.banner.BannerSource source = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearSource() {
      bitField0_ = (bitField0_ & ~0x00000020);
      source_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object sourceId_ = "";
    /**
     * <pre>
     *source来源id
     * </pre>
     *
     * <code>string sourceId = 7;</code>
     * @return The sourceId.
     */
    public java.lang.String getSourceId() {
      java.lang.Object ref = sourceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sourceId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *source来源id
     * </pre>
     *
     * <code>string sourceId = 7;</code>
     * @return The bytes for sourceId.
     */
    public com.google.protobuf.ByteString
        getSourceIdBytes() {
      java.lang.Object ref = sourceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sourceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *source来源id
     * </pre>
     *
     * <code>string sourceId = 7;</code>
     * @param value The sourceId to set.
     * @return This builder for chaining.
     */
    public Builder setSourceId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      sourceId_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *source来源id
     * </pre>
     *
     * <code>string sourceId = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearSourceId() {
      sourceId_ = getDefaultInstance().getSourceId();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *source来源id
     * </pre>
     *
     * <code>string sourceId = 7;</code>
     * @param value The bytes for sourceId to set.
     * @return This builder for chaining.
     */
    public Builder setSourceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      sourceId_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private int order_ ;
    /**
     * <pre>
     *bannerId
     * </pre>
     *
     * <code>int32 order = 8;</code>
     * @return The order.
     */
    @java.lang.Override
    public int getOrder() {
      return order_;
    }
    /**
     * <pre>
     *bannerId
     * </pre>
     *
     * <code>int32 order = 8;</code>
     * @param value The order to set.
     * @return This builder for chaining.
     */
    public Builder setOrder(int value) {

      order_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *bannerId
     * </pre>
     *
     * <code>int32 order = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearOrder() {
      bitField0_ = (bitField0_ & ~0x00000080);
      order_ = 0;
      onChanged();
      return this;
    }

    private int channel_ = 0;
    /**
     * <pre>
     *banner显示在什么终端
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.banner.BannerChannel channel = 9;</code>
     * @return The enum numeric value on the wire for channel.
     */
    @java.lang.Override public int getChannelValue() {
      return channel_;
    }
    /**
     * <pre>
     *banner显示在什么终端
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.banner.BannerChannel channel = 9;</code>
     * @param value The enum numeric value on the wire for channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannelValue(int value) {
      channel_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *banner显示在什么终端
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.banner.BannerChannel channel = 9;</code>
     * @return The channel.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.banner.BannerChannel getChannel() {
      com.kikitrade.activity.facade.banner.BannerChannel result = com.kikitrade.activity.facade.banner.BannerChannel.forNumber(channel_);
      return result == null ? com.kikitrade.activity.facade.banner.BannerChannel.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *banner显示在什么终端
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.banner.BannerChannel channel = 9;</code>
     * @param value The channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannel(com.kikitrade.activity.facade.banner.BannerChannel value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000100;
      channel_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *banner显示在什么终端
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.banner.BannerChannel channel = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannel() {
      bitField0_ = (bitField0_ & ~0x00000100);
      channel_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object local_ = "";
    /**
     * <pre>
     *显示在页面的什么位置，home、detail
     * </pre>
     *
     * <code>string local = 10;</code>
     * @return The local.
     */
    public java.lang.String getLocal() {
      java.lang.Object ref = local_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        local_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *显示在页面的什么位置，home、detail
     * </pre>
     *
     * <code>string local = 10;</code>
     * @return The bytes for local.
     */
    public com.google.protobuf.ByteString
        getLocalBytes() {
      java.lang.Object ref = local_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        local_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *显示在页面的什么位置，home、detail
     * </pre>
     *
     * <code>string local = 10;</code>
     * @param value The local to set.
     * @return This builder for chaining.
     */
    public Builder setLocal(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      local_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *显示在页面的什么位置，home、detail
     * </pre>
     *
     * <code>string local = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearLocal() {
      local_ = getDefaultInstance().getLocal();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *显示在页面的什么位置，home、detail
     * </pre>
     *
     * <code>string local = 10;</code>
     * @param value The bytes for local to set.
     * @return This builder for chaining.
     */
    public Builder setLocalBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      local_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private java.lang.Object saasId_ = "";
    /**
     * <code>string saasId = 11;</code>
     * @return The saasId.
     */
    public java.lang.String getSaasId() {
      java.lang.Object ref = saasId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        saasId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string saasId = 11;</code>
     * @return The bytes for saasId.
     */
    public com.google.protobuf.ByteString
        getSaasIdBytes() {
      java.lang.Object ref = saasId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        saasId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string saasId = 11;</code>
     * @param value The saasId to set.
     * @return This builder for chaining.
     */
    public Builder setSaasId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      saasId_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>string saasId = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearSaasId() {
      saasId_ = getDefaultInstance().getSaasId();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }
    /**
     * <code>string saasId = 11;</code>
     * @param value The bytes for saasId to set.
     * @return This builder for chaining.
     */
    public Builder setSaasIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      saasId_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }

    private int status_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.banner.CommonStatus status = 12;</code>
     * @return The enum numeric value on the wire for status.
     */
    @java.lang.Override public int getStatusValue() {
      return status_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.banner.CommonStatus status = 12;</code>
     * @param value The enum numeric value on the wire for status to set.
     * @return This builder for chaining.
     */
    public Builder setStatusValue(int value) {
      status_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.banner.CommonStatus status = 12;</code>
     * @return The status.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.banner.CommonStatus getStatus() {
      com.kikitrade.activity.facade.banner.CommonStatus result = com.kikitrade.activity.facade.banner.CommonStatus.forNumber(status_);
      return result == null ? com.kikitrade.activity.facade.banner.CommonStatus.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.banner.CommonStatus status = 12;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(com.kikitrade.activity.facade.banner.CommonStatus value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000800;
      status_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.banner.CommonStatus status = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      bitField0_ = (bitField0_ & ~0x00000800);
      status_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.banner.BannerDTO)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.banner.BannerDTO)
  private static final com.kikitrade.activity.facade.banner.BannerDTO DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.banner.BannerDTO();
  }

  public static com.kikitrade.activity.facade.banner.BannerDTO getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<BannerDTO>
      PARSER = new com.google.protobuf.AbstractParser<BannerDTO>() {
    @java.lang.Override
    public BannerDTO parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<BannerDTO> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<BannerDTO> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.banner.BannerDTO getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

