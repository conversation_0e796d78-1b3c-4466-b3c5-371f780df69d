// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.award.LotteryListResponse}
 */
public final class LotteryListResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.award.LotteryListResponse)
    LotteryListResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LotteryListResponse.newBuilder() to construct.
  private LotteryListResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LotteryListResponse() {
    lottery_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LotteryListResponse();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_LotteryListResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_LotteryListResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.award.LotteryListResponse.class, com.kikitrade.activity.facade.award.LotteryListResponse.Builder.class);
  }

  public static final int LOTTERY_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.award.Lottery> lottery_;
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.award.Lottery> getLotteryList() {
    return lottery_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.award.LotteryOrBuilder> 
      getLotteryOrBuilderList() {
    return lottery_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   */
  @java.lang.Override
  public int getLotteryCount() {
    return lottery_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.Lottery getLottery(int index) {
    return lottery_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.LotteryOrBuilder getLotteryOrBuilder(
      int index) {
    return lottery_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < lottery_.size(); i++) {
      output.writeMessage(1, lottery_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < lottery_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, lottery_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.award.LotteryListResponse)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.award.LotteryListResponse other = (com.kikitrade.activity.facade.award.LotteryListResponse) obj;

    if (!getLotteryList()
        .equals(other.getLotteryList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getLotteryCount() > 0) {
      hash = (37 * hash) + LOTTERY_FIELD_NUMBER;
      hash = (53 * hash) + getLotteryList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.award.LotteryListResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.LotteryListResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.LotteryListResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.LotteryListResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.LotteryListResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.LotteryListResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.LotteryListResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.LotteryListResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.award.LotteryListResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.award.LotteryListResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.LotteryListResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.LotteryListResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.award.LotteryListResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.award.LotteryListResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.award.LotteryListResponse)
      com.kikitrade.activity.facade.award.LotteryListResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_LotteryListResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_LotteryListResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.award.LotteryListResponse.class, com.kikitrade.activity.facade.award.LotteryListResponse.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.award.LotteryListResponse.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (lotteryBuilder_ == null) {
        lottery_ = java.util.Collections.emptyList();
      } else {
        lottery_ = null;
        lotteryBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_LotteryListResponse_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.LotteryListResponse getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.award.LotteryListResponse.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.LotteryListResponse build() {
      com.kikitrade.activity.facade.award.LotteryListResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.LotteryListResponse buildPartial() {
      com.kikitrade.activity.facade.award.LotteryListResponse result = new com.kikitrade.activity.facade.award.LotteryListResponse(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.award.LotteryListResponse result) {
      if (lotteryBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          lottery_ = java.util.Collections.unmodifiableList(lottery_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.lottery_ = lottery_;
      } else {
        result.lottery_ = lotteryBuilder_.build();
      }
    }

    private void buildPartial0(com.kikitrade.activity.facade.award.LotteryListResponse result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.award.LotteryListResponse) {
        return mergeFrom((com.kikitrade.activity.facade.award.LotteryListResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.award.LotteryListResponse other) {
      if (other == com.kikitrade.activity.facade.award.LotteryListResponse.getDefaultInstance()) return this;
      if (lotteryBuilder_ == null) {
        if (!other.lottery_.isEmpty()) {
          if (lottery_.isEmpty()) {
            lottery_ = other.lottery_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureLotteryIsMutable();
            lottery_.addAll(other.lottery_);
          }
          onChanged();
        }
      } else {
        if (!other.lottery_.isEmpty()) {
          if (lotteryBuilder_.isEmpty()) {
            lotteryBuilder_.dispose();
            lotteryBuilder_ = null;
            lottery_ = other.lottery_;
            bitField0_ = (bitField0_ & ~0x00000001);
            lotteryBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getLotteryFieldBuilder() : null;
          } else {
            lotteryBuilder_.addAllMessages(other.lottery_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.kikitrade.activity.facade.award.Lottery m =
                  input.readMessage(
                      com.kikitrade.activity.facade.award.Lottery.parser(),
                      extensionRegistry);
              if (lotteryBuilder_ == null) {
                ensureLotteryIsMutable();
                lottery_.add(m);
              } else {
                lotteryBuilder_.addMessage(m);
              }
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<com.kikitrade.activity.facade.award.Lottery> lottery_ =
      java.util.Collections.emptyList();
    private void ensureLotteryIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        lottery_ = new java.util.ArrayList<com.kikitrade.activity.facade.award.Lottery>(lottery_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.Lottery, com.kikitrade.activity.facade.award.Lottery.Builder, com.kikitrade.activity.facade.award.LotteryOrBuilder> lotteryBuilder_;

    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.Lottery> getLotteryList() {
      if (lotteryBuilder_ == null) {
        return java.util.Collections.unmodifiableList(lottery_);
      } else {
        return lotteryBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public int getLotteryCount() {
      if (lotteryBuilder_ == null) {
        return lottery_.size();
      } else {
        return lotteryBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public com.kikitrade.activity.facade.award.Lottery getLottery(int index) {
      if (lotteryBuilder_ == null) {
        return lottery_.get(index);
      } else {
        return lotteryBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public Builder setLottery(
        int index, com.kikitrade.activity.facade.award.Lottery value) {
      if (lotteryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLotteryIsMutable();
        lottery_.set(index, value);
        onChanged();
      } else {
        lotteryBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public Builder setLottery(
        int index, com.kikitrade.activity.facade.award.Lottery.Builder builderForValue) {
      if (lotteryBuilder_ == null) {
        ensureLotteryIsMutable();
        lottery_.set(index, builderForValue.build());
        onChanged();
      } else {
        lotteryBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public Builder addLottery(com.kikitrade.activity.facade.award.Lottery value) {
      if (lotteryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLotteryIsMutable();
        lottery_.add(value);
        onChanged();
      } else {
        lotteryBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public Builder addLottery(
        int index, com.kikitrade.activity.facade.award.Lottery value) {
      if (lotteryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLotteryIsMutable();
        lottery_.add(index, value);
        onChanged();
      } else {
        lotteryBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public Builder addLottery(
        com.kikitrade.activity.facade.award.Lottery.Builder builderForValue) {
      if (lotteryBuilder_ == null) {
        ensureLotteryIsMutable();
        lottery_.add(builderForValue.build());
        onChanged();
      } else {
        lotteryBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public Builder addLottery(
        int index, com.kikitrade.activity.facade.award.Lottery.Builder builderForValue) {
      if (lotteryBuilder_ == null) {
        ensureLotteryIsMutable();
        lottery_.add(index, builderForValue.build());
        onChanged();
      } else {
        lotteryBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public Builder addAllLottery(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.award.Lottery> values) {
      if (lotteryBuilder_ == null) {
        ensureLotteryIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, lottery_);
        onChanged();
      } else {
        lotteryBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public Builder clearLottery() {
      if (lotteryBuilder_ == null) {
        lottery_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        lotteryBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public Builder removeLottery(int index) {
      if (lotteryBuilder_ == null) {
        ensureLotteryIsMutable();
        lottery_.remove(index);
        onChanged();
      } else {
        lotteryBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public com.kikitrade.activity.facade.award.Lottery.Builder getLotteryBuilder(
        int index) {
      return getLotteryFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public com.kikitrade.activity.facade.award.LotteryOrBuilder getLotteryOrBuilder(
        int index) {
      if (lotteryBuilder_ == null) {
        return lottery_.get(index);  } else {
        return lotteryBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.award.LotteryOrBuilder> 
         getLotteryOrBuilderList() {
      if (lotteryBuilder_ != null) {
        return lotteryBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(lottery_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public com.kikitrade.activity.facade.award.Lottery.Builder addLotteryBuilder() {
      return getLotteryFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.award.Lottery.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public com.kikitrade.activity.facade.award.Lottery.Builder addLotteryBuilder(
        int index) {
      return getLotteryFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.award.Lottery.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.Lottery.Builder> 
         getLotteryBuilderList() {
      return getLotteryFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.Lottery, com.kikitrade.activity.facade.award.Lottery.Builder, com.kikitrade.activity.facade.award.LotteryOrBuilder> 
        getLotteryFieldBuilder() {
      if (lotteryBuilder_ == null) {
        lotteryBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.award.Lottery, com.kikitrade.activity.facade.award.Lottery.Builder, com.kikitrade.activity.facade.award.LotteryOrBuilder>(
                lottery_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        lottery_ = null;
      }
      return lotteryBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.award.LotteryListResponse)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.award.LotteryListResponse)
  private static final com.kikitrade.activity.facade.award.LotteryListResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.award.LotteryListResponse();
  }

  public static com.kikitrade.activity.facade.award.LotteryListResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LotteryListResponse>
      PARSER = new com.google.protobuf.AbstractParser<LotteryListResponse>() {
    @java.lang.Override
    public LotteryListResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<LotteryListResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LotteryListResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.award.LotteryListResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

