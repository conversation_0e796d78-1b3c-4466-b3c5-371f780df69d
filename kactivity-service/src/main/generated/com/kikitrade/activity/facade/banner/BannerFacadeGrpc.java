package com.kikitrade.activity.facade.banner;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.58.0)",
    comments = "Source: Banner.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class BannerFacadeGrpc {

  private BannerFacadeGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.kikitrade.activity.facade.banner.BannerFacade";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.banner.BannerDTO,
      com.kikitrade.activity.facade.banner.BannerSaveResponse> getSaveMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "save",
      requestType = com.kikitrade.activity.facade.banner.BannerDTO.class,
      responseType = com.kikitrade.activity.facade.banner.BannerSaveResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.banner.BannerDTO,
      com.kikitrade.activity.facade.banner.BannerSaveResponse> getSaveMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.banner.BannerDTO, com.kikitrade.activity.facade.banner.BannerSaveResponse> getSaveMethod;
    if ((getSaveMethod = BannerFacadeGrpc.getSaveMethod) == null) {
      synchronized (BannerFacadeGrpc.class) {
        if ((getSaveMethod = BannerFacadeGrpc.getSaveMethod) == null) {
          BannerFacadeGrpc.getSaveMethod = getSaveMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.banner.BannerDTO, com.kikitrade.activity.facade.banner.BannerSaveResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "save"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.banner.BannerDTO.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.banner.BannerSaveResponse.getDefaultInstance()))
              .setSchemaDescriptor(new BannerFacadeMethodDescriptorSupplier("save"))
              .build();
        }
      }
    }
    return getSaveMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static BannerFacadeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<BannerFacadeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<BannerFacadeStub>() {
        @java.lang.Override
        public BannerFacadeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new BannerFacadeStub(channel, callOptions);
        }
      };
    return BannerFacadeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static BannerFacadeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<BannerFacadeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<BannerFacadeBlockingStub>() {
        @java.lang.Override
        public BannerFacadeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new BannerFacadeBlockingStub(channel, callOptions);
        }
      };
    return BannerFacadeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static BannerFacadeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<BannerFacadeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<BannerFacadeFutureStub>() {
        @java.lang.Override
        public BannerFacadeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new BannerFacadeFutureStub(channel, callOptions);
        }
      };
    return BannerFacadeFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     */
    default void save(com.kikitrade.activity.facade.banner.BannerDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.banner.BannerSaveResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSaveMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service BannerFacade.
   */
  public static abstract class BannerFacadeImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return BannerFacadeGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service BannerFacade.
   */
  public static final class BannerFacadeStub
      extends io.grpc.stub.AbstractAsyncStub<BannerFacadeStub> {
    private BannerFacadeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected BannerFacadeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new BannerFacadeStub(channel, callOptions);
    }

    /**
     */
    public void save(com.kikitrade.activity.facade.banner.BannerDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.banner.BannerSaveResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSaveMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service BannerFacade.
   */
  public static final class BannerFacadeBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<BannerFacadeBlockingStub> {
    private BannerFacadeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected BannerFacadeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new BannerFacadeBlockingStub(channel, callOptions);
    }

    /**
     */
    public com.kikitrade.activity.facade.banner.BannerSaveResponse save(com.kikitrade.activity.facade.banner.BannerDTO request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSaveMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service BannerFacade.
   */
  public static final class BannerFacadeFutureStub
      extends io.grpc.stub.AbstractFutureStub<BannerFacadeFutureStub> {
    private BannerFacadeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected BannerFacadeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new BannerFacadeFutureStub(channel, callOptions);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.banner.BannerSaveResponse> save(
        com.kikitrade.activity.facade.banner.BannerDTO request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSaveMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_SAVE = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_SAVE:
          serviceImpl.save((com.kikitrade.activity.facade.banner.BannerDTO) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.banner.BannerSaveResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getSaveMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.banner.BannerDTO,
              com.kikitrade.activity.facade.banner.BannerSaveResponse>(
                service, METHODID_SAVE)))
        .build();
  }

  private static abstract class BannerFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    BannerFacadeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.kikitrade.activity.facade.banner.BannerFacadeOutClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("BannerFacade");
    }
  }

  private static final class BannerFacadeFileDescriptorSupplier
      extends BannerFacadeBaseDescriptorSupplier {
    BannerFacadeFileDescriptorSupplier() {}
  }

  private static final class BannerFacadeMethodDescriptorSupplier
      extends BannerFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    BannerFacadeMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (BannerFacadeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new BannerFacadeFileDescriptorSupplier())
              .addMethod(getSaveMethod())
              .build();
        }
      }
    }
    return result;
  }
}
