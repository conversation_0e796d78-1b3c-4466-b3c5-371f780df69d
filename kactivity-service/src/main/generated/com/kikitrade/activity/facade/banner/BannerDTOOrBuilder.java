// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Banner.proto

package com.kikitrade.activity.facade.banner;

public interface BannerDTOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.banner.BannerDTO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string image = 2;</code>
   * @return The image.
   */
  java.lang.String getImage();
  /**
   * <code>string image = 2;</code>
   * @return The bytes for image.
   */
  com.google.protobuf.ByteString
      getImageBytes();

  /**
   * <code>string name = 3;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <code>string name = 3;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <code>string desc = 4;</code>
   * @return The desc.
   */
  java.lang.String getDesc();
  /**
   * <code>string desc = 4;</code>
   * @return The bytes for desc.
   */
  com.google.protobuf.ByteString
      getDescBytes();

  /**
   * <pre>
   *banner跳转地址
   * </pre>
   *
   * <code>string url = 5;</code>
   * @return The url.
   */
  java.lang.String getUrl();
  /**
   * <pre>
   *banner跳转地址
   * </pre>
   *
   * <code>string url = 5;</code>
   * @return The bytes for url.
   */
  com.google.protobuf.ByteString
      getUrlBytes();

  /**
   * <pre>
   *banner来源
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.banner.BannerSource source = 6;</code>
   * @return The enum numeric value on the wire for source.
   */
  int getSourceValue();
  /**
   * <pre>
   *banner来源
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.banner.BannerSource source = 6;</code>
   * @return The source.
   */
  com.kikitrade.activity.facade.banner.BannerSource getSource();

  /**
   * <pre>
   *source来源id
   * </pre>
   *
   * <code>string sourceId = 7;</code>
   * @return The sourceId.
   */
  java.lang.String getSourceId();
  /**
   * <pre>
   *source来源id
   * </pre>
   *
   * <code>string sourceId = 7;</code>
   * @return The bytes for sourceId.
   */
  com.google.protobuf.ByteString
      getSourceIdBytes();

  /**
   * <pre>
   *bannerId
   * </pre>
   *
   * <code>int32 order = 8;</code>
   * @return The order.
   */
  int getOrder();

  /**
   * <pre>
   *banner显示在什么终端
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.banner.BannerChannel channel = 9;</code>
   * @return The enum numeric value on the wire for channel.
   */
  int getChannelValue();
  /**
   * <pre>
   *banner显示在什么终端
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.banner.BannerChannel channel = 9;</code>
   * @return The channel.
   */
  com.kikitrade.activity.facade.banner.BannerChannel getChannel();

  /**
   * <pre>
   *显示在页面的什么位置，home、detail
   * </pre>
   *
   * <code>string local = 10;</code>
   * @return The local.
   */
  java.lang.String getLocal();
  /**
   * <pre>
   *显示在页面的什么位置，home、detail
   * </pre>
   *
   * <code>string local = 10;</code>
   * @return The bytes for local.
   */
  com.google.protobuf.ByteString
      getLocalBytes();

  /**
   * <code>string saasId = 11;</code>
   * @return The saasId.
   */
  java.lang.String getSaasId();
  /**
   * <code>string saasId = 11;</code>
   * @return The bytes for saasId.
   */
  com.google.protobuf.ByteString
      getSaasIdBytes();

  /**
   * <code>.com.kikitrade.activity.facade.banner.CommonStatus status = 12;</code>
   * @return The enum numeric value on the wire for status.
   */
  int getStatusValue();
  /**
   * <code>.com.kikitrade.activity.facade.banner.CommonStatus status = 12;</code>
   * @return The status.
   */
  com.kikitrade.activity.facade.banner.CommonStatus getStatus();
}
