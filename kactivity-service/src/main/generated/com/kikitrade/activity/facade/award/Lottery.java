// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.award.Lottery}
 */
public final class Lottery extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.award.Lottery)
    LotteryOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Lottery.newBuilder() to construct.
  private Lottery(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Lottery() {
    id_ = "";
    valid_ = "";
    remark_ = "";
    status_ = "";
    vipLevel_ = "";
    amount_ = "";
    timesLimit_ = "";
    rewardLimit_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Lottery();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_Lottery_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_Lottery_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.award.Lottery.class, com.kikitrade.activity.facade.award.Lottery.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int VALID_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object valid_ = "";
  /**
   * <pre>
   *valid
   * </pre>
   *
   * <code>string valid = 2;</code>
   * @return The valid.
   */
  @java.lang.Override
  public java.lang.String getValid() {
    java.lang.Object ref = valid_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      valid_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *valid
   * </pre>
   *
   * <code>string valid = 2;</code>
   * @return The bytes for valid.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getValidBytes() {
    java.lang.Object ref = valid_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      valid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REMARK_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object remark_ = "";
  /**
   * <pre>
   *remark
   * </pre>
   *
   * <code>string remark = 3;</code>
   * @return The remark.
   */
  @java.lang.Override
  public java.lang.String getRemark() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      remark_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *remark
   * </pre>
   *
   * <code>string remark = 3;</code>
   * @return The bytes for remark.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRemarkBytes() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      remark_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATUS_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object status_ = "";
  /**
   * <pre>
   *status
   * </pre>
   *
   * <code>string status = 4;</code>
   * @return The status.
   */
  @java.lang.Override
  public java.lang.String getStatus() {
    java.lang.Object ref = status_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      status_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *status
   * </pre>
   *
   * <code>string status = 4;</code>
   * @return The bytes for status.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStatusBytes() {
    java.lang.Object ref = status_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      status_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int VIPLEVEL_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object vipLevel_ = "";
  /**
   * <pre>
   *规则vip等级
   * </pre>
   *
   * <code>string vipLevel = 5;</code>
   * @return The vipLevel.
   */
  @java.lang.Override
  public java.lang.String getVipLevel() {
    java.lang.Object ref = vipLevel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      vipLevel_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *规则vip等级
   * </pre>
   *
   * <code>string vipLevel = 5;</code>
   * @return The bytes for vipLevel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getVipLevelBytes() {
    java.lang.Object ref = vipLevel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      vipLevel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AMOUNT_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object amount_ = "";
  /**
   * <pre>
   *消耗燃料（次）
   * </pre>
   *
   * <code>string amount = 6;</code>
   * @return The amount.
   */
  @java.lang.Override
  public java.lang.String getAmount() {
    java.lang.Object ref = amount_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      amount_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *消耗燃料（次）
   * </pre>
   *
   * <code>string amount = 6;</code>
   * @return The bytes for amount.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAmountBytes() {
    java.lang.Object ref = amount_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      amount_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TIMESLIMIT_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object timesLimit_ = "";
  /**
   * <pre>
   *参与次数
   * </pre>
   *
   * <code>string timesLimit = 7;</code>
   * @return The timesLimit.
   */
  @java.lang.Override
  public java.lang.String getTimesLimit() {
    java.lang.Object ref = timesLimit_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      timesLimit_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *参与次数
   * </pre>
   *
   * <code>string timesLimit = 7;</code>
   * @return The bytes for timesLimit.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTimesLimitBytes() {
    java.lang.Object ref = timesLimit_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      timesLimit_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REWARDLIMIT_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object rewardLimit_ = "";
  /**
   * <pre>
   *奖励上限
   * </pre>
   *
   * <code>string rewardLimit = 8;</code>
   * @return The rewardLimit.
   */
  @java.lang.Override
  public java.lang.String getRewardLimit() {
    java.lang.Object ref = rewardLimit_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      rewardLimit_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *奖励上限
   * </pre>
   *
   * <code>string rewardLimit = 8;</code>
   * @return The bytes for rewardLimit.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardLimitBytes() {
    java.lang.Object ref = rewardLimit_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      rewardLimit_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(valid_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, valid_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(remark_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, remark_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(status_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(vipLevel_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, vipLevel_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amount_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, amount_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(timesLimit_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, timesLimit_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(rewardLimit_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, rewardLimit_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(valid_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, valid_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(remark_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, remark_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(status_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(vipLevel_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, vipLevel_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amount_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, amount_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(timesLimit_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, timesLimit_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(rewardLimit_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, rewardLimit_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.award.Lottery)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.award.Lottery other = (com.kikitrade.activity.facade.award.Lottery) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getValid()
        .equals(other.getValid())) return false;
    if (!getRemark()
        .equals(other.getRemark())) return false;
    if (!getStatus()
        .equals(other.getStatus())) return false;
    if (!getVipLevel()
        .equals(other.getVipLevel())) return false;
    if (!getAmount()
        .equals(other.getAmount())) return false;
    if (!getTimesLimit()
        .equals(other.getTimesLimit())) return false;
    if (!getRewardLimit()
        .equals(other.getRewardLimit())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + VALID_FIELD_NUMBER;
    hash = (53 * hash) + getValid().hashCode();
    hash = (37 * hash) + REMARK_FIELD_NUMBER;
    hash = (53 * hash) + getRemark().hashCode();
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + getStatus().hashCode();
    hash = (37 * hash) + VIPLEVEL_FIELD_NUMBER;
    hash = (53 * hash) + getVipLevel().hashCode();
    hash = (37 * hash) + AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getAmount().hashCode();
    hash = (37 * hash) + TIMESLIMIT_FIELD_NUMBER;
    hash = (53 * hash) + getTimesLimit().hashCode();
    hash = (37 * hash) + REWARDLIMIT_FIELD_NUMBER;
    hash = (53 * hash) + getRewardLimit().hashCode();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.award.Lottery parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.Lottery parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.Lottery parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.Lottery parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.Lottery parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.Lottery parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.Lottery parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.Lottery parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.award.Lottery parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.award.Lottery parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.Lottery parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.Lottery parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.award.Lottery prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.award.Lottery}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.award.Lottery)
      com.kikitrade.activity.facade.award.LotteryOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_Lottery_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_Lottery_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.award.Lottery.class, com.kikitrade.activity.facade.award.Lottery.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.award.Lottery.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      valid_ = "";
      remark_ = "";
      status_ = "";
      vipLevel_ = "";
      amount_ = "";
      timesLimit_ = "";
      rewardLimit_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_Lottery_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.Lottery getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.award.Lottery.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.Lottery build() {
      com.kikitrade.activity.facade.award.Lottery result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.Lottery buildPartial() {
      com.kikitrade.activity.facade.award.Lottery result = new com.kikitrade.activity.facade.award.Lottery(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.kikitrade.activity.facade.award.Lottery result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.valid_ = valid_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.remark_ = remark_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.status_ = status_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.vipLevel_ = vipLevel_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.amount_ = amount_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.timesLimit_ = timesLimit_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.rewardLimit_ = rewardLimit_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.award.Lottery) {
        return mergeFrom((com.kikitrade.activity.facade.award.Lottery)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.award.Lottery other) {
      if (other == com.kikitrade.activity.facade.award.Lottery.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getValid().isEmpty()) {
        valid_ = other.valid_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getRemark().isEmpty()) {
        remark_ = other.remark_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getStatus().isEmpty()) {
        status_ = other.status_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (!other.getVipLevel().isEmpty()) {
        vipLevel_ = other.vipLevel_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (!other.getAmount().isEmpty()) {
        amount_ = other.amount_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (!other.getTimesLimit().isEmpty()) {
        timesLimit_ = other.timesLimit_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (!other.getRewardLimit().isEmpty()) {
        rewardLimit_ = other.rewardLimit_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              valid_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              remark_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              status_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              vipLevel_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              amount_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              timesLimit_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              rewardLimit_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object valid_ = "";
    /**
     * <pre>
     *valid
     * </pre>
     *
     * <code>string valid = 2;</code>
     * @return The valid.
     */
    public java.lang.String getValid() {
      java.lang.Object ref = valid_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        valid_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *valid
     * </pre>
     *
     * <code>string valid = 2;</code>
     * @return The bytes for valid.
     */
    public com.google.protobuf.ByteString
        getValidBytes() {
      java.lang.Object ref = valid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        valid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *valid
     * </pre>
     *
     * <code>string valid = 2;</code>
     * @param value The valid to set.
     * @return This builder for chaining.
     */
    public Builder setValid(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      valid_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *valid
     * </pre>
     *
     * <code>string valid = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearValid() {
      valid_ = getDefaultInstance().getValid();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *valid
     * </pre>
     *
     * <code>string valid = 2;</code>
     * @param value The bytes for valid to set.
     * @return This builder for chaining.
     */
    public Builder setValidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      valid_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object remark_ = "";
    /**
     * <pre>
     *remark
     * </pre>
     *
     * <code>string remark = 3;</code>
     * @return The remark.
     */
    public java.lang.String getRemark() {
      java.lang.Object ref = remark_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        remark_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *remark
     * </pre>
     *
     * <code>string remark = 3;</code>
     * @return The bytes for remark.
     */
    public com.google.protobuf.ByteString
        getRemarkBytes() {
      java.lang.Object ref = remark_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        remark_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *remark
     * </pre>
     *
     * <code>string remark = 3;</code>
     * @param value The remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemark(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      remark_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *remark
     * </pre>
     *
     * <code>string remark = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearRemark() {
      remark_ = getDefaultInstance().getRemark();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *remark
     * </pre>
     *
     * <code>string remark = 3;</code>
     * @param value The bytes for remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemarkBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      remark_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object status_ = "";
    /**
     * <pre>
     *status
     * </pre>
     *
     * <code>string status = 4;</code>
     * @return The status.
     */
    public java.lang.String getStatus() {
      java.lang.Object ref = status_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        status_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *status
     * </pre>
     *
     * <code>string status = 4;</code>
     * @return The bytes for status.
     */
    public com.google.protobuf.ByteString
        getStatusBytes() {
      java.lang.Object ref = status_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        status_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *status
     * </pre>
     *
     * <code>string status = 4;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      status_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *status
     * </pre>
     *
     * <code>string status = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      status_ = getDefaultInstance().getStatus();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *status
     * </pre>
     *
     * <code>string status = 4;</code>
     * @param value The bytes for status to set.
     * @return This builder for chaining.
     */
    public Builder setStatusBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      status_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object vipLevel_ = "";
    /**
     * <pre>
     *规则vip等级
     * </pre>
     *
     * <code>string vipLevel = 5;</code>
     * @return The vipLevel.
     */
    public java.lang.String getVipLevel() {
      java.lang.Object ref = vipLevel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        vipLevel_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *规则vip等级
     * </pre>
     *
     * <code>string vipLevel = 5;</code>
     * @return The bytes for vipLevel.
     */
    public com.google.protobuf.ByteString
        getVipLevelBytes() {
      java.lang.Object ref = vipLevel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        vipLevel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *规则vip等级
     * </pre>
     *
     * <code>string vipLevel = 5;</code>
     * @param value The vipLevel to set.
     * @return This builder for chaining.
     */
    public Builder setVipLevel(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      vipLevel_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *规则vip等级
     * </pre>
     *
     * <code>string vipLevel = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearVipLevel() {
      vipLevel_ = getDefaultInstance().getVipLevel();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *规则vip等级
     * </pre>
     *
     * <code>string vipLevel = 5;</code>
     * @param value The bytes for vipLevel to set.
     * @return This builder for chaining.
     */
    public Builder setVipLevelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      vipLevel_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private java.lang.Object amount_ = "";
    /**
     * <pre>
     *消耗燃料（次）
     * </pre>
     *
     * <code>string amount = 6;</code>
     * @return The amount.
     */
    public java.lang.String getAmount() {
      java.lang.Object ref = amount_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        amount_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *消耗燃料（次）
     * </pre>
     *
     * <code>string amount = 6;</code>
     * @return The bytes for amount.
     */
    public com.google.protobuf.ByteString
        getAmountBytes() {
      java.lang.Object ref = amount_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        amount_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *消耗燃料（次）
     * </pre>
     *
     * <code>string amount = 6;</code>
     * @param value The amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmount(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      amount_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *消耗燃料（次）
     * </pre>
     *
     * <code>string amount = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmount() {
      amount_ = getDefaultInstance().getAmount();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *消耗燃料（次）
     * </pre>
     *
     * <code>string amount = 6;</code>
     * @param value The bytes for amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmountBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      amount_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object timesLimit_ = "";
    /**
     * <pre>
     *参与次数
     * </pre>
     *
     * <code>string timesLimit = 7;</code>
     * @return The timesLimit.
     */
    public java.lang.String getTimesLimit() {
      java.lang.Object ref = timesLimit_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        timesLimit_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *参与次数
     * </pre>
     *
     * <code>string timesLimit = 7;</code>
     * @return The bytes for timesLimit.
     */
    public com.google.protobuf.ByteString
        getTimesLimitBytes() {
      java.lang.Object ref = timesLimit_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        timesLimit_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *参与次数
     * </pre>
     *
     * <code>string timesLimit = 7;</code>
     * @param value The timesLimit to set.
     * @return This builder for chaining.
     */
    public Builder setTimesLimit(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      timesLimit_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *参与次数
     * </pre>
     *
     * <code>string timesLimit = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearTimesLimit() {
      timesLimit_ = getDefaultInstance().getTimesLimit();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *参与次数
     * </pre>
     *
     * <code>string timesLimit = 7;</code>
     * @param value The bytes for timesLimit to set.
     * @return This builder for chaining.
     */
    public Builder setTimesLimitBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      timesLimit_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object rewardLimit_ = "";
    /**
     * <pre>
     *奖励上限
     * </pre>
     *
     * <code>string rewardLimit = 8;</code>
     * @return The rewardLimit.
     */
    public java.lang.String getRewardLimit() {
      java.lang.Object ref = rewardLimit_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        rewardLimit_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *奖励上限
     * </pre>
     *
     * <code>string rewardLimit = 8;</code>
     * @return The bytes for rewardLimit.
     */
    public com.google.protobuf.ByteString
        getRewardLimitBytes() {
      java.lang.Object ref = rewardLimit_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rewardLimit_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *奖励上限
     * </pre>
     *
     * <code>string rewardLimit = 8;</code>
     * @param value The rewardLimit to set.
     * @return This builder for chaining.
     */
    public Builder setRewardLimit(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      rewardLimit_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖励上限
     * </pre>
     *
     * <code>string rewardLimit = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewardLimit() {
      rewardLimit_ = getDefaultInstance().getRewardLimit();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖励上限
     * </pre>
     *
     * <code>string rewardLimit = 8;</code>
     * @param value The bytes for rewardLimit to set.
     * @return This builder for chaining.
     */
    public Builder setRewardLimitBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      rewardLimit_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.award.Lottery)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.award.Lottery)
  private static final com.kikitrade.activity.facade.award.Lottery DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.award.Lottery();
  }

  public static com.kikitrade.activity.facade.award.Lottery getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Lottery>
      PARSER = new com.google.protobuf.AbstractParser<Lottery>() {
    @java.lang.Override
    public Lottery parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<Lottery> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Lottery> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.award.Lottery getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

