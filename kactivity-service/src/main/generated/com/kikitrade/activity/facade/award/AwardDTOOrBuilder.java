// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface AwardDTOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.AwardDTO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated string id = 1;</code>
   * @return A list containing the id.
   */
  java.util.List<java.lang.String>
      getIdList();
  /**
   * <code>repeated string id = 1;</code>
   * @return The count of id.
   */
  int getIdCount();
  /**
   * <code>repeated string id = 1;</code>
   * @param index The index of the element to return.
   * @return The id at the given index.
   */
  java.lang.String getId(int index);
  /**
   * <code>repeated string id = 1;</code>
   * @param index The index of the value to return.
   * @return The bytes of the id at the given index.
   */
  com.google.protobuf.ByteString
      getIdBytes(int index);
}
