// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: TaskFacade.proto

package com.kikitrade.activity.facade.task;

public interface CommonResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.task.CommonResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>.com.kikitrade.activity.facade.task.TaskVO data = 3;</code>
   * @return Whether the data field is set.
   */
  boolean hasData();
  /**
   * <code>.com.kikitrade.activity.facade.task.TaskVO data = 3;</code>
   * @return The data.
   */
  com.kikitrade.activity.facade.task.TaskVO getData();
  /**
   * <code>.com.kikitrade.activity.facade.task.TaskVO data = 3;</code>
   */
  com.kikitrade.activity.facade.task.TaskVOOrBuilder getDataOrBuilder();
}
