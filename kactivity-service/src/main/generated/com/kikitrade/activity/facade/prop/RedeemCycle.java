// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PropFacade.proto

package com.kikitrade.activity.facade.prop;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.prop.RedeemCycle}
 */
public enum RedeemCycle
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>Total = 0;</code>
   */
  Total(0),
  /**
   * <code>Daily = 1;</code>
   */
  Daily(1),
  /**
   * <code>Weekly = 2;</code>
   */
  Weekly(2),
  /**
   * <code>Monthly = 3;</code>
   */
  Monthly(3),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>Total = 0;</code>
   */
  public static final int Total_VALUE = 0;
  /**
   * <code>Daily = 1;</code>
   */
  public static final int Daily_VALUE = 1;
  /**
   * <code>Weekly = 2;</code>
   */
  public static final int Weekly_VALUE = 2;
  /**
   * <code>Monthly = 3;</code>
   */
  public static final int Monthly_VALUE = 3;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static RedeemCycle valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static RedeemCycle forNumber(int value) {
    switch (value) {
      case 0: return Total;
      case 1: return Daily;
      case 2: return Weekly;
      case 3: return Monthly;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<RedeemCycle>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      RedeemCycle> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<RedeemCycle>() {
          public RedeemCycle findValueByNumber(int number) {
            return RedeemCycle.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.prop.PropFacadeOuterClass.getDescriptor().getEnumTypes().get(0);
  }

  private static final RedeemCycle[] VALUES = values();

  public static RedeemCycle valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private RedeemCycle(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.prop.RedeemCycle)
}

