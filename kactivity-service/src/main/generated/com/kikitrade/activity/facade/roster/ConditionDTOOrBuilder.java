// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityRosterRule.proto

package com.kikitrade.activity.facade.roster;

public interface ConditionDTOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.roster.ConditionDTO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string code = 2;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <code>string code = 2;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <code>.com.kikitrade.activity.facade.roster.Status status = 3;</code>
   * @return The enum numeric value on the wire for status.
   */
  int getStatusValue();
  /**
   * <code>.com.kikitrade.activity.facade.roster.Status status = 3;</code>
   * @return The status.
   */
  com.kikitrade.activity.facade.roster.Status getStatus();

  /**
   * <code>string remark = 4;</code>
   * @return The remark.
   */
  java.lang.String getRemark();
  /**
   * <code>string remark = 4;</code>
   * @return The bytes for remark.
   */
  com.google.protobuf.ByteString
      getRemarkBytes();

  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
   */
  java.util.List<com.kikitrade.activity.facade.roster.Condition> 
      getConditionsList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
   */
  com.kikitrade.activity.facade.roster.Condition getConditions(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
   */
  int getConditionsCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.roster.ConditionOrBuilder> 
      getConditionsOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
   */
  com.kikitrade.activity.facade.roster.ConditionOrBuilder getConditionsOrBuilder(
      int index);
}
