package com.kikitrade.activity.facade.prop;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.58.0)",
    comments = "Source: PropFacade.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class PropFacadeGrpc {

  private PropFacadeGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.kikitrade.activity.facade.prop.PropFacade";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.prop.UpsertPropRequest,
      com.kikitrade.activity.facade.prop.UpsertPropReply> getUpsertPropMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "upsertProp",
      requestType = com.kikitrade.activity.facade.prop.UpsertPropRequest.class,
      responseType = com.kikitrade.activity.facade.prop.UpsertPropReply.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.prop.UpsertPropRequest,
      com.kikitrade.activity.facade.prop.UpsertPropReply> getUpsertPropMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.prop.UpsertPropRequest, com.kikitrade.activity.facade.prop.UpsertPropReply> getUpsertPropMethod;
    if ((getUpsertPropMethod = PropFacadeGrpc.getUpsertPropMethod) == null) {
      synchronized (PropFacadeGrpc.class) {
        if ((getUpsertPropMethod = PropFacadeGrpc.getUpsertPropMethod) == null) {
          PropFacadeGrpc.getUpsertPropMethod = getUpsertPropMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.prop.UpsertPropRequest, com.kikitrade.activity.facade.prop.UpsertPropReply>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "upsertProp"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.prop.UpsertPropRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.prop.UpsertPropReply.getDefaultInstance()))
              .setSchemaDescriptor(new PropFacadeMethodDescriptorSupplier("upsertProp"))
              .build();
        }
      }
    }
    return getUpsertPropMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.prop.PropsQueryRequest,
      com.kikitrade.activity.facade.prop.PropsQueryReply> getQueryPropsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "queryProps",
      requestType = com.kikitrade.activity.facade.prop.PropsQueryRequest.class,
      responseType = com.kikitrade.activity.facade.prop.PropsQueryReply.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.prop.PropsQueryRequest,
      com.kikitrade.activity.facade.prop.PropsQueryReply> getQueryPropsMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.prop.PropsQueryRequest, com.kikitrade.activity.facade.prop.PropsQueryReply> getQueryPropsMethod;
    if ((getQueryPropsMethod = PropFacadeGrpc.getQueryPropsMethod) == null) {
      synchronized (PropFacadeGrpc.class) {
        if ((getQueryPropsMethod = PropFacadeGrpc.getQueryPropsMethod) == null) {
          PropFacadeGrpc.getQueryPropsMethod = getQueryPropsMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.prop.PropsQueryRequest, com.kikitrade.activity.facade.prop.PropsQueryReply>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "queryProps"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.prop.PropsQueryRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.prop.PropsQueryReply.getDefaultInstance()))
              .setSchemaDescriptor(new PropFacadeMethodDescriptorSupplier("queryProps"))
              .build();
        }
      }
    }
    return getQueryPropsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.prop.GetPropRequest,
      com.kikitrade.activity.facade.prop.PropGetReply> getGetPropMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "getProp",
      requestType = com.kikitrade.activity.facade.prop.GetPropRequest.class,
      responseType = com.kikitrade.activity.facade.prop.PropGetReply.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.prop.GetPropRequest,
      com.kikitrade.activity.facade.prop.PropGetReply> getGetPropMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.prop.GetPropRequest, com.kikitrade.activity.facade.prop.PropGetReply> getGetPropMethod;
    if ((getGetPropMethod = PropFacadeGrpc.getGetPropMethod) == null) {
      synchronized (PropFacadeGrpc.class) {
        if ((getGetPropMethod = PropFacadeGrpc.getGetPropMethod) == null) {
          PropFacadeGrpc.getGetPropMethod = getGetPropMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.prop.GetPropRequest, com.kikitrade.activity.facade.prop.PropGetReply>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "getProp"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.prop.GetPropRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.prop.PropGetReply.getDefaultInstance()))
              .setSchemaDescriptor(new PropFacadeMethodDescriptorSupplier("getProp"))
              .build();
        }
      }
    }
    return getGetPropMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static PropFacadeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<PropFacadeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<PropFacadeStub>() {
        @java.lang.Override
        public PropFacadeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new PropFacadeStub(channel, callOptions);
        }
      };
    return PropFacadeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static PropFacadeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<PropFacadeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<PropFacadeBlockingStub>() {
        @java.lang.Override
        public PropFacadeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new PropFacadeBlockingStub(channel, callOptions);
        }
      };
    return PropFacadeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static PropFacadeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<PropFacadeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<PropFacadeFutureStub>() {
        @java.lang.Override
        public PropFacadeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new PropFacadeFutureStub(channel, callOptions);
        }
      };
    return PropFacadeFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     * <pre>
     **
     *新增/保存 道具
     * </pre>
     */
    default void upsertProp(com.kikitrade.activity.facade.prop.UpsertPropRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.prop.UpsertPropReply> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUpsertPropMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *道具列表
     * </pre>
     */
    default void queryProps(com.kikitrade.activity.facade.prop.PropsQueryRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.prop.PropsQueryReply> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryPropsMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *根据id获取道具详情
     * </pre>
     */
    default void getProp(com.kikitrade.activity.facade.prop.GetPropRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.prop.PropGetReply> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetPropMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service PropFacade.
   */
  public static abstract class PropFacadeImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return PropFacadeGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service PropFacade.
   */
  public static final class PropFacadeStub
      extends io.grpc.stub.AbstractAsyncStub<PropFacadeStub> {
    private PropFacadeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected PropFacadeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new PropFacadeStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *新增/保存 道具
     * </pre>
     */
    public void upsertProp(com.kikitrade.activity.facade.prop.UpsertPropRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.prop.UpsertPropReply> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUpsertPropMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *道具列表
     * </pre>
     */
    public void queryProps(com.kikitrade.activity.facade.prop.PropsQueryRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.prop.PropsQueryReply> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryPropsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *根据id获取道具详情
     * </pre>
     */
    public void getProp(com.kikitrade.activity.facade.prop.GetPropRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.prop.PropGetReply> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetPropMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service PropFacade.
   */
  public static final class PropFacadeBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<PropFacadeBlockingStub> {
    private PropFacadeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected PropFacadeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new PropFacadeBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *新增/保存 道具
     * </pre>
     */
    public com.kikitrade.activity.facade.prop.UpsertPropReply upsertProp(com.kikitrade.activity.facade.prop.UpsertPropRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUpsertPropMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *道具列表
     * </pre>
     */
    public com.kikitrade.activity.facade.prop.PropsQueryReply queryProps(com.kikitrade.activity.facade.prop.PropsQueryRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryPropsMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *根据id获取道具详情
     * </pre>
     */
    public com.kikitrade.activity.facade.prop.PropGetReply getProp(com.kikitrade.activity.facade.prop.GetPropRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetPropMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service PropFacade.
   */
  public static final class PropFacadeFutureStub
      extends io.grpc.stub.AbstractFutureStub<PropFacadeFutureStub> {
    private PropFacadeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected PropFacadeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new PropFacadeFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *新增/保存 道具
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.prop.UpsertPropReply> upsertProp(
        com.kikitrade.activity.facade.prop.UpsertPropRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUpsertPropMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *道具列表
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.prop.PropsQueryReply> queryProps(
        com.kikitrade.activity.facade.prop.PropsQueryRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryPropsMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *根据id获取道具详情
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.prop.PropGetReply> getProp(
        com.kikitrade.activity.facade.prop.GetPropRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetPropMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_UPSERT_PROP = 0;
  private static final int METHODID_QUERY_PROPS = 1;
  private static final int METHODID_GET_PROP = 2;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_UPSERT_PROP:
          serviceImpl.upsertProp((com.kikitrade.activity.facade.prop.UpsertPropRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.prop.UpsertPropReply>) responseObserver);
          break;
        case METHODID_QUERY_PROPS:
          serviceImpl.queryProps((com.kikitrade.activity.facade.prop.PropsQueryRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.prop.PropsQueryReply>) responseObserver);
          break;
        case METHODID_GET_PROP:
          serviceImpl.getProp((com.kikitrade.activity.facade.prop.GetPropRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.prop.PropGetReply>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getUpsertPropMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.prop.UpsertPropRequest,
              com.kikitrade.activity.facade.prop.UpsertPropReply>(
                service, METHODID_UPSERT_PROP)))
        .addMethod(
          getQueryPropsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.prop.PropsQueryRequest,
              com.kikitrade.activity.facade.prop.PropsQueryReply>(
                service, METHODID_QUERY_PROPS)))
        .addMethod(
          getGetPropMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.prop.GetPropRequest,
              com.kikitrade.activity.facade.prop.PropGetReply>(
                service, METHODID_GET_PROP)))
        .build();
  }

  private static abstract class PropFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    PropFacadeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.kikitrade.activity.facade.prop.PropFacadeOuterClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("PropFacade");
    }
  }

  private static final class PropFacadeFileDescriptorSupplier
      extends PropFacadeBaseDescriptorSupplier {
    PropFacadeFileDescriptorSupplier() {}
  }

  private static final class PropFacadeMethodDescriptorSupplier
      extends PropFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    PropFacadeMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (PropFacadeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new PropFacadeFileDescriptorSupplier())
              .addMethod(getUpsertPropMethod())
              .addMethod(getQueryPropsMethod())
              .addMethod(getGetPropMethod())
              .build();
        }
      }
    }
    return result;
  }
}
