// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PropFacade.proto

package com.kikitrade.activity.facade.prop;

public interface UpsertPropRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.prop.UpsertPropRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int32 id = 1;</code>
   * @return The id.
   */
  int getId();

  /**
   * <code>.com.kikitrade.activity.facade.prop.PropGateType gateType = 2;</code>
   * @return The enum numeric value on the wire for gateType.
   */
  int getGateTypeValue();
  /**
   * <code>.com.kikitrade.activity.facade.prop.PropGateType gateType = 2;</code>
   * @return The gateType.
   */
  com.kikitrade.activity.facade.prop.PropGateType getGateType();

  /**
   * <pre>
   *如果是会员类型，这里传明星会员等级列表,多个数据，隔开
   * </pre>
   *
   * <code>string gateValue = 3;</code>
   * @return The gateValue.
   */
  java.lang.String getGateValue();
  /**
   * <pre>
   *如果是会员类型，这里传明星会员等级列表,多个数据，隔开
   * </pre>
   *
   * <code>string gateValue = 3;</code>
   * @return The bytes for gateValue.
   */
  com.google.protobuf.ByteString
      getGateValueBytes();

  /**
   * <pre>
   *道具名称
   * </pre>
   *
   * <code>string name = 4;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   *道具名称
   * </pre>
   *
   * <code>string name = 4;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * 道具简介
   * </pre>
   *
   * <code>string remark = 5;</code>
   * @return The remark.
   */
  java.lang.String getRemark();
  /**
   * <pre>
   * 道具简介
   * </pre>
   *
   * <code>string remark = 5;</code>
   * @return The bytes for remark.
   */
  com.google.protobuf.ByteString
      getRemarkBytes();

  /**
   * <pre>
   * 状态
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.prop.Status status = 6;</code>
   * @return The enum numeric value on the wire for status.
   */
  int getStatusValue();
  /**
   * <pre>
   * 状态
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.prop.Status status = 6;</code>
   * @return The status.
   */
  com.kikitrade.activity.facade.prop.Status getStatus();

  /**
   * <pre>
   *图片列表
   * </pre>
   *
   * <code>repeated string images = 7;</code>
   * @return A list containing the images.
   */
  java.util.List<java.lang.String>
      getImagesList();
  /**
   * <pre>
   *图片列表
   * </pre>
   *
   * <code>repeated string images = 7;</code>
   * @return The count of images.
   */
  int getImagesCount();
  /**
   * <pre>
   *图片列表
   * </pre>
   *
   * <code>repeated string images = 7;</code>
   * @param index The index of the element to return.
   * @return The images at the given index.
   */
  java.lang.String getImages(int index);
  /**
   * <pre>
   *图片列表
   * </pre>
   *
   * <code>repeated string images = 7;</code>
   * @param index The index of the value to return.
   * @return The bytes of the images at the given index.
   */
  com.google.protobuf.ByteString
      getImagesBytes(int index);

  /**
   * <pre>
   *超链接
   * </pre>
   *
   * <code>string hyperlink = 8;</code>
   * @return The hyperlink.
   */
  java.lang.String getHyperlink();
  /**
   * <pre>
   *超链接
   * </pre>
   *
   * <code>string hyperlink = 8;</code>
   * @return The bytes for hyperlink.
   */
  com.google.protobuf.ByteString
      getHyperlinkBytes();

  /**
   * <pre>
   * 道具实际内容类型,比如：道具是券，那么这里就是券id
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.prop.PropContentType contentType = 9;</code>
   * @return The enum numeric value on the wire for contentType.
   */
  int getContentTypeValue();
  /**
   * <pre>
   * 道具实际内容类型,比如：道具是券，那么这里就是券id
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.prop.PropContentType contentType = 9;</code>
   * @return The contentType.
   */
  com.kikitrade.activity.facade.prop.PropContentType getContentType();

  /**
   * <pre>
   * 道具实际内容id
   * </pre>
   *
   * <code>string contentId = 10;</code>
   * @return The contentId.
   */
  java.lang.String getContentId();
  /**
   * <pre>
   * 道具实际内容id
   * </pre>
   *
   * <code>string contentId = 10;</code>
   * @return The bytes for contentId.
   */
  com.google.protobuf.ByteString
      getContentIdBytes();

  /**
   * <pre>
   * 所需积分数量
   * </pre>
   *
   * <code>string point = 11;</code>
   * @return The point.
   */
  java.lang.String getPoint();
  /**
   * <pre>
   * 所需积分数量
   * </pre>
   *
   * <code>string point = 11;</code>
   * @return The bytes for point.
   */
  com.google.protobuf.ByteString
      getPointBytes();

  /**
   * <pre>
   *发行数量
   * </pre>
   *
   * <code>int64 publishAmount = 12;</code>
   * @return The publishAmount.
   */
  long getPublishAmount();

  /**
   * <pre>
   * 兑换次数限制. 0表示不限制
   * </pre>
   *
   * <code>int32 redeemTimes = 13;</code>
   * @return The redeemTimes.
   */
  int getRedeemTimes();

  /**
   * <code>.com.kikitrade.activity.facade.prop.RedeemCycle redeemCycle = 14;</code>
   * @return The enum numeric value on the wire for redeemCycle.
   */
  int getRedeemCycleValue();
  /**
   * <code>.com.kikitrade.activity.facade.prop.RedeemCycle redeemCycle = 14;</code>
   * @return The redeemCycle.
   */
  com.kikitrade.activity.facade.prop.RedeemCycle getRedeemCycle();

  /**
   * <pre>
   *有效天数
   * </pre>
   *
   * <code>int32 periodDays = 15;</code>
   * @return The periodDays.
   */
  int getPeriodDays();

  /**
   * <pre>
   * 排序
   * </pre>
   *
   * <code>int32 sort = 16;</code>
   * @return The sort.
   */
  int getSort();
}
