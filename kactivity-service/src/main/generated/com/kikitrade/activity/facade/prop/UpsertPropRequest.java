// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PropFacade.proto

package com.kikitrade.activity.facade.prop;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.prop.UpsertPropRequest}
 */
public final class UpsertPropRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.prop.UpsertPropRequest)
    UpsertPropRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use UpsertPropRequest.newBuilder() to construct.
  private UpsertPropRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private UpsertPropRequest() {
    gateType_ = 0;
    gateValue_ = "";
    name_ = "";
    remark_ = "";
    status_ = 0;
    images_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    hyperlink_ = "";
    contentType_ = 0;
    contentId_ = "";
    point_ = "";
    redeemCycle_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new UpsertPropRequest();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.prop.PropFacadeOuterClass.internal_static_com_kikitrade_activity_facade_prop_UpsertPropRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.prop.PropFacadeOuterClass.internal_static_com_kikitrade_activity_facade_prop_UpsertPropRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.prop.UpsertPropRequest.class, com.kikitrade.activity.facade.prop.UpsertPropRequest.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  private int id_ = 0;
  /**
   * <code>int32 id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public int getId() {
    return id_;
  }

  public static final int GATETYPE_FIELD_NUMBER = 2;
  private int gateType_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.prop.PropGateType gateType = 2;</code>
   * @return The enum numeric value on the wire for gateType.
   */
  @java.lang.Override public int getGateTypeValue() {
    return gateType_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.prop.PropGateType gateType = 2;</code>
   * @return The gateType.
   */
  @java.lang.Override public com.kikitrade.activity.facade.prop.PropGateType getGateType() {
    com.kikitrade.activity.facade.prop.PropGateType result = com.kikitrade.activity.facade.prop.PropGateType.forNumber(gateType_);
    return result == null ? com.kikitrade.activity.facade.prop.PropGateType.UNRECOGNIZED : result;
  }

  public static final int GATEVALUE_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object gateValue_ = "";
  /**
   * <pre>
   *如果是会员类型，这里传明星会员等级列表,多个数据，隔开
   * </pre>
   *
   * <code>string gateValue = 3;</code>
   * @return The gateValue.
   */
  @java.lang.Override
  public java.lang.String getGateValue() {
    java.lang.Object ref = gateValue_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      gateValue_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *如果是会员类型，这里传明星会员等级列表,多个数据，隔开
   * </pre>
   *
   * <code>string gateValue = 3;</code>
   * @return The bytes for gateValue.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getGateValueBytes() {
    java.lang.Object ref = gateValue_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      gateValue_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object name_ = "";
  /**
   * <pre>
   *道具名称
   * </pre>
   *
   * <code>string name = 4;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *道具名称
   * </pre>
   *
   * <code>string name = 4;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REMARK_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object remark_ = "";
  /**
   * <pre>
   * 道具简介
   * </pre>
   *
   * <code>string remark = 5;</code>
   * @return The remark.
   */
  @java.lang.Override
  public java.lang.String getRemark() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      remark_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 道具简介
   * </pre>
   *
   * <code>string remark = 5;</code>
   * @return The bytes for remark.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRemarkBytes() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      remark_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATUS_FIELD_NUMBER = 6;
  private int status_ = 0;
  /**
   * <pre>
   * 状态
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.prop.Status status = 6;</code>
   * @return The enum numeric value on the wire for status.
   */
  @java.lang.Override public int getStatusValue() {
    return status_;
  }
  /**
   * <pre>
   * 状态
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.prop.Status status = 6;</code>
   * @return The status.
   */
  @java.lang.Override public com.kikitrade.activity.facade.prop.Status getStatus() {
    com.kikitrade.activity.facade.prop.Status result = com.kikitrade.activity.facade.prop.Status.forNumber(status_);
    return result == null ? com.kikitrade.activity.facade.prop.Status.UNRECOGNIZED : result;
  }

  public static final int IMAGES_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private com.google.protobuf.LazyStringArrayList images_ =
      com.google.protobuf.LazyStringArrayList.emptyList();
  /**
   * <pre>
   *图片列表
   * </pre>
   *
   * <code>repeated string images = 7;</code>
   * @return A list containing the images.
   */
  public com.google.protobuf.ProtocolStringList
      getImagesList() {
    return images_;
  }
  /**
   * <pre>
   *图片列表
   * </pre>
   *
   * <code>repeated string images = 7;</code>
   * @return The count of images.
   */
  public int getImagesCount() {
    return images_.size();
  }
  /**
   * <pre>
   *图片列表
   * </pre>
   *
   * <code>repeated string images = 7;</code>
   * @param index The index of the element to return.
   * @return The images at the given index.
   */
  public java.lang.String getImages(int index) {
    return images_.get(index);
  }
  /**
   * <pre>
   *图片列表
   * </pre>
   *
   * <code>repeated string images = 7;</code>
   * @param index The index of the value to return.
   * @return The bytes of the images at the given index.
   */
  public com.google.protobuf.ByteString
      getImagesBytes(int index) {
    return images_.getByteString(index);
  }

  public static final int HYPERLINK_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object hyperlink_ = "";
  /**
   * <pre>
   *超链接
   * </pre>
   *
   * <code>string hyperlink = 8;</code>
   * @return The hyperlink.
   */
  @java.lang.Override
  public java.lang.String getHyperlink() {
    java.lang.Object ref = hyperlink_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      hyperlink_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *超链接
   * </pre>
   *
   * <code>string hyperlink = 8;</code>
   * @return The bytes for hyperlink.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getHyperlinkBytes() {
    java.lang.Object ref = hyperlink_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      hyperlink_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CONTENTTYPE_FIELD_NUMBER = 9;
  private int contentType_ = 0;
  /**
   * <pre>
   * 道具实际内容类型,比如：道具是券，那么这里就是券id
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.prop.PropContentType contentType = 9;</code>
   * @return The enum numeric value on the wire for contentType.
   */
  @java.lang.Override public int getContentTypeValue() {
    return contentType_;
  }
  /**
   * <pre>
   * 道具实际内容类型,比如：道具是券，那么这里就是券id
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.prop.PropContentType contentType = 9;</code>
   * @return The contentType.
   */
  @java.lang.Override public com.kikitrade.activity.facade.prop.PropContentType getContentType() {
    com.kikitrade.activity.facade.prop.PropContentType result = com.kikitrade.activity.facade.prop.PropContentType.forNumber(contentType_);
    return result == null ? com.kikitrade.activity.facade.prop.PropContentType.UNRECOGNIZED : result;
  }

  public static final int CONTENTID_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object contentId_ = "";
  /**
   * <pre>
   * 道具实际内容id
   * </pre>
   *
   * <code>string contentId = 10;</code>
   * @return The contentId.
   */
  @java.lang.Override
  public java.lang.String getContentId() {
    java.lang.Object ref = contentId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      contentId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 道具实际内容id
   * </pre>
   *
   * <code>string contentId = 10;</code>
   * @return The bytes for contentId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getContentIdBytes() {
    java.lang.Object ref = contentId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      contentId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int POINT_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private volatile java.lang.Object point_ = "";
  /**
   * <pre>
   * 所需积分数量
   * </pre>
   *
   * <code>string point = 11;</code>
   * @return The point.
   */
  @java.lang.Override
  public java.lang.String getPoint() {
    java.lang.Object ref = point_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      point_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 所需积分数量
   * </pre>
   *
   * <code>string point = 11;</code>
   * @return The bytes for point.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPointBytes() {
    java.lang.Object ref = point_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      point_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PUBLISHAMOUNT_FIELD_NUMBER = 12;
  private long publishAmount_ = 0L;
  /**
   * <pre>
   *发行数量
   * </pre>
   *
   * <code>int64 publishAmount = 12;</code>
   * @return The publishAmount.
   */
  @java.lang.Override
  public long getPublishAmount() {
    return publishAmount_;
  }

  public static final int REDEEMTIMES_FIELD_NUMBER = 13;
  private int redeemTimes_ = 0;
  /**
   * <pre>
   * 兑换次数限制. 0表示不限制
   * </pre>
   *
   * <code>int32 redeemTimes = 13;</code>
   * @return The redeemTimes.
   */
  @java.lang.Override
  public int getRedeemTimes() {
    return redeemTimes_;
  }

  public static final int REDEEMCYCLE_FIELD_NUMBER = 14;
  private int redeemCycle_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.prop.RedeemCycle redeemCycle = 14;</code>
   * @return The enum numeric value on the wire for redeemCycle.
   */
  @java.lang.Override public int getRedeemCycleValue() {
    return redeemCycle_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.prop.RedeemCycle redeemCycle = 14;</code>
   * @return The redeemCycle.
   */
  @java.lang.Override public com.kikitrade.activity.facade.prop.RedeemCycle getRedeemCycle() {
    com.kikitrade.activity.facade.prop.RedeemCycle result = com.kikitrade.activity.facade.prop.RedeemCycle.forNumber(redeemCycle_);
    return result == null ? com.kikitrade.activity.facade.prop.RedeemCycle.UNRECOGNIZED : result;
  }

  public static final int PERIODDAYS_FIELD_NUMBER = 15;
  private int periodDays_ = 0;
  /**
   * <pre>
   *有效天数
   * </pre>
   *
   * <code>int32 periodDays = 15;</code>
   * @return The periodDays.
   */
  @java.lang.Override
  public int getPeriodDays() {
    return periodDays_;
  }

  public static final int SORT_FIELD_NUMBER = 16;
  private int sort_ = 0;
  /**
   * <pre>
   * 排序
   * </pre>
   *
   * <code>int32 sort = 16;</code>
   * @return The sort.
   */
  @java.lang.Override
  public int getSort() {
    return sort_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (id_ != 0) {
      output.writeInt32(1, id_);
    }
    if (gateType_ != com.kikitrade.activity.facade.prop.PropGateType.None.getNumber()) {
      output.writeEnum(2, gateType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(gateValue_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, gateValue_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, name_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(remark_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, remark_);
    }
    if (status_ != com.kikitrade.activity.facade.prop.Status.Active.getNumber()) {
      output.writeEnum(6, status_);
    }
    for (int i = 0; i < images_.size(); i++) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, images_.getRaw(i));
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(hyperlink_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, hyperlink_);
    }
    if (contentType_ != com.kikitrade.activity.facade.prop.PropContentType.Coupon.getNumber()) {
      output.writeEnum(9, contentType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(contentId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, contentId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(point_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, point_);
    }
    if (publishAmount_ != 0L) {
      output.writeInt64(12, publishAmount_);
    }
    if (redeemTimes_ != 0) {
      output.writeInt32(13, redeemTimes_);
    }
    if (redeemCycle_ != com.kikitrade.activity.facade.prop.RedeemCycle.Total.getNumber()) {
      output.writeEnum(14, redeemCycle_);
    }
    if (periodDays_ != 0) {
      output.writeInt32(15, periodDays_);
    }
    if (sort_ != 0) {
      output.writeInt32(16, sort_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (id_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, id_);
    }
    if (gateType_ != com.kikitrade.activity.facade.prop.PropGateType.None.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, gateType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(gateValue_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, gateValue_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, name_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(remark_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, remark_);
    }
    if (status_ != com.kikitrade.activity.facade.prop.Status.Active.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(6, status_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < images_.size(); i++) {
        dataSize += computeStringSizeNoTag(images_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getImagesList().size();
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(hyperlink_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, hyperlink_);
    }
    if (contentType_ != com.kikitrade.activity.facade.prop.PropContentType.Coupon.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(9, contentType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(contentId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, contentId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(point_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, point_);
    }
    if (publishAmount_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(12, publishAmount_);
    }
    if (redeemTimes_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(13, redeemTimes_);
    }
    if (redeemCycle_ != com.kikitrade.activity.facade.prop.RedeemCycle.Total.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(14, redeemCycle_);
    }
    if (periodDays_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(15, periodDays_);
    }
    if (sort_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(16, sort_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.prop.UpsertPropRequest)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.prop.UpsertPropRequest other = (com.kikitrade.activity.facade.prop.UpsertPropRequest) obj;

    if (getId()
        != other.getId()) return false;
    if (gateType_ != other.gateType_) return false;
    if (!getGateValue()
        .equals(other.getGateValue())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (!getRemark()
        .equals(other.getRemark())) return false;
    if (status_ != other.status_) return false;
    if (!getImagesList()
        .equals(other.getImagesList())) return false;
    if (!getHyperlink()
        .equals(other.getHyperlink())) return false;
    if (contentType_ != other.contentType_) return false;
    if (!getContentId()
        .equals(other.getContentId())) return false;
    if (!getPoint()
        .equals(other.getPoint())) return false;
    if (getPublishAmount()
        != other.getPublishAmount()) return false;
    if (getRedeemTimes()
        != other.getRedeemTimes()) return false;
    if (redeemCycle_ != other.redeemCycle_) return false;
    if (getPeriodDays()
        != other.getPeriodDays()) return false;
    if (getSort()
        != other.getSort()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId();
    hash = (37 * hash) + GATETYPE_FIELD_NUMBER;
    hash = (53 * hash) + gateType_;
    hash = (37 * hash) + GATEVALUE_FIELD_NUMBER;
    hash = (53 * hash) + getGateValue().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + REMARK_FIELD_NUMBER;
    hash = (53 * hash) + getRemark().hashCode();
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + status_;
    if (getImagesCount() > 0) {
      hash = (37 * hash) + IMAGES_FIELD_NUMBER;
      hash = (53 * hash) + getImagesList().hashCode();
    }
    hash = (37 * hash) + HYPERLINK_FIELD_NUMBER;
    hash = (53 * hash) + getHyperlink().hashCode();
    hash = (37 * hash) + CONTENTTYPE_FIELD_NUMBER;
    hash = (53 * hash) + contentType_;
    hash = (37 * hash) + CONTENTID_FIELD_NUMBER;
    hash = (53 * hash) + getContentId().hashCode();
    hash = (37 * hash) + POINT_FIELD_NUMBER;
    hash = (53 * hash) + getPoint().hashCode();
    hash = (37 * hash) + PUBLISHAMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getPublishAmount());
    hash = (37 * hash) + REDEEMTIMES_FIELD_NUMBER;
    hash = (53 * hash) + getRedeemTimes();
    hash = (37 * hash) + REDEEMCYCLE_FIELD_NUMBER;
    hash = (53 * hash) + redeemCycle_;
    hash = (37 * hash) + PERIODDAYS_FIELD_NUMBER;
    hash = (53 * hash) + getPeriodDays();
    hash = (37 * hash) + SORT_FIELD_NUMBER;
    hash = (53 * hash) + getSort();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.prop.UpsertPropRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.prop.UpsertPropRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.prop.UpsertPropRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.prop.UpsertPropRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.prop.UpsertPropRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.prop.UpsertPropRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.prop.UpsertPropRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.prop.UpsertPropRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.prop.UpsertPropRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.prop.UpsertPropRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.prop.UpsertPropRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.prop.UpsertPropRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.prop.UpsertPropRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.prop.UpsertPropRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.prop.UpsertPropRequest)
      com.kikitrade.activity.facade.prop.UpsertPropRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.prop.PropFacadeOuterClass.internal_static_com_kikitrade_activity_facade_prop_UpsertPropRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.prop.PropFacadeOuterClass.internal_static_com_kikitrade_activity_facade_prop_UpsertPropRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.prop.UpsertPropRequest.class, com.kikitrade.activity.facade.prop.UpsertPropRequest.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.prop.UpsertPropRequest.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = 0;
      gateType_ = 0;
      gateValue_ = "";
      name_ = "";
      remark_ = "";
      status_ = 0;
      images_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      hyperlink_ = "";
      contentType_ = 0;
      contentId_ = "";
      point_ = "";
      publishAmount_ = 0L;
      redeemTimes_ = 0;
      redeemCycle_ = 0;
      periodDays_ = 0;
      sort_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.prop.PropFacadeOuterClass.internal_static_com_kikitrade_activity_facade_prop_UpsertPropRequest_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.prop.UpsertPropRequest getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.prop.UpsertPropRequest.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.prop.UpsertPropRequest build() {
      com.kikitrade.activity.facade.prop.UpsertPropRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.prop.UpsertPropRequest buildPartial() {
      com.kikitrade.activity.facade.prop.UpsertPropRequest result = new com.kikitrade.activity.facade.prop.UpsertPropRequest(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.kikitrade.activity.facade.prop.UpsertPropRequest result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.gateType_ = gateType_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.gateValue_ = gateValue_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.name_ = name_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.remark_ = remark_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.status_ = status_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        images_.makeImmutable();
        result.images_ = images_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.hyperlink_ = hyperlink_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.contentType_ = contentType_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.contentId_ = contentId_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.point_ = point_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.publishAmount_ = publishAmount_;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.redeemTimes_ = redeemTimes_;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.redeemCycle_ = redeemCycle_;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.periodDays_ = periodDays_;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.sort_ = sort_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.prop.UpsertPropRequest) {
        return mergeFrom((com.kikitrade.activity.facade.prop.UpsertPropRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.prop.UpsertPropRequest other) {
      if (other == com.kikitrade.activity.facade.prop.UpsertPropRequest.getDefaultInstance()) return this;
      if (other.getId() != 0) {
        setId(other.getId());
      }
      if (other.gateType_ != 0) {
        setGateTypeValue(other.getGateTypeValue());
      }
      if (!other.getGateValue().isEmpty()) {
        gateValue_ = other.gateValue_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (!other.getRemark().isEmpty()) {
        remark_ = other.remark_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (other.status_ != 0) {
        setStatusValue(other.getStatusValue());
      }
      if (!other.images_.isEmpty()) {
        if (images_.isEmpty()) {
          images_ = other.images_;
          bitField0_ |= 0x00000040;
        } else {
          ensureImagesIsMutable();
          images_.addAll(other.images_);
        }
        onChanged();
      }
      if (!other.getHyperlink().isEmpty()) {
        hyperlink_ = other.hyperlink_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (other.contentType_ != 0) {
        setContentTypeValue(other.getContentTypeValue());
      }
      if (!other.getContentId().isEmpty()) {
        contentId_ = other.contentId_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (!other.getPoint().isEmpty()) {
        point_ = other.point_;
        bitField0_ |= 0x00000400;
        onChanged();
      }
      if (other.getPublishAmount() != 0L) {
        setPublishAmount(other.getPublishAmount());
      }
      if (other.getRedeemTimes() != 0) {
        setRedeemTimes(other.getRedeemTimes());
      }
      if (other.redeemCycle_ != 0) {
        setRedeemCycleValue(other.getRedeemCycleValue());
      }
      if (other.getPeriodDays() != 0) {
        setPeriodDays(other.getPeriodDays());
      }
      if (other.getSort() != 0) {
        setSort(other.getSort());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              id_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              gateType_ = input.readEnum();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              gateValue_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              name_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              remark_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 48: {
              status_ = input.readEnum();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 58: {
              java.lang.String s = input.readStringRequireUtf8();
              ensureImagesIsMutable();
              images_.add(s);
              break;
            } // case 58
            case 66: {
              hyperlink_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 72: {
              contentType_ = input.readEnum();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 82: {
              contentId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 90: {
              point_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 96: {
              publishAmount_ = input.readInt64();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            case 104: {
              redeemTimes_ = input.readInt32();
              bitField0_ |= 0x00001000;
              break;
            } // case 104
            case 112: {
              redeemCycle_ = input.readEnum();
              bitField0_ |= 0x00002000;
              break;
            } // case 112
            case 120: {
              periodDays_ = input.readInt32();
              bitField0_ |= 0x00004000;
              break;
            } // case 120
            case 128: {
              sort_ = input.readInt32();
              bitField0_ |= 0x00008000;
              break;
            } // case 128
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int id_ ;
    /**
     * <code>int32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }
    /**
     * <code>int32 id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(int value) {

      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>int32 id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      id_ = 0;
      onChanged();
      return this;
    }

    private int gateType_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.prop.PropGateType gateType = 2;</code>
     * @return The enum numeric value on the wire for gateType.
     */
    @java.lang.Override public int getGateTypeValue() {
      return gateType_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.PropGateType gateType = 2;</code>
     * @param value The enum numeric value on the wire for gateType to set.
     * @return This builder for chaining.
     */
    public Builder setGateTypeValue(int value) {
      gateType_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.PropGateType gateType = 2;</code>
     * @return The gateType.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.prop.PropGateType getGateType() {
      com.kikitrade.activity.facade.prop.PropGateType result = com.kikitrade.activity.facade.prop.PropGateType.forNumber(gateType_);
      return result == null ? com.kikitrade.activity.facade.prop.PropGateType.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.PropGateType gateType = 2;</code>
     * @param value The gateType to set.
     * @return This builder for chaining.
     */
    public Builder setGateType(com.kikitrade.activity.facade.prop.PropGateType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000002;
      gateType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.PropGateType gateType = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearGateType() {
      bitField0_ = (bitField0_ & ~0x00000002);
      gateType_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object gateValue_ = "";
    /**
     * <pre>
     *如果是会员类型，这里传明星会员等级列表,多个数据，隔开
     * </pre>
     *
     * <code>string gateValue = 3;</code>
     * @return The gateValue.
     */
    public java.lang.String getGateValue() {
      java.lang.Object ref = gateValue_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        gateValue_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *如果是会员类型，这里传明星会员等级列表,多个数据，隔开
     * </pre>
     *
     * <code>string gateValue = 3;</code>
     * @return The bytes for gateValue.
     */
    public com.google.protobuf.ByteString
        getGateValueBytes() {
      java.lang.Object ref = gateValue_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gateValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *如果是会员类型，这里传明星会员等级列表,多个数据，隔开
     * </pre>
     *
     * <code>string gateValue = 3;</code>
     * @param value The gateValue to set.
     * @return This builder for chaining.
     */
    public Builder setGateValue(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      gateValue_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *如果是会员类型，这里传明星会员等级列表,多个数据，隔开
     * </pre>
     *
     * <code>string gateValue = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearGateValue() {
      gateValue_ = getDefaultInstance().getGateValue();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *如果是会员类型，这里传明星会员等级列表,多个数据，隔开
     * </pre>
     *
     * <code>string gateValue = 3;</code>
     * @param value The bytes for gateValue to set.
     * @return This builder for chaining.
     */
    public Builder setGateValueBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      gateValue_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <pre>
     *道具名称
     * </pre>
     *
     * <code>string name = 4;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *道具名称
     * </pre>
     *
     * <code>string name = 4;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *道具名称
     * </pre>
     *
     * <code>string name = 4;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *道具名称
     * </pre>
     *
     * <code>string name = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      name_ = getDefaultInstance().getName();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *道具名称
     * </pre>
     *
     * <code>string name = 4;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      name_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object remark_ = "";
    /**
     * <pre>
     * 道具简介
     * </pre>
     *
     * <code>string remark = 5;</code>
     * @return The remark.
     */
    public java.lang.String getRemark() {
      java.lang.Object ref = remark_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        remark_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 道具简介
     * </pre>
     *
     * <code>string remark = 5;</code>
     * @return The bytes for remark.
     */
    public com.google.protobuf.ByteString
        getRemarkBytes() {
      java.lang.Object ref = remark_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        remark_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 道具简介
     * </pre>
     *
     * <code>string remark = 5;</code>
     * @param value The remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemark(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      remark_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 道具简介
     * </pre>
     *
     * <code>string remark = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearRemark() {
      remark_ = getDefaultInstance().getRemark();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 道具简介
     * </pre>
     *
     * <code>string remark = 5;</code>
     * @param value The bytes for remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemarkBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      remark_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private int status_ = 0;
    /**
     * <pre>
     * 状态
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.prop.Status status = 6;</code>
     * @return The enum numeric value on the wire for status.
     */
    @java.lang.Override public int getStatusValue() {
      return status_;
    }
    /**
     * <pre>
     * 状态
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.prop.Status status = 6;</code>
     * @param value The enum numeric value on the wire for status to set.
     * @return This builder for chaining.
     */
    public Builder setStatusValue(int value) {
      status_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 状态
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.prop.Status status = 6;</code>
     * @return The status.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.prop.Status getStatus() {
      com.kikitrade.activity.facade.prop.Status result = com.kikitrade.activity.facade.prop.Status.forNumber(status_);
      return result == null ? com.kikitrade.activity.facade.prop.Status.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 状态
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.prop.Status status = 6;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(com.kikitrade.activity.facade.prop.Status value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000020;
      status_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 状态
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.prop.Status status = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      bitField0_ = (bitField0_ & ~0x00000020);
      status_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringArrayList images_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    private void ensureImagesIsMutable() {
      if (!images_.isModifiable()) {
        images_ = new com.google.protobuf.LazyStringArrayList(images_);
      }
      bitField0_ |= 0x00000040;
    }
    /**
     * <pre>
     *图片列表
     * </pre>
     *
     * <code>repeated string images = 7;</code>
     * @return A list containing the images.
     */
    public com.google.protobuf.ProtocolStringList
        getImagesList() {
      images_.makeImmutable();
      return images_;
    }
    /**
     * <pre>
     *图片列表
     * </pre>
     *
     * <code>repeated string images = 7;</code>
     * @return The count of images.
     */
    public int getImagesCount() {
      return images_.size();
    }
    /**
     * <pre>
     *图片列表
     * </pre>
     *
     * <code>repeated string images = 7;</code>
     * @param index The index of the element to return.
     * @return The images at the given index.
     */
    public java.lang.String getImages(int index) {
      return images_.get(index);
    }
    /**
     * <pre>
     *图片列表
     * </pre>
     *
     * <code>repeated string images = 7;</code>
     * @param index The index of the value to return.
     * @return The bytes of the images at the given index.
     */
    public com.google.protobuf.ByteString
        getImagesBytes(int index) {
      return images_.getByteString(index);
    }
    /**
     * <pre>
     *图片列表
     * </pre>
     *
     * <code>repeated string images = 7;</code>
     * @param index The index to set the value at.
     * @param value The images to set.
     * @return This builder for chaining.
     */
    public Builder setImages(
        int index, java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureImagesIsMutable();
      images_.set(index, value);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *图片列表
     * </pre>
     *
     * <code>repeated string images = 7;</code>
     * @param value The images to add.
     * @return This builder for chaining.
     */
    public Builder addImages(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureImagesIsMutable();
      images_.add(value);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *图片列表
     * </pre>
     *
     * <code>repeated string images = 7;</code>
     * @param values The images to add.
     * @return This builder for chaining.
     */
    public Builder addAllImages(
        java.lang.Iterable<java.lang.String> values) {
      ensureImagesIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, images_);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *图片列表
     * </pre>
     *
     * <code>repeated string images = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearImages() {
      images_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
      bitField0_ = (bitField0_ & ~0x00000040);;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *图片列表
     * </pre>
     *
     * <code>repeated string images = 7;</code>
     * @param value The bytes of the images to add.
     * @return This builder for chaining.
     */
    public Builder addImagesBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ensureImagesIsMutable();
      images_.add(value);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object hyperlink_ = "";
    /**
     * <pre>
     *超链接
     * </pre>
     *
     * <code>string hyperlink = 8;</code>
     * @return The hyperlink.
     */
    public java.lang.String getHyperlink() {
      java.lang.Object ref = hyperlink_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        hyperlink_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *超链接
     * </pre>
     *
     * <code>string hyperlink = 8;</code>
     * @return The bytes for hyperlink.
     */
    public com.google.protobuf.ByteString
        getHyperlinkBytes() {
      java.lang.Object ref = hyperlink_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hyperlink_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *超链接
     * </pre>
     *
     * <code>string hyperlink = 8;</code>
     * @param value The hyperlink to set.
     * @return This builder for chaining.
     */
    public Builder setHyperlink(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      hyperlink_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *超链接
     * </pre>
     *
     * <code>string hyperlink = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearHyperlink() {
      hyperlink_ = getDefaultInstance().getHyperlink();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *超链接
     * </pre>
     *
     * <code>string hyperlink = 8;</code>
     * @param value The bytes for hyperlink to set.
     * @return This builder for chaining.
     */
    public Builder setHyperlinkBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      hyperlink_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private int contentType_ = 0;
    /**
     * <pre>
     * 道具实际内容类型,比如：道具是券，那么这里就是券id
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.prop.PropContentType contentType = 9;</code>
     * @return The enum numeric value on the wire for contentType.
     */
    @java.lang.Override public int getContentTypeValue() {
      return contentType_;
    }
    /**
     * <pre>
     * 道具实际内容类型,比如：道具是券，那么这里就是券id
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.prop.PropContentType contentType = 9;</code>
     * @param value The enum numeric value on the wire for contentType to set.
     * @return This builder for chaining.
     */
    public Builder setContentTypeValue(int value) {
      contentType_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 道具实际内容类型,比如：道具是券，那么这里就是券id
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.prop.PropContentType contentType = 9;</code>
     * @return The contentType.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.prop.PropContentType getContentType() {
      com.kikitrade.activity.facade.prop.PropContentType result = com.kikitrade.activity.facade.prop.PropContentType.forNumber(contentType_);
      return result == null ? com.kikitrade.activity.facade.prop.PropContentType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 道具实际内容类型,比如：道具是券，那么这里就是券id
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.prop.PropContentType contentType = 9;</code>
     * @param value The contentType to set.
     * @return This builder for chaining.
     */
    public Builder setContentType(com.kikitrade.activity.facade.prop.PropContentType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000100;
      contentType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 道具实际内容类型,比如：道具是券，那么这里就是券id
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.prop.PropContentType contentType = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearContentType() {
      bitField0_ = (bitField0_ & ~0x00000100);
      contentType_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object contentId_ = "";
    /**
     * <pre>
     * 道具实际内容id
     * </pre>
     *
     * <code>string contentId = 10;</code>
     * @return The contentId.
     */
    public java.lang.String getContentId() {
      java.lang.Object ref = contentId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        contentId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 道具实际内容id
     * </pre>
     *
     * <code>string contentId = 10;</code>
     * @return The bytes for contentId.
     */
    public com.google.protobuf.ByteString
        getContentIdBytes() {
      java.lang.Object ref = contentId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        contentId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 道具实际内容id
     * </pre>
     *
     * <code>string contentId = 10;</code>
     * @param value The contentId to set.
     * @return This builder for chaining.
     */
    public Builder setContentId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      contentId_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 道具实际内容id
     * </pre>
     *
     * <code>string contentId = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearContentId() {
      contentId_ = getDefaultInstance().getContentId();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 道具实际内容id
     * </pre>
     *
     * <code>string contentId = 10;</code>
     * @param value The bytes for contentId to set.
     * @return This builder for chaining.
     */
    public Builder setContentIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      contentId_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private java.lang.Object point_ = "";
    /**
     * <pre>
     * 所需积分数量
     * </pre>
     *
     * <code>string point = 11;</code>
     * @return The point.
     */
    public java.lang.String getPoint() {
      java.lang.Object ref = point_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        point_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 所需积分数量
     * </pre>
     *
     * <code>string point = 11;</code>
     * @return The bytes for point.
     */
    public com.google.protobuf.ByteString
        getPointBytes() {
      java.lang.Object ref = point_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        point_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 所需积分数量
     * </pre>
     *
     * <code>string point = 11;</code>
     * @param value The point to set.
     * @return This builder for chaining.
     */
    public Builder setPoint(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      point_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 所需积分数量
     * </pre>
     *
     * <code>string point = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearPoint() {
      point_ = getDefaultInstance().getPoint();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 所需积分数量
     * </pre>
     *
     * <code>string point = 11;</code>
     * @param value The bytes for point to set.
     * @return This builder for chaining.
     */
    public Builder setPointBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      point_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }

    private long publishAmount_ ;
    /**
     * <pre>
     *发行数量
     * </pre>
     *
     * <code>int64 publishAmount = 12;</code>
     * @return The publishAmount.
     */
    @java.lang.Override
    public long getPublishAmount() {
      return publishAmount_;
    }
    /**
     * <pre>
     *发行数量
     * </pre>
     *
     * <code>int64 publishAmount = 12;</code>
     * @param value The publishAmount to set.
     * @return This builder for chaining.
     */
    public Builder setPublishAmount(long value) {

      publishAmount_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *发行数量
     * </pre>
     *
     * <code>int64 publishAmount = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearPublishAmount() {
      bitField0_ = (bitField0_ & ~0x00000800);
      publishAmount_ = 0L;
      onChanged();
      return this;
    }

    private int redeemTimes_ ;
    /**
     * <pre>
     * 兑换次数限制. 0表示不限制
     * </pre>
     *
     * <code>int32 redeemTimes = 13;</code>
     * @return The redeemTimes.
     */
    @java.lang.Override
    public int getRedeemTimes() {
      return redeemTimes_;
    }
    /**
     * <pre>
     * 兑换次数限制. 0表示不限制
     * </pre>
     *
     * <code>int32 redeemTimes = 13;</code>
     * @param value The redeemTimes to set.
     * @return This builder for chaining.
     */
    public Builder setRedeemTimes(int value) {

      redeemTimes_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 兑换次数限制. 0表示不限制
     * </pre>
     *
     * <code>int32 redeemTimes = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearRedeemTimes() {
      bitField0_ = (bitField0_ & ~0x00001000);
      redeemTimes_ = 0;
      onChanged();
      return this;
    }

    private int redeemCycle_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.prop.RedeemCycle redeemCycle = 14;</code>
     * @return The enum numeric value on the wire for redeemCycle.
     */
    @java.lang.Override public int getRedeemCycleValue() {
      return redeemCycle_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.RedeemCycle redeemCycle = 14;</code>
     * @param value The enum numeric value on the wire for redeemCycle to set.
     * @return This builder for chaining.
     */
    public Builder setRedeemCycleValue(int value) {
      redeemCycle_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.RedeemCycle redeemCycle = 14;</code>
     * @return The redeemCycle.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.prop.RedeemCycle getRedeemCycle() {
      com.kikitrade.activity.facade.prop.RedeemCycle result = com.kikitrade.activity.facade.prop.RedeemCycle.forNumber(redeemCycle_);
      return result == null ? com.kikitrade.activity.facade.prop.RedeemCycle.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.RedeemCycle redeemCycle = 14;</code>
     * @param value The redeemCycle to set.
     * @return This builder for chaining.
     */
    public Builder setRedeemCycle(com.kikitrade.activity.facade.prop.RedeemCycle value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00002000;
      redeemCycle_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.RedeemCycle redeemCycle = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearRedeemCycle() {
      bitField0_ = (bitField0_ & ~0x00002000);
      redeemCycle_ = 0;
      onChanged();
      return this;
    }

    private int periodDays_ ;
    /**
     * <pre>
     *有效天数
     * </pre>
     *
     * <code>int32 periodDays = 15;</code>
     * @return The periodDays.
     */
    @java.lang.Override
    public int getPeriodDays() {
      return periodDays_;
    }
    /**
     * <pre>
     *有效天数
     * </pre>
     *
     * <code>int32 periodDays = 15;</code>
     * @param value The periodDays to set.
     * @return This builder for chaining.
     */
    public Builder setPeriodDays(int value) {

      periodDays_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *有效天数
     * </pre>
     *
     * <code>int32 periodDays = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearPeriodDays() {
      bitField0_ = (bitField0_ & ~0x00004000);
      periodDays_ = 0;
      onChanged();
      return this;
    }

    private int sort_ ;
    /**
     * <pre>
     * 排序
     * </pre>
     *
     * <code>int32 sort = 16;</code>
     * @return The sort.
     */
    @java.lang.Override
    public int getSort() {
      return sort_;
    }
    /**
     * <pre>
     * 排序
     * </pre>
     *
     * <code>int32 sort = 16;</code>
     * @param value The sort to set.
     * @return This builder for chaining.
     */
    public Builder setSort(int value) {

      sort_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 排序
     * </pre>
     *
     * <code>int32 sort = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearSort() {
      bitField0_ = (bitField0_ & ~0x00008000);
      sort_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.prop.UpsertPropRequest)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.prop.UpsertPropRequest)
  private static final com.kikitrade.activity.facade.prop.UpsertPropRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.prop.UpsertPropRequest();
  }

  public static com.kikitrade.activity.facade.prop.UpsertPropRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UpsertPropRequest>
      PARSER = new com.google.protobuf.AbstractParser<UpsertPropRequest>() {
    @java.lang.Override
    public UpsertPropRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UpsertPropRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UpsertPropRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.prop.UpsertPropRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

