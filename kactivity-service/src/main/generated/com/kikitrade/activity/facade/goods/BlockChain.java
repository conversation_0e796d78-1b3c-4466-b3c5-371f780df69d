// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Goods.proto

package com.kikitrade.activity.facade.goods;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.goods.BlockChain}
 */
public enum BlockChain
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>evm = 0;</code>
   */
  evm(0),
  /**
   * <code>sui = 1;</code>
   */
  sui(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>evm = 0;</code>
   */
  public static final int evm_VALUE = 0;
  /**
   * <code>sui = 1;</code>
   */
  public static final int sui_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static BlockChain valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static BlockChain forNumber(int value) {
    switch (value) {
      case 0: return evm;
      case 1: return sui;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<BlockChain>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      BlockChain> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<BlockChain>() {
          public BlockChain findValueByNumber(int number) {
            return BlockChain.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.goods.GoodFacadeOutClass.getDescriptor().getEnumTypes().get(2);
  }

  private static final BlockChain[] VALUES = values();

  public static BlockChain valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private BlockChain(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.goods.BlockChain)
}

