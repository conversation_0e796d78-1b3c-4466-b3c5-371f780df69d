// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PropFacade.proto

package com.kikitrade.activity.facade.prop;

public interface PropsQueryRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.prop.PropsQueryRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string saasId = 1;</code>
   * @return The saasId.
   */
  java.lang.String getSaasId();
  /**
   * <code>string saasId = 1;</code>
   * @return The bytes for saasId.
   */
  com.google.protobuf.ByteString
      getSaasIdBytes();

  /**
   * <code>int32 offset = 2;</code>
   * @return The offset.
   */
  int getOffset();

  /**
   * <code>int32 limit = 3;</code>
   * @return The limit.
   */
  int getLimit();

  /**
   * <pre>
   *道具名称
   * </pre>
   *
   * <code>string name = 4;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   *道具名称
   * </pre>
   *
   * <code>string name = 4;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * 状态
   * </pre>
   *
   * <code>string status = 5;</code>
   * @return The status.
   */
  java.lang.String getStatus();
  /**
   * <pre>
   * 状态
   * </pre>
   *
   * <code>string status = 5;</code>
   * @return The bytes for status.
   */
  com.google.protobuf.ByteString
      getStatusBytes();
}
