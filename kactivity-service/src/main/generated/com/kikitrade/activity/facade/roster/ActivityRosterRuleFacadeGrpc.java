package com.kikitrade.activity.facade.roster;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.58.0)",
    comments = "Source: ActivityRosterRule.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class ActivityRosterRuleFacadeGrpc {

  private ActivityRosterRuleFacadeGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.kikitrade.activity.facade.roster.ActivityRosterRuleFacade";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.roster.EmptyRequest,
      com.kikitrade.activity.facade.roster.ConditionParameterResponse> getGetConditionNamesMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "getConditionNames",
      requestType = com.kikitrade.activity.facade.roster.EmptyRequest.class,
      responseType = com.kikitrade.activity.facade.roster.ConditionParameterResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.roster.EmptyRequest,
      com.kikitrade.activity.facade.roster.ConditionParameterResponse> getGetConditionNamesMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.roster.EmptyRequest, com.kikitrade.activity.facade.roster.ConditionParameterResponse> getGetConditionNamesMethod;
    if ((getGetConditionNamesMethod = ActivityRosterRuleFacadeGrpc.getGetConditionNamesMethod) == null) {
      synchronized (ActivityRosterRuleFacadeGrpc.class) {
        if ((getGetConditionNamesMethod = ActivityRosterRuleFacadeGrpc.getGetConditionNamesMethod) == null) {
          ActivityRosterRuleFacadeGrpc.getGetConditionNamesMethod = getGetConditionNamesMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.roster.EmptyRequest, com.kikitrade.activity.facade.roster.ConditionParameterResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "getConditionNames"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.roster.EmptyRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.roster.ConditionParameterResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityRosterRuleFacadeMethodDescriptorSupplier("getConditionNames"))
              .build();
        }
      }
    }
    return getGetConditionNamesMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.roster.ConditionDTO,
      com.kikitrade.activity.facade.roster.CommonResponse> getSaveConditionMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "saveCondition",
      requestType = com.kikitrade.activity.facade.roster.ConditionDTO.class,
      responseType = com.kikitrade.activity.facade.roster.CommonResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.roster.ConditionDTO,
      com.kikitrade.activity.facade.roster.CommonResponse> getSaveConditionMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.roster.ConditionDTO, com.kikitrade.activity.facade.roster.CommonResponse> getSaveConditionMethod;
    if ((getSaveConditionMethod = ActivityRosterRuleFacadeGrpc.getSaveConditionMethod) == null) {
      synchronized (ActivityRosterRuleFacadeGrpc.class) {
        if ((getSaveConditionMethod = ActivityRosterRuleFacadeGrpc.getSaveConditionMethod) == null) {
          ActivityRosterRuleFacadeGrpc.getSaveConditionMethod = getSaveConditionMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.roster.ConditionDTO, com.kikitrade.activity.facade.roster.CommonResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "saveCondition"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.roster.ConditionDTO.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.roster.CommonResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityRosterRuleFacadeMethodDescriptorSupplier("saveCondition"))
              .build();
        }
      }
    }
    return getSaveConditionMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static ActivityRosterRuleFacadeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ActivityRosterRuleFacadeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ActivityRosterRuleFacadeStub>() {
        @java.lang.Override
        public ActivityRosterRuleFacadeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ActivityRosterRuleFacadeStub(channel, callOptions);
        }
      };
    return ActivityRosterRuleFacadeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static ActivityRosterRuleFacadeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ActivityRosterRuleFacadeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ActivityRosterRuleFacadeBlockingStub>() {
        @java.lang.Override
        public ActivityRosterRuleFacadeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ActivityRosterRuleFacadeBlockingStub(channel, callOptions);
        }
      };
    return ActivityRosterRuleFacadeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static ActivityRosterRuleFacadeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ActivityRosterRuleFacadeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ActivityRosterRuleFacadeFutureStub>() {
        @java.lang.Override
        public ActivityRosterRuleFacadeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ActivityRosterRuleFacadeFutureStub(channel, callOptions);
        }
      };
    return ActivityRosterRuleFacadeFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     * <pre>
     **
     *查询conditionNameList
     * </pre>
     */
    default void getConditionNames(com.kikitrade.activity.facade.roster.EmptyRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.roster.ConditionParameterResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetConditionNamesMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *保存查询规则
     * </pre>
     */
    default void saveCondition(com.kikitrade.activity.facade.roster.ConditionDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.roster.CommonResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSaveConditionMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service ActivityRosterRuleFacade.
   */
  public static abstract class ActivityRosterRuleFacadeImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return ActivityRosterRuleFacadeGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service ActivityRosterRuleFacade.
   */
  public static final class ActivityRosterRuleFacadeStub
      extends io.grpc.stub.AbstractAsyncStub<ActivityRosterRuleFacadeStub> {
    private ActivityRosterRuleFacadeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ActivityRosterRuleFacadeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ActivityRosterRuleFacadeStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *查询conditionNameList
     * </pre>
     */
    public void getConditionNames(com.kikitrade.activity.facade.roster.EmptyRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.roster.ConditionParameterResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetConditionNamesMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *保存查询规则
     * </pre>
     */
    public void saveCondition(com.kikitrade.activity.facade.roster.ConditionDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.roster.CommonResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSaveConditionMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service ActivityRosterRuleFacade.
   */
  public static final class ActivityRosterRuleFacadeBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<ActivityRosterRuleFacadeBlockingStub> {
    private ActivityRosterRuleFacadeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ActivityRosterRuleFacadeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ActivityRosterRuleFacadeBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *查询conditionNameList
     * </pre>
     */
    public com.kikitrade.activity.facade.roster.ConditionParameterResponse getConditionNames(com.kikitrade.activity.facade.roster.EmptyRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetConditionNamesMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *保存查询规则
     * </pre>
     */
    public com.kikitrade.activity.facade.roster.CommonResponse saveCondition(com.kikitrade.activity.facade.roster.ConditionDTO request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSaveConditionMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service ActivityRosterRuleFacade.
   */
  public static final class ActivityRosterRuleFacadeFutureStub
      extends io.grpc.stub.AbstractFutureStub<ActivityRosterRuleFacadeFutureStub> {
    private ActivityRosterRuleFacadeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ActivityRosterRuleFacadeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ActivityRosterRuleFacadeFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *查询conditionNameList
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.roster.ConditionParameterResponse> getConditionNames(
        com.kikitrade.activity.facade.roster.EmptyRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetConditionNamesMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *保存查询规则
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.roster.CommonResponse> saveCondition(
        com.kikitrade.activity.facade.roster.ConditionDTO request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSaveConditionMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_GET_CONDITION_NAMES = 0;
  private static final int METHODID_SAVE_CONDITION = 1;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_GET_CONDITION_NAMES:
          serviceImpl.getConditionNames((com.kikitrade.activity.facade.roster.EmptyRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.roster.ConditionParameterResponse>) responseObserver);
          break;
        case METHODID_SAVE_CONDITION:
          serviceImpl.saveCondition((com.kikitrade.activity.facade.roster.ConditionDTO) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.roster.CommonResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getGetConditionNamesMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.roster.EmptyRequest,
              com.kikitrade.activity.facade.roster.ConditionParameterResponse>(
                service, METHODID_GET_CONDITION_NAMES)))
        .addMethod(
          getSaveConditionMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.roster.ConditionDTO,
              com.kikitrade.activity.facade.roster.CommonResponse>(
                service, METHODID_SAVE_CONDITION)))
        .build();
  }

  private static abstract class ActivityRosterRuleFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    ActivityRosterRuleFacadeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("ActivityRosterRuleFacade");
    }
  }

  private static final class ActivityRosterRuleFacadeFileDescriptorSupplier
      extends ActivityRosterRuleFacadeBaseDescriptorSupplier {
    ActivityRosterRuleFacadeFileDescriptorSupplier() {}
  }

  private static final class ActivityRosterRuleFacadeMethodDescriptorSupplier
      extends ActivityRosterRuleFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    ActivityRosterRuleFacadeMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (ActivityRosterRuleFacadeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new ActivityRosterRuleFacadeFileDescriptorSupplier())
              .addMethod(getGetConditionNamesMethod())
              .addMethod(getSaveConditionMethod())
              .build();
        }
      }
    }
    return result;
  }
}
