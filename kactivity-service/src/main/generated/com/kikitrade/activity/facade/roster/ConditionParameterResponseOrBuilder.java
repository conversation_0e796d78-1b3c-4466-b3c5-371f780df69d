// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityRosterRule.proto

package com.kikitrade.activity.facade.roster;

public interface ConditionParameterResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.roster.ConditionParameterResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
   */
  java.util.List<com.kikitrade.activity.facade.roster.ConditionParameter> 
      getConditionParameterList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
   */
  com.kikitrade.activity.facade.roster.ConditionParameter getConditionParameter(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
   */
  int getConditionParameterCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.roster.ConditionParameterOrBuilder> 
      getConditionParameterOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionParameter conditionParameter = 1;</code>
   */
  com.kikitrade.activity.facade.roster.ConditionParameterOrBuilder getConditionParameterOrBuilder(
      int index);
}
