/*
* Licensed to the Apache Software Foundation (ASF) under one or more
* contributor license agreements.  See the NOTICE file distributed with
* this work for additional information regarding copyright ownership.
* The ASF licenses this file to You under the Apache License, Version 2.0
* (the "License"); you may not use this file except in compliance with
* the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

    package com.kikitrade.activity.facade.coupon;

import org.apache.dubbo.common.stream.StreamObserver;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.PathResolver;
import org.apache.dubbo.rpc.RpcException;
import org.apache.dubbo.rpc.ServerService;
import org.apache.dubbo.rpc.TriRpcStatus;
import org.apache.dubbo.rpc.model.MethodDescriptor;
import org.apache.dubbo.rpc.model.ServiceDescriptor;
import org.apache.dubbo.rpc.model.StubMethodDescriptor;
import org.apache.dubbo.rpc.model.StubServiceDescriptor;
import org.apache.dubbo.rpc.stub.BiStreamMethodHandler;
import org.apache.dubbo.rpc.stub.ServerStreamMethodHandler;
import org.apache.dubbo.rpc.stub.StubInvocationUtil;
import org.apache.dubbo.rpc.stub.StubInvoker;
import org.apache.dubbo.rpc.stub.StubMethodHandler;
import org.apache.dubbo.rpc.stub.StubSuppliers;
import org.apache.dubbo.rpc.stub.UnaryStubMethodHandler;

import com.google.protobuf.Message;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.concurrent.CompletableFuture;

public final class DubboDiscountCouponFacadeTriple {

    public static final String SERVICE_NAME = DiscountCouponFacade.SERVICE_NAME;

    private static final StubServiceDescriptor serviceDescriptor = new StubServiceDescriptor(SERVICE_NAME,DiscountCouponFacade.class);

    static {
        org.apache.dubbo.rpc.protocol.tri.service.SchemaDescriptorRegistry.addSchemaDescriptor(SERVICE_NAME,DiscountCouponFacadeOuterClass.getDescriptor());
        StubSuppliers.addSupplier(SERVICE_NAME, DubboDiscountCouponFacadeTriple::newStub);
        StubSuppliers.addSupplier(DiscountCouponFacade.JAVA_SERVICE_NAME,  DubboDiscountCouponFacadeTriple::newStub);
        StubSuppliers.addDescriptor(SERVICE_NAME, serviceDescriptor);
        StubSuppliers.addDescriptor(DiscountCouponFacade.JAVA_SERVICE_NAME, serviceDescriptor);
    }

    @SuppressWarnings("all")
    public static DiscountCouponFacade newStub(Invoker<?> invoker) {
        return new DiscountCouponFacadeStub((Invoker<DiscountCouponFacade>)invoker);
    }

    private static final StubMethodDescriptor upsertDiscountCouponMethod = new StubMethodDescriptor("upsertDiscountCoupon",
    com.kikitrade.activity.facade.coupon.DiscountCouponRequest.class, com.kikitrade.activity.facade.coupon.DiscountCouponResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.coupon.DiscountCouponRequest::parseFrom,
    com.kikitrade.activity.facade.coupon.DiscountCouponResponse::parseFrom);

    private static final StubMethodDescriptor upsertDiscountCouponAsyncMethod = new StubMethodDescriptor("upsertDiscountCoupon",
    com.kikitrade.activity.facade.coupon.DiscountCouponRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.coupon.DiscountCouponRequest::parseFrom,
    com.kikitrade.activity.facade.coupon.DiscountCouponResponse::parseFrom);

    private static final StubMethodDescriptor upsertDiscountCouponProxyAsyncMethod = new StubMethodDescriptor("upsertDiscountCouponAsync",
    com.kikitrade.activity.facade.coupon.DiscountCouponRequest.class, com.kikitrade.activity.facade.coupon.DiscountCouponResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.coupon.DiscountCouponRequest::parseFrom,
    com.kikitrade.activity.facade.coupon.DiscountCouponResponse::parseFrom);





    public static class DiscountCouponFacadeStub implements DiscountCouponFacade{
        private final Invoker<DiscountCouponFacade> invoker;

        public DiscountCouponFacadeStub(Invoker<DiscountCouponFacade> invoker) {
            this.invoker = invoker;
        }

        @Override
        public com.kikitrade.activity.facade.coupon.DiscountCouponResponse upsertDiscountCoupon(com.kikitrade.activity.facade.coupon.DiscountCouponRequest request){
            return StubInvocationUtil.unaryCall(invoker, upsertDiscountCouponMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.coupon.DiscountCouponResponse> upsertDiscountCouponAsync(com.kikitrade.activity.facade.coupon.DiscountCouponRequest request){
            return StubInvocationUtil.unaryCall(invoker, upsertDiscountCouponAsyncMethod, request);
        }

        @Override
        public void upsertDiscountCoupon(com.kikitrade.activity.facade.coupon.DiscountCouponRequest request, StreamObserver<com.kikitrade.activity.facade.coupon.DiscountCouponResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, upsertDiscountCouponMethod , request, responseObserver);
        }



    }

    public static abstract class DiscountCouponFacadeImplBase implements DiscountCouponFacade, ServerService<DiscountCouponFacade> {

        private <T, R> BiConsumer<T, StreamObserver<R>> syncToAsync(java.util.function.Function<T, R> syncFun) {
            return new BiConsumer<T, StreamObserver<R>>() {
                @Override
                public void accept(T t, StreamObserver<R> observer) {
                    try {
                        R ret = syncFun.apply(t);
                        observer.onNext(ret);
                        observer.onCompleted();
                    } catch (Throwable e) {
                        observer.onError(e);
                    }
                }
            };
        }

        @Override
        public final Invoker<DiscountCouponFacade> getInvoker(URL url) {
            PathResolver pathResolver = url.getOrDefaultFrameworkModel()
            .getExtensionLoader(PathResolver.class)
            .getDefaultExtension();
            Map<String,StubMethodHandler<?, ?>> handlers = new HashMap<>();

            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/upsertDiscountCoupon" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/upsertDiscountCouponAsync" );

            BiConsumer<com.kikitrade.activity.facade.coupon.DiscountCouponRequest, StreamObserver<com.kikitrade.activity.facade.coupon.DiscountCouponResponse>> upsertDiscountCouponFunc = this::upsertDiscountCoupon;
            handlers.put(upsertDiscountCouponMethod.getMethodName(), new UnaryStubMethodHandler<>(upsertDiscountCouponFunc));
            BiConsumer<com.kikitrade.activity.facade.coupon.DiscountCouponRequest, StreamObserver<com.kikitrade.activity.facade.coupon.DiscountCouponResponse>> upsertDiscountCouponAsyncFunc = syncToAsync(this::upsertDiscountCoupon);
            handlers.put(upsertDiscountCouponProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(upsertDiscountCouponAsyncFunc));




            return new StubInvoker<>(this, url, DiscountCouponFacade.class, handlers);
        }


        @Override
        public com.kikitrade.activity.facade.coupon.DiscountCouponResponse upsertDiscountCoupon(com.kikitrade.activity.facade.coupon.DiscountCouponRequest request){
            throw unimplementedMethodException(upsertDiscountCouponMethod);
        }





        @Override
        public final ServiceDescriptor getServiceDescriptor() {
            return serviceDescriptor;
        }
        private RpcException unimplementedMethodException(StubMethodDescriptor methodDescriptor) {
            return TriRpcStatus.UNIMPLEMENTED.withDescription(String.format("Method %s is unimplemented",
                "/" + serviceDescriptor.getInterfaceName() + "/" + methodDescriptor.getMethodName())).asException();
        }
    }

}
