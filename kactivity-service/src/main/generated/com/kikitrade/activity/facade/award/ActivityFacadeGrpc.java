package com.kikitrade.activity.facade.award;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.58.0)",
    comments = "Source: ActivityFacade.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class ActivityFacadeGrpc {

  private ActivityFacadeGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.kikitrade.activity.facade.award.ActivityFacade";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ActivityDTO,
      com.kikitrade.activity.facade.award.ActivityResponse> getSaveOrUpdateActivityMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "saveOrUpdateActivity",
      requestType = com.kikitrade.activity.facade.award.ActivityDTO.class,
      responseType = com.kikitrade.activity.facade.award.ActivityResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ActivityDTO,
      com.kikitrade.activity.facade.award.ActivityResponse> getSaveOrUpdateActivityMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ActivityDTO, com.kikitrade.activity.facade.award.ActivityResponse> getSaveOrUpdateActivityMethod;
    if ((getSaveOrUpdateActivityMethod = ActivityFacadeGrpc.getSaveOrUpdateActivityMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getSaveOrUpdateActivityMethod = ActivityFacadeGrpc.getSaveOrUpdateActivityMethod) == null) {
          ActivityFacadeGrpc.getSaveOrUpdateActivityMethod = getSaveOrUpdateActivityMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.ActivityDTO, com.kikitrade.activity.facade.award.ActivityResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "saveOrUpdateActivity"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ActivityDTO.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ActivityResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("saveOrUpdateActivity"))
              .build();
        }
      }
    }
    return getSaveOrUpdateActivityMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ActivityBatchDTO,
      com.kikitrade.activity.facade.award.ActivityBatchResponse> getSaveOrUpdateBatchMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "saveOrUpdateBatch",
      requestType = com.kikitrade.activity.facade.award.ActivityBatchDTO.class,
      responseType = com.kikitrade.activity.facade.award.ActivityBatchResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ActivityBatchDTO,
      com.kikitrade.activity.facade.award.ActivityBatchResponse> getSaveOrUpdateBatchMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ActivityBatchDTO, com.kikitrade.activity.facade.award.ActivityBatchResponse> getSaveOrUpdateBatchMethod;
    if ((getSaveOrUpdateBatchMethod = ActivityFacadeGrpc.getSaveOrUpdateBatchMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getSaveOrUpdateBatchMethod = ActivityFacadeGrpc.getSaveOrUpdateBatchMethod) == null) {
          ActivityFacadeGrpc.getSaveOrUpdateBatchMethod = getSaveOrUpdateBatchMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.ActivityBatchDTO, com.kikitrade.activity.facade.award.ActivityBatchResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "saveOrUpdateBatch"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ActivityBatchDTO.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ActivityBatchResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("saveOrUpdateBatch"))
              .build();
        }
      }
    }
    return getSaveOrUpdateBatchMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ActivityBatchDTO,
      com.kikitrade.activity.facade.award.ActivityBatchResponse> getDeleteBatchMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "deleteBatch",
      requestType = com.kikitrade.activity.facade.award.ActivityBatchDTO.class,
      responseType = com.kikitrade.activity.facade.award.ActivityBatchResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ActivityBatchDTO,
      com.kikitrade.activity.facade.award.ActivityBatchResponse> getDeleteBatchMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ActivityBatchDTO, com.kikitrade.activity.facade.award.ActivityBatchResponse> getDeleteBatchMethod;
    if ((getDeleteBatchMethod = ActivityFacadeGrpc.getDeleteBatchMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getDeleteBatchMethod = ActivityFacadeGrpc.getDeleteBatchMethod) == null) {
          ActivityFacadeGrpc.getDeleteBatchMethod = getDeleteBatchMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.ActivityBatchDTO, com.kikitrade.activity.facade.award.ActivityBatchResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "deleteBatch"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ActivityBatchDTO.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ActivityBatchResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("deleteBatch"))
              .build();
        }
      }
    }
    return getDeleteBatchMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ActivityBatchRequest,
      com.kikitrade.activity.facade.award.ActivityBatchListResponse> getQueryBatchForListMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "queryBatchForList",
      requestType = com.kikitrade.activity.facade.award.ActivityBatchRequest.class,
      responseType = com.kikitrade.activity.facade.award.ActivityBatchListResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ActivityBatchRequest,
      com.kikitrade.activity.facade.award.ActivityBatchListResponse> getQueryBatchForListMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ActivityBatchRequest, com.kikitrade.activity.facade.award.ActivityBatchListResponse> getQueryBatchForListMethod;
    if ((getQueryBatchForListMethod = ActivityFacadeGrpc.getQueryBatchForListMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getQueryBatchForListMethod = ActivityFacadeGrpc.getQueryBatchForListMethod) == null) {
          ActivityFacadeGrpc.getQueryBatchForListMethod = getQueryBatchForListMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.ActivityBatchRequest, com.kikitrade.activity.facade.award.ActivityBatchListResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "queryBatchForList"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ActivityBatchRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ActivityBatchListResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("queryBatchForList"))
              .build();
        }
      }
    }
    return getQueryBatchForListMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ActivityBatchDetailRequest,
      com.kikitrade.activity.facade.award.ActivityBatch> getQueryDetailMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "queryDetail",
      requestType = com.kikitrade.activity.facade.award.ActivityBatchDetailRequest.class,
      responseType = com.kikitrade.activity.facade.award.ActivityBatch.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ActivityBatchDetailRequest,
      com.kikitrade.activity.facade.award.ActivityBatch> getQueryDetailMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ActivityBatchDetailRequest, com.kikitrade.activity.facade.award.ActivityBatch> getQueryDetailMethod;
    if ((getQueryDetailMethod = ActivityFacadeGrpc.getQueryDetailMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getQueryDetailMethod = ActivityFacadeGrpc.getQueryDetailMethod) == null) {
          ActivityFacadeGrpc.getQueryDetailMethod = getQueryDetailMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.ActivityBatchDetailRequest, com.kikitrade.activity.facade.award.ActivityBatch>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "queryDetail"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ActivityBatchDetailRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ActivityBatch.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("queryDetail"))
              .build();
        }
      }
    }
    return getQueryDetailMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.AuditRequest,
      com.kikitrade.activity.facade.award.AuditResponse> getAuditMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "audit",
      requestType = com.kikitrade.activity.facade.award.AuditRequest.class,
      responseType = com.kikitrade.activity.facade.award.AuditResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.AuditRequest,
      com.kikitrade.activity.facade.award.AuditResponse> getAuditMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.AuditRequest, com.kikitrade.activity.facade.award.AuditResponse> getAuditMethod;
    if ((getAuditMethod = ActivityFacadeGrpc.getAuditMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getAuditMethod = ActivityFacadeGrpc.getAuditMethod) == null) {
          ActivityFacadeGrpc.getAuditMethod = getAuditMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.AuditRequest, com.kikitrade.activity.facade.award.AuditResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "audit"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.AuditRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.AuditResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("audit"))
              .build();
        }
      }
    }
    return getAuditMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.AwardRequest,
      com.kikitrade.activity.facade.award.AwardListResponse> getQueryRewardListMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "queryRewardList",
      requestType = com.kikitrade.activity.facade.award.AwardRequest.class,
      responseType = com.kikitrade.activity.facade.award.AwardListResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.AwardRequest,
      com.kikitrade.activity.facade.award.AwardListResponse> getQueryRewardListMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.AwardRequest, com.kikitrade.activity.facade.award.AwardListResponse> getQueryRewardListMethod;
    if ((getQueryRewardListMethod = ActivityFacadeGrpc.getQueryRewardListMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getQueryRewardListMethod = ActivityFacadeGrpc.getQueryRewardListMethod) == null) {
          ActivityFacadeGrpc.getQueryRewardListMethod = getQueryRewardListMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.AwardRequest, com.kikitrade.activity.facade.award.AwardListResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "queryRewardList"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.AwardRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.AwardListResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("queryRewardList"))
              .build();
        }
      }
    }
    return getQueryRewardListMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.AwardDTO,
      com.kikitrade.activity.facade.award.ModifyAwardResponse> getDeleteRewardMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "deleteReward",
      requestType = com.kikitrade.activity.facade.award.AwardDTO.class,
      responseType = com.kikitrade.activity.facade.award.ModifyAwardResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.AwardDTO,
      com.kikitrade.activity.facade.award.ModifyAwardResponse> getDeleteRewardMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.AwardDTO, com.kikitrade.activity.facade.award.ModifyAwardResponse> getDeleteRewardMethod;
    if ((getDeleteRewardMethod = ActivityFacadeGrpc.getDeleteRewardMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getDeleteRewardMethod = ActivityFacadeGrpc.getDeleteRewardMethod) == null) {
          ActivityFacadeGrpc.getDeleteRewardMethod = getDeleteRewardMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.AwardDTO, com.kikitrade.activity.facade.award.ModifyAwardResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "deleteReward"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.AwardDTO.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ModifyAwardResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("deleteReward"))
              .build();
        }
      }
    }
    return getDeleteRewardMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.UploadRequest,
      com.kikitrade.activity.facade.award.UploadResponse> getUploadFileMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "uploadFile",
      requestType = com.kikitrade.activity.facade.award.UploadRequest.class,
      responseType = com.kikitrade.activity.facade.award.UploadResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.UploadRequest,
      com.kikitrade.activity.facade.award.UploadResponse> getUploadFileMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.UploadRequest, com.kikitrade.activity.facade.award.UploadResponse> getUploadFileMethod;
    if ((getUploadFileMethod = ActivityFacadeGrpc.getUploadFileMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getUploadFileMethod = ActivityFacadeGrpc.getUploadFileMethod) == null) {
          ActivityFacadeGrpc.getUploadFileMethod = getUploadFileMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.UploadRequest, com.kikitrade.activity.facade.award.UploadResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "uploadFile"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.UploadRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.UploadResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("uploadFile"))
              .build();
        }
      }
    }
    return getUploadFileMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ExportDataRequest,
      com.kikitrade.activity.facade.award.ExportDataResponse> getExportDataMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "exportData",
      requestType = com.kikitrade.activity.facade.award.ExportDataRequest.class,
      responseType = com.kikitrade.activity.facade.award.ExportDataResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ExportDataRequest,
      com.kikitrade.activity.facade.award.ExportDataResponse> getExportDataMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ExportDataRequest, com.kikitrade.activity.facade.award.ExportDataResponse> getExportDataMethod;
    if ((getExportDataMethod = ActivityFacadeGrpc.getExportDataMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getExportDataMethod = ActivityFacadeGrpc.getExportDataMethod) == null) {
          ActivityFacadeGrpc.getExportDataMethod = getExportDataMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.ExportDataRequest, com.kikitrade.activity.facade.award.ExportDataResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "exportData"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ExportDataRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ExportDataResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("exportData"))
              .build();
        }
      }
    }
    return getExportDataMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ImportDataRequest,
      com.kikitrade.activity.facade.award.ImportDataResponse> getImportDataMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "importData",
      requestType = com.kikitrade.activity.facade.award.ImportDataRequest.class,
      responseType = com.kikitrade.activity.facade.award.ImportDataResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ImportDataRequest,
      com.kikitrade.activity.facade.award.ImportDataResponse> getImportDataMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ImportDataRequest, com.kikitrade.activity.facade.award.ImportDataResponse> getImportDataMethod;
    if ((getImportDataMethod = ActivityFacadeGrpc.getImportDataMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getImportDataMethod = ActivityFacadeGrpc.getImportDataMethod) == null) {
          ActivityFacadeGrpc.getImportDataMethod = getImportDataMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.ImportDataRequest, com.kikitrade.activity.facade.award.ImportDataResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "importData"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ImportDataRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ImportDataResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("importData"))
              .build();
        }
      }
    }
    return getImportDataMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.LotteryDTO,
      com.kikitrade.activity.facade.award.LotteryResponse> getSaveLotteryMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "saveLottery",
      requestType = com.kikitrade.activity.facade.award.LotteryDTO.class,
      responseType = com.kikitrade.activity.facade.award.LotteryResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.LotteryDTO,
      com.kikitrade.activity.facade.award.LotteryResponse> getSaveLotteryMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.LotteryDTO, com.kikitrade.activity.facade.award.LotteryResponse> getSaveLotteryMethod;
    if ((getSaveLotteryMethod = ActivityFacadeGrpc.getSaveLotteryMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getSaveLotteryMethod = ActivityFacadeGrpc.getSaveLotteryMethod) == null) {
          ActivityFacadeGrpc.getSaveLotteryMethod = getSaveLotteryMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.LotteryDTO, com.kikitrade.activity.facade.award.LotteryResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "saveLottery"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.LotteryDTO.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.LotteryResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("saveLottery"))
              .build();
        }
      }
    }
    return getSaveLotteryMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.LotteryDeleteDTO,
      com.kikitrade.activity.facade.award.LotteryResponse> getDeleteLotteryMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "deleteLottery",
      requestType = com.kikitrade.activity.facade.award.LotteryDeleteDTO.class,
      responseType = com.kikitrade.activity.facade.award.LotteryResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.LotteryDeleteDTO,
      com.kikitrade.activity.facade.award.LotteryResponse> getDeleteLotteryMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.LotteryDeleteDTO, com.kikitrade.activity.facade.award.LotteryResponse> getDeleteLotteryMethod;
    if ((getDeleteLotteryMethod = ActivityFacadeGrpc.getDeleteLotteryMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getDeleteLotteryMethod = ActivityFacadeGrpc.getDeleteLotteryMethod) == null) {
          ActivityFacadeGrpc.getDeleteLotteryMethod = getDeleteLotteryMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.LotteryDeleteDTO, com.kikitrade.activity.facade.award.LotteryResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "deleteLottery"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.LotteryDeleteDTO.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.LotteryResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("deleteLottery"))
              .build();
        }
      }
    }
    return getDeleteLotteryMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.LotteryRequest,
      com.kikitrade.activity.facade.award.LotteryListResponse> getLotteryListMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "lotteryList",
      requestType = com.kikitrade.activity.facade.award.LotteryRequest.class,
      responseType = com.kikitrade.activity.facade.award.LotteryListResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.LotteryRequest,
      com.kikitrade.activity.facade.award.LotteryListResponse> getLotteryListMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.LotteryRequest, com.kikitrade.activity.facade.award.LotteryListResponse> getLotteryListMethod;
    if ((getLotteryListMethod = ActivityFacadeGrpc.getLotteryListMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getLotteryListMethod = ActivityFacadeGrpc.getLotteryListMethod) == null) {
          ActivityFacadeGrpc.getLotteryListMethod = getLotteryListMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.LotteryRequest, com.kikitrade.activity.facade.award.LotteryListResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "lotteryList"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.LotteryRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.LotteryListResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("lotteryList"))
              .build();
        }
      }
    }
    return getLotteryListMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.LotteryDetailRequest,
      com.kikitrade.activity.facade.award.LotteryVO> getLotteryDetailMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "lotteryDetail",
      requestType = com.kikitrade.activity.facade.award.LotteryDetailRequest.class,
      responseType = com.kikitrade.activity.facade.award.LotteryVO.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.LotteryDetailRequest,
      com.kikitrade.activity.facade.award.LotteryVO> getLotteryDetailMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.LotteryDetailRequest, com.kikitrade.activity.facade.award.LotteryVO> getLotteryDetailMethod;
    if ((getLotteryDetailMethod = ActivityFacadeGrpc.getLotteryDetailMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getLotteryDetailMethod = ActivityFacadeGrpc.getLotteryDetailMethod) == null) {
          ActivityFacadeGrpc.getLotteryDetailMethod = getLotteryDetailMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.LotteryDetailRequest, com.kikitrade.activity.facade.award.LotteryVO>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "lotteryDetail"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.LotteryDetailRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.LotteryVO.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("lotteryDetail"))
              .build();
        }
      }
    }
    return getLotteryDetailMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.EmptyRequest,
      com.kikitrade.activity.facade.award.ConditionCode> getGetConditionCodesMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "getConditionCodes",
      requestType = com.kikitrade.activity.facade.award.EmptyRequest.class,
      responseType = com.kikitrade.activity.facade.award.ConditionCode.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.EmptyRequest,
      com.kikitrade.activity.facade.award.ConditionCode> getGetConditionCodesMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.EmptyRequest, com.kikitrade.activity.facade.award.ConditionCode> getGetConditionCodesMethod;
    if ((getGetConditionCodesMethod = ActivityFacadeGrpc.getGetConditionCodesMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getGetConditionCodesMethod = ActivityFacadeGrpc.getGetConditionCodesMethod) == null) {
          ActivityFacadeGrpc.getGetConditionCodesMethod = getGetConditionCodesMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.EmptyRequest, com.kikitrade.activity.facade.award.ConditionCode>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "getConditionCodes"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.EmptyRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ConditionCode.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("getConditionCodes"))
              .build();
        }
      }
    }
    return getGetConditionCodesMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ConditionRequest,
      com.kikitrade.activity.facade.award.ConditionResponse> getGetConditionMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "getCondition",
      requestType = com.kikitrade.activity.facade.award.ConditionRequest.class,
      responseType = com.kikitrade.activity.facade.award.ConditionResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ConditionRequest,
      com.kikitrade.activity.facade.award.ConditionResponse> getGetConditionMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.award.ConditionRequest, com.kikitrade.activity.facade.award.ConditionResponse> getGetConditionMethod;
    if ((getGetConditionMethod = ActivityFacadeGrpc.getGetConditionMethod) == null) {
      synchronized (ActivityFacadeGrpc.class) {
        if ((getGetConditionMethod = ActivityFacadeGrpc.getGetConditionMethod) == null) {
          ActivityFacadeGrpc.getGetConditionMethod = getGetConditionMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.award.ConditionRequest, com.kikitrade.activity.facade.award.ConditionResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "getCondition"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ConditionRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.award.ConditionResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityFacadeMethodDescriptorSupplier("getCondition"))
              .build();
        }
      }
    }
    return getGetConditionMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static ActivityFacadeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ActivityFacadeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ActivityFacadeStub>() {
        @java.lang.Override
        public ActivityFacadeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ActivityFacadeStub(channel, callOptions);
        }
      };
    return ActivityFacadeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static ActivityFacadeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ActivityFacadeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ActivityFacadeBlockingStub>() {
        @java.lang.Override
        public ActivityFacadeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ActivityFacadeBlockingStub(channel, callOptions);
        }
      };
    return ActivityFacadeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static ActivityFacadeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ActivityFacadeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ActivityFacadeFutureStub>() {
        @java.lang.Override
        public ActivityFacadeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ActivityFacadeFutureStub(channel, callOptions);
        }
      };
    return ActivityFacadeFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     * <pre>
     **
     *保存或修改活动
     * </pre>
     */
    default void saveOrUpdateActivity(com.kikitrade.activity.facade.award.ActivityDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSaveOrUpdateActivityMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *新增或修改批次(过期)
     * </pre>
     */
    default void saveOrUpdateBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSaveOrUpdateBatchMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *删除批次(过期)
     * </pre>
     */
    default void deleteBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getDeleteBatchMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *批次列表查询(过期)
     * </pre>
     */
    default void queryBatchForList(com.kikitrade.activity.facade.award.ActivityBatchRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchListResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryBatchForListMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *批次详情接口(过期)
     * </pre>
     */
    default void queryDetail(com.kikitrade.activity.facade.award.ActivityBatchDetailRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatch> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryDetailMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *终审通过(过期)
     * </pre>
     */
    default void audit(com.kikitrade.activity.facade.award.AuditRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.AuditResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getAuditMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *查询奖例列表(过期)
     * </pre>
     */
    default void queryRewardList(com.kikitrade.activity.facade.award.AwardRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.AwardListResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryRewardListMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *删除详情(过期)
     * </pre>
     */
    default void deleteReward(com.kikitrade.activity.facade.award.AwardDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ModifyAwardResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getDeleteRewardMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *上传文件(过期)
     * </pre>
     */
    default void uploadFile(com.kikitrade.activity.facade.award.UploadRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.UploadResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUploadFileMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *导出数据
     * </pre>
     */
    default void exportData(com.kikitrade.activity.facade.award.ExportDataRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ExportDataResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getExportDataMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *导入数据
     * </pre>
     */
    default void importData(com.kikitrade.activity.facade.award.ImportDataRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ImportDataResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getImportDataMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *保存奖池
     * </pre>
     */
    default void saveLottery(com.kikitrade.activity.facade.award.LotteryDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSaveLotteryMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *删除奖池
     * </pre>
     */
    default void deleteLottery(com.kikitrade.activity.facade.award.LotteryDeleteDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getDeleteLotteryMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *奖池列表
     * </pre>
     */
    default void lotteryList(com.kikitrade.activity.facade.award.LotteryRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.LotteryListResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getLotteryListMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *奖池详情
     * </pre>
     */
    default void lotteryDetail(com.kikitrade.activity.facade.award.LotteryDetailRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.LotteryVO> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getLotteryDetailMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *查询规则名称列表
     * </pre>
     */
    default void getConditionCodes(com.kikitrade.activity.facade.award.EmptyRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ConditionCode> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetConditionCodesMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *查询规则详情
     * </pre>
     */
    default void getCondition(com.kikitrade.activity.facade.award.ConditionRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ConditionResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetConditionMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service ActivityFacade.
   */
  public static abstract class ActivityFacadeImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return ActivityFacadeGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service ActivityFacade.
   */
  public static final class ActivityFacadeStub
      extends io.grpc.stub.AbstractAsyncStub<ActivityFacadeStub> {
    private ActivityFacadeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ActivityFacadeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ActivityFacadeStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *保存或修改活动
     * </pre>
     */
    public void saveOrUpdateActivity(com.kikitrade.activity.facade.award.ActivityDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSaveOrUpdateActivityMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *新增或修改批次(过期)
     * </pre>
     */
    public void saveOrUpdateBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSaveOrUpdateBatchMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *删除批次(过期)
     * </pre>
     */
    public void deleteBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getDeleteBatchMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *批次列表查询(过期)
     * </pre>
     */
    public void queryBatchForList(com.kikitrade.activity.facade.award.ActivityBatchRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchListResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryBatchForListMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *批次详情接口(过期)
     * </pre>
     */
    public void queryDetail(com.kikitrade.activity.facade.award.ActivityBatchDetailRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatch> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryDetailMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *终审通过(过期)
     * </pre>
     */
    public void audit(com.kikitrade.activity.facade.award.AuditRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.AuditResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getAuditMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *查询奖例列表(过期)
     * </pre>
     */
    public void queryRewardList(com.kikitrade.activity.facade.award.AwardRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.AwardListResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryRewardListMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *删除详情(过期)
     * </pre>
     */
    public void deleteReward(com.kikitrade.activity.facade.award.AwardDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ModifyAwardResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getDeleteRewardMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *上传文件(过期)
     * </pre>
     */
    public void uploadFile(com.kikitrade.activity.facade.award.UploadRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.UploadResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUploadFileMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *导出数据
     * </pre>
     */
    public void exportData(com.kikitrade.activity.facade.award.ExportDataRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ExportDataResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getExportDataMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *导入数据
     * </pre>
     */
    public void importData(com.kikitrade.activity.facade.award.ImportDataRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ImportDataResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getImportDataMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *保存奖池
     * </pre>
     */
    public void saveLottery(com.kikitrade.activity.facade.award.LotteryDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSaveLotteryMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *删除奖池
     * </pre>
     */
    public void deleteLottery(com.kikitrade.activity.facade.award.LotteryDeleteDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getDeleteLotteryMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *奖池列表
     * </pre>
     */
    public void lotteryList(com.kikitrade.activity.facade.award.LotteryRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.LotteryListResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getLotteryListMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *奖池详情
     * </pre>
     */
    public void lotteryDetail(com.kikitrade.activity.facade.award.LotteryDetailRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.LotteryVO> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getLotteryDetailMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *查询规则名称列表
     * </pre>
     */
    public void getConditionCodes(com.kikitrade.activity.facade.award.EmptyRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ConditionCode> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetConditionCodesMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *查询规则详情
     * </pre>
     */
    public void getCondition(com.kikitrade.activity.facade.award.ConditionRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ConditionResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetConditionMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service ActivityFacade.
   */
  public static final class ActivityFacadeBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<ActivityFacadeBlockingStub> {
    private ActivityFacadeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ActivityFacadeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ActivityFacadeBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *保存或修改活动
     * </pre>
     */
    public com.kikitrade.activity.facade.award.ActivityResponse saveOrUpdateActivity(com.kikitrade.activity.facade.award.ActivityDTO request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSaveOrUpdateActivityMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *新增或修改批次(过期)
     * </pre>
     */
    public com.kikitrade.activity.facade.award.ActivityBatchResponse saveOrUpdateBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSaveOrUpdateBatchMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *删除批次(过期)
     * </pre>
     */
    public com.kikitrade.activity.facade.award.ActivityBatchResponse deleteBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getDeleteBatchMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *批次列表查询(过期)
     * </pre>
     */
    public com.kikitrade.activity.facade.award.ActivityBatchListResponse queryBatchForList(com.kikitrade.activity.facade.award.ActivityBatchRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryBatchForListMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *批次详情接口(过期)
     * </pre>
     */
    public com.kikitrade.activity.facade.award.ActivityBatch queryDetail(com.kikitrade.activity.facade.award.ActivityBatchDetailRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryDetailMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *终审通过(过期)
     * </pre>
     */
    public com.kikitrade.activity.facade.award.AuditResponse audit(com.kikitrade.activity.facade.award.AuditRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getAuditMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *查询奖例列表(过期)
     * </pre>
     */
    public com.kikitrade.activity.facade.award.AwardListResponse queryRewardList(com.kikitrade.activity.facade.award.AwardRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryRewardListMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *删除详情(过期)
     * </pre>
     */
    public com.kikitrade.activity.facade.award.ModifyAwardResponse deleteReward(com.kikitrade.activity.facade.award.AwardDTO request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getDeleteRewardMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *上传文件(过期)
     * </pre>
     */
    public com.kikitrade.activity.facade.award.UploadResponse uploadFile(com.kikitrade.activity.facade.award.UploadRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUploadFileMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *导出数据
     * </pre>
     */
    public com.kikitrade.activity.facade.award.ExportDataResponse exportData(com.kikitrade.activity.facade.award.ExportDataRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getExportDataMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *导入数据
     * </pre>
     */
    public com.kikitrade.activity.facade.award.ImportDataResponse importData(com.kikitrade.activity.facade.award.ImportDataRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getImportDataMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *保存奖池
     * </pre>
     */
    public com.kikitrade.activity.facade.award.LotteryResponse saveLottery(com.kikitrade.activity.facade.award.LotteryDTO request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSaveLotteryMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *删除奖池
     * </pre>
     */
    public com.kikitrade.activity.facade.award.LotteryResponse deleteLottery(com.kikitrade.activity.facade.award.LotteryDeleteDTO request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getDeleteLotteryMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *奖池列表
     * </pre>
     */
    public com.kikitrade.activity.facade.award.LotteryListResponse lotteryList(com.kikitrade.activity.facade.award.LotteryRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getLotteryListMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *奖池详情
     * </pre>
     */
    public com.kikitrade.activity.facade.award.LotteryVO lotteryDetail(com.kikitrade.activity.facade.award.LotteryDetailRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getLotteryDetailMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *查询规则名称列表
     * </pre>
     */
    public com.kikitrade.activity.facade.award.ConditionCode getConditionCodes(com.kikitrade.activity.facade.award.EmptyRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetConditionCodesMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *查询规则详情
     * </pre>
     */
    public com.kikitrade.activity.facade.award.ConditionResponse getCondition(com.kikitrade.activity.facade.award.ConditionRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetConditionMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service ActivityFacade.
   */
  public static final class ActivityFacadeFutureStub
      extends io.grpc.stub.AbstractFutureStub<ActivityFacadeFutureStub> {
    private ActivityFacadeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ActivityFacadeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ActivityFacadeFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *保存或修改活动
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.ActivityResponse> saveOrUpdateActivity(
        com.kikitrade.activity.facade.award.ActivityDTO request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSaveOrUpdateActivityMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *新增或修改批次(过期)
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.ActivityBatchResponse> saveOrUpdateBatch(
        com.kikitrade.activity.facade.award.ActivityBatchDTO request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSaveOrUpdateBatchMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *删除批次(过期)
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.ActivityBatchResponse> deleteBatch(
        com.kikitrade.activity.facade.award.ActivityBatchDTO request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getDeleteBatchMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *批次列表查询(过期)
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.ActivityBatchListResponse> queryBatchForList(
        com.kikitrade.activity.facade.award.ActivityBatchRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryBatchForListMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *批次详情接口(过期)
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.ActivityBatch> queryDetail(
        com.kikitrade.activity.facade.award.ActivityBatchDetailRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryDetailMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *终审通过(过期)
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.AuditResponse> audit(
        com.kikitrade.activity.facade.award.AuditRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getAuditMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *查询奖例列表(过期)
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.AwardListResponse> queryRewardList(
        com.kikitrade.activity.facade.award.AwardRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryRewardListMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *删除详情(过期)
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.ModifyAwardResponse> deleteReward(
        com.kikitrade.activity.facade.award.AwardDTO request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getDeleteRewardMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *上传文件(过期)
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.UploadResponse> uploadFile(
        com.kikitrade.activity.facade.award.UploadRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUploadFileMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *导出数据
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.ExportDataResponse> exportData(
        com.kikitrade.activity.facade.award.ExportDataRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getExportDataMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *导入数据
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.ImportDataResponse> importData(
        com.kikitrade.activity.facade.award.ImportDataRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getImportDataMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *保存奖池
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.LotteryResponse> saveLottery(
        com.kikitrade.activity.facade.award.LotteryDTO request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSaveLotteryMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *删除奖池
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.LotteryResponse> deleteLottery(
        com.kikitrade.activity.facade.award.LotteryDeleteDTO request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getDeleteLotteryMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *奖池列表
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.LotteryListResponse> lotteryList(
        com.kikitrade.activity.facade.award.LotteryRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getLotteryListMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *奖池详情
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.LotteryVO> lotteryDetail(
        com.kikitrade.activity.facade.award.LotteryDetailRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getLotteryDetailMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *查询规则名称列表
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.ConditionCode> getConditionCodes(
        com.kikitrade.activity.facade.award.EmptyRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetConditionCodesMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *查询规则详情
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.award.ConditionResponse> getCondition(
        com.kikitrade.activity.facade.award.ConditionRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetConditionMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_SAVE_OR_UPDATE_ACTIVITY = 0;
  private static final int METHODID_SAVE_OR_UPDATE_BATCH = 1;
  private static final int METHODID_DELETE_BATCH = 2;
  private static final int METHODID_QUERY_BATCH_FOR_LIST = 3;
  private static final int METHODID_QUERY_DETAIL = 4;
  private static final int METHODID_AUDIT = 5;
  private static final int METHODID_QUERY_REWARD_LIST = 6;
  private static final int METHODID_DELETE_REWARD = 7;
  private static final int METHODID_UPLOAD_FILE = 8;
  private static final int METHODID_EXPORT_DATA = 9;
  private static final int METHODID_IMPORT_DATA = 10;
  private static final int METHODID_SAVE_LOTTERY = 11;
  private static final int METHODID_DELETE_LOTTERY = 12;
  private static final int METHODID_LOTTERY_LIST = 13;
  private static final int METHODID_LOTTERY_DETAIL = 14;
  private static final int METHODID_GET_CONDITION_CODES = 15;
  private static final int METHODID_GET_CONDITION = 16;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_SAVE_OR_UPDATE_ACTIVITY:
          serviceImpl.saveOrUpdateActivity((com.kikitrade.activity.facade.award.ActivityDTO) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityResponse>) responseObserver);
          break;
        case METHODID_SAVE_OR_UPDATE_BATCH:
          serviceImpl.saveOrUpdateBatch((com.kikitrade.activity.facade.award.ActivityBatchDTO) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse>) responseObserver);
          break;
        case METHODID_DELETE_BATCH:
          serviceImpl.deleteBatch((com.kikitrade.activity.facade.award.ActivityBatchDTO) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse>) responseObserver);
          break;
        case METHODID_QUERY_BATCH_FOR_LIST:
          serviceImpl.queryBatchForList((com.kikitrade.activity.facade.award.ActivityBatchRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchListResponse>) responseObserver);
          break;
        case METHODID_QUERY_DETAIL:
          serviceImpl.queryDetail((com.kikitrade.activity.facade.award.ActivityBatchDetailRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatch>) responseObserver);
          break;
        case METHODID_AUDIT:
          serviceImpl.audit((com.kikitrade.activity.facade.award.AuditRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.AuditResponse>) responseObserver);
          break;
        case METHODID_QUERY_REWARD_LIST:
          serviceImpl.queryRewardList((com.kikitrade.activity.facade.award.AwardRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.AwardListResponse>) responseObserver);
          break;
        case METHODID_DELETE_REWARD:
          serviceImpl.deleteReward((com.kikitrade.activity.facade.award.AwardDTO) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ModifyAwardResponse>) responseObserver);
          break;
        case METHODID_UPLOAD_FILE:
          serviceImpl.uploadFile((com.kikitrade.activity.facade.award.UploadRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.UploadResponse>) responseObserver);
          break;
        case METHODID_EXPORT_DATA:
          serviceImpl.exportData((com.kikitrade.activity.facade.award.ExportDataRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ExportDataResponse>) responseObserver);
          break;
        case METHODID_IMPORT_DATA:
          serviceImpl.importData((com.kikitrade.activity.facade.award.ImportDataRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ImportDataResponse>) responseObserver);
          break;
        case METHODID_SAVE_LOTTERY:
          serviceImpl.saveLottery((com.kikitrade.activity.facade.award.LotteryDTO) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse>) responseObserver);
          break;
        case METHODID_DELETE_LOTTERY:
          serviceImpl.deleteLottery((com.kikitrade.activity.facade.award.LotteryDeleteDTO) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse>) responseObserver);
          break;
        case METHODID_LOTTERY_LIST:
          serviceImpl.lotteryList((com.kikitrade.activity.facade.award.LotteryRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.LotteryListResponse>) responseObserver);
          break;
        case METHODID_LOTTERY_DETAIL:
          serviceImpl.lotteryDetail((com.kikitrade.activity.facade.award.LotteryDetailRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.LotteryVO>) responseObserver);
          break;
        case METHODID_GET_CONDITION_CODES:
          serviceImpl.getConditionCodes((com.kikitrade.activity.facade.award.EmptyRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ConditionCode>) responseObserver);
          break;
        case METHODID_GET_CONDITION:
          serviceImpl.getCondition((com.kikitrade.activity.facade.award.ConditionRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ConditionResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getSaveOrUpdateActivityMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.ActivityDTO,
              com.kikitrade.activity.facade.award.ActivityResponse>(
                service, METHODID_SAVE_OR_UPDATE_ACTIVITY)))
        .addMethod(
          getSaveOrUpdateBatchMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.ActivityBatchDTO,
              com.kikitrade.activity.facade.award.ActivityBatchResponse>(
                service, METHODID_SAVE_OR_UPDATE_BATCH)))
        .addMethod(
          getDeleteBatchMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.ActivityBatchDTO,
              com.kikitrade.activity.facade.award.ActivityBatchResponse>(
                service, METHODID_DELETE_BATCH)))
        .addMethod(
          getQueryBatchForListMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.ActivityBatchRequest,
              com.kikitrade.activity.facade.award.ActivityBatchListResponse>(
                service, METHODID_QUERY_BATCH_FOR_LIST)))
        .addMethod(
          getQueryDetailMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.ActivityBatchDetailRequest,
              com.kikitrade.activity.facade.award.ActivityBatch>(
                service, METHODID_QUERY_DETAIL)))
        .addMethod(
          getAuditMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.AuditRequest,
              com.kikitrade.activity.facade.award.AuditResponse>(
                service, METHODID_AUDIT)))
        .addMethod(
          getQueryRewardListMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.AwardRequest,
              com.kikitrade.activity.facade.award.AwardListResponse>(
                service, METHODID_QUERY_REWARD_LIST)))
        .addMethod(
          getDeleteRewardMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.AwardDTO,
              com.kikitrade.activity.facade.award.ModifyAwardResponse>(
                service, METHODID_DELETE_REWARD)))
        .addMethod(
          getUploadFileMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.UploadRequest,
              com.kikitrade.activity.facade.award.UploadResponse>(
                service, METHODID_UPLOAD_FILE)))
        .addMethod(
          getExportDataMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.ExportDataRequest,
              com.kikitrade.activity.facade.award.ExportDataResponse>(
                service, METHODID_EXPORT_DATA)))
        .addMethod(
          getImportDataMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.ImportDataRequest,
              com.kikitrade.activity.facade.award.ImportDataResponse>(
                service, METHODID_IMPORT_DATA)))
        .addMethod(
          getSaveLotteryMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.LotteryDTO,
              com.kikitrade.activity.facade.award.LotteryResponse>(
                service, METHODID_SAVE_LOTTERY)))
        .addMethod(
          getDeleteLotteryMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.LotteryDeleteDTO,
              com.kikitrade.activity.facade.award.LotteryResponse>(
                service, METHODID_DELETE_LOTTERY)))
        .addMethod(
          getLotteryListMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.LotteryRequest,
              com.kikitrade.activity.facade.award.LotteryListResponse>(
                service, METHODID_LOTTERY_LIST)))
        .addMethod(
          getLotteryDetailMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.LotteryDetailRequest,
              com.kikitrade.activity.facade.award.LotteryVO>(
                service, METHODID_LOTTERY_DETAIL)))
        .addMethod(
          getGetConditionCodesMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.EmptyRequest,
              com.kikitrade.activity.facade.award.ConditionCode>(
                service, METHODID_GET_CONDITION_CODES)))
        .addMethod(
          getGetConditionMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.award.ConditionRequest,
              com.kikitrade.activity.facade.award.ConditionResponse>(
                service, METHODID_GET_CONDITION)))
        .build();
  }

  private static abstract class ActivityFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    ActivityFacadeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("ActivityFacade");
    }
  }

  private static final class ActivityFacadeFileDescriptorSupplier
      extends ActivityFacadeBaseDescriptorSupplier {
    ActivityFacadeFileDescriptorSupplier() {}
  }

  private static final class ActivityFacadeMethodDescriptorSupplier
      extends ActivityFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    ActivityFacadeMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (ActivityFacadeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new ActivityFacadeFileDescriptorSupplier())
              .addMethod(getSaveOrUpdateActivityMethod())
              .addMethod(getSaveOrUpdateBatchMethod())
              .addMethod(getDeleteBatchMethod())
              .addMethod(getQueryBatchForListMethod())
              .addMethod(getQueryDetailMethod())
              .addMethod(getAuditMethod())
              .addMethod(getQueryRewardListMethod())
              .addMethod(getDeleteRewardMethod())
              .addMethod(getUploadFileMethod())
              .addMethod(getExportDataMethod())
              .addMethod(getImportDataMethod())
              .addMethod(getSaveLotteryMethod())
              .addMethod(getDeleteLotteryMethod())
              .addMethod(getLotteryListMethod())
              .addMethod(getLotteryDetailMethod())
              .addMethod(getGetConditionCodesMethod())
              .addMethod(getGetConditionMethod())
              .build();
        }
      }
    }
    return result;
  }
}
