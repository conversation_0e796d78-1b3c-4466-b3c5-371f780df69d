// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityTemplate.proto

package com.kikitrade.activity.facade.template;

public interface ActivityMaterialDTOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.template.ActivityMaterialDTO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string activityId = 1;</code>
   * @return The activityId.
   */
  java.lang.String getActivityId();
  /**
   * <code>string activityId = 1;</code>
   * @return The bytes for activityId.
   */
  com.google.protobuf.ByteString
      getActivityIdBytes();

  /**
   * <code>string templateStatus = 2;</code>
   * @return The templateStatus.
   */
  java.lang.String getTemplateStatus();
  /**
   * <code>string templateStatus = 2;</code>
   * @return The bytes for templateStatus.
   */
  com.google.protobuf.ByteString
      getTemplateStatusBytes();

  /**
   * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
   */
  java.util.List<com.kikitrade.activity.facade.template.ActivityMaterial> 
      getMaterialList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
   */
  com.kikitrade.activity.facade.template.ActivityMaterial getMaterial(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
   */
  int getMaterialCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.template.ActivityMaterialOrBuilder> 
      getMaterialOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.template.ActivityMaterial material = 3;</code>
   */
  com.kikitrade.activity.facade.template.ActivityMaterialOrBuilder getMaterialOrBuilder(
      int index);
}
