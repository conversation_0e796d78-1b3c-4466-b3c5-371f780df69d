// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StickerFacade.proto

package com.kikitrade.activity.facade.sticker;

public final class StickerFacadeOuterClass {
  private StickerFacadeOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_sticker_UpsertStickerRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_sticker_UpsertStickerRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_sticker_UpsertStickerResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_sticker_UpsertStickerResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023StickerFacade.proto\022%com.kikitrade.act" +
      "ivity.facade.sticker\032\037google/protobuf/ti" +
      "mestamp.proto\"\303\003\n\024UpsertStickerRequest\022\n" +
      "\n\002id\030\001 \001(\t\022\017\n\007celebId\030\002 \001(\t\022\014\n\004name\030\003 \001(" +
      "\t\022\014\n\004icon\030\004 \001(\t\022\023\n\013description\030\005 \001(\t\022\r\n\005" +
      "image\030\006 \003(\t\022C\n\tpriceType\030\007 \001(\01620.com.kik" +
      "itrade.activity.facade.sticker.PriceType" +
      "\022I\n\014currencyType\030\010 \001(\01623.com.kikitrade.a" +
      "ctivity.facade.sticker.CurrencyType\022\r\n\005p" +
      "rice\030\t \001(\t\022H\n\010gateType\030\n \001(\01626.com.kikit" +
      "rade.activity.facade.sticker.StickerGate" +
      "Type\022\021\n\tgateValue\030\013 \001(\t\022\014\n\004sort\030\014 \001(\005\022D\n" +
      "\006status\030\r \001(\01624.com.kikitrade.activity.f" +
      "acade.sticker.StickerStatus\"E\n\025UpsertSti" +
      "ckerResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007message" +
      "\030\002 \001(\t\022\n\n\002id\030\003 \001(\t*-\n\tPriceType\022\010\n\004free\020" +
      "\000\022\010\n\004gems\020\001\022\014\n\010currency\020\002*\027\n\014CurrencyTyp" +
      "e\022\007\n\003USD\020\000*(\n\rStickerStatus\022\n\n\006active\020\000\022" +
      "\013\n\007disable\020\001**\n\017StickerGateType\022\007\n\003all\020\000" +
      "\022\016\n\nmembership\020\0012\234\001\n\rStickerFacade\022\212\001\n\ru" +
      "psertSticker\022;.com.kikitrade.activity.fa" +
      "cade.sticker.UpsertStickerRequest\032<.com." +
      "kikitrade.activity.facade.sticker.Upsert" +
      "StickerResponseB)\n%com.kikitrade.activit" +
      "y.facade.stickerP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.TimestampProto.getDescriptor(),
        });
    internal_static_com_kikitrade_activity_facade_sticker_UpsertStickerRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_activity_facade_sticker_UpsertStickerRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_sticker_UpsertStickerRequest_descriptor,
        new java.lang.String[] { "Id", "CelebId", "Name", "Icon", "Description", "Image", "PriceType", "CurrencyType", "Price", "GateType", "GateValue", "Sort", "Status", });
    internal_static_com_kikitrade_activity_facade_sticker_UpsertStickerResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_activity_facade_sticker_UpsertStickerResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_sticker_UpsertStickerResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "Id", });
    com.google.protobuf.TimestampProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
