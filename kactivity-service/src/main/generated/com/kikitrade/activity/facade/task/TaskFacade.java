// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: TaskFacade.proto

package com.kikitrade.activity.facade.task;

public final class TaskFacade {
  private TaskFacade() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_task_TaskDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_task_TaskDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_task_CommonResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_task_CommonResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_task_TaskVO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_task_TaskVO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\020TaskFacade.proto\022\"com.kikitrade.activi" +
      "ty.facade.task\032\037google/protobuf/timestam" +
      "p.proto\"\230\005\n\007TaskDTO\022\n\n\002id\030\001 \001(\t\022\022\n\ntaskN" +
      "ameCn\030\002 \001(\t\022\022\n\ntaskNameEn\030\003 \001(\t\022\022\n\ntaskN" +
      "ameHk\030\004 \001(\t\022:\n\006status\030\005 \001(\0162*.com.kikitr" +
      "ade.activity.facade.task.Status\022\016\n\006descC" +
      "n\030\006 \001(\t\022\016\n\006descEn\030\007 \001(\t\022\016\n\006descHk\030\010 \001(\t\022" +
      "\021\n\tstartTime\030\t \001(\t\022\017\n\007endTime\030\n \001(\t\022\020\n\010v" +
      "ipLevel\030\013 \001(\t\022?\n\004type\030\014 \001(\01621.com.kikitr" +
      "ade.activity.facade.task.TaskLevelType\022@" +
      "\n\tcycleType\030\r \001(\0162-.com.kikitrade.activi" +
      "ty.facade.task.CycleType\022<\n\005cycle\030\016 \001(\0162" +
      "-.com.kikitrade.activity.facade.task.Tas" +
      "kCycle\022<\n\005event\030\017 \001(\0162-.com.kikitrade.ac" +
      "tivity.facade.task.EventCode\022\031\n\021complete" +
      "Threshold\030\020 \001(\t\022\r\n\005award\030\021 \001(\t\022\013\n\003url\030\022 " +
      "\001(\t\022\014\n\004icon\030\023 \001(\t\022H\n\rawardTimeType\030\024 \001(\016" +
      "21.com.kikitrade.activity.facade.task.Aw" +
      "ardTimeType\022\025\n\rcompleteTimes\030\025 \001(\t\"l\n\016Co" +
      "mmonResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007message" +
      "\030\002 \001(\t\0228\n\004data\030\003 \001(\0132*.com.kikitrade.act" +
      "ivity.facade.task.TaskVO\"(\n\006TaskVO\022\n\n\002id" +
      "\030\001 \001(\t\022\022\n\nactivityId\030\002 \001(\t* \n\006Status\022\n\n\006" +
      "ACTIVE\020\000\022\n\n\006UNABLE\020\001* \n\tCycleType\022\t\n\005CYC" +
      "LE\020\000\022\010\n\004ONCE\020\001*)\n\rTaskLevelType\022\n\n\006NORMA" +
      "L\020\000\022\014\n\010ADVANCED\020\001*\220\002\n\tEventCode\022\010\n\004NONE\020" +
      "\000\022\013\n\007SIGN_IN\020\001\022\n\n\006FOLLOW\020\002\022\013\n\007COMMENT\020\003\022" +
      "\010\n\004LIKE\020\004\022\010\n\004POST\020\005\022\016\n\nSHARE_POST\020\006\022\016\n\nS" +
      "HARE_NEWS\020\007\022\021\n\rPOST_SPLENDID\020\010\022\007\n\003KYC\020\t\022" +
      "\n\n\006KYC_L1\020\n\022\n\n\006KYC_L2\020\013\022\030\n\024TRANSACTION_A" +
      "NALYSIS\020\014\022\022\n\016ASSET_ANALYSIS\020\r\022\021\n\rORDER_C" +
      "REATED\020\016\022\021\n\rORDER_MATCHED\020\017\022\014\n\010FOLLOWED\020" +
      "\020\022\t\n\005LIKED\020\021*/\n\tTaskCycle\022\t\n\005DAILY\020\000\022\n\n\006" +
      "WEEKLY\020\001\022\013\n\007MONTHLY\020\002**\n\rAwardTimeType\022\014" +
      "\n\010REALTIME\020\000\022\013\n\007OFFLINE\020\0012}\n\022ActivityTas" +
      "kFacade\022g\n\004save\022+.com.kikitrade.activity" +
      ".facade.task.TaskDTO\0322.com.kikitrade.act" +
      "ivity.facade.task.CommonResponseB&\n\"com." +
      "kikitrade.activity.facade.taskP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.TimestampProto.getDescriptor(),
        });
    internal_static_com_kikitrade_activity_facade_task_TaskDTO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_activity_facade_task_TaskDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_task_TaskDTO_descriptor,
        new java.lang.String[] { "Id", "TaskNameCn", "TaskNameEn", "TaskNameHk", "Status", "DescCn", "DescEn", "DescHk", "StartTime", "EndTime", "VipLevel", "Type", "CycleType", "Cycle", "Event", "CompleteThreshold", "Award", "Url", "Icon", "AwardTimeType", "CompleteTimes", });
    internal_static_com_kikitrade_activity_facade_task_CommonResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_activity_facade_task_CommonResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_task_CommonResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "Data", });
    internal_static_com_kikitrade_activity_facade_task_TaskVO_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_kikitrade_activity_facade_task_TaskVO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_task_TaskVO_descriptor,
        new java.lang.String[] { "Id", "ActivityId", });
    com.google.protobuf.TimestampProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
