// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public final class ActivityFacadeOuterClass {
  private ActivityFacadeOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_UploadResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_UploadResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_RewardRule_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_RewardRule_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ConditionRule_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ConditionRule_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ActivityDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ActivityDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ActivityResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ActivityResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ActivityBatchDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ActivityBatchDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ActivityBatch_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ActivityBatch_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ActivityBatchRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ActivityBatchRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ActivityBatchListResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ActivityBatchListResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ActivityBatchDetailRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ActivityBatchDetailRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ActivityBatchResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ActivityBatchResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_AuditRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_AuditRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_AuditResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_AuditResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_Award_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_Award_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_AwardRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_AwardRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_AwardListResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_AwardListResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_AwardDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_AwardDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ModifyDetail_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ModifyDetail_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ModifyAwardResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ModifyAwardResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_UploadRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_UploadRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ExportDataRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ExportDataRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ImportDataRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ImportDataRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_Condition_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_Condition_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ExportDataResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ExportDataResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ImportDataResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ImportDataResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_EmptyRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_EmptyRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ConditionRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ConditionRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ConditionCode_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ConditionCode_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ConditionResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ConditionResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_ConditionVO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_ConditionVO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_LotteryDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_LotteryDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_LotteryVO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_LotteryVO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_Lottery_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_Lottery_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_LotteryItem_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_LotteryItem_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_LotteryDeleteDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_LotteryDeleteDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_LotteryResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_LotteryResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_LotteryRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_LotteryRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_LotteryDetailRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_LotteryDetailRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_award_LotteryListResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_award_LotteryListResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024ActivityFacade.proto\022#com.kikitrade.ac" +
      "tivity.facade.award\032\037google/protobuf/tim" +
      "estamp.proto\"?\n\016UploadResponse\022\017\n\007succes" +
      "s\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\022\013\n\003url\030\003 \001(\t\"\236\001" +
      "\n\nRewardRule\022\r\n\005level\030\001 \001(\t\022\013\n\003min\030\002 \001(\t" +
      "\022\013\n\003max\030\003 \001(\t\022\014\n\004side\030\004 \001(\t\022\020\n\010userType\030" +
      "\005 \001(\t\022\021\n\tawardType\030\006 \001(\t\022\023\n\013awardAmount\030" +
      "\007 \001(\t\022\r\n\005award\030\010 \001(\t\022\020\n\010vipLevel\030\t \001(\t\"K" +
      "\n\rConditionRule\022\014\n\004name\030\001 \001(\t\022\r\n\005alias\030\002" +
      " \001(\t\022\016\n\006filter\030\003 \001(\t\022\r\n\005value\030\004 \001(\t\"\204\005\n\013" +
      "ActivityDTO\022\n\n\002id\030\001 \001(\t\022\024\n\014activityName\030" +
      "\002 \001(\t\022C\n\004type\030\003 \001(\01625.com.kikitrade.acti" +
      "vity.facade.award.ActivityTypeEnum\022\021\n\tst" +
      "artTime\030\004 \001(\t\022\017\n\007endTime\030\005 \001(\t\022\016\n\006remark" +
      "\030\006 \001(\t\022G\n\006status\030\007 \001(\01627.com.kikitrade.a" +
      "ctivity.facade.award.ActivityStatusEnum\022" +
      "\024\n\014activityArea\030\010 \001(\t\022\027\n\017autoCreateBatch" +
      "\030\t \001(\010\022K\n\016batchFrequency\030\n \001(\01623.com.kik" +
      "itrade.activity.facade.award.BatchFreque" +
      "ncy\022C\n\nrewardRule\030\013 \003(\0132/.com.kikitrade." +
      "activity.facade.award.RewardRule\022I\n\rcond" +
      "itionRule\030\014 \003(\01322.com.kikitrade.activity" +
      ".facade.award.ConditionRule\022\023\n\013autoAppro" +
      "ve\030\r \001(\010\022\016\n\006taskId\030\016 \001(\t\022\025\n\rconditionCod" +
      "e\030\017 \001(\t\022I\n\007subType\030\020 \001(\01628.com.kikitrade" +
      ".activity.facade.award.ActivitySubTypeEn" +
      "um\"@\n\020ActivityResponse\022\017\n\007success\030\001 \001(\010\022" +
      "\017\n\007message\030\002 \001(\t\022\n\n\002id\030\003 \001(\t\"\267\002\n\020Activit" +
      "yBatchDTO\022\n\n\002id\030\001 \001(\t\022\021\n\tbatchName\030\002 \001(\t" +
      "\022\022\n\nactivityId\030\003 \001(\t\022\024\n\014activityName\030\004 \001" +
      "(\t\022\022\n\nrewardType\030\005 \001(\t\022\016\n\006amount\030\006 \001(\t\022\020" +
      "\n\010currency\030\007 \001(\t\022\016\n\006remark\030\010 \001(\t\022\021\n\tsche" +
      "duled\030\t \001(\010\022\025\n\rscheduledTime\030\n \001(\t\022\017\n\007am" +
      "ended\030\013 \001(\t\022\024\n\014sourceOssUrl\030\014 \001(\t\022C\n\nrew" +
      "ardRule\030\r \003(\0132/.com.kikitrade.activity.f" +
      "acade.award.RewardRule\"\302\004\n\rActivityBatch" +
      "\022\n\n\002id\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\022\n\nactivityId" +
      "\030\003 \001(\t\022\024\n\014activityName\030\004 \001(\t\022\022\n\nrewardTy" +
      "pe\030\005 \001(\t\022\016\n\006amount\030\006 \001(\t\022\020\n\010currency\030\007 \001" +
      "(\t\022\016\n\006remark\030\010 \001(\t\022\021\n\tscheduled\030\t \001(\010\022\025\n" +
      "\rscheduledTime\030\n \001(\t\022\016\n\006saasId\030\013 \001(\t\022\023\n\013" +
      "prizeAmount\030\014 \001(\t\022\017\n\007winners\030\r \001(\t\022\017\n\007am" +
      "ended\030\016 \001(\t\022\020\n\010modified\030\017 \001(\t\022D\n\006status\030" +
      "\020 \001(\01624.com.kikitrade.activity.facade.aw" +
      "ard.BatchStatusEnum\022\016\n\006ossUrl\030\021 \001(\t\022\024\n\014g" +
      "enerateTime\030\022 \001(\t\022\024\n\014sourceOssUrl\030\023 \001(\t\022" +
      "C\n\nrewardRule\030\024 \003(\0132/.com.kikitrade.acti" +
      "vity.facade.award.RewardRule\022\024\n\014activity" +
      "Type\030\025 \001(\t\022G\n\006source\030\026 \001(\01627.com.kikitra" +
      "de.activity.facade.award.ActivitySourceE" +
      "num\"\261\001\n\024ActivityBatchRequest\022\n\n\002id\030\001 \001(\t" +
      "\022\021\n\tbatchName\030\002 \001(\t\022\016\n\006pageNo\030\003 \001(\005\022\020\n\010p" +
      "ageSize\030\004 \001(\005\022D\n\006status\030\005 \001(\01624.com.kiki" +
      "trade.activity.facade.award.BatchStatusE" +
      "num\022\022\n\nactivityId\030\006 \001(\t\"\210\001\n\031ActivityBatc" +
      "hListResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007messag" +
      "e\030\002 \001(\t\022I\n\ractivityBatch\030\003 \003(\01322.com.kik" +
      "itrade.activity.facade.award.ActivityBat" +
      "ch\"(\n\032ActivityBatchDetailRequest\022\n\n\002id\030\001" +
      " \001(\t\"\204\001\n\025ActivityBatchResponse\022\017\n\007succes" +
      "s\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\022I\n\ractivityBatc" +
      "h\030\003 \001(\01322.com.kikitrade.activity.facade." +
      "award.ActivityBatch\"a\n\014AuditRequest\022\n\n\002i" +
      "d\030\001 \001(\t\022E\n\tauditType\030\002 \001(\01622.com.kikitra" +
      "de.activity.facade.award.AuditTypeEnum\"1" +
      "\n\rAuditResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007mess" +
      "age\030\002 \001(\t\"\264\001\n\005Award\022\n\n\002id\030\001 \001(\t\022\022\n\ncusto" +
      "merId\030\002 \001(\t\022\r\n\005phone\030\003 \001(\t\022\r\n\005email\030\004 \001(" +
      "\t\022\021\n\tawardTime\030\005 \001(\t\022I\n\013awardStatus\030\006 \001(" +
      "\01624.com.kikitrade.activity.facade.award." +
      "BatchStatusEnum\022\017\n\007message\030\007 \001(\t\"\032\n\014Awar" +
      "dRequest\022\n\n\002id\030\001 \001(\t\"U\n\021AwardListRespons" +
      "e\022\017\n\007success\030\001 \001(\010\022\016\n\006pageNo\030\002 \001(\003\022\020\n\010pa" +
      "geSize\030\003 \001(\003\022\r\n\005total\030\004 \001(\003\"\026\n\010AwardDTO\022" +
      "\n\n\002id\030\001 \003(\t\"+\n\014ModifyDetail\022\n\n\002id\030\001 \001(\t\022" +
      "\017\n\007message\030\002 \001(\t\"z\n\023ModifyAwardResponse\022" +
      "\017\n\007success\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\022A\n\006det" +
      "ail\030\003 \003(\01321.com.kikitrade.activity.facad" +
      "e.award.ModifyDetail\"2\n\rUploadRequest\022\017\n" +
      "\007batchId\030\001 \001(\t\022\020\n\010fileName\030\002 \001(\t\"x\n\021Expo" +
      "rtDataRequest\022\n\n\002id\030\001 \001(\t\022\023\n\013conditionId" +
      "\030\002 \001(\t\022B\n\nconditions\030\003 \003(\0132..com.kikitra" +
      "de.activity.facade.award.Condition\"0\n\021Im" +
      "portDataRequest\022\n\n\002id\030\001 \001(\t\022\017\n\007batchId\030\002" +
      " \001(\t\"(\n\tCondition\022\014\n\004code\030\001 \001(\t\022\r\n\005value" +
      "\030\002 \001(\t\"C\n\022ExportDataResponse\022\017\n\007success\030" +
      "\001 \001(\010\022\017\n\007message\030\002 \001(\t\022\013\n\003url\030\003 \001(\t\"6\n\022I" +
      "mportDataResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007me" +
      "ssage\030\002 \001(\t\"\016\n\014EmptyRequest\" \n\020Condition" +
      "Request\022\014\n\004code\030\001 \001(\t\"\035\n\rConditionCode\022\014" +
      "\n\004code\030\001 \003(\t\"X\n\021ConditionResponse\022C\n\tcon" +
      "dition\030\001 \003(\01320.com.kikitrade.activity.fa" +
      "cade.award.ConditionVO\":\n\013ConditionVO\022\014\n" +
      "\004name\030\001 \001(\t\022\016\n\006filter\030\002 \001(\t\022\r\n\005alisa\030\003 \001" +
      "(\t\"\322\001\n\nLotteryDTO\022\n\n\002id\030\001 \001(\t\022\r\n\005valid\030\002" +
      " \001(\t\022\016\n\006remark\030\003 \001(\t\022\016\n\006status\030\004 \001(\t\022\020\n\010" +
      "vipLevel\030\005 \001(\t\022\016\n\006amount\030\006 \001(\t\022\022\n\ntimesL" +
      "imit\030\007 \001(\t\022\023\n\013rewardLimit\030\010 \001(\t\022>\n\004item\030" +
      "\t \003(\01320.com.kikitrade.activity.facade.aw" +
      "ard.LotteryItem\"\212\001\n\tLotteryVO\022=\n\007lottery" +
      "\030\001 \001(\0132,.com.kikitrade.activity.facade.a" +
      "ward.Lottery\022>\n\004item\030\002 \003(\01320.com.kikitra" +
      "de.activity.facade.award.LotteryItem\"\217\001\n" +
      "\007Lottery\022\n\n\002id\030\001 \001(\t\022\r\n\005valid\030\002 \001(\t\022\016\n\006r" +
      "emark\030\003 \001(\t\022\016\n\006status\030\004 \001(\t\022\020\n\010vipLevel\030" +
      "\005 \001(\t\022\016\n\006amount\030\006 \001(\t\022\022\n\ntimesLimit\030\007 \001(" +
      "\t\022\023\n\013rewardLimit\030\010 \001(\t\"\220\001\n\013LotteryItem\022\014" +
      "\n\004name\030\001 \001(\t\022\020\n\010currency\030\002 \001(\t\022\016\n\006amount" +
      "\030\003 \001(\t\022\013\n\003num\030\004 \001(\005\022\017\n\007percent\030\005 \001(\t\022\r\n\005" +
      "isLow\030\006 \001(\010\022\021\n\tawardType\030\007 \001(\t\022\021\n\tremain" +
      "Num\030\010 \001(\005\"\036\n\020LotteryDeleteDTO\022\n\n\002id\030\001 \001(" +
      "\t\"v\n\017LotteryResponse\022\017\n\007success\030\001 \001(\010\022\017\n" +
      "\007message\030\002 \001(\t\022A\n\tlotteryVO\030\003 \001(\0132..com." +
      "kikitrade.activity.facade.award.LotteryV" +
      "O\">\n\016LotteryRequest\022\r\n\005valid\030\001 \001(\t\022\016\n\006of" +
      "fset\030\002 \001(\005\022\r\n\005limit\030\003 \001(\005\"\"\n\024LotteryDeta" +
      "ilRequest\022\n\n\002id\030\001 \001(\t\"T\n\023LotteryListResp" +
      "onse\022=\n\007lottery\030\001 \003(\0132,.com.kikitrade.ac" +
      "tivity.facade.award.Lottery*+\n\022ActivityS" +
      "ourceEnum\022\010\n\004TASK\020\000\022\013\n\007OPERATE\020\001*H\n\020Acti" +
      "vityTypeEnum\022\r\n\tHIERARCHY\020\000\022\n\n\006INVITE\020\001\022" +
      "\r\n\tCUSTOMIZE\020\002\022\n\n\006NORMAL\020\003*;\n\023ActivitySu" +
      "bTypeEnum\022\010\n\004NONE\020\000\022\020\n\014TRADE_REBATE\020\001\022\010\n" +
      "\004KYC1\020\002*?\n\022ActivityStatusEnum\022\t\n\005DRAFT\020\000" +
      "\022\n\n\006ACTIVE\020\001\022\t\n\005PAUSE\020\002\022\007\n\003END\020\003*\317\001\n\017Bat" +
      "chStatusEnum\022\007\n\003ALL\020\000\022\020\n\014NOT_IMPORTED\020\001\022" +
      "\r\n\tIMPORTING\020\002\022\021\n\rIMPORT_FAILED\020\003\022\r\n\tUNA" +
      "UDITED\020\004\022\014\n\010REJECTED\020\005\022\014\n\010APPROVED\020\006\022\014\n\010" +
      "AWARDING\020\007\022\020\n\014AWARD_FAILED\020\010\022\021\n\rAWARD_SU" +
      "CCESS\020\t\022\020\n\014ODPS_RUNNING\020\n\022\017\n\013ODPS_FAILED" +
      "\020\013*(\n\rAuditTypeEnum\022\013\n\007APPROVE\020\000\022\n\n\006REJE" +
      "CT\020\001*L\n\016BatchFrequency\022\r\n\tEVERY_DAY\020\000\022\n\n" +
      "\006FINISH\020\001\022\017\n\013EVERY_MONTH\020\002\022\016\n\nEVERY_WEEK" +
      "\020\0032\370\020\n\016ActivityFacade\022\177\n\024saveOrUpdateAct" +
      "ivity\0220.com.kikitrade.activity.facade.aw" +
      "ard.ActivityDTO\0325.com.kikitrade.activity" +
      ".facade.award.ActivityResponse\022\206\001\n\021saveO" +
      "rUpdateBatch\0225.com.kikitrade.activity.fa" +
      "cade.award.ActivityBatchDTO\032:.com.kikitr" +
      "ade.activity.facade.award.ActivityBatchR" +
      "esponse\022\200\001\n\013deleteBatch\0225.com.kikitrade." +
      "activity.facade.award.ActivityBatchDTO\032:" +
      ".com.kikitrade.activity.facade.award.Act" +
      "ivityBatchResponse\022\216\001\n\021queryBatchForList" +
      "\0229.com.kikitrade.activity.facade.award.A" +
      "ctivityBatchRequest\032>.com.kikitrade.acti" +
      "vity.facade.award.ActivityBatchListRespo" +
      "nse\022\202\001\n\013queryDetail\022?.com.kikitrade.acti" +
      "vity.facade.award.ActivityBatchDetailReq" +
      "uest\0322.com.kikitrade.activity.facade.awa" +
      "rd.ActivityBatch\022n\n\005audit\0221.com.kikitrad" +
      "e.activity.facade.award.AuditRequest\0322.c" +
      "om.kikitrade.activity.facade.award.Audit" +
      "Response\022|\n\017queryRewardList\0221.com.kikitr" +
      "ade.activity.facade.award.AwardRequest\0326" +
      ".com.kikitrade.activity.facade.award.Awa" +
      "rdListResponse\022w\n\014deleteReward\022-.com.kik" +
      "itrade.activity.facade.award.AwardDTO\0328." +
      "com.kikitrade.activity.facade.award.Modi" +
      "fyAwardResponse\022u\n\nuploadFile\0222.com.kiki" +
      "trade.activity.facade.award.UploadReques" +
      "t\0323.com.kikitrade.activity.facade.award." +
      "UploadResponse\022}\n\nexportData\0226.com.kikit" +
      "rade.activity.facade.award.ExportDataReq" +
      "uest\0327.com.kikitrade.activity.facade.awa" +
      "rd.ExportDataResponse\022}\n\nimportData\0226.co" +
      "m.kikitrade.activity.facade.award.Import" +
      "DataRequest\0327.com.kikitrade.activity.fac" +
      "ade.award.ImportDataResponse\022t\n\013saveLott" +
      "ery\022/.com.kikitrade.activity.facade.awar" +
      "d.LotteryDTO\0324.com.kikitrade.activity.fa" +
      "cade.award.LotteryResponse\022|\n\rdeleteLott" +
      "ery\0225.com.kikitrade.activity.facade.awar" +
      "d.LotteryDeleteDTO\0324.com.kikitrade.activ" +
      "ity.facade.award.LotteryResponse\022|\n\013lott" +
      "eryList\0223.com.kikitrade.activity.facade." +
      "award.LotteryRequest\0328.com.kikitrade.act" +
      "ivity.facade.award.LotteryListResponse\022z" +
      "\n\rlotteryDetail\0229.com.kikitrade.activity" +
      ".facade.award.LotteryDetailRequest\032..com" +
      ".kikitrade.activity.facade.award.Lottery" +
      "VO\022z\n\021getConditionCodes\0221.com.kikitrade." +
      "activity.facade.award.EmptyRequest\0322.com" +
      ".kikitrade.activity.facade.award.Conditi" +
      "onCode\022}\n\014getCondition\0225.com.kikitrade.a" +
      "ctivity.facade.award.ConditionRequest\0326." +
      "com.kikitrade.activity.facade.award.Cond" +
      "itionResponseB\'\n#com.kikitrade.activity." +
      "facade.awardP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.TimestampProto.getDescriptor(),
        });
    internal_static_com_kikitrade_activity_facade_award_UploadResponse_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_activity_facade_award_UploadResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_UploadResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "Url", });
    internal_static_com_kikitrade_activity_facade_award_RewardRule_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_activity_facade_award_RewardRule_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_RewardRule_descriptor,
        new java.lang.String[] { "Level", "Min", "Max", "Side", "UserType", "AwardType", "AwardAmount", "Award", "VipLevel", });
    internal_static_com_kikitrade_activity_facade_award_ConditionRule_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_kikitrade_activity_facade_award_ConditionRule_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ConditionRule_descriptor,
        new java.lang.String[] { "Name", "Alias", "Filter", "Value", });
    internal_static_com_kikitrade_activity_facade_award_ActivityDTO_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_kikitrade_activity_facade_award_ActivityDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ActivityDTO_descriptor,
        new java.lang.String[] { "Id", "ActivityName", "Type", "StartTime", "EndTime", "Remark", "Status", "ActivityArea", "AutoCreateBatch", "BatchFrequency", "RewardRule", "ConditionRule", "AutoApprove", "TaskId", "ConditionCode", "SubType", });
    internal_static_com_kikitrade_activity_facade_award_ActivityResponse_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_kikitrade_activity_facade_award_ActivityResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ActivityResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "Id", });
    internal_static_com_kikitrade_activity_facade_award_ActivityBatchDTO_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_kikitrade_activity_facade_award_ActivityBatchDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ActivityBatchDTO_descriptor,
        new java.lang.String[] { "Id", "BatchName", "ActivityId", "ActivityName", "RewardType", "Amount", "Currency", "Remark", "Scheduled", "ScheduledTime", "Amended", "SourceOssUrl", "RewardRule", });
    internal_static_com_kikitrade_activity_facade_award_ActivityBatch_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_kikitrade_activity_facade_award_ActivityBatch_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ActivityBatch_descriptor,
        new java.lang.String[] { "Id", "Name", "ActivityId", "ActivityName", "RewardType", "Amount", "Currency", "Remark", "Scheduled", "ScheduledTime", "SaasId", "PrizeAmount", "Winners", "Amended", "Modified", "Status", "OssUrl", "GenerateTime", "SourceOssUrl", "RewardRule", "ActivityType", "Source", });
    internal_static_com_kikitrade_activity_facade_award_ActivityBatchRequest_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_kikitrade_activity_facade_award_ActivityBatchRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ActivityBatchRequest_descriptor,
        new java.lang.String[] { "Id", "BatchName", "PageNo", "PageSize", "Status", "ActivityId", });
    internal_static_com_kikitrade_activity_facade_award_ActivityBatchListResponse_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_kikitrade_activity_facade_award_ActivityBatchListResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ActivityBatchListResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "ActivityBatch", });
    internal_static_com_kikitrade_activity_facade_award_ActivityBatchDetailRequest_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_kikitrade_activity_facade_award_ActivityBatchDetailRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ActivityBatchDetailRequest_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_com_kikitrade_activity_facade_award_ActivityBatchResponse_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_kikitrade_activity_facade_award_ActivityBatchResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ActivityBatchResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "ActivityBatch", });
    internal_static_com_kikitrade_activity_facade_award_AuditRequest_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_kikitrade_activity_facade_award_AuditRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_AuditRequest_descriptor,
        new java.lang.String[] { "Id", "AuditType", });
    internal_static_com_kikitrade_activity_facade_award_AuditResponse_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_kikitrade_activity_facade_award_AuditResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_AuditResponse_descriptor,
        new java.lang.String[] { "Success", "Message", });
    internal_static_com_kikitrade_activity_facade_award_Award_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_kikitrade_activity_facade_award_Award_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_Award_descriptor,
        new java.lang.String[] { "Id", "CustomerId", "Phone", "Email", "AwardTime", "AwardStatus", "Message", });
    internal_static_com_kikitrade_activity_facade_award_AwardRequest_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_com_kikitrade_activity_facade_award_AwardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_AwardRequest_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_com_kikitrade_activity_facade_award_AwardListResponse_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_com_kikitrade_activity_facade_award_AwardListResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_AwardListResponse_descriptor,
        new java.lang.String[] { "Success", "PageNo", "PageSize", "Total", });
    internal_static_com_kikitrade_activity_facade_award_AwardDTO_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_com_kikitrade_activity_facade_award_AwardDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_AwardDTO_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_com_kikitrade_activity_facade_award_ModifyDetail_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_com_kikitrade_activity_facade_award_ModifyDetail_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ModifyDetail_descriptor,
        new java.lang.String[] { "Id", "Message", });
    internal_static_com_kikitrade_activity_facade_award_ModifyAwardResponse_descriptor =
      getDescriptor().getMessageTypes().get(18);
    internal_static_com_kikitrade_activity_facade_award_ModifyAwardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ModifyAwardResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "Detail", });
    internal_static_com_kikitrade_activity_facade_award_UploadRequest_descriptor =
      getDescriptor().getMessageTypes().get(19);
    internal_static_com_kikitrade_activity_facade_award_UploadRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_UploadRequest_descriptor,
        new java.lang.String[] { "BatchId", "FileName", });
    internal_static_com_kikitrade_activity_facade_award_ExportDataRequest_descriptor =
      getDescriptor().getMessageTypes().get(20);
    internal_static_com_kikitrade_activity_facade_award_ExportDataRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ExportDataRequest_descriptor,
        new java.lang.String[] { "Id", "ConditionId", "Conditions", });
    internal_static_com_kikitrade_activity_facade_award_ImportDataRequest_descriptor =
      getDescriptor().getMessageTypes().get(21);
    internal_static_com_kikitrade_activity_facade_award_ImportDataRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ImportDataRequest_descriptor,
        new java.lang.String[] { "Id", "BatchId", });
    internal_static_com_kikitrade_activity_facade_award_Condition_descriptor =
      getDescriptor().getMessageTypes().get(22);
    internal_static_com_kikitrade_activity_facade_award_Condition_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_Condition_descriptor,
        new java.lang.String[] { "Code", "Value", });
    internal_static_com_kikitrade_activity_facade_award_ExportDataResponse_descriptor =
      getDescriptor().getMessageTypes().get(23);
    internal_static_com_kikitrade_activity_facade_award_ExportDataResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ExportDataResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "Url", });
    internal_static_com_kikitrade_activity_facade_award_ImportDataResponse_descriptor =
      getDescriptor().getMessageTypes().get(24);
    internal_static_com_kikitrade_activity_facade_award_ImportDataResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ImportDataResponse_descriptor,
        new java.lang.String[] { "Success", "Message", });
    internal_static_com_kikitrade_activity_facade_award_EmptyRequest_descriptor =
      getDescriptor().getMessageTypes().get(25);
    internal_static_com_kikitrade_activity_facade_award_EmptyRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_EmptyRequest_descriptor,
        new java.lang.String[] { });
    internal_static_com_kikitrade_activity_facade_award_ConditionRequest_descriptor =
      getDescriptor().getMessageTypes().get(26);
    internal_static_com_kikitrade_activity_facade_award_ConditionRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ConditionRequest_descriptor,
        new java.lang.String[] { "Code", });
    internal_static_com_kikitrade_activity_facade_award_ConditionCode_descriptor =
      getDescriptor().getMessageTypes().get(27);
    internal_static_com_kikitrade_activity_facade_award_ConditionCode_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ConditionCode_descriptor,
        new java.lang.String[] { "Code", });
    internal_static_com_kikitrade_activity_facade_award_ConditionResponse_descriptor =
      getDescriptor().getMessageTypes().get(28);
    internal_static_com_kikitrade_activity_facade_award_ConditionResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ConditionResponse_descriptor,
        new java.lang.String[] { "Condition", });
    internal_static_com_kikitrade_activity_facade_award_ConditionVO_descriptor =
      getDescriptor().getMessageTypes().get(29);
    internal_static_com_kikitrade_activity_facade_award_ConditionVO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_ConditionVO_descriptor,
        new java.lang.String[] { "Name", "Filter", "Alisa", });
    internal_static_com_kikitrade_activity_facade_award_LotteryDTO_descriptor =
      getDescriptor().getMessageTypes().get(30);
    internal_static_com_kikitrade_activity_facade_award_LotteryDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_LotteryDTO_descriptor,
        new java.lang.String[] { "Id", "Valid", "Remark", "Status", "VipLevel", "Amount", "TimesLimit", "RewardLimit", "Item", });
    internal_static_com_kikitrade_activity_facade_award_LotteryVO_descriptor =
      getDescriptor().getMessageTypes().get(31);
    internal_static_com_kikitrade_activity_facade_award_LotteryVO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_LotteryVO_descriptor,
        new java.lang.String[] { "Lottery", "Item", });
    internal_static_com_kikitrade_activity_facade_award_Lottery_descriptor =
      getDescriptor().getMessageTypes().get(32);
    internal_static_com_kikitrade_activity_facade_award_Lottery_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_Lottery_descriptor,
        new java.lang.String[] { "Id", "Valid", "Remark", "Status", "VipLevel", "Amount", "TimesLimit", "RewardLimit", });
    internal_static_com_kikitrade_activity_facade_award_LotteryItem_descriptor =
      getDescriptor().getMessageTypes().get(33);
    internal_static_com_kikitrade_activity_facade_award_LotteryItem_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_LotteryItem_descriptor,
        new java.lang.String[] { "Name", "Currency", "Amount", "Num", "Percent", "IsLow", "AwardType", "RemainNum", });
    internal_static_com_kikitrade_activity_facade_award_LotteryDeleteDTO_descriptor =
      getDescriptor().getMessageTypes().get(34);
    internal_static_com_kikitrade_activity_facade_award_LotteryDeleteDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_LotteryDeleteDTO_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_com_kikitrade_activity_facade_award_LotteryResponse_descriptor =
      getDescriptor().getMessageTypes().get(35);
    internal_static_com_kikitrade_activity_facade_award_LotteryResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_LotteryResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "LotteryVO", });
    internal_static_com_kikitrade_activity_facade_award_LotteryRequest_descriptor =
      getDescriptor().getMessageTypes().get(36);
    internal_static_com_kikitrade_activity_facade_award_LotteryRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_LotteryRequest_descriptor,
        new java.lang.String[] { "Valid", "Offset", "Limit", });
    internal_static_com_kikitrade_activity_facade_award_LotteryDetailRequest_descriptor =
      getDescriptor().getMessageTypes().get(37);
    internal_static_com_kikitrade_activity_facade_award_LotteryDetailRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_LotteryDetailRequest_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_com_kikitrade_activity_facade_award_LotteryListResponse_descriptor =
      getDescriptor().getMessageTypes().get(38);
    internal_static_com_kikitrade_activity_facade_award_LotteryListResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_award_LotteryListResponse_descriptor,
        new java.lang.String[] { "Lottery", });
    com.google.protobuf.TimestampProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
