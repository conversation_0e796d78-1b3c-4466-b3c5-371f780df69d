// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityRosterRule.proto

package com.kikitrade.activity.facade.roster;

public interface ConditionParameterOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.roster.ConditionParameter)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string code = 1;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <code>string code = 1;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
   */
  java.util.List<com.kikitrade.activity.facade.roster.ConditionVO> 
      getConditionList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
   */
  com.kikitrade.activity.facade.roster.ConditionVO getCondition(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
   */
  int getConditionCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.roster.ConditionVOOrBuilder> 
      getConditionOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.ConditionVO condition = 2;</code>
   */
  com.kikitrade.activity.facade.roster.ConditionVOOrBuilder getConditionOrBuilder(
      int index);
}
