// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.award.RewardRule}
 */
public final class RewardRule extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.award.RewardRule)
    RewardRuleOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RewardRule.newBuilder() to construct.
  private RewardRule(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RewardRule() {
    level_ = "";
    min_ = "";
    max_ = "";
    side_ = "";
    userType_ = "";
    awardType_ = "";
    awardAmount_ = "";
    award_ = "";
    vipLevel_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new RewardRule();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_RewardRule_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_RewardRule_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.award.RewardRule.class, com.kikitrade.activity.facade.award.RewardRule.Builder.class);
  }

  public static final int LEVEL_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object level_ = "";
  /**
   * <code>string level = 1;</code>
   * @return The level.
   */
  @java.lang.Override
  public java.lang.String getLevel() {
    java.lang.Object ref = level_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      level_ = s;
      return s;
    }
  }
  /**
   * <code>string level = 1;</code>
   * @return The bytes for level.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLevelBytes() {
    java.lang.Object ref = level_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      level_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MIN_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object min_ = "";
  /**
   * <code>string min = 2;</code>
   * @return The min.
   */
  @java.lang.Override
  public java.lang.String getMin() {
    java.lang.Object ref = min_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      min_ = s;
      return s;
    }
  }
  /**
   * <code>string min = 2;</code>
   * @return The bytes for min.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMinBytes() {
    java.lang.Object ref = min_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      min_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MAX_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object max_ = "";
  /**
   * <code>string max = 3;</code>
   * @return The max.
   */
  @java.lang.Override
  public java.lang.String getMax() {
    java.lang.Object ref = max_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      max_ = s;
      return s;
    }
  }
  /**
   * <code>string max = 3;</code>
   * @return The bytes for max.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMaxBytes() {
    java.lang.Object ref = max_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      max_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SIDE_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object side_ = "";
  /**
   * <code>string side = 4;</code>
   * @return The side.
   */
  @java.lang.Override
  public java.lang.String getSide() {
    java.lang.Object ref = side_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      side_ = s;
      return s;
    }
  }
  /**
   * <code>string side = 4;</code>
   * @return The bytes for side.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSideBytes() {
    java.lang.Object ref = side_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      side_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int USERTYPE_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object userType_ = "";
  /**
   * <code>string userType = 5;</code>
   * @return The userType.
   */
  @java.lang.Override
  public java.lang.String getUserType() {
    java.lang.Object ref = userType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      userType_ = s;
      return s;
    }
  }
  /**
   * <code>string userType = 5;</code>
   * @return The bytes for userType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUserTypeBytes() {
    java.lang.Object ref = userType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      userType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AWARDTYPE_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object awardType_ = "";
  /**
   * <code>string awardType = 6;</code>
   * @return The awardType.
   */
  @java.lang.Override
  public java.lang.String getAwardType() {
    java.lang.Object ref = awardType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      awardType_ = s;
      return s;
    }
  }
  /**
   * <code>string awardType = 6;</code>
   * @return The bytes for awardType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAwardTypeBytes() {
    java.lang.Object ref = awardType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      awardType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AWARDAMOUNT_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object awardAmount_ = "";
  /**
   * <code>string awardAmount = 7;</code>
   * @return The awardAmount.
   */
  @java.lang.Override
  public java.lang.String getAwardAmount() {
    java.lang.Object ref = awardAmount_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      awardAmount_ = s;
      return s;
    }
  }
  /**
   * <code>string awardAmount = 7;</code>
   * @return The bytes for awardAmount.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAwardAmountBytes() {
    java.lang.Object ref = awardAmount_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      awardAmount_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AWARD_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object award_ = "";
  /**
   * <code>string award = 8;</code>
   * @return The award.
   */
  @java.lang.Override
  public java.lang.String getAward() {
    java.lang.Object ref = award_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      award_ = s;
      return s;
    }
  }
  /**
   * <code>string award = 8;</code>
   * @return The bytes for award.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAwardBytes() {
    java.lang.Object ref = award_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      award_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int VIPLEVEL_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object vipLevel_ = "";
  /**
   * <code>string vipLevel = 9;</code>
   * @return The vipLevel.
   */
  @java.lang.Override
  public java.lang.String getVipLevel() {
    java.lang.Object ref = vipLevel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      vipLevel_ = s;
      return s;
    }
  }
  /**
   * <code>string vipLevel = 9;</code>
   * @return The bytes for vipLevel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getVipLevelBytes() {
    java.lang.Object ref = vipLevel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      vipLevel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(level_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, level_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(min_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, min_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(max_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, max_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(side_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, side_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(userType_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, userType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(awardType_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, awardType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(awardAmount_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, awardAmount_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(award_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, award_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(vipLevel_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, vipLevel_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(level_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, level_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(min_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, min_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(max_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, max_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(side_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, side_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(userType_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, userType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(awardType_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, awardType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(awardAmount_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, awardAmount_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(award_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, award_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(vipLevel_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, vipLevel_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.award.RewardRule)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.award.RewardRule other = (com.kikitrade.activity.facade.award.RewardRule) obj;

    if (!getLevel()
        .equals(other.getLevel())) return false;
    if (!getMin()
        .equals(other.getMin())) return false;
    if (!getMax()
        .equals(other.getMax())) return false;
    if (!getSide()
        .equals(other.getSide())) return false;
    if (!getUserType()
        .equals(other.getUserType())) return false;
    if (!getAwardType()
        .equals(other.getAwardType())) return false;
    if (!getAwardAmount()
        .equals(other.getAwardAmount())) return false;
    if (!getAward()
        .equals(other.getAward())) return false;
    if (!getVipLevel()
        .equals(other.getVipLevel())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + LEVEL_FIELD_NUMBER;
    hash = (53 * hash) + getLevel().hashCode();
    hash = (37 * hash) + MIN_FIELD_NUMBER;
    hash = (53 * hash) + getMin().hashCode();
    hash = (37 * hash) + MAX_FIELD_NUMBER;
    hash = (53 * hash) + getMax().hashCode();
    hash = (37 * hash) + SIDE_FIELD_NUMBER;
    hash = (53 * hash) + getSide().hashCode();
    hash = (37 * hash) + USERTYPE_FIELD_NUMBER;
    hash = (53 * hash) + getUserType().hashCode();
    hash = (37 * hash) + AWARDTYPE_FIELD_NUMBER;
    hash = (53 * hash) + getAwardType().hashCode();
    hash = (37 * hash) + AWARDAMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getAwardAmount().hashCode();
    hash = (37 * hash) + AWARD_FIELD_NUMBER;
    hash = (53 * hash) + getAward().hashCode();
    hash = (37 * hash) + VIPLEVEL_FIELD_NUMBER;
    hash = (53 * hash) + getVipLevel().hashCode();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.award.RewardRule parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.RewardRule parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.RewardRule parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.RewardRule parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.RewardRule parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.RewardRule parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.RewardRule parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.RewardRule parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.award.RewardRule parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.award.RewardRule parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.RewardRule parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.RewardRule parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.award.RewardRule prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.award.RewardRule}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.award.RewardRule)
      com.kikitrade.activity.facade.award.RewardRuleOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_RewardRule_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_RewardRule_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.award.RewardRule.class, com.kikitrade.activity.facade.award.RewardRule.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.award.RewardRule.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      level_ = "";
      min_ = "";
      max_ = "";
      side_ = "";
      userType_ = "";
      awardType_ = "";
      awardAmount_ = "";
      award_ = "";
      vipLevel_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_RewardRule_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.RewardRule getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.award.RewardRule.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.RewardRule build() {
      com.kikitrade.activity.facade.award.RewardRule result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.RewardRule buildPartial() {
      com.kikitrade.activity.facade.award.RewardRule result = new com.kikitrade.activity.facade.award.RewardRule(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.kikitrade.activity.facade.award.RewardRule result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.level_ = level_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.min_ = min_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.max_ = max_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.side_ = side_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.userType_ = userType_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.awardType_ = awardType_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.awardAmount_ = awardAmount_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.award_ = award_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.vipLevel_ = vipLevel_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.award.RewardRule) {
        return mergeFrom((com.kikitrade.activity.facade.award.RewardRule)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.award.RewardRule other) {
      if (other == com.kikitrade.activity.facade.award.RewardRule.getDefaultInstance()) return this;
      if (!other.getLevel().isEmpty()) {
        level_ = other.level_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getMin().isEmpty()) {
        min_ = other.min_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getMax().isEmpty()) {
        max_ = other.max_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getSide().isEmpty()) {
        side_ = other.side_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (!other.getUserType().isEmpty()) {
        userType_ = other.userType_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (!other.getAwardType().isEmpty()) {
        awardType_ = other.awardType_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (!other.getAwardAmount().isEmpty()) {
        awardAmount_ = other.awardAmount_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (!other.getAward().isEmpty()) {
        award_ = other.award_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (!other.getVipLevel().isEmpty()) {
        vipLevel_ = other.vipLevel_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              level_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              min_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              max_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              side_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              userType_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              awardType_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              awardAmount_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              award_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 74: {
              vipLevel_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object level_ = "";
    /**
     * <code>string level = 1;</code>
     * @return The level.
     */
    public java.lang.String getLevel() {
      java.lang.Object ref = level_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        level_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string level = 1;</code>
     * @return The bytes for level.
     */
    public com.google.protobuf.ByteString
        getLevelBytes() {
      java.lang.Object ref = level_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        level_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string level = 1;</code>
     * @param value The level to set.
     * @return This builder for chaining.
     */
    public Builder setLevel(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      level_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string level = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearLevel() {
      level_ = getDefaultInstance().getLevel();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string level = 1;</code>
     * @param value The bytes for level to set.
     * @return This builder for chaining.
     */
    public Builder setLevelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      level_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object min_ = "";
    /**
     * <code>string min = 2;</code>
     * @return The min.
     */
    public java.lang.String getMin() {
      java.lang.Object ref = min_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        min_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string min = 2;</code>
     * @return The bytes for min.
     */
    public com.google.protobuf.ByteString
        getMinBytes() {
      java.lang.Object ref = min_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        min_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string min = 2;</code>
     * @param value The min to set.
     * @return This builder for chaining.
     */
    public Builder setMin(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      min_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string min = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMin() {
      min_ = getDefaultInstance().getMin();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string min = 2;</code>
     * @param value The bytes for min to set.
     * @return This builder for chaining.
     */
    public Builder setMinBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      min_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object max_ = "";
    /**
     * <code>string max = 3;</code>
     * @return The max.
     */
    public java.lang.String getMax() {
      java.lang.Object ref = max_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        max_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string max = 3;</code>
     * @return The bytes for max.
     */
    public com.google.protobuf.ByteString
        getMaxBytes() {
      java.lang.Object ref = max_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        max_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string max = 3;</code>
     * @param value The max to set.
     * @return This builder for chaining.
     */
    public Builder setMax(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      max_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>string max = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearMax() {
      max_ = getDefaultInstance().getMax();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>string max = 3;</code>
     * @param value The bytes for max to set.
     * @return This builder for chaining.
     */
    public Builder setMaxBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      max_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object side_ = "";
    /**
     * <code>string side = 4;</code>
     * @return The side.
     */
    public java.lang.String getSide() {
      java.lang.Object ref = side_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        side_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string side = 4;</code>
     * @return The bytes for side.
     */
    public com.google.protobuf.ByteString
        getSideBytes() {
      java.lang.Object ref = side_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        side_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string side = 4;</code>
     * @param value The side to set.
     * @return This builder for chaining.
     */
    public Builder setSide(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      side_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>string side = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearSide() {
      side_ = getDefaultInstance().getSide();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>string side = 4;</code>
     * @param value The bytes for side to set.
     * @return This builder for chaining.
     */
    public Builder setSideBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      side_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object userType_ = "";
    /**
     * <code>string userType = 5;</code>
     * @return The userType.
     */
    public java.lang.String getUserType() {
      java.lang.Object ref = userType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        userType_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string userType = 5;</code>
     * @return The bytes for userType.
     */
    public com.google.protobuf.ByteString
        getUserTypeBytes() {
      java.lang.Object ref = userType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string userType = 5;</code>
     * @param value The userType to set.
     * @return This builder for chaining.
     */
    public Builder setUserType(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      userType_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>string userType = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearUserType() {
      userType_ = getDefaultInstance().getUserType();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>string userType = 5;</code>
     * @param value The bytes for userType to set.
     * @return This builder for chaining.
     */
    public Builder setUserTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      userType_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private java.lang.Object awardType_ = "";
    /**
     * <code>string awardType = 6;</code>
     * @return The awardType.
     */
    public java.lang.String getAwardType() {
      java.lang.Object ref = awardType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        awardType_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string awardType = 6;</code>
     * @return The bytes for awardType.
     */
    public com.google.protobuf.ByteString
        getAwardTypeBytes() {
      java.lang.Object ref = awardType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        awardType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string awardType = 6;</code>
     * @param value The awardType to set.
     * @return This builder for chaining.
     */
    public Builder setAwardType(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      awardType_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>string awardType = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearAwardType() {
      awardType_ = getDefaultInstance().getAwardType();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>string awardType = 6;</code>
     * @param value The bytes for awardType to set.
     * @return This builder for chaining.
     */
    public Builder setAwardTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      awardType_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object awardAmount_ = "";
    /**
     * <code>string awardAmount = 7;</code>
     * @return The awardAmount.
     */
    public java.lang.String getAwardAmount() {
      java.lang.Object ref = awardAmount_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        awardAmount_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string awardAmount = 7;</code>
     * @return The bytes for awardAmount.
     */
    public com.google.protobuf.ByteString
        getAwardAmountBytes() {
      java.lang.Object ref = awardAmount_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        awardAmount_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string awardAmount = 7;</code>
     * @param value The awardAmount to set.
     * @return This builder for chaining.
     */
    public Builder setAwardAmount(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      awardAmount_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>string awardAmount = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearAwardAmount() {
      awardAmount_ = getDefaultInstance().getAwardAmount();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <code>string awardAmount = 7;</code>
     * @param value The bytes for awardAmount to set.
     * @return This builder for chaining.
     */
    public Builder setAwardAmountBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      awardAmount_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object award_ = "";
    /**
     * <code>string award = 8;</code>
     * @return The award.
     */
    public java.lang.String getAward() {
      java.lang.Object ref = award_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        award_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string award = 8;</code>
     * @return The bytes for award.
     */
    public com.google.protobuf.ByteString
        getAwardBytes() {
      java.lang.Object ref = award_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        award_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string award = 8;</code>
     * @param value The award to set.
     * @return This builder for chaining.
     */
    public Builder setAward(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      award_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>string award = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearAward() {
      award_ = getDefaultInstance().getAward();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <code>string award = 8;</code>
     * @param value The bytes for award to set.
     * @return This builder for chaining.
     */
    public Builder setAwardBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      award_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private java.lang.Object vipLevel_ = "";
    /**
     * <code>string vipLevel = 9;</code>
     * @return The vipLevel.
     */
    public java.lang.String getVipLevel() {
      java.lang.Object ref = vipLevel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        vipLevel_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string vipLevel = 9;</code>
     * @return The bytes for vipLevel.
     */
    public com.google.protobuf.ByteString
        getVipLevelBytes() {
      java.lang.Object ref = vipLevel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        vipLevel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string vipLevel = 9;</code>
     * @param value The vipLevel to set.
     * @return This builder for chaining.
     */
    public Builder setVipLevel(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      vipLevel_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>string vipLevel = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearVipLevel() {
      vipLevel_ = getDefaultInstance().getVipLevel();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <code>string vipLevel = 9;</code>
     * @param value The bytes for vipLevel to set.
     * @return This builder for chaining.
     */
    public Builder setVipLevelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      vipLevel_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.award.RewardRule)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.award.RewardRule)
  private static final com.kikitrade.activity.facade.award.RewardRule DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.award.RewardRule();
  }

  public static com.kikitrade.activity.facade.award.RewardRule getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RewardRule>
      PARSER = new com.google.protobuf.AbstractParser<RewardRule>() {
    @java.lang.Override
    public RewardRule parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<RewardRule> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RewardRule> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.award.RewardRule getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

