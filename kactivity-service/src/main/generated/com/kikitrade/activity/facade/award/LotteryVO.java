// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.award.LotteryVO}
 */
public final class LotteryVO extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.award.LotteryVO)
    LotteryVOOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LotteryVO.newBuilder() to construct.
  private LotteryVO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LotteryVO() {
    item_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LotteryVO();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_LotteryVO_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_LotteryVO_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.award.LotteryVO.class, com.kikitrade.activity.facade.award.LotteryVO.Builder.class);
  }

  private int bitField0_;
  public static final int LOTTERY_FIELD_NUMBER = 1;
  private com.kikitrade.activity.facade.award.Lottery lottery_;
  /**
   * <pre>
   *奖池基本信息
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   * @return Whether the lottery field is set.
   */
  @java.lang.Override
  public boolean hasLottery() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <pre>
   *奖池基本信息
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   * @return The lottery.
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.Lottery getLottery() {
    return lottery_ == null ? com.kikitrade.activity.facade.award.Lottery.getDefaultInstance() : lottery_;
  }
  /**
   * <pre>
   *奖池基本信息
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.LotteryOrBuilder getLotteryOrBuilder() {
    return lottery_ == null ? com.kikitrade.activity.facade.award.Lottery.getDefaultInstance() : lottery_;
  }

  public static final int ITEM_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.award.LotteryItem> item_;
  /**
   * <pre>
   *奖池奖品
   * </pre>
   *
   * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.award.LotteryItem> getItemList() {
    return item_;
  }
  /**
   * <pre>
   *奖池奖品
   * </pre>
   *
   * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.award.LotteryItemOrBuilder> 
      getItemOrBuilderList() {
    return item_;
  }
  /**
   * <pre>
   *奖池奖品
   * </pre>
   *
   * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
   */
  @java.lang.Override
  public int getItemCount() {
    return item_.size();
  }
  /**
   * <pre>
   *奖池奖品
   * </pre>
   *
   * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.LotteryItem getItem(int index) {
    return item_.get(index);
  }
  /**
   * <pre>
   *奖池奖品
   * </pre>
   *
   * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.LotteryItemOrBuilder getItemOrBuilder(
      int index) {
    return item_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getLottery());
    }
    for (int i = 0; i < item_.size(); i++) {
      output.writeMessage(2, item_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getLottery());
    }
    for (int i = 0; i < item_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, item_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.award.LotteryVO)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.award.LotteryVO other = (com.kikitrade.activity.facade.award.LotteryVO) obj;

    if (hasLottery() != other.hasLottery()) return false;
    if (hasLottery()) {
      if (!getLottery()
          .equals(other.getLottery())) return false;
    }
    if (!getItemList()
        .equals(other.getItemList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasLottery()) {
      hash = (37 * hash) + LOTTERY_FIELD_NUMBER;
      hash = (53 * hash) + getLottery().hashCode();
    }
    if (getItemCount() > 0) {
      hash = (37 * hash) + ITEM_FIELD_NUMBER;
      hash = (53 * hash) + getItemList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.award.LotteryVO parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.LotteryVO parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.LotteryVO parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.LotteryVO parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.LotteryVO parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.LotteryVO parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.LotteryVO parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.LotteryVO parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.award.LotteryVO parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.award.LotteryVO parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.LotteryVO parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.LotteryVO parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.award.LotteryVO prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.award.LotteryVO}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.award.LotteryVO)
      com.kikitrade.activity.facade.award.LotteryVOOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_LotteryVO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_LotteryVO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.award.LotteryVO.class, com.kikitrade.activity.facade.award.LotteryVO.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.award.LotteryVO.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getLotteryFieldBuilder();
        getItemFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      lottery_ = null;
      if (lotteryBuilder_ != null) {
        lotteryBuilder_.dispose();
        lotteryBuilder_ = null;
      }
      if (itemBuilder_ == null) {
        item_ = java.util.Collections.emptyList();
      } else {
        item_ = null;
        itemBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_LotteryVO_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.LotteryVO getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.award.LotteryVO.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.LotteryVO build() {
      com.kikitrade.activity.facade.award.LotteryVO result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.LotteryVO buildPartial() {
      com.kikitrade.activity.facade.award.LotteryVO result = new com.kikitrade.activity.facade.award.LotteryVO(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.award.LotteryVO result) {
      if (itemBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          item_ = java.util.Collections.unmodifiableList(item_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.item_ = item_;
      } else {
        result.item_ = itemBuilder_.build();
      }
    }

    private void buildPartial0(com.kikitrade.activity.facade.award.LotteryVO result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.lottery_ = lotteryBuilder_ == null
            ? lottery_
            : lotteryBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.award.LotteryVO) {
        return mergeFrom((com.kikitrade.activity.facade.award.LotteryVO)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.award.LotteryVO other) {
      if (other == com.kikitrade.activity.facade.award.LotteryVO.getDefaultInstance()) return this;
      if (other.hasLottery()) {
        mergeLottery(other.getLottery());
      }
      if (itemBuilder_ == null) {
        if (!other.item_.isEmpty()) {
          if (item_.isEmpty()) {
            item_ = other.item_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureItemIsMutable();
            item_.addAll(other.item_);
          }
          onChanged();
        }
      } else {
        if (!other.item_.isEmpty()) {
          if (itemBuilder_.isEmpty()) {
            itemBuilder_.dispose();
            itemBuilder_ = null;
            item_ = other.item_;
            bitField0_ = (bitField0_ & ~0x00000002);
            itemBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getItemFieldBuilder() : null;
          } else {
            itemBuilder_.addAllMessages(other.item_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  getLotteryFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              com.kikitrade.activity.facade.award.LotteryItem m =
                  input.readMessage(
                      com.kikitrade.activity.facade.award.LotteryItem.parser(),
                      extensionRegistry);
              if (itemBuilder_ == null) {
                ensureItemIsMutable();
                item_.add(m);
              } else {
                itemBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private com.kikitrade.activity.facade.award.Lottery lottery_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.kikitrade.activity.facade.award.Lottery, com.kikitrade.activity.facade.award.Lottery.Builder, com.kikitrade.activity.facade.award.LotteryOrBuilder> lotteryBuilder_;
    /**
     * <pre>
     *奖池基本信息
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     * @return Whether the lottery field is set.
     */
    public boolean hasLottery() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     *奖池基本信息
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     * @return The lottery.
     */
    public com.kikitrade.activity.facade.award.Lottery getLottery() {
      if (lotteryBuilder_ == null) {
        return lottery_ == null ? com.kikitrade.activity.facade.award.Lottery.getDefaultInstance() : lottery_;
      } else {
        return lotteryBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *奖池基本信息
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public Builder setLottery(com.kikitrade.activity.facade.award.Lottery value) {
      if (lotteryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        lottery_ = value;
      } else {
        lotteryBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖池基本信息
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public Builder setLottery(
        com.kikitrade.activity.facade.award.Lottery.Builder builderForValue) {
      if (lotteryBuilder_ == null) {
        lottery_ = builderForValue.build();
      } else {
        lotteryBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖池基本信息
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public Builder mergeLottery(com.kikitrade.activity.facade.award.Lottery value) {
      if (lotteryBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          lottery_ != null &&
          lottery_ != com.kikitrade.activity.facade.award.Lottery.getDefaultInstance()) {
          getLotteryBuilder().mergeFrom(value);
        } else {
          lottery_ = value;
        }
      } else {
        lotteryBuilder_.mergeFrom(value);
      }
      if (lottery_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <pre>
     *奖池基本信息
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public Builder clearLottery() {
      bitField0_ = (bitField0_ & ~0x00000001);
      lottery_ = null;
      if (lotteryBuilder_ != null) {
        lotteryBuilder_.dispose();
        lotteryBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖池基本信息
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public com.kikitrade.activity.facade.award.Lottery.Builder getLotteryBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return getLotteryFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *奖池基本信息
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    public com.kikitrade.activity.facade.award.LotteryOrBuilder getLotteryOrBuilder() {
      if (lotteryBuilder_ != null) {
        return lotteryBuilder_.getMessageOrBuilder();
      } else {
        return lottery_ == null ?
            com.kikitrade.activity.facade.award.Lottery.getDefaultInstance() : lottery_;
      }
    }
    /**
     * <pre>
     *奖池基本信息
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.kikitrade.activity.facade.award.Lottery, com.kikitrade.activity.facade.award.Lottery.Builder, com.kikitrade.activity.facade.award.LotteryOrBuilder> 
        getLotteryFieldBuilder() {
      if (lotteryBuilder_ == null) {
        lotteryBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.kikitrade.activity.facade.award.Lottery, com.kikitrade.activity.facade.award.Lottery.Builder, com.kikitrade.activity.facade.award.LotteryOrBuilder>(
                getLottery(),
                getParentForChildren(),
                isClean());
        lottery_ = null;
      }
      return lotteryBuilder_;
    }

    private java.util.List<com.kikitrade.activity.facade.award.LotteryItem> item_ =
      java.util.Collections.emptyList();
    private void ensureItemIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        item_ = new java.util.ArrayList<com.kikitrade.activity.facade.award.LotteryItem>(item_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.LotteryItem, com.kikitrade.activity.facade.award.LotteryItem.Builder, com.kikitrade.activity.facade.award.LotteryItemOrBuilder> itemBuilder_;

    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.LotteryItem> getItemList() {
      if (itemBuilder_ == null) {
        return java.util.Collections.unmodifiableList(item_);
      } else {
        return itemBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public int getItemCount() {
      if (itemBuilder_ == null) {
        return item_.size();
      } else {
        return itemBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public com.kikitrade.activity.facade.award.LotteryItem getItem(int index) {
      if (itemBuilder_ == null) {
        return item_.get(index);
      } else {
        return itemBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public Builder setItem(
        int index, com.kikitrade.activity.facade.award.LotteryItem value) {
      if (itemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureItemIsMutable();
        item_.set(index, value);
        onChanged();
      } else {
        itemBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public Builder setItem(
        int index, com.kikitrade.activity.facade.award.LotteryItem.Builder builderForValue) {
      if (itemBuilder_ == null) {
        ensureItemIsMutable();
        item_.set(index, builderForValue.build());
        onChanged();
      } else {
        itemBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public Builder addItem(com.kikitrade.activity.facade.award.LotteryItem value) {
      if (itemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureItemIsMutable();
        item_.add(value);
        onChanged();
      } else {
        itemBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public Builder addItem(
        int index, com.kikitrade.activity.facade.award.LotteryItem value) {
      if (itemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureItemIsMutable();
        item_.add(index, value);
        onChanged();
      } else {
        itemBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public Builder addItem(
        com.kikitrade.activity.facade.award.LotteryItem.Builder builderForValue) {
      if (itemBuilder_ == null) {
        ensureItemIsMutable();
        item_.add(builderForValue.build());
        onChanged();
      } else {
        itemBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public Builder addItem(
        int index, com.kikitrade.activity.facade.award.LotteryItem.Builder builderForValue) {
      if (itemBuilder_ == null) {
        ensureItemIsMutable();
        item_.add(index, builderForValue.build());
        onChanged();
      } else {
        itemBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public Builder addAllItem(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.award.LotteryItem> values) {
      if (itemBuilder_ == null) {
        ensureItemIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, item_);
        onChanged();
      } else {
        itemBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public Builder clearItem() {
      if (itemBuilder_ == null) {
        item_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        itemBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public Builder removeItem(int index) {
      if (itemBuilder_ == null) {
        ensureItemIsMutable();
        item_.remove(index);
        onChanged();
      } else {
        itemBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public com.kikitrade.activity.facade.award.LotteryItem.Builder getItemBuilder(
        int index) {
      return getItemFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public com.kikitrade.activity.facade.award.LotteryItemOrBuilder getItemOrBuilder(
        int index) {
      if (itemBuilder_ == null) {
        return item_.get(index);  } else {
        return itemBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.award.LotteryItemOrBuilder> 
         getItemOrBuilderList() {
      if (itemBuilder_ != null) {
        return itemBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(item_);
      }
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public com.kikitrade.activity.facade.award.LotteryItem.Builder addItemBuilder() {
      return getItemFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.award.LotteryItem.getDefaultInstance());
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public com.kikitrade.activity.facade.award.LotteryItem.Builder addItemBuilder(
        int index) {
      return getItemFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.award.LotteryItem.getDefaultInstance());
    }
    /**
     * <pre>
     *奖池奖品
     * </pre>
     *
     * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.LotteryItem.Builder> 
         getItemBuilderList() {
      return getItemFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.LotteryItem, com.kikitrade.activity.facade.award.LotteryItem.Builder, com.kikitrade.activity.facade.award.LotteryItemOrBuilder> 
        getItemFieldBuilder() {
      if (itemBuilder_ == null) {
        itemBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.award.LotteryItem, com.kikitrade.activity.facade.award.LotteryItem.Builder, com.kikitrade.activity.facade.award.LotteryItemOrBuilder>(
                item_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        item_ = null;
      }
      return itemBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.award.LotteryVO)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.award.LotteryVO)
  private static final com.kikitrade.activity.facade.award.LotteryVO DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.award.LotteryVO();
  }

  public static com.kikitrade.activity.facade.award.LotteryVO getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LotteryVO>
      PARSER = new com.google.protobuf.AbstractParser<LotteryVO>() {
    @java.lang.Override
    public LotteryVO parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<LotteryVO> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LotteryVO> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.award.LotteryVO getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

