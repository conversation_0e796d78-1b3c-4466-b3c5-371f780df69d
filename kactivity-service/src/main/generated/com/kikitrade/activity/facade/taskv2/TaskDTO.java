// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Task.proto

package com.kikitrade.activity.facade.taskv2;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.taskv2.TaskDTO}
 */
public final class TaskDTO extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.taskv2.TaskDTO)
    TaskDTOOrBuilder {
private static final long serialVersionUID = 0L;
  // Use TaskDTO.newBuilder() to construct.
  private TaskDTO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private TaskDTO() {
    id_ = "";
    title_ = "";
    desc_ = "";
    labelContent_ = "";
    labelColor_ = "";
    startTime_ = "";
    endTime_ = "";
    listImage_ = "";
    detailImage_ = "";
    shareImage_ = "";
    shareContent_ = "";
    cycle_ = 0;
    progressType_ = 0;
    rewardForm_ = 0;
    provideType_ = 0;
    rewardDTO_ = java.util.Collections.emptyList();
    subTaskDTO_ = java.util.Collections.emptyList();
    status_ = 0;
    saasId_ = "";
    code_ = "";
    twitterSubject_ = "";
    twitterTo_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new TaskDTO();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.internal_static_com_kikitrade_activity_facade_taskv2_TaskDTO_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.internal_static_com_kikitrade_activity_facade_taskv2_TaskDTO_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.taskv2.TaskDTO.class, com.kikitrade.activity.facade.taskv2.TaskDTO.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <pre>
   *任务id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *任务id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TITLE_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object title_ = "";
  /**
   * <pre>
   *quest name
   * </pre>
   *
   * <code>string title = 2;</code>
   * @return The title.
   */
  @java.lang.Override
  public java.lang.String getTitle() {
    java.lang.Object ref = title_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      title_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *quest name
   * </pre>
   *
   * <code>string title = 2;</code>
   * @return The bytes for title.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTitleBytes() {
    java.lang.Object ref = title_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      title_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DESC_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object desc_ = "";
  /**
   * <pre>
   *quest description
   * </pre>
   *
   * <code>string desc = 3;</code>
   * @return The desc.
   */
  @java.lang.Override
  public java.lang.String getDesc() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      desc_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *quest description
   * </pre>
   *
   * <code>string desc = 3;</code>
   * @return The bytes for desc.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescBytes() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      desc_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LABELCONTENT_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object labelContent_ = "";
  /**
   * <pre>
   *label content
   * </pre>
   *
   * <code>string labelContent = 4;</code>
   * @return The labelContent.
   */
  @java.lang.Override
  public java.lang.String getLabelContent() {
    java.lang.Object ref = labelContent_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      labelContent_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *label content
   * </pre>
   *
   * <code>string labelContent = 4;</code>
   * @return The bytes for labelContent.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLabelContentBytes() {
    java.lang.Object ref = labelContent_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      labelContent_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LABELCOLOR_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object labelColor_ = "";
  /**
   * <pre>
   *label color
   * </pre>
   *
   * <code>string labelColor = 5;</code>
   * @return The labelColor.
   */
  @java.lang.Override
  public java.lang.String getLabelColor() {
    java.lang.Object ref = labelColor_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      labelColor_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *label color
   * </pre>
   *
   * <code>string labelColor = 5;</code>
   * @return The bytes for labelColor.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLabelColorBytes() {
    java.lang.Object ref = labelColor_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      labelColor_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STARTTIME_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object startTime_ = "";
  /**
   * <pre>
   *start time
   * </pre>
   *
   * <code>string startTime = 6;</code>
   * @return The startTime.
   */
  @java.lang.Override
  public java.lang.String getStartTime() {
    java.lang.Object ref = startTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      startTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *start time
   * </pre>
   *
   * <code>string startTime = 6;</code>
   * @return The bytes for startTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStartTimeBytes() {
    java.lang.Object ref = startTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      startTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ENDTIME_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object endTime_ = "";
  /**
   * <pre>
   * </pre>
   *
   * <code>string endTime = 7;</code>
   * @return The endTime.
   */
  @java.lang.Override
  public java.lang.String getEndTime() {
    java.lang.Object ref = endTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      endTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * </pre>
   *
   * <code>string endTime = 7;</code>
   * @return The bytes for endTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getEndTimeBytes() {
    java.lang.Object ref = endTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      endTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LISTIMAGE_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object listImage_ = "";
  /**
   * <code>string listImage = 8;</code>
   * @return The listImage.
   */
  @java.lang.Override
  public java.lang.String getListImage() {
    java.lang.Object ref = listImage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      listImage_ = s;
      return s;
    }
  }
  /**
   * <code>string listImage = 8;</code>
   * @return The bytes for listImage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getListImageBytes() {
    java.lang.Object ref = listImage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      listImage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DETAILIMAGE_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object detailImage_ = "";
  /**
   * <code>string detailImage = 9;</code>
   * @return The detailImage.
   */
  @java.lang.Override
  public java.lang.String getDetailImage() {
    java.lang.Object ref = detailImage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      detailImage_ = s;
      return s;
    }
  }
  /**
   * <code>string detailImage = 9;</code>
   * @return The bytes for detailImage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDetailImageBytes() {
    java.lang.Object ref = detailImage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      detailImage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SHAREIMAGE_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object shareImage_ = "";
  /**
   * <code>string shareImage = 10;</code>
   * @return The shareImage.
   */
  @java.lang.Override
  public java.lang.String getShareImage() {
    java.lang.Object ref = shareImage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      shareImage_ = s;
      return s;
    }
  }
  /**
   * <code>string shareImage = 10;</code>
   * @return The bytes for shareImage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getShareImageBytes() {
    java.lang.Object ref = shareImage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      shareImage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SHARECONTENT_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private volatile java.lang.Object shareContent_ = "";
  /**
   * <pre>
   *分享文案
   * </pre>
   *
   * <code>string shareContent = 11;</code>
   * @return The shareContent.
   */
  @java.lang.Override
  public java.lang.String getShareContent() {
    java.lang.Object ref = shareContent_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      shareContent_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *分享文案
   * </pre>
   *
   * <code>string shareContent = 11;</code>
   * @return The bytes for shareContent.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getShareContentBytes() {
    java.lang.Object ref = shareContent_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      shareContent_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CYCLE_FIELD_NUMBER = 12;
  private int cycle_ = 0;
  /**
   * <pre>
   *quest duration type
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.TaskCycle cycle = 12;</code>
   * @return The enum numeric value on the wire for cycle.
   */
  @java.lang.Override public int getCycleValue() {
    return cycle_;
  }
  /**
   * <pre>
   *quest duration type
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.TaskCycle cycle = 12;</code>
   * @return The cycle.
   */
  @java.lang.Override public com.kikitrade.activity.facade.taskv2.TaskCycle getCycle() {
    com.kikitrade.activity.facade.taskv2.TaskCycle result = com.kikitrade.activity.facade.taskv2.TaskCycle.forNumber(cycle_);
    return result == null ? com.kikitrade.activity.facade.taskv2.TaskCycle.UNRECOGNIZED : result;
  }

  public static final int LIMIT_FIELD_NUMBER = 13;
  private int limit_ = 0;
  /**
   * <pre>
   *周期内完成任务次数上限，默认-1，表示不限制
   * </pre>
   *
   * <code>int32 limit = 13;</code>
   * @return The limit.
   */
  @java.lang.Override
  public int getLimit() {
    return limit_;
  }

  public static final int LIMITVIP_FIELD_NUMBER = 14;
  private int limitVip_ = 0;
  /**
   * <pre>
   *vip用户周期内完成任务次数上限，默认-1，表示不限制
   * </pre>
   *
   * <code>int32 limitVip = 14;</code>
   * @return The limitVip.
   */
  @java.lang.Override
  public int getLimitVip() {
    return limitVip_;
  }

  public static final int REWARDFREQUENCY_FIELD_NUMBER = 15;
  private int rewardFrequency_ = 0;
  /**
   * <pre>
   *奖励频率，每rewardFrequency发次奖励
   * </pre>
   *
   * <code>int32 rewardFrequency = 15;</code>
   * @return The rewardFrequency.
   */
  @java.lang.Override
  public int getRewardFrequency() {
    return rewardFrequency_;
  }

  public static final int PROGRESSTYPE_FIELD_NUMBER = 16;
  private int progressType_ = 0;
  /**
   * <pre>
   *任务进度计算方式
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.ProgressType progressType = 16;</code>
   * @return The enum numeric value on the wire for progressType.
   */
  @java.lang.Override public int getProgressTypeValue() {
    return progressType_;
  }
  /**
   * <pre>
   *任务进度计算方式
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.ProgressType progressType = 16;</code>
   * @return The progressType.
   */
  @java.lang.Override public com.kikitrade.activity.facade.taskv2.ProgressType getProgressType() {
    com.kikitrade.activity.facade.taskv2.ProgressType result = com.kikitrade.activity.facade.taskv2.ProgressType.forNumber(progressType_);
    return result == null ? com.kikitrade.activity.facade.taskv2.ProgressType.UNRECOGNIZED : result;
  }

  public static final int REWARDFORM_FIELD_NUMBER = 17;
  private int rewardForm_ = 0;
  /**
   * <pre>
   *奖品筛选方式
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.RewardForm rewardForm = 17;</code>
   * @return The enum numeric value on the wire for rewardForm.
   */
  @java.lang.Override public int getRewardFormValue() {
    return rewardForm_;
  }
  /**
   * <pre>
   *奖品筛选方式
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.RewardForm rewardForm = 17;</code>
   * @return The rewardForm.
   */
  @java.lang.Override public com.kikitrade.activity.facade.taskv2.RewardForm getRewardForm() {
    com.kikitrade.activity.facade.taskv2.RewardForm result = com.kikitrade.activity.facade.taskv2.RewardForm.forNumber(rewardForm_);
    return result == null ? com.kikitrade.activity.facade.taskv2.RewardForm.UNRECOGNIZED : result;
  }

  public static final int PROVIDETYPE_FIELD_NUMBER = 18;
  private int provideType_ = 0;
  /**
   * <pre>
   *发放方式
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.ProvideType provideType = 18;</code>
   * @return The enum numeric value on the wire for provideType.
   */
  @java.lang.Override public int getProvideTypeValue() {
    return provideType_;
  }
  /**
   * <pre>
   *发放方式
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.taskv2.ProvideType provideType = 18;</code>
   * @return The provideType.
   */
  @java.lang.Override public com.kikitrade.activity.facade.taskv2.ProvideType getProvideType() {
    com.kikitrade.activity.facade.taskv2.ProvideType result = com.kikitrade.activity.facade.taskv2.ProvideType.forNumber(provideType_);
    return result == null ? com.kikitrade.activity.facade.taskv2.ProvideType.UNRECOGNIZED : result;
  }

  public static final int REWARDDTO_FIELD_NUMBER = 19;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.taskv2.RewardDTO> rewardDTO_;
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.taskv2.RewardDTO> getRewardDTOList() {
    return rewardDTO_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.taskv2.RewardDTOOrBuilder> 
      getRewardDTOOrBuilderList() {
    return rewardDTO_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
   */
  @java.lang.Override
  public int getRewardDTOCount() {
    return rewardDTO_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.taskv2.RewardDTO getRewardDTO(int index) {
    return rewardDTO_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.taskv2.RewardDTOOrBuilder getRewardDTOOrBuilder(
      int index) {
    return rewardDTO_.get(index);
  }

  public static final int SUBTASKDTO_FIELD_NUMBER = 20;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.taskv2.SubTaskDTO> subTaskDTO_;
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.taskv2.SubTaskDTO> getSubTaskDTOList() {
    return subTaskDTO_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.taskv2.SubTaskDTOOrBuilder> 
      getSubTaskDTOOrBuilderList() {
    return subTaskDTO_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
   */
  @java.lang.Override
  public int getSubTaskDTOCount() {
    return subTaskDTO_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.taskv2.SubTaskDTO getSubTaskDTO(int index) {
    return subTaskDTO_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.taskv2.SubTaskDTOOrBuilder getSubTaskDTOOrBuilder(
      int index) {
    return subTaskDTO_.get(index);
  }

  public static final int STATUS_FIELD_NUMBER = 21;
  private int status_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.taskv2.CommonStatus status = 21;</code>
   * @return The enum numeric value on the wire for status.
   */
  @java.lang.Override public int getStatusValue() {
    return status_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.taskv2.CommonStatus status = 21;</code>
   * @return The status.
   */
  @java.lang.Override public com.kikitrade.activity.facade.taskv2.CommonStatus getStatus() {
    com.kikitrade.activity.facade.taskv2.CommonStatus result = com.kikitrade.activity.facade.taskv2.CommonStatus.forNumber(status_);
    return result == null ? com.kikitrade.activity.facade.taskv2.CommonStatus.UNRECOGNIZED : result;
  }

  public static final int SAASID_FIELD_NUMBER = 22;
  @SuppressWarnings("serial")
  private volatile java.lang.Object saasId_ = "";
  /**
   * <code>string saasId = 22;</code>
   * @return The saasId.
   */
  @java.lang.Override
  public java.lang.String getSaasId() {
    java.lang.Object ref = saasId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      saasId_ = s;
      return s;
    }
  }
  /**
   * <code>string saasId = 22;</code>
   * @return The bytes for saasId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSaasIdBytes() {
    java.lang.Object ref = saasId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      saasId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CODE_FIELD_NUMBER = 23;
  @SuppressWarnings("serial")
  private volatile java.lang.Object code_ = "";
  /**
   * <code>string code = 23;</code>
   * @return The code.
   */
  @java.lang.Override
  public java.lang.String getCode() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      code_ = s;
      return s;
    }
  }
  /**
   * <code>string code = 23;</code>
   * @return The bytes for code.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCodeBytes() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      code_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TWITTERSUBJECT_FIELD_NUMBER = 24;
  @SuppressWarnings("serial")
  private volatile java.lang.Object twitterSubject_ = "";
  /**
   * <code>string twitterSubject = 24;</code>
   * @return The twitterSubject.
   */
  @java.lang.Override
  public java.lang.String getTwitterSubject() {
    java.lang.Object ref = twitterSubject_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      twitterSubject_ = s;
      return s;
    }
  }
  /**
   * <code>string twitterSubject = 24;</code>
   * @return The bytes for twitterSubject.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTwitterSubjectBytes() {
    java.lang.Object ref = twitterSubject_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      twitterSubject_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TWITTERTO_FIELD_NUMBER = 25;
  @SuppressWarnings("serial")
  private volatile java.lang.Object twitterTo_ = "";
  /**
   * <code>string twitterTo = 25;</code>
   * @return The twitterTo.
   */
  @java.lang.Override
  public java.lang.String getTwitterTo() {
    java.lang.Object ref = twitterTo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      twitterTo_ = s;
      return s;
    }
  }
  /**
   * <code>string twitterTo = 25;</code>
   * @return The bytes for twitterTo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTwitterToBytes() {
    java.lang.Object ref = twitterTo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      twitterTo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(title_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, title_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(desc_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, desc_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(labelContent_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, labelContent_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(labelColor_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, labelColor_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(startTime_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, startTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(endTime_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, endTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(listImage_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, listImage_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(detailImage_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, detailImage_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(shareImage_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, shareImage_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(shareContent_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, shareContent_);
    }
    if (cycle_ != com.kikitrade.activity.facade.taskv2.TaskCycle.once.getNumber()) {
      output.writeEnum(12, cycle_);
    }
    if (limit_ != 0) {
      output.writeInt32(13, limit_);
    }
    if (limitVip_ != 0) {
      output.writeInt32(14, limitVip_);
    }
    if (rewardFrequency_ != 0) {
      output.writeInt32(15, rewardFrequency_);
    }
    if (progressType_ != com.kikitrade.activity.facade.taskv2.ProgressType.add.getNumber()) {
      output.writeEnum(16, progressType_);
    }
    if (rewardForm_ != com.kikitrade.activity.facade.taskv2.RewardForm.none.getNumber()) {
      output.writeEnum(17, rewardForm_);
    }
    if (provideType_ != com.kikitrade.activity.facade.taskv2.ProvideType.auto.getNumber()) {
      output.writeEnum(18, provideType_);
    }
    for (int i = 0; i < rewardDTO_.size(); i++) {
      output.writeMessage(19, rewardDTO_.get(i));
    }
    for (int i = 0; i < subTaskDTO_.size(); i++) {
      output.writeMessage(20, subTaskDTO_.get(i));
    }
    if (status_ != com.kikitrade.activity.facade.taskv2.CommonStatus.ACTIVE.getNumber()) {
      output.writeEnum(21, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(saasId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 22, saasId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(code_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 23, code_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(twitterSubject_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 24, twitterSubject_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(twitterTo_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 25, twitterTo_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(title_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, title_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(desc_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, desc_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(labelContent_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, labelContent_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(labelColor_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, labelColor_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(startTime_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, startTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(endTime_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, endTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(listImage_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, listImage_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(detailImage_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, detailImage_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(shareImage_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, shareImage_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(shareContent_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, shareContent_);
    }
    if (cycle_ != com.kikitrade.activity.facade.taskv2.TaskCycle.once.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(12, cycle_);
    }
    if (limit_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(13, limit_);
    }
    if (limitVip_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(14, limitVip_);
    }
    if (rewardFrequency_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(15, rewardFrequency_);
    }
    if (progressType_ != com.kikitrade.activity.facade.taskv2.ProgressType.add.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(16, progressType_);
    }
    if (rewardForm_ != com.kikitrade.activity.facade.taskv2.RewardForm.none.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(17, rewardForm_);
    }
    if (provideType_ != com.kikitrade.activity.facade.taskv2.ProvideType.auto.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(18, provideType_);
    }
    for (int i = 0; i < rewardDTO_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(19, rewardDTO_.get(i));
    }
    for (int i = 0; i < subTaskDTO_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(20, subTaskDTO_.get(i));
    }
    if (status_ != com.kikitrade.activity.facade.taskv2.CommonStatus.ACTIVE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(21, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(saasId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(22, saasId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(code_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(23, code_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(twitterSubject_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(24, twitterSubject_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(twitterTo_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(25, twitterTo_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.taskv2.TaskDTO)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.taskv2.TaskDTO other = (com.kikitrade.activity.facade.taskv2.TaskDTO) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getTitle()
        .equals(other.getTitle())) return false;
    if (!getDesc()
        .equals(other.getDesc())) return false;
    if (!getLabelContent()
        .equals(other.getLabelContent())) return false;
    if (!getLabelColor()
        .equals(other.getLabelColor())) return false;
    if (!getStartTime()
        .equals(other.getStartTime())) return false;
    if (!getEndTime()
        .equals(other.getEndTime())) return false;
    if (!getListImage()
        .equals(other.getListImage())) return false;
    if (!getDetailImage()
        .equals(other.getDetailImage())) return false;
    if (!getShareImage()
        .equals(other.getShareImage())) return false;
    if (!getShareContent()
        .equals(other.getShareContent())) return false;
    if (cycle_ != other.cycle_) return false;
    if (getLimit()
        != other.getLimit()) return false;
    if (getLimitVip()
        != other.getLimitVip()) return false;
    if (getRewardFrequency()
        != other.getRewardFrequency()) return false;
    if (progressType_ != other.progressType_) return false;
    if (rewardForm_ != other.rewardForm_) return false;
    if (provideType_ != other.provideType_) return false;
    if (!getRewardDTOList()
        .equals(other.getRewardDTOList())) return false;
    if (!getSubTaskDTOList()
        .equals(other.getSubTaskDTOList())) return false;
    if (status_ != other.status_) return false;
    if (!getSaasId()
        .equals(other.getSaasId())) return false;
    if (!getCode()
        .equals(other.getCode())) return false;
    if (!getTwitterSubject()
        .equals(other.getTwitterSubject())) return false;
    if (!getTwitterTo()
        .equals(other.getTwitterTo())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + TITLE_FIELD_NUMBER;
    hash = (53 * hash) + getTitle().hashCode();
    hash = (37 * hash) + DESC_FIELD_NUMBER;
    hash = (53 * hash) + getDesc().hashCode();
    hash = (37 * hash) + LABELCONTENT_FIELD_NUMBER;
    hash = (53 * hash) + getLabelContent().hashCode();
    hash = (37 * hash) + LABELCOLOR_FIELD_NUMBER;
    hash = (53 * hash) + getLabelColor().hashCode();
    hash = (37 * hash) + STARTTIME_FIELD_NUMBER;
    hash = (53 * hash) + getStartTime().hashCode();
    hash = (37 * hash) + ENDTIME_FIELD_NUMBER;
    hash = (53 * hash) + getEndTime().hashCode();
    hash = (37 * hash) + LISTIMAGE_FIELD_NUMBER;
    hash = (53 * hash) + getListImage().hashCode();
    hash = (37 * hash) + DETAILIMAGE_FIELD_NUMBER;
    hash = (53 * hash) + getDetailImage().hashCode();
    hash = (37 * hash) + SHAREIMAGE_FIELD_NUMBER;
    hash = (53 * hash) + getShareImage().hashCode();
    hash = (37 * hash) + SHARECONTENT_FIELD_NUMBER;
    hash = (53 * hash) + getShareContent().hashCode();
    hash = (37 * hash) + CYCLE_FIELD_NUMBER;
    hash = (53 * hash) + cycle_;
    hash = (37 * hash) + LIMIT_FIELD_NUMBER;
    hash = (53 * hash) + getLimit();
    hash = (37 * hash) + LIMITVIP_FIELD_NUMBER;
    hash = (53 * hash) + getLimitVip();
    hash = (37 * hash) + REWARDFREQUENCY_FIELD_NUMBER;
    hash = (53 * hash) + getRewardFrequency();
    hash = (37 * hash) + PROGRESSTYPE_FIELD_NUMBER;
    hash = (53 * hash) + progressType_;
    hash = (37 * hash) + REWARDFORM_FIELD_NUMBER;
    hash = (53 * hash) + rewardForm_;
    hash = (37 * hash) + PROVIDETYPE_FIELD_NUMBER;
    hash = (53 * hash) + provideType_;
    if (getRewardDTOCount() > 0) {
      hash = (37 * hash) + REWARDDTO_FIELD_NUMBER;
      hash = (53 * hash) + getRewardDTOList().hashCode();
    }
    if (getSubTaskDTOCount() > 0) {
      hash = (37 * hash) + SUBTASKDTO_FIELD_NUMBER;
      hash = (53 * hash) + getSubTaskDTOList().hashCode();
    }
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + status_;
    hash = (37 * hash) + SAASID_FIELD_NUMBER;
    hash = (53 * hash) + getSaasId().hashCode();
    hash = (37 * hash) + CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCode().hashCode();
    hash = (37 * hash) + TWITTERSUBJECT_FIELD_NUMBER;
    hash = (53 * hash) + getTwitterSubject().hashCode();
    hash = (37 * hash) + TWITTERTO_FIELD_NUMBER;
    hash = (53 * hash) + getTwitterTo().hashCode();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.taskv2.TaskDTO parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.taskv2.TaskDTO parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.taskv2.TaskDTO parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.taskv2.TaskDTO parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.taskv2.TaskDTO parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.taskv2.TaskDTO parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.taskv2.TaskDTO parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.taskv2.TaskDTO parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.taskv2.TaskDTO parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.taskv2.TaskDTO parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.taskv2.TaskDTO parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.taskv2.TaskDTO parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.taskv2.TaskDTO prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.taskv2.TaskDTO}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.taskv2.TaskDTO)
      com.kikitrade.activity.facade.taskv2.TaskDTOOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.internal_static_com_kikitrade_activity_facade_taskv2_TaskDTO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.internal_static_com_kikitrade_activity_facade_taskv2_TaskDTO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.taskv2.TaskDTO.class, com.kikitrade.activity.facade.taskv2.TaskDTO.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.taskv2.TaskDTO.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      title_ = "";
      desc_ = "";
      labelContent_ = "";
      labelColor_ = "";
      startTime_ = "";
      endTime_ = "";
      listImage_ = "";
      detailImage_ = "";
      shareImage_ = "";
      shareContent_ = "";
      cycle_ = 0;
      limit_ = 0;
      limitVip_ = 0;
      rewardFrequency_ = 0;
      progressType_ = 0;
      rewardForm_ = 0;
      provideType_ = 0;
      if (rewardDTOBuilder_ == null) {
        rewardDTO_ = java.util.Collections.emptyList();
      } else {
        rewardDTO_ = null;
        rewardDTOBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00040000);
      if (subTaskDTOBuilder_ == null) {
        subTaskDTO_ = java.util.Collections.emptyList();
      } else {
        subTaskDTO_ = null;
        subTaskDTOBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00080000);
      status_ = 0;
      saasId_ = "";
      code_ = "";
      twitterSubject_ = "";
      twitterTo_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.internal_static_com_kikitrade_activity_facade_taskv2_TaskDTO_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.taskv2.TaskDTO getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.taskv2.TaskDTO.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.taskv2.TaskDTO build() {
      com.kikitrade.activity.facade.taskv2.TaskDTO result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.taskv2.TaskDTO buildPartial() {
      com.kikitrade.activity.facade.taskv2.TaskDTO result = new com.kikitrade.activity.facade.taskv2.TaskDTO(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.taskv2.TaskDTO result) {
      if (rewardDTOBuilder_ == null) {
        if (((bitField0_ & 0x00040000) != 0)) {
          rewardDTO_ = java.util.Collections.unmodifiableList(rewardDTO_);
          bitField0_ = (bitField0_ & ~0x00040000);
        }
        result.rewardDTO_ = rewardDTO_;
      } else {
        result.rewardDTO_ = rewardDTOBuilder_.build();
      }
      if (subTaskDTOBuilder_ == null) {
        if (((bitField0_ & 0x00080000) != 0)) {
          subTaskDTO_ = java.util.Collections.unmodifiableList(subTaskDTO_);
          bitField0_ = (bitField0_ & ~0x00080000);
        }
        result.subTaskDTO_ = subTaskDTO_;
      } else {
        result.subTaskDTO_ = subTaskDTOBuilder_.build();
      }
    }

    private void buildPartial0(com.kikitrade.activity.facade.taskv2.TaskDTO result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.title_ = title_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.desc_ = desc_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.labelContent_ = labelContent_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.labelColor_ = labelColor_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.startTime_ = startTime_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.endTime_ = endTime_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.listImage_ = listImage_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.detailImage_ = detailImage_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.shareImage_ = shareImage_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.shareContent_ = shareContent_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.cycle_ = cycle_;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.limit_ = limit_;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.limitVip_ = limitVip_;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.rewardFrequency_ = rewardFrequency_;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.progressType_ = progressType_;
      }
      if (((from_bitField0_ & 0x00010000) != 0)) {
        result.rewardForm_ = rewardForm_;
      }
      if (((from_bitField0_ & 0x00020000) != 0)) {
        result.provideType_ = provideType_;
      }
      if (((from_bitField0_ & 0x00100000) != 0)) {
        result.status_ = status_;
      }
      if (((from_bitField0_ & 0x00200000) != 0)) {
        result.saasId_ = saasId_;
      }
      if (((from_bitField0_ & 0x00400000) != 0)) {
        result.code_ = code_;
      }
      if (((from_bitField0_ & 0x00800000) != 0)) {
        result.twitterSubject_ = twitterSubject_;
      }
      if (((from_bitField0_ & 0x01000000) != 0)) {
        result.twitterTo_ = twitterTo_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.taskv2.TaskDTO) {
        return mergeFrom((com.kikitrade.activity.facade.taskv2.TaskDTO)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.taskv2.TaskDTO other) {
      if (other == com.kikitrade.activity.facade.taskv2.TaskDTO.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getTitle().isEmpty()) {
        title_ = other.title_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getDesc().isEmpty()) {
        desc_ = other.desc_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getLabelContent().isEmpty()) {
        labelContent_ = other.labelContent_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (!other.getLabelColor().isEmpty()) {
        labelColor_ = other.labelColor_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (!other.getStartTime().isEmpty()) {
        startTime_ = other.startTime_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (!other.getEndTime().isEmpty()) {
        endTime_ = other.endTime_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (!other.getListImage().isEmpty()) {
        listImage_ = other.listImage_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (!other.getDetailImage().isEmpty()) {
        detailImage_ = other.detailImage_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (!other.getShareImage().isEmpty()) {
        shareImage_ = other.shareImage_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (!other.getShareContent().isEmpty()) {
        shareContent_ = other.shareContent_;
        bitField0_ |= 0x00000400;
        onChanged();
      }
      if (other.cycle_ != 0) {
        setCycleValue(other.getCycleValue());
      }
      if (other.getLimit() != 0) {
        setLimit(other.getLimit());
      }
      if (other.getLimitVip() != 0) {
        setLimitVip(other.getLimitVip());
      }
      if (other.getRewardFrequency() != 0) {
        setRewardFrequency(other.getRewardFrequency());
      }
      if (other.progressType_ != 0) {
        setProgressTypeValue(other.getProgressTypeValue());
      }
      if (other.rewardForm_ != 0) {
        setRewardFormValue(other.getRewardFormValue());
      }
      if (other.provideType_ != 0) {
        setProvideTypeValue(other.getProvideTypeValue());
      }
      if (rewardDTOBuilder_ == null) {
        if (!other.rewardDTO_.isEmpty()) {
          if (rewardDTO_.isEmpty()) {
            rewardDTO_ = other.rewardDTO_;
            bitField0_ = (bitField0_ & ~0x00040000);
          } else {
            ensureRewardDTOIsMutable();
            rewardDTO_.addAll(other.rewardDTO_);
          }
          onChanged();
        }
      } else {
        if (!other.rewardDTO_.isEmpty()) {
          if (rewardDTOBuilder_.isEmpty()) {
            rewardDTOBuilder_.dispose();
            rewardDTOBuilder_ = null;
            rewardDTO_ = other.rewardDTO_;
            bitField0_ = (bitField0_ & ~0x00040000);
            rewardDTOBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getRewardDTOFieldBuilder() : null;
          } else {
            rewardDTOBuilder_.addAllMessages(other.rewardDTO_);
          }
        }
      }
      if (subTaskDTOBuilder_ == null) {
        if (!other.subTaskDTO_.isEmpty()) {
          if (subTaskDTO_.isEmpty()) {
            subTaskDTO_ = other.subTaskDTO_;
            bitField0_ = (bitField0_ & ~0x00080000);
          } else {
            ensureSubTaskDTOIsMutable();
            subTaskDTO_.addAll(other.subTaskDTO_);
          }
          onChanged();
        }
      } else {
        if (!other.subTaskDTO_.isEmpty()) {
          if (subTaskDTOBuilder_.isEmpty()) {
            subTaskDTOBuilder_.dispose();
            subTaskDTOBuilder_ = null;
            subTaskDTO_ = other.subTaskDTO_;
            bitField0_ = (bitField0_ & ~0x00080000);
            subTaskDTOBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getSubTaskDTOFieldBuilder() : null;
          } else {
            subTaskDTOBuilder_.addAllMessages(other.subTaskDTO_);
          }
        }
      }
      if (other.status_ != 0) {
        setStatusValue(other.getStatusValue());
      }
      if (!other.getSaasId().isEmpty()) {
        saasId_ = other.saasId_;
        bitField0_ |= 0x00200000;
        onChanged();
      }
      if (!other.getCode().isEmpty()) {
        code_ = other.code_;
        bitField0_ |= 0x00400000;
        onChanged();
      }
      if (!other.getTwitterSubject().isEmpty()) {
        twitterSubject_ = other.twitterSubject_;
        bitField0_ |= 0x00800000;
        onChanged();
      }
      if (!other.getTwitterTo().isEmpty()) {
        twitterTo_ = other.twitterTo_;
        bitField0_ |= 0x01000000;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              title_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              desc_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              labelContent_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              labelColor_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              startTime_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              endTime_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              listImage_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 74: {
              detailImage_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 82: {
              shareImage_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 90: {
              shareContent_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 96: {
              cycle_ = input.readEnum();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            case 104: {
              limit_ = input.readInt32();
              bitField0_ |= 0x00001000;
              break;
            } // case 104
            case 112: {
              limitVip_ = input.readInt32();
              bitField0_ |= 0x00002000;
              break;
            } // case 112
            case 120: {
              rewardFrequency_ = input.readInt32();
              bitField0_ |= 0x00004000;
              break;
            } // case 120
            case 128: {
              progressType_ = input.readEnum();
              bitField0_ |= 0x00008000;
              break;
            } // case 128
            case 136: {
              rewardForm_ = input.readEnum();
              bitField0_ |= 0x00010000;
              break;
            } // case 136
            case 144: {
              provideType_ = input.readEnum();
              bitField0_ |= 0x00020000;
              break;
            } // case 144
            case 154: {
              com.kikitrade.activity.facade.taskv2.RewardDTO m =
                  input.readMessage(
                      com.kikitrade.activity.facade.taskv2.RewardDTO.parser(),
                      extensionRegistry);
              if (rewardDTOBuilder_ == null) {
                ensureRewardDTOIsMutable();
                rewardDTO_.add(m);
              } else {
                rewardDTOBuilder_.addMessage(m);
              }
              break;
            } // case 154
            case 162: {
              com.kikitrade.activity.facade.taskv2.SubTaskDTO m =
                  input.readMessage(
                      com.kikitrade.activity.facade.taskv2.SubTaskDTO.parser(),
                      extensionRegistry);
              if (subTaskDTOBuilder_ == null) {
                ensureSubTaskDTOIsMutable();
                subTaskDTO_.add(m);
              } else {
                subTaskDTOBuilder_.addMessage(m);
              }
              break;
            } // case 162
            case 168: {
              status_ = input.readEnum();
              bitField0_ |= 0x00100000;
              break;
            } // case 168
            case 178: {
              saasId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00200000;
              break;
            } // case 178
            case 186: {
              code_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00400000;
              break;
            } // case 186
            case 194: {
              twitterSubject_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00800000;
              break;
            } // case 194
            case 202: {
              twitterTo_ = input.readStringRequireUtf8();
              bitField0_ |= 0x01000000;
              break;
            } // case 202
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <pre>
     *任务id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *任务id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *任务id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *任务id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *任务id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object title_ = "";
    /**
     * <pre>
     *quest name
     * </pre>
     *
     * <code>string title = 2;</code>
     * @return The title.
     */
    public java.lang.String getTitle() {
      java.lang.Object ref = title_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        title_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *quest name
     * </pre>
     *
     * <code>string title = 2;</code>
     * @return The bytes for title.
     */
    public com.google.protobuf.ByteString
        getTitleBytes() {
      java.lang.Object ref = title_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        title_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *quest name
     * </pre>
     *
     * <code>string title = 2;</code>
     * @param value The title to set.
     * @return This builder for chaining.
     */
    public Builder setTitle(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      title_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *quest name
     * </pre>
     *
     * <code>string title = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearTitle() {
      title_ = getDefaultInstance().getTitle();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *quest name
     * </pre>
     *
     * <code>string title = 2;</code>
     * @param value The bytes for title to set.
     * @return This builder for chaining.
     */
    public Builder setTitleBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      title_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object desc_ = "";
    /**
     * <pre>
     *quest description
     * </pre>
     *
     * <code>string desc = 3;</code>
     * @return The desc.
     */
    public java.lang.String getDesc() {
      java.lang.Object ref = desc_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        desc_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *quest description
     * </pre>
     *
     * <code>string desc = 3;</code>
     * @return The bytes for desc.
     */
    public com.google.protobuf.ByteString
        getDescBytes() {
      java.lang.Object ref = desc_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        desc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *quest description
     * </pre>
     *
     * <code>string desc = 3;</code>
     * @param value The desc to set.
     * @return This builder for chaining.
     */
    public Builder setDesc(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      desc_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *quest description
     * </pre>
     *
     * <code>string desc = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearDesc() {
      desc_ = getDefaultInstance().getDesc();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *quest description
     * </pre>
     *
     * <code>string desc = 3;</code>
     * @param value The bytes for desc to set.
     * @return This builder for chaining.
     */
    public Builder setDescBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      desc_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object labelContent_ = "";
    /**
     * <pre>
     *label content
     * </pre>
     *
     * <code>string labelContent = 4;</code>
     * @return The labelContent.
     */
    public java.lang.String getLabelContent() {
      java.lang.Object ref = labelContent_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        labelContent_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *label content
     * </pre>
     *
     * <code>string labelContent = 4;</code>
     * @return The bytes for labelContent.
     */
    public com.google.protobuf.ByteString
        getLabelContentBytes() {
      java.lang.Object ref = labelContent_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        labelContent_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *label content
     * </pre>
     *
     * <code>string labelContent = 4;</code>
     * @param value The labelContent to set.
     * @return This builder for chaining.
     */
    public Builder setLabelContent(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      labelContent_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *label content
     * </pre>
     *
     * <code>string labelContent = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearLabelContent() {
      labelContent_ = getDefaultInstance().getLabelContent();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *label content
     * </pre>
     *
     * <code>string labelContent = 4;</code>
     * @param value The bytes for labelContent to set.
     * @return This builder for chaining.
     */
    public Builder setLabelContentBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      labelContent_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object labelColor_ = "";
    /**
     * <pre>
     *label color
     * </pre>
     *
     * <code>string labelColor = 5;</code>
     * @return The labelColor.
     */
    public java.lang.String getLabelColor() {
      java.lang.Object ref = labelColor_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        labelColor_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *label color
     * </pre>
     *
     * <code>string labelColor = 5;</code>
     * @return The bytes for labelColor.
     */
    public com.google.protobuf.ByteString
        getLabelColorBytes() {
      java.lang.Object ref = labelColor_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        labelColor_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *label color
     * </pre>
     *
     * <code>string labelColor = 5;</code>
     * @param value The labelColor to set.
     * @return This builder for chaining.
     */
    public Builder setLabelColor(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      labelColor_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *label color
     * </pre>
     *
     * <code>string labelColor = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearLabelColor() {
      labelColor_ = getDefaultInstance().getLabelColor();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *label color
     * </pre>
     *
     * <code>string labelColor = 5;</code>
     * @param value The bytes for labelColor to set.
     * @return This builder for chaining.
     */
    public Builder setLabelColorBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      labelColor_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private java.lang.Object startTime_ = "";
    /**
     * <pre>
     *start time
     * </pre>
     *
     * <code>string startTime = 6;</code>
     * @return The startTime.
     */
    public java.lang.String getStartTime() {
      java.lang.Object ref = startTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        startTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *start time
     * </pre>
     *
     * <code>string startTime = 6;</code>
     * @return The bytes for startTime.
     */
    public com.google.protobuf.ByteString
        getStartTimeBytes() {
      java.lang.Object ref = startTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        startTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *start time
     * </pre>
     *
     * <code>string startTime = 6;</code>
     * @param value The startTime to set.
     * @return This builder for chaining.
     */
    public Builder setStartTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      startTime_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *start time
     * </pre>
     *
     * <code>string startTime = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearStartTime() {
      startTime_ = getDefaultInstance().getStartTime();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *start time
     * </pre>
     *
     * <code>string startTime = 6;</code>
     * @param value The bytes for startTime to set.
     * @return This builder for chaining.
     */
    public Builder setStartTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      startTime_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object endTime_ = "";
    /**
     * <pre>
     * </pre>
     *
     * <code>string endTime = 7;</code>
     * @return The endTime.
     */
    public java.lang.String getEndTime() {
      java.lang.Object ref = endTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        endTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>string endTime = 7;</code>
     * @return The bytes for endTime.
     */
    public com.google.protobuf.ByteString
        getEndTimeBytes() {
      java.lang.Object ref = endTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        endTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>string endTime = 7;</code>
     * @param value The endTime to set.
     * @return This builder for chaining.
     */
    public Builder setEndTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      endTime_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>string endTime = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearEndTime() {
      endTime_ = getDefaultInstance().getEndTime();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>string endTime = 7;</code>
     * @param value The bytes for endTime to set.
     * @return This builder for chaining.
     */
    public Builder setEndTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      endTime_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object listImage_ = "";
    /**
     * <code>string listImage = 8;</code>
     * @return The listImage.
     */
    public java.lang.String getListImage() {
      java.lang.Object ref = listImage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        listImage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string listImage = 8;</code>
     * @return The bytes for listImage.
     */
    public com.google.protobuf.ByteString
        getListImageBytes() {
      java.lang.Object ref = listImage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        listImage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string listImage = 8;</code>
     * @param value The listImage to set.
     * @return This builder for chaining.
     */
    public Builder setListImage(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      listImage_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>string listImage = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearListImage() {
      listImage_ = getDefaultInstance().getListImage();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <code>string listImage = 8;</code>
     * @param value The bytes for listImage to set.
     * @return This builder for chaining.
     */
    public Builder setListImageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      listImage_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private java.lang.Object detailImage_ = "";
    /**
     * <code>string detailImage = 9;</code>
     * @return The detailImage.
     */
    public java.lang.String getDetailImage() {
      java.lang.Object ref = detailImage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        detailImage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string detailImage = 9;</code>
     * @return The bytes for detailImage.
     */
    public com.google.protobuf.ByteString
        getDetailImageBytes() {
      java.lang.Object ref = detailImage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        detailImage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string detailImage = 9;</code>
     * @param value The detailImage to set.
     * @return This builder for chaining.
     */
    public Builder setDetailImage(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      detailImage_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>string detailImage = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearDetailImage() {
      detailImage_ = getDefaultInstance().getDetailImage();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <code>string detailImage = 9;</code>
     * @param value The bytes for detailImage to set.
     * @return This builder for chaining.
     */
    public Builder setDetailImageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      detailImage_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private java.lang.Object shareImage_ = "";
    /**
     * <code>string shareImage = 10;</code>
     * @return The shareImage.
     */
    public java.lang.String getShareImage() {
      java.lang.Object ref = shareImage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        shareImage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string shareImage = 10;</code>
     * @return The bytes for shareImage.
     */
    public com.google.protobuf.ByteString
        getShareImageBytes() {
      java.lang.Object ref = shareImage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        shareImage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string shareImage = 10;</code>
     * @param value The shareImage to set.
     * @return This builder for chaining.
     */
    public Builder setShareImage(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      shareImage_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>string shareImage = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearShareImage() {
      shareImage_ = getDefaultInstance().getShareImage();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <code>string shareImage = 10;</code>
     * @param value The bytes for shareImage to set.
     * @return This builder for chaining.
     */
    public Builder setShareImageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      shareImage_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private java.lang.Object shareContent_ = "";
    /**
     * <pre>
     *分享文案
     * </pre>
     *
     * <code>string shareContent = 11;</code>
     * @return The shareContent.
     */
    public java.lang.String getShareContent() {
      java.lang.Object ref = shareContent_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        shareContent_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *分享文案
     * </pre>
     *
     * <code>string shareContent = 11;</code>
     * @return The bytes for shareContent.
     */
    public com.google.protobuf.ByteString
        getShareContentBytes() {
      java.lang.Object ref = shareContent_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        shareContent_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *分享文案
     * </pre>
     *
     * <code>string shareContent = 11;</code>
     * @param value The shareContent to set.
     * @return This builder for chaining.
     */
    public Builder setShareContent(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      shareContent_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *分享文案
     * </pre>
     *
     * <code>string shareContent = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearShareContent() {
      shareContent_ = getDefaultInstance().getShareContent();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *分享文案
     * </pre>
     *
     * <code>string shareContent = 11;</code>
     * @param value The bytes for shareContent to set.
     * @return This builder for chaining.
     */
    public Builder setShareContentBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      shareContent_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }

    private int cycle_ = 0;
    /**
     * <pre>
     *quest duration type
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.TaskCycle cycle = 12;</code>
     * @return The enum numeric value on the wire for cycle.
     */
    @java.lang.Override public int getCycleValue() {
      return cycle_;
    }
    /**
     * <pre>
     *quest duration type
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.TaskCycle cycle = 12;</code>
     * @param value The enum numeric value on the wire for cycle to set.
     * @return This builder for chaining.
     */
    public Builder setCycleValue(int value) {
      cycle_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *quest duration type
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.TaskCycle cycle = 12;</code>
     * @return The cycle.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.taskv2.TaskCycle getCycle() {
      com.kikitrade.activity.facade.taskv2.TaskCycle result = com.kikitrade.activity.facade.taskv2.TaskCycle.forNumber(cycle_);
      return result == null ? com.kikitrade.activity.facade.taskv2.TaskCycle.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *quest duration type
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.TaskCycle cycle = 12;</code>
     * @param value The cycle to set.
     * @return This builder for chaining.
     */
    public Builder setCycle(com.kikitrade.activity.facade.taskv2.TaskCycle value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000800;
      cycle_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *quest duration type
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.TaskCycle cycle = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearCycle() {
      bitField0_ = (bitField0_ & ~0x00000800);
      cycle_ = 0;
      onChanged();
      return this;
    }

    private int limit_ ;
    /**
     * <pre>
     *周期内完成任务次数上限，默认-1，表示不限制
     * </pre>
     *
     * <code>int32 limit = 13;</code>
     * @return The limit.
     */
    @java.lang.Override
    public int getLimit() {
      return limit_;
    }
    /**
     * <pre>
     *周期内完成任务次数上限，默认-1，表示不限制
     * </pre>
     *
     * <code>int32 limit = 13;</code>
     * @param value The limit to set.
     * @return This builder for chaining.
     */
    public Builder setLimit(int value) {

      limit_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *周期内完成任务次数上限，默认-1，表示不限制
     * </pre>
     *
     * <code>int32 limit = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearLimit() {
      bitField0_ = (bitField0_ & ~0x00001000);
      limit_ = 0;
      onChanged();
      return this;
    }

    private int limitVip_ ;
    /**
     * <pre>
     *vip用户周期内完成任务次数上限，默认-1，表示不限制
     * </pre>
     *
     * <code>int32 limitVip = 14;</code>
     * @return The limitVip.
     */
    @java.lang.Override
    public int getLimitVip() {
      return limitVip_;
    }
    /**
     * <pre>
     *vip用户周期内完成任务次数上限，默认-1，表示不限制
     * </pre>
     *
     * <code>int32 limitVip = 14;</code>
     * @param value The limitVip to set.
     * @return This builder for chaining.
     */
    public Builder setLimitVip(int value) {

      limitVip_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *vip用户周期内完成任务次数上限，默认-1，表示不限制
     * </pre>
     *
     * <code>int32 limitVip = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearLimitVip() {
      bitField0_ = (bitField0_ & ~0x00002000);
      limitVip_ = 0;
      onChanged();
      return this;
    }

    private int rewardFrequency_ ;
    /**
     * <pre>
     *奖励频率，每rewardFrequency发次奖励
     * </pre>
     *
     * <code>int32 rewardFrequency = 15;</code>
     * @return The rewardFrequency.
     */
    @java.lang.Override
    public int getRewardFrequency() {
      return rewardFrequency_;
    }
    /**
     * <pre>
     *奖励频率，每rewardFrequency发次奖励
     * </pre>
     *
     * <code>int32 rewardFrequency = 15;</code>
     * @param value The rewardFrequency to set.
     * @return This builder for chaining.
     */
    public Builder setRewardFrequency(int value) {

      rewardFrequency_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖励频率，每rewardFrequency发次奖励
     * </pre>
     *
     * <code>int32 rewardFrequency = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewardFrequency() {
      bitField0_ = (bitField0_ & ~0x00004000);
      rewardFrequency_ = 0;
      onChanged();
      return this;
    }

    private int progressType_ = 0;
    /**
     * <pre>
     *任务进度计算方式
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.ProgressType progressType = 16;</code>
     * @return The enum numeric value on the wire for progressType.
     */
    @java.lang.Override public int getProgressTypeValue() {
      return progressType_;
    }
    /**
     * <pre>
     *任务进度计算方式
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.ProgressType progressType = 16;</code>
     * @param value The enum numeric value on the wire for progressType to set.
     * @return This builder for chaining.
     */
    public Builder setProgressTypeValue(int value) {
      progressType_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *任务进度计算方式
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.ProgressType progressType = 16;</code>
     * @return The progressType.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.taskv2.ProgressType getProgressType() {
      com.kikitrade.activity.facade.taskv2.ProgressType result = com.kikitrade.activity.facade.taskv2.ProgressType.forNumber(progressType_);
      return result == null ? com.kikitrade.activity.facade.taskv2.ProgressType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *任务进度计算方式
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.ProgressType progressType = 16;</code>
     * @param value The progressType to set.
     * @return This builder for chaining.
     */
    public Builder setProgressType(com.kikitrade.activity.facade.taskv2.ProgressType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00008000;
      progressType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *任务进度计算方式
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.ProgressType progressType = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearProgressType() {
      bitField0_ = (bitField0_ & ~0x00008000);
      progressType_ = 0;
      onChanged();
      return this;
    }

    private int rewardForm_ = 0;
    /**
     * <pre>
     *奖品筛选方式
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.RewardForm rewardForm = 17;</code>
     * @return The enum numeric value on the wire for rewardForm.
     */
    @java.lang.Override public int getRewardFormValue() {
      return rewardForm_;
    }
    /**
     * <pre>
     *奖品筛选方式
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.RewardForm rewardForm = 17;</code>
     * @param value The enum numeric value on the wire for rewardForm to set.
     * @return This builder for chaining.
     */
    public Builder setRewardFormValue(int value) {
      rewardForm_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖品筛选方式
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.RewardForm rewardForm = 17;</code>
     * @return The rewardForm.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.taskv2.RewardForm getRewardForm() {
      com.kikitrade.activity.facade.taskv2.RewardForm result = com.kikitrade.activity.facade.taskv2.RewardForm.forNumber(rewardForm_);
      return result == null ? com.kikitrade.activity.facade.taskv2.RewardForm.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *奖品筛选方式
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.RewardForm rewardForm = 17;</code>
     * @param value The rewardForm to set.
     * @return This builder for chaining.
     */
    public Builder setRewardForm(com.kikitrade.activity.facade.taskv2.RewardForm value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00010000;
      rewardForm_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖品筛选方式
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.RewardForm rewardForm = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewardForm() {
      bitField0_ = (bitField0_ & ~0x00010000);
      rewardForm_ = 0;
      onChanged();
      return this;
    }

    private int provideType_ = 0;
    /**
     * <pre>
     *发放方式
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.ProvideType provideType = 18;</code>
     * @return The enum numeric value on the wire for provideType.
     */
    @java.lang.Override public int getProvideTypeValue() {
      return provideType_;
    }
    /**
     * <pre>
     *发放方式
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.ProvideType provideType = 18;</code>
     * @param value The enum numeric value on the wire for provideType to set.
     * @return This builder for chaining.
     */
    public Builder setProvideTypeValue(int value) {
      provideType_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *发放方式
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.ProvideType provideType = 18;</code>
     * @return The provideType.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.taskv2.ProvideType getProvideType() {
      com.kikitrade.activity.facade.taskv2.ProvideType result = com.kikitrade.activity.facade.taskv2.ProvideType.forNumber(provideType_);
      return result == null ? com.kikitrade.activity.facade.taskv2.ProvideType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *发放方式
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.ProvideType provideType = 18;</code>
     * @param value The provideType to set.
     * @return This builder for chaining.
     */
    public Builder setProvideType(com.kikitrade.activity.facade.taskv2.ProvideType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00020000;
      provideType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *发放方式
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.taskv2.ProvideType provideType = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearProvideType() {
      bitField0_ = (bitField0_ & ~0x00020000);
      provideType_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<com.kikitrade.activity.facade.taskv2.RewardDTO> rewardDTO_ =
      java.util.Collections.emptyList();
    private void ensureRewardDTOIsMutable() {
      if (!((bitField0_ & 0x00040000) != 0)) {
        rewardDTO_ = new java.util.ArrayList<com.kikitrade.activity.facade.taskv2.RewardDTO>(rewardDTO_);
        bitField0_ |= 0x00040000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.taskv2.RewardDTO, com.kikitrade.activity.facade.taskv2.RewardDTO.Builder, com.kikitrade.activity.facade.taskv2.RewardDTOOrBuilder> rewardDTOBuilder_;

    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.taskv2.RewardDTO> getRewardDTOList() {
      if (rewardDTOBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rewardDTO_);
      } else {
        return rewardDTOBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public int getRewardDTOCount() {
      if (rewardDTOBuilder_ == null) {
        return rewardDTO_.size();
      } else {
        return rewardDTOBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public com.kikitrade.activity.facade.taskv2.RewardDTO getRewardDTO(int index) {
      if (rewardDTOBuilder_ == null) {
        return rewardDTO_.get(index);
      } else {
        return rewardDTOBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public Builder setRewardDTO(
        int index, com.kikitrade.activity.facade.taskv2.RewardDTO value) {
      if (rewardDTOBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardDTOIsMutable();
        rewardDTO_.set(index, value);
        onChanged();
      } else {
        rewardDTOBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public Builder setRewardDTO(
        int index, com.kikitrade.activity.facade.taskv2.RewardDTO.Builder builderForValue) {
      if (rewardDTOBuilder_ == null) {
        ensureRewardDTOIsMutable();
        rewardDTO_.set(index, builderForValue.build());
        onChanged();
      } else {
        rewardDTOBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public Builder addRewardDTO(com.kikitrade.activity.facade.taskv2.RewardDTO value) {
      if (rewardDTOBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardDTOIsMutable();
        rewardDTO_.add(value);
        onChanged();
      } else {
        rewardDTOBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public Builder addRewardDTO(
        int index, com.kikitrade.activity.facade.taskv2.RewardDTO value) {
      if (rewardDTOBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardDTOIsMutable();
        rewardDTO_.add(index, value);
        onChanged();
      } else {
        rewardDTOBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public Builder addRewardDTO(
        com.kikitrade.activity.facade.taskv2.RewardDTO.Builder builderForValue) {
      if (rewardDTOBuilder_ == null) {
        ensureRewardDTOIsMutable();
        rewardDTO_.add(builderForValue.build());
        onChanged();
      } else {
        rewardDTOBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public Builder addRewardDTO(
        int index, com.kikitrade.activity.facade.taskv2.RewardDTO.Builder builderForValue) {
      if (rewardDTOBuilder_ == null) {
        ensureRewardDTOIsMutable();
        rewardDTO_.add(index, builderForValue.build());
        onChanged();
      } else {
        rewardDTOBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public Builder addAllRewardDTO(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.taskv2.RewardDTO> values) {
      if (rewardDTOBuilder_ == null) {
        ensureRewardDTOIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rewardDTO_);
        onChanged();
      } else {
        rewardDTOBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public Builder clearRewardDTO() {
      if (rewardDTOBuilder_ == null) {
        rewardDTO_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00040000);
        onChanged();
      } else {
        rewardDTOBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public Builder removeRewardDTO(int index) {
      if (rewardDTOBuilder_ == null) {
        ensureRewardDTOIsMutable();
        rewardDTO_.remove(index);
        onChanged();
      } else {
        rewardDTOBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public com.kikitrade.activity.facade.taskv2.RewardDTO.Builder getRewardDTOBuilder(
        int index) {
      return getRewardDTOFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public com.kikitrade.activity.facade.taskv2.RewardDTOOrBuilder getRewardDTOOrBuilder(
        int index) {
      if (rewardDTOBuilder_ == null) {
        return rewardDTO_.get(index);  } else {
        return rewardDTOBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.taskv2.RewardDTOOrBuilder> 
         getRewardDTOOrBuilderList() {
      if (rewardDTOBuilder_ != null) {
        return rewardDTOBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rewardDTO_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public com.kikitrade.activity.facade.taskv2.RewardDTO.Builder addRewardDTOBuilder() {
      return getRewardDTOFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.taskv2.RewardDTO.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public com.kikitrade.activity.facade.taskv2.RewardDTO.Builder addRewardDTOBuilder(
        int index) {
      return getRewardDTOFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.taskv2.RewardDTO.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.RewardDTO rewardDTO = 19;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.taskv2.RewardDTO.Builder> 
         getRewardDTOBuilderList() {
      return getRewardDTOFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.taskv2.RewardDTO, com.kikitrade.activity.facade.taskv2.RewardDTO.Builder, com.kikitrade.activity.facade.taskv2.RewardDTOOrBuilder> 
        getRewardDTOFieldBuilder() {
      if (rewardDTOBuilder_ == null) {
        rewardDTOBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.taskv2.RewardDTO, com.kikitrade.activity.facade.taskv2.RewardDTO.Builder, com.kikitrade.activity.facade.taskv2.RewardDTOOrBuilder>(
                rewardDTO_,
                ((bitField0_ & 0x00040000) != 0),
                getParentForChildren(),
                isClean());
        rewardDTO_ = null;
      }
      return rewardDTOBuilder_;
    }

    private java.util.List<com.kikitrade.activity.facade.taskv2.SubTaskDTO> subTaskDTO_ =
      java.util.Collections.emptyList();
    private void ensureSubTaskDTOIsMutable() {
      if (!((bitField0_ & 0x00080000) != 0)) {
        subTaskDTO_ = new java.util.ArrayList<com.kikitrade.activity.facade.taskv2.SubTaskDTO>(subTaskDTO_);
        bitField0_ |= 0x00080000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.taskv2.SubTaskDTO, com.kikitrade.activity.facade.taskv2.SubTaskDTO.Builder, com.kikitrade.activity.facade.taskv2.SubTaskDTOOrBuilder> subTaskDTOBuilder_;

    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.taskv2.SubTaskDTO> getSubTaskDTOList() {
      if (subTaskDTOBuilder_ == null) {
        return java.util.Collections.unmodifiableList(subTaskDTO_);
      } else {
        return subTaskDTOBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public int getSubTaskDTOCount() {
      if (subTaskDTOBuilder_ == null) {
        return subTaskDTO_.size();
      } else {
        return subTaskDTOBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public com.kikitrade.activity.facade.taskv2.SubTaskDTO getSubTaskDTO(int index) {
      if (subTaskDTOBuilder_ == null) {
        return subTaskDTO_.get(index);
      } else {
        return subTaskDTOBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public Builder setSubTaskDTO(
        int index, com.kikitrade.activity.facade.taskv2.SubTaskDTO value) {
      if (subTaskDTOBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSubTaskDTOIsMutable();
        subTaskDTO_.set(index, value);
        onChanged();
      } else {
        subTaskDTOBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public Builder setSubTaskDTO(
        int index, com.kikitrade.activity.facade.taskv2.SubTaskDTO.Builder builderForValue) {
      if (subTaskDTOBuilder_ == null) {
        ensureSubTaskDTOIsMutable();
        subTaskDTO_.set(index, builderForValue.build());
        onChanged();
      } else {
        subTaskDTOBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public Builder addSubTaskDTO(com.kikitrade.activity.facade.taskv2.SubTaskDTO value) {
      if (subTaskDTOBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSubTaskDTOIsMutable();
        subTaskDTO_.add(value);
        onChanged();
      } else {
        subTaskDTOBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public Builder addSubTaskDTO(
        int index, com.kikitrade.activity.facade.taskv2.SubTaskDTO value) {
      if (subTaskDTOBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSubTaskDTOIsMutable();
        subTaskDTO_.add(index, value);
        onChanged();
      } else {
        subTaskDTOBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public Builder addSubTaskDTO(
        com.kikitrade.activity.facade.taskv2.SubTaskDTO.Builder builderForValue) {
      if (subTaskDTOBuilder_ == null) {
        ensureSubTaskDTOIsMutable();
        subTaskDTO_.add(builderForValue.build());
        onChanged();
      } else {
        subTaskDTOBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public Builder addSubTaskDTO(
        int index, com.kikitrade.activity.facade.taskv2.SubTaskDTO.Builder builderForValue) {
      if (subTaskDTOBuilder_ == null) {
        ensureSubTaskDTOIsMutable();
        subTaskDTO_.add(index, builderForValue.build());
        onChanged();
      } else {
        subTaskDTOBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public Builder addAllSubTaskDTO(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.taskv2.SubTaskDTO> values) {
      if (subTaskDTOBuilder_ == null) {
        ensureSubTaskDTOIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, subTaskDTO_);
        onChanged();
      } else {
        subTaskDTOBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public Builder clearSubTaskDTO() {
      if (subTaskDTOBuilder_ == null) {
        subTaskDTO_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00080000);
        onChanged();
      } else {
        subTaskDTOBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public Builder removeSubTaskDTO(int index) {
      if (subTaskDTOBuilder_ == null) {
        ensureSubTaskDTOIsMutable();
        subTaskDTO_.remove(index);
        onChanged();
      } else {
        subTaskDTOBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public com.kikitrade.activity.facade.taskv2.SubTaskDTO.Builder getSubTaskDTOBuilder(
        int index) {
      return getSubTaskDTOFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public com.kikitrade.activity.facade.taskv2.SubTaskDTOOrBuilder getSubTaskDTOOrBuilder(
        int index) {
      if (subTaskDTOBuilder_ == null) {
        return subTaskDTO_.get(index);  } else {
        return subTaskDTOBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.taskv2.SubTaskDTOOrBuilder> 
         getSubTaskDTOOrBuilderList() {
      if (subTaskDTOBuilder_ != null) {
        return subTaskDTOBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(subTaskDTO_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public com.kikitrade.activity.facade.taskv2.SubTaskDTO.Builder addSubTaskDTOBuilder() {
      return getSubTaskDTOFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.taskv2.SubTaskDTO.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public com.kikitrade.activity.facade.taskv2.SubTaskDTO.Builder addSubTaskDTOBuilder(
        int index) {
      return getSubTaskDTOFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.taskv2.SubTaskDTO.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.taskv2.SubTaskDTO subTaskDTO = 20;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.taskv2.SubTaskDTO.Builder> 
         getSubTaskDTOBuilderList() {
      return getSubTaskDTOFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.taskv2.SubTaskDTO, com.kikitrade.activity.facade.taskv2.SubTaskDTO.Builder, com.kikitrade.activity.facade.taskv2.SubTaskDTOOrBuilder> 
        getSubTaskDTOFieldBuilder() {
      if (subTaskDTOBuilder_ == null) {
        subTaskDTOBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.taskv2.SubTaskDTO, com.kikitrade.activity.facade.taskv2.SubTaskDTO.Builder, com.kikitrade.activity.facade.taskv2.SubTaskDTOOrBuilder>(
                subTaskDTO_,
                ((bitField0_ & 0x00080000) != 0),
                getParentForChildren(),
                isClean());
        subTaskDTO_ = null;
      }
      return subTaskDTOBuilder_;
    }

    private int status_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.taskv2.CommonStatus status = 21;</code>
     * @return The enum numeric value on the wire for status.
     */
    @java.lang.Override public int getStatusValue() {
      return status_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.taskv2.CommonStatus status = 21;</code>
     * @param value The enum numeric value on the wire for status to set.
     * @return This builder for chaining.
     */
    public Builder setStatusValue(int value) {
      status_ = value;
      bitField0_ |= 0x00100000;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.taskv2.CommonStatus status = 21;</code>
     * @return The status.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.taskv2.CommonStatus getStatus() {
      com.kikitrade.activity.facade.taskv2.CommonStatus result = com.kikitrade.activity.facade.taskv2.CommonStatus.forNumber(status_);
      return result == null ? com.kikitrade.activity.facade.taskv2.CommonStatus.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.taskv2.CommonStatus status = 21;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(com.kikitrade.activity.facade.taskv2.CommonStatus value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00100000;
      status_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.taskv2.CommonStatus status = 21;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      bitField0_ = (bitField0_ & ~0x00100000);
      status_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object saasId_ = "";
    /**
     * <code>string saasId = 22;</code>
     * @return The saasId.
     */
    public java.lang.String getSaasId() {
      java.lang.Object ref = saasId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        saasId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string saasId = 22;</code>
     * @return The bytes for saasId.
     */
    public com.google.protobuf.ByteString
        getSaasIdBytes() {
      java.lang.Object ref = saasId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        saasId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string saasId = 22;</code>
     * @param value The saasId to set.
     * @return This builder for chaining.
     */
    public Builder setSaasId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      saasId_ = value;
      bitField0_ |= 0x00200000;
      onChanged();
      return this;
    }
    /**
     * <code>string saasId = 22;</code>
     * @return This builder for chaining.
     */
    public Builder clearSaasId() {
      saasId_ = getDefaultInstance().getSaasId();
      bitField0_ = (bitField0_ & ~0x00200000);
      onChanged();
      return this;
    }
    /**
     * <code>string saasId = 22;</code>
     * @param value The bytes for saasId to set.
     * @return This builder for chaining.
     */
    public Builder setSaasIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      saasId_ = value;
      bitField0_ |= 0x00200000;
      onChanged();
      return this;
    }

    private java.lang.Object code_ = "";
    /**
     * <code>string code = 23;</code>
     * @return The code.
     */
    public java.lang.String getCode() {
      java.lang.Object ref = code_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        code_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string code = 23;</code>
     * @return The bytes for code.
     */
    public com.google.protobuf.ByteString
        getCodeBytes() {
      java.lang.Object ref = code_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        code_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string code = 23;</code>
     * @param value The code to set.
     * @return This builder for chaining.
     */
    public Builder setCode(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      code_ = value;
      bitField0_ |= 0x00400000;
      onChanged();
      return this;
    }
    /**
     * <code>string code = 23;</code>
     * @return This builder for chaining.
     */
    public Builder clearCode() {
      code_ = getDefaultInstance().getCode();
      bitField0_ = (bitField0_ & ~0x00400000);
      onChanged();
      return this;
    }
    /**
     * <code>string code = 23;</code>
     * @param value The bytes for code to set.
     * @return This builder for chaining.
     */
    public Builder setCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      code_ = value;
      bitField0_ |= 0x00400000;
      onChanged();
      return this;
    }

    private java.lang.Object twitterSubject_ = "";
    /**
     * <code>string twitterSubject = 24;</code>
     * @return The twitterSubject.
     */
    public java.lang.String getTwitterSubject() {
      java.lang.Object ref = twitterSubject_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        twitterSubject_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string twitterSubject = 24;</code>
     * @return The bytes for twitterSubject.
     */
    public com.google.protobuf.ByteString
        getTwitterSubjectBytes() {
      java.lang.Object ref = twitterSubject_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        twitterSubject_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string twitterSubject = 24;</code>
     * @param value The twitterSubject to set.
     * @return This builder for chaining.
     */
    public Builder setTwitterSubject(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      twitterSubject_ = value;
      bitField0_ |= 0x00800000;
      onChanged();
      return this;
    }
    /**
     * <code>string twitterSubject = 24;</code>
     * @return This builder for chaining.
     */
    public Builder clearTwitterSubject() {
      twitterSubject_ = getDefaultInstance().getTwitterSubject();
      bitField0_ = (bitField0_ & ~0x00800000);
      onChanged();
      return this;
    }
    /**
     * <code>string twitterSubject = 24;</code>
     * @param value The bytes for twitterSubject to set.
     * @return This builder for chaining.
     */
    public Builder setTwitterSubjectBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      twitterSubject_ = value;
      bitField0_ |= 0x00800000;
      onChanged();
      return this;
    }

    private java.lang.Object twitterTo_ = "";
    /**
     * <code>string twitterTo = 25;</code>
     * @return The twitterTo.
     */
    public java.lang.String getTwitterTo() {
      java.lang.Object ref = twitterTo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        twitterTo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string twitterTo = 25;</code>
     * @return The bytes for twitterTo.
     */
    public com.google.protobuf.ByteString
        getTwitterToBytes() {
      java.lang.Object ref = twitterTo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        twitterTo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string twitterTo = 25;</code>
     * @param value The twitterTo to set.
     * @return This builder for chaining.
     */
    public Builder setTwitterTo(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      twitterTo_ = value;
      bitField0_ |= 0x01000000;
      onChanged();
      return this;
    }
    /**
     * <code>string twitterTo = 25;</code>
     * @return This builder for chaining.
     */
    public Builder clearTwitterTo() {
      twitterTo_ = getDefaultInstance().getTwitterTo();
      bitField0_ = (bitField0_ & ~0x01000000);
      onChanged();
      return this;
    }
    /**
     * <code>string twitterTo = 25;</code>
     * @param value The bytes for twitterTo to set.
     * @return This builder for chaining.
     */
    public Builder setTwitterToBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      twitterTo_ = value;
      bitField0_ |= 0x01000000;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.taskv2.TaskDTO)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.taskv2.TaskDTO)
  private static final com.kikitrade.activity.facade.taskv2.TaskDTO DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.taskv2.TaskDTO();
  }

  public static com.kikitrade.activity.facade.taskv2.TaskDTO getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TaskDTO>
      PARSER = new com.google.protobuf.AbstractParser<TaskDTO>() {
    @java.lang.Override
    public TaskDTO parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<TaskDTO> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TaskDTO> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.taskv2.TaskDTO getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

