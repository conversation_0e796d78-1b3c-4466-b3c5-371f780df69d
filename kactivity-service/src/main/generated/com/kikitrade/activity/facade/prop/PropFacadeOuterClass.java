// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PropFacade.proto

package com.kikitrade.activity.facade.prop;

public final class PropFacadeOuterClass {
  private PropFacadeOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_prop_Prop_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_prop_Prop_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_prop_UpsertPropRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_prop_UpsertPropRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_prop_UpsertPropReply_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_prop_UpsertPropReply_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_prop_PropsQueryRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_prop_PropsQueryRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_prop_PropsQueryReply_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_prop_PropsQueryReply_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_prop_GetPropRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_prop_GetPropRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_prop_PropGetReply_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_prop_PropGetReply_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\020PropFacade.proto\022\"com.kikitrade.activi" +
      "ty.facade.prop\032\037google/protobuf/timestam" +
      "p.proto\"\371\003\n\004Prop\022\n\n\002id\030\001 \001(\005\022B\n\010gateType" +
      "\030\002 \001(\01620.com.kikitrade.activity.facade.p" +
      "rop.PropGateType\022\021\n\tgateValue\030\003 \001(\t\022\014\n\004n" +
      "ame\030\004 \001(\t\022\016\n\006remark\030\005 \001(\t\022:\n\006status\030\006 \001(" +
      "\0162*.com.kikitrade.activity.facade.prop.S" +
      "tatus\022\016\n\006images\030\007 \003(\t\022\021\n\thyperlink\030\010 \001(\t" +
      "\022H\n\013contentType\030\t \001(\01623.com.kikitrade.ac" +
      "tivity.facade.prop.PropContentType\022\021\n\tco" +
      "ntentId\030\n \001(\t\022\r\n\005point\030\013 \001(\t\022\025\n\rpublishA" +
      "mount\030\014 \001(\003\022\021\n\tavailable\030\r \001(\003\022\023\n\013redeem" +
      "Times\030\016 \001(\005\022D\n\013redeemCycle\030\017 \001(\0162/.com.k" +
      "ikitrade.activity.facade.prop.RedeemCycl" +
      "e\022\022\n\nperiodDays\030\020 \001(\005\022\014\n\004sort\030\021 \001(\005\"\363\003\n\021" +
      "UpsertPropRequest\022\n\n\002id\030\001 \001(\005\022B\n\010gateTyp" +
      "e\030\002 \001(\01620.com.kikitrade.activity.facade." +
      "prop.PropGateType\022\021\n\tgateValue\030\003 \001(\t\022\014\n\004" +
      "name\030\004 \001(\t\022\016\n\006remark\030\005 \001(\t\022:\n\006status\030\006 \001" +
      "(\0162*.com.kikitrade.activity.facade.prop." +
      "Status\022\016\n\006images\030\007 \003(\t\022\021\n\thyperlink\030\010 \001(" +
      "\t\022H\n\013contentType\030\t \001(\01623.com.kikitrade.a" +
      "ctivity.facade.prop.PropContentType\022\021\n\tc" +
      "ontentId\030\n \001(\t\022\r\n\005point\030\013 \001(\t\022\025\n\rpublish" +
      "Amount\030\014 \001(\003\022\023\n\013redeemTimes\030\r \001(\005\022D\n\013red" +
      "eemCycle\030\016 \001(\0162/.com.kikitrade.activity." +
      "facade.prop.RedeemCycle\022\022\n\nperiodDays\030\017 " +
      "\001(\005\022\014\n\004sort\030\020 \001(\005\"?\n\017UpsertPropReply\022\017\n\007" +
      "success\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\022\n\n\002id\030\003 \001" +
      "(\005\"`\n\021PropsQueryRequest\022\016\n\006saasId\030\001 \001(\t\022" +
      "\016\n\006offset\030\002 \001(\005\022\r\n\005limit\030\003 \001(\005\022\014\n\004name\030\004" +
      " \001(\t\022\016\n\006status\030\005 \001(\t\"l\n\017PropsQueryReply\022" +
      "\017\n\007success\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\0227\n\005pro" +
      "ps\030\003 \003(\0132(.com.kikitrade.activity.facade" +
      ".prop.Prop\",\n\016GetPropRequest\022\n\n\002id\030\001 \001(\t" +
      "\022\016\n\006saasId\030\002 \001(\t\"h\n\014PropGetReply\022\017\n\007succ" +
      "ess\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\0226\n\004prop\030\003 \001(\013" +
      "2(.com.kikitrade.activity.facade.prop.Pr" +
      "op*<\n\013RedeemCycle\022\t\n\005Total\020\000\022\t\n\005Daily\020\001\022" +
      "\n\n\006Weekly\020\002\022\013\n\007Monthly\020\003*\035\n\017PropContentT" +
      "ype\022\n\n\006Coupon\020\000*!\n\006Status\022\n\n\006Active\020\000\022\013\n" +
      "\007Disable\020\001*(\n\014PropGateType\022\010\n\004None\020\000\022\016\n\n" +
      "Membership\020\0012\361\002\n\nPropFacade\022x\n\nupsertPro" +
      "p\0225.com.kikitrade.activity.facade.prop.U" +
      "psertPropRequest\0323.com.kikitrade.activit" +
      "y.facade.prop.UpsertPropReply\022x\n\nqueryPr" +
      "ops\0225.com.kikitrade.activity.facade.prop" +
      ".PropsQueryRequest\0323.com.kikitrade.activ" +
      "ity.facade.prop.PropsQueryReply\022o\n\007getPr" +
      "op\0222.com.kikitrade.activity.facade.prop." +
      "GetPropRequest\0320.com.kikitrade.activity." +
      "facade.prop.PropGetReplyB&\n\"com.kikitrad" +
      "e.activity.facade.propP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.TimestampProto.getDescriptor(),
        });
    internal_static_com_kikitrade_activity_facade_prop_Prop_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_activity_facade_prop_Prop_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_prop_Prop_descriptor,
        new java.lang.String[] { "Id", "GateType", "GateValue", "Name", "Remark", "Status", "Images", "Hyperlink", "ContentType", "ContentId", "Point", "PublishAmount", "Available", "RedeemTimes", "RedeemCycle", "PeriodDays", "Sort", });
    internal_static_com_kikitrade_activity_facade_prop_UpsertPropRequest_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_activity_facade_prop_UpsertPropRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_prop_UpsertPropRequest_descriptor,
        new java.lang.String[] { "Id", "GateType", "GateValue", "Name", "Remark", "Status", "Images", "Hyperlink", "ContentType", "ContentId", "Point", "PublishAmount", "RedeemTimes", "RedeemCycle", "PeriodDays", "Sort", });
    internal_static_com_kikitrade_activity_facade_prop_UpsertPropReply_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_kikitrade_activity_facade_prop_UpsertPropReply_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_prop_UpsertPropReply_descriptor,
        new java.lang.String[] { "Success", "Message", "Id", });
    internal_static_com_kikitrade_activity_facade_prop_PropsQueryRequest_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_kikitrade_activity_facade_prop_PropsQueryRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_prop_PropsQueryRequest_descriptor,
        new java.lang.String[] { "SaasId", "Offset", "Limit", "Name", "Status", });
    internal_static_com_kikitrade_activity_facade_prop_PropsQueryReply_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_kikitrade_activity_facade_prop_PropsQueryReply_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_prop_PropsQueryReply_descriptor,
        new java.lang.String[] { "Success", "Message", "Props", });
    internal_static_com_kikitrade_activity_facade_prop_GetPropRequest_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_kikitrade_activity_facade_prop_GetPropRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_prop_GetPropRequest_descriptor,
        new java.lang.String[] { "Id", "SaasId", });
    internal_static_com_kikitrade_activity_facade_prop_PropGetReply_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_kikitrade_activity_facade_prop_PropGetReply_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_prop_PropGetReply_descriptor,
        new java.lang.String[] { "Success", "Message", "Prop", });
    com.google.protobuf.TimestampProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
