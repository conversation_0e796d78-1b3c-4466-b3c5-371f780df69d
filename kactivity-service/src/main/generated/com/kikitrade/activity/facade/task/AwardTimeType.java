// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: TaskFacade.proto

package com.kikitrade.activity.facade.task;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.task.AwardTimeType}
 */
public enum AwardTimeType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *实时发奖
   * </pre>
   *
   * <code>REALTIME = 0;</code>
   */
  REALTIME(0),
  /**
   * <pre>
   *异步发奖
   * </pre>
   *
   * <code>OFFLINE = 1;</code>
   */
  OFFLINE(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *实时发奖
   * </pre>
   *
   * <code>REALTIME = 0;</code>
   */
  public static final int REALTIME_VALUE = 0;
  /**
   * <pre>
   *异步发奖
   * </pre>
   *
   * <code>OFFLINE = 1;</code>
   */
  public static final int OFFLINE_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static AwardTimeType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static AwardTimeType forNumber(int value) {
    switch (value) {
      case 0: return REALTIME;
      case 1: return OFFLINE;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<AwardTimeType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      AwardTimeType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<AwardTimeType>() {
          public AwardTimeType findValueByNumber(int number) {
            return AwardTimeType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.task.ActivityTaskFacadeOutClass.getDescriptor().getEnumTypes().get(5);
  }

  private static final AwardTimeType[] VALUES = values();

  public static AwardTimeType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private AwardTimeType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.task.AwardTimeType)
}

