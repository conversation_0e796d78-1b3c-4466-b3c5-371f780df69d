// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: DiscountCouponFacade.proto

package com.kikitrade.activity.facade.coupon;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.coupon.DiscountType}
 */
public enum DiscountType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>RATE = 0;</code>
   */
  RATE(0),
  /**
   * <code>REDUCE = 1;</code>
   */
  REDUCE(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>RATE = 0;</code>
   */
  public static final int RATE_VALUE = 0;
  /**
   * <code>REDUCE = 1;</code>
   */
  public static final int REDUCE_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static DiscountType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static DiscountType forNumber(int value) {
    switch (value) {
      case 0: return RATE;
      case 1: return REDUCE;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<DiscountType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      DiscountType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<DiscountType>() {
          public DiscountType findValueByNumber(int number) {
            return DiscountType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.coupon.DiscountCouponFacadeOuterClass.getDescriptor().getEnumTypes().get(0);
  }

  private static final DiscountType[] VALUES = values();

  public static DiscountType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private DiscountType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.coupon.DiscountType)
}

