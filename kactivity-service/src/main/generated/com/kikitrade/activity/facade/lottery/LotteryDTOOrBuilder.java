// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Lottery.proto

package com.kikitrade.activity.facade.lottery;

public interface LotteryDTOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.lottery.LotteryDTO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *每次抽奖消耗
   * </pre>
   *
   * <code>string amount = 1;</code>
   * @return The amount.
   */
  java.lang.String getAmount();
  /**
   * <pre>
   *每次抽奖消耗
   * </pre>
   *
   * <code>string amount = 1;</code>
   * @return The bytes for amount.
   */
  com.google.protobuf.ByteString
      getAmountBytes();

  /**
   * <pre>
   *每日抽奖次数上限，小于等于0，不限制
   * </pre>
   *
   * <code>int32 limit = 2;</code>
   * @return The limit.
   */
  int getLimit();

  /**
   * <pre>
   *转盘数
   * </pre>
   *
   * <code>int32 reels = 3;</code>
   * @return The reels.
   */
  int getReels();

  /**
   * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
   */
  java.util.List<com.kikitrade.activity.facade.lottery.RewardVO> 
      getRewardVOList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
   */
  com.kikitrade.activity.facade.lottery.RewardVO getRewardVO(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
   */
  int getRewardVOCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.lottery.RewardVOOrBuilder> 
      getRewardVOOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.lottery.RewardVO rewardVO = 4;</code>
   */
  com.kikitrade.activity.facade.lottery.RewardVOOrBuilder getRewardVOOrBuilder(
      int index);

  /**
   * <code>string saasId = 5;</code>
   * @return The saasId.
   */
  java.lang.String getSaasId();
  /**
   * <code>string saasId = 5;</code>
   * @return The bytes for saasId.
   */
  com.google.protobuf.ByteString
      getSaasIdBytes();

  /**
   * <code>string id = 6;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 6;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>.com.kikitrade.activity.facade.lottery.CommonStatus status = 7;</code>
   * @return The enum numeric value on the wire for status.
   */
  int getStatusValue();
  /**
   * <code>.com.kikitrade.activity.facade.lottery.CommonStatus status = 7;</code>
   * @return The status.
   */
  com.kikitrade.activity.facade.lottery.CommonStatus getStatus();
}
