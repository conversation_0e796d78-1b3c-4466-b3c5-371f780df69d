// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StickerFacade.proto

package com.kikitrade.activity.facade.sticker;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.sticker.PriceType}
 */
public enum PriceType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>free = 0;</code>
   */
  free(0),
  /**
   * <code>gems = 1;</code>
   */
  gems(1),
  /**
   * <code>currency = 2;</code>
   */
  currency(2),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>free = 0;</code>
   */
  public static final int free_VALUE = 0;
  /**
   * <code>gems = 1;</code>
   */
  public static final int gems_VALUE = 1;
  /**
   * <code>currency = 2;</code>
   */
  public static final int currency_VALUE = 2;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static PriceType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static PriceType forNumber(int value) {
    switch (value) {
      case 0: return free;
      case 1: return gems;
      case 2: return currency;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<PriceType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      PriceType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<PriceType>() {
          public PriceType findValueByNumber(int number) {
            return PriceType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.sticker.StickerFacadeOuterClass.getDescriptor().getEnumTypes().get(0);
  }

  private static final PriceType[] VALUES = values();

  public static PriceType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private PriceType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.sticker.PriceType)
}

