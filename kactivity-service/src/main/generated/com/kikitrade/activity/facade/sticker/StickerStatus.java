// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StickerFacade.proto

package com.kikitrade.activity.facade.sticker;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.sticker.StickerStatus}
 */
public enum StickerStatus
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>active = 0;</code>
   */
  active(0),
  /**
   * <code>disable = 1;</code>
   */
  disable(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>active = 0;</code>
   */
  public static final int active_VALUE = 0;
  /**
   * <code>disable = 1;</code>
   */
  public static final int disable_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static StickerStatus valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static StickerStatus forNumber(int value) {
    switch (value) {
      case 0: return active;
      case 1: return disable;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<StickerStatus>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      StickerStatus> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<StickerStatus>() {
          public StickerStatus findValueByNumber(int number) {
            return StickerStatus.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.sticker.StickerFacadeOuterClass.getDescriptor().getEnumTypes().get(2);
  }

  private static final StickerStatus[] VALUES = values();

  public static StickerStatus valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private StickerStatus(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.sticker.StickerStatus)
}

