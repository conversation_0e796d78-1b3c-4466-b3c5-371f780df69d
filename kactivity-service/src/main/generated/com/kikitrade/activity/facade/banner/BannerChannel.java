// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Banner.proto

package com.kikitrade.activity.facade.banner;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.banner.BannerChannel}
 */
public enum BannerChannel
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>APP = 0;</code>
   */
  APP(0),
  /**
   * <code>H5 = 1;</code>
   */
  H5(1),
  /**
   * <code>PC = 2;</code>
   */
  PC(2),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>APP = 0;</code>
   */
  public static final int APP_VALUE = 0;
  /**
   * <code>H5 = 1;</code>
   */
  public static final int H5_VALUE = 1;
  /**
   * <code>PC = 2;</code>
   */
  public static final int PC_VALUE = 2;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static BannerChannel valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static BannerChannel forNumber(int value) {
    switch (value) {
      case 0: return APP;
      case 1: return H5;
      case 2: return PC;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<BannerChannel>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      BannerChannel> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<BannerChannel>() {
          public BannerChannel findValueByNumber(int number) {
            return BannerChannel.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.banner.BannerFacadeOutClass.getDescriptor().getEnumTypes().get(1);
  }

  private static final BannerChannel[] VALUES = values();

  public static BannerChannel valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private BannerChannel(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.banner.BannerChannel)
}

