// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StickerFacade.proto

package com.kikitrade.activity.facade.sticker;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.sticker.UpsertStickerRequest}
 */
public final class UpsertStickerRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.sticker.UpsertStickerRequest)
    UpsertStickerRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use UpsertStickerRequest.newBuilder() to construct.
  private UpsertStickerRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private UpsertStickerRequest() {
    id_ = "";
    celebId_ = "";
    name_ = "";
    icon_ = "";
    description_ = "";
    image_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    priceType_ = 0;
    currencyType_ = 0;
    price_ = "";
    gateType_ = 0;
    gateValue_ = "";
    status_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new UpsertStickerRequest();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.sticker.StickerFacadeOuterClass.internal_static_com_kikitrade_activity_facade_sticker_UpsertStickerRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.sticker.StickerFacadeOuterClass.internal_static_com_kikitrade_activity_facade_sticker_UpsertStickerRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.sticker.UpsertStickerRequest.class, com.kikitrade.activity.facade.sticker.UpsertStickerRequest.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CELEBID_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object celebId_ = "";
  /**
   * <pre>
   * 所属艺人id
   * </pre>
   *
   * <code>string celebId = 2;</code>
   * @return The celebId.
   */
  @java.lang.Override
  public java.lang.String getCelebId() {
    java.lang.Object ref = celebId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      celebId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 所属艺人id
   * </pre>
   *
   * <code>string celebId = 2;</code>
   * @return The bytes for celebId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCelebIdBytes() {
    java.lang.Object ref = celebId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      celebId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object name_ = "";
  /**
   * <pre>
   * 名称
   * </pre>
   *
   * <code>string name = 3;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 名称
   * </pre>
   *
   * <code>string name = 3;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ICON_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object icon_ = "";
  /**
   * <pre>
   * 图标
   * </pre>
   *
   * <code>string icon = 4;</code>
   * @return The icon.
   */
  @java.lang.Override
  public java.lang.String getIcon() {
    java.lang.Object ref = icon_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      icon_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 图标
   * </pre>
   *
   * <code>string icon = 4;</code>
   * @return The bytes for icon.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIconBytes() {
    java.lang.Object ref = icon_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      icon_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DESCRIPTION_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object description_ = "";
  /**
   * <pre>
   * 描述
   * </pre>
   *
   * <code>string description = 5;</code>
   * @return The description.
   */
  @java.lang.Override
  public java.lang.String getDescription() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      description_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 描述
   * </pre>
   *
   * <code>string description = 5;</code>
   * @return The bytes for description.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescriptionBytes() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      description_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IMAGE_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private com.google.protobuf.LazyStringArrayList image_ =
      com.google.protobuf.LazyStringArrayList.emptyList();
  /**
   * <pre>
   * 具体的表情包
   * </pre>
   *
   * <code>repeated string image = 6;</code>
   * @return A list containing the image.
   */
  public com.google.protobuf.ProtocolStringList
      getImageList() {
    return image_;
  }
  /**
   * <pre>
   * 具体的表情包
   * </pre>
   *
   * <code>repeated string image = 6;</code>
   * @return The count of image.
   */
  public int getImageCount() {
    return image_.size();
  }
  /**
   * <pre>
   * 具体的表情包
   * </pre>
   *
   * <code>repeated string image = 6;</code>
   * @param index The index of the element to return.
   * @return The image at the given index.
   */
  public java.lang.String getImage(int index) {
    return image_.get(index);
  }
  /**
   * <pre>
   * 具体的表情包
   * </pre>
   *
   * <code>repeated string image = 6;</code>
   * @param index The index of the value to return.
   * @return The bytes of the image at the given index.
   */
  public com.google.protobuf.ByteString
      getImageBytes(int index) {
    return image_.getByteString(index);
  }

  public static final int PRICETYPE_FIELD_NUMBER = 7;
  private int priceType_ = 0;
  /**
   * <pre>
   * 价格类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.PriceType priceType = 7;</code>
   * @return The enum numeric value on the wire for priceType.
   */
  @java.lang.Override public int getPriceTypeValue() {
    return priceType_;
  }
  /**
   * <pre>
   * 价格类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.PriceType priceType = 7;</code>
   * @return The priceType.
   */
  @java.lang.Override public com.kikitrade.activity.facade.sticker.PriceType getPriceType() {
    com.kikitrade.activity.facade.sticker.PriceType result = com.kikitrade.activity.facade.sticker.PriceType.forNumber(priceType_);
    return result == null ? com.kikitrade.activity.facade.sticker.PriceType.UNRECOGNIZED : result;
  }

  public static final int CURRENCYTYPE_FIELD_NUMBER = 8;
  private int currencyType_ = 0;
  /**
   * <pre>
   * 法币类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.CurrencyType currencyType = 8;</code>
   * @return The enum numeric value on the wire for currencyType.
   */
  @java.lang.Override public int getCurrencyTypeValue() {
    return currencyType_;
  }
  /**
   * <pre>
   * 法币类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.CurrencyType currencyType = 8;</code>
   * @return The currencyType.
   */
  @java.lang.Override public com.kikitrade.activity.facade.sticker.CurrencyType getCurrencyType() {
    com.kikitrade.activity.facade.sticker.CurrencyType result = com.kikitrade.activity.facade.sticker.CurrencyType.forNumber(currencyType_);
    return result == null ? com.kikitrade.activity.facade.sticker.CurrencyType.UNRECOGNIZED : result;
  }

  public static final int PRICE_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object price_ = "";
  /**
   * <pre>
   * 价格
   * </pre>
   *
   * <code>string price = 9;</code>
   * @return The price.
   */
  @java.lang.Override
  public java.lang.String getPrice() {
    java.lang.Object ref = price_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      price_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 价格
   * </pre>
   *
   * <code>string price = 9;</code>
   * @return The bytes for price.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPriceBytes() {
    java.lang.Object ref = price_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      price_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int GATETYPE_FIELD_NUMBER = 10;
  private int gateType_ = 0;
  /**
   * <pre>
   * 门槛类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.StickerGateType gateType = 10;</code>
   * @return The enum numeric value on the wire for gateType.
   */
  @java.lang.Override public int getGateTypeValue() {
    return gateType_;
  }
  /**
   * <pre>
   * 门槛类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.StickerGateType gateType = 10;</code>
   * @return The gateType.
   */
  @java.lang.Override public com.kikitrade.activity.facade.sticker.StickerGateType getGateType() {
    com.kikitrade.activity.facade.sticker.StickerGateType result = com.kikitrade.activity.facade.sticker.StickerGateType.forNumber(gateType_);
    return result == null ? com.kikitrade.activity.facade.sticker.StickerGateType.UNRECOGNIZED : result;
  }

  public static final int GATEVALUE_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private volatile java.lang.Object gateValue_ = "";
  /**
   * <pre>
   * 门槛值
   * </pre>
   *
   * <code>string gateValue = 11;</code>
   * @return The gateValue.
   */
  @java.lang.Override
  public java.lang.String getGateValue() {
    java.lang.Object ref = gateValue_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      gateValue_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 门槛值
   * </pre>
   *
   * <code>string gateValue = 11;</code>
   * @return The bytes for gateValue.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getGateValueBytes() {
    java.lang.Object ref = gateValue_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      gateValue_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SORT_FIELD_NUMBER = 12;
  private int sort_ = 0;
  /**
   * <pre>
   * 排序
   * </pre>
   *
   * <code>int32 sort = 12;</code>
   * @return The sort.
   */
  @java.lang.Override
  public int getSort() {
    return sort_;
  }

  public static final int STATUS_FIELD_NUMBER = 13;
  private int status_ = 0;
  /**
   * <pre>
   * 状态
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.StickerStatus status = 13;</code>
   * @return The enum numeric value on the wire for status.
   */
  @java.lang.Override public int getStatusValue() {
    return status_;
  }
  /**
   * <pre>
   * 状态
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.sticker.StickerStatus status = 13;</code>
   * @return The status.
   */
  @java.lang.Override public com.kikitrade.activity.facade.sticker.StickerStatus getStatus() {
    com.kikitrade.activity.facade.sticker.StickerStatus result = com.kikitrade.activity.facade.sticker.StickerStatus.forNumber(status_);
    return result == null ? com.kikitrade.activity.facade.sticker.StickerStatus.UNRECOGNIZED : result;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(celebId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, celebId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, name_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(icon_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, icon_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(description_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, description_);
    }
    for (int i = 0; i < image_.size(); i++) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, image_.getRaw(i));
    }
    if (priceType_ != com.kikitrade.activity.facade.sticker.PriceType.free.getNumber()) {
      output.writeEnum(7, priceType_);
    }
    if (currencyType_ != com.kikitrade.activity.facade.sticker.CurrencyType.USD.getNumber()) {
      output.writeEnum(8, currencyType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(price_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, price_);
    }
    if (gateType_ != com.kikitrade.activity.facade.sticker.StickerGateType.all.getNumber()) {
      output.writeEnum(10, gateType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(gateValue_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, gateValue_);
    }
    if (sort_ != 0) {
      output.writeInt32(12, sort_);
    }
    if (status_ != com.kikitrade.activity.facade.sticker.StickerStatus.active.getNumber()) {
      output.writeEnum(13, status_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(celebId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, celebId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, name_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(icon_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, icon_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(description_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, description_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < image_.size(); i++) {
        dataSize += computeStringSizeNoTag(image_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getImageList().size();
    }
    if (priceType_ != com.kikitrade.activity.facade.sticker.PriceType.free.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(7, priceType_);
    }
    if (currencyType_ != com.kikitrade.activity.facade.sticker.CurrencyType.USD.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(8, currencyType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(price_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, price_);
    }
    if (gateType_ != com.kikitrade.activity.facade.sticker.StickerGateType.all.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(10, gateType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(gateValue_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, gateValue_);
    }
    if (sort_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(12, sort_);
    }
    if (status_ != com.kikitrade.activity.facade.sticker.StickerStatus.active.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(13, status_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.sticker.UpsertStickerRequest)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.sticker.UpsertStickerRequest other = (com.kikitrade.activity.facade.sticker.UpsertStickerRequest) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getCelebId()
        .equals(other.getCelebId())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (!getIcon()
        .equals(other.getIcon())) return false;
    if (!getDescription()
        .equals(other.getDescription())) return false;
    if (!getImageList()
        .equals(other.getImageList())) return false;
    if (priceType_ != other.priceType_) return false;
    if (currencyType_ != other.currencyType_) return false;
    if (!getPrice()
        .equals(other.getPrice())) return false;
    if (gateType_ != other.gateType_) return false;
    if (!getGateValue()
        .equals(other.getGateValue())) return false;
    if (getSort()
        != other.getSort()) return false;
    if (status_ != other.status_) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + CELEBID_FIELD_NUMBER;
    hash = (53 * hash) + getCelebId().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + ICON_FIELD_NUMBER;
    hash = (53 * hash) + getIcon().hashCode();
    hash = (37 * hash) + DESCRIPTION_FIELD_NUMBER;
    hash = (53 * hash) + getDescription().hashCode();
    if (getImageCount() > 0) {
      hash = (37 * hash) + IMAGE_FIELD_NUMBER;
      hash = (53 * hash) + getImageList().hashCode();
    }
    hash = (37 * hash) + PRICETYPE_FIELD_NUMBER;
    hash = (53 * hash) + priceType_;
    hash = (37 * hash) + CURRENCYTYPE_FIELD_NUMBER;
    hash = (53 * hash) + currencyType_;
    hash = (37 * hash) + PRICE_FIELD_NUMBER;
    hash = (53 * hash) + getPrice().hashCode();
    hash = (37 * hash) + GATETYPE_FIELD_NUMBER;
    hash = (53 * hash) + gateType_;
    hash = (37 * hash) + GATEVALUE_FIELD_NUMBER;
    hash = (53 * hash) + getGateValue().hashCode();
    hash = (37 * hash) + SORT_FIELD_NUMBER;
    hash = (53 * hash) + getSort();
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + status_;
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.sticker.UpsertStickerRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.sticker.UpsertStickerRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.sticker.UpsertStickerRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.sticker.UpsertStickerRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.sticker.UpsertStickerRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.sticker.UpsertStickerRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.sticker.UpsertStickerRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.sticker.UpsertStickerRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.sticker.UpsertStickerRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.sticker.UpsertStickerRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.sticker.UpsertStickerRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.sticker.UpsertStickerRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.sticker.UpsertStickerRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.sticker.UpsertStickerRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.sticker.UpsertStickerRequest)
      com.kikitrade.activity.facade.sticker.UpsertStickerRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.sticker.StickerFacadeOuterClass.internal_static_com_kikitrade_activity_facade_sticker_UpsertStickerRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.sticker.StickerFacadeOuterClass.internal_static_com_kikitrade_activity_facade_sticker_UpsertStickerRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.sticker.UpsertStickerRequest.class, com.kikitrade.activity.facade.sticker.UpsertStickerRequest.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.sticker.UpsertStickerRequest.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      celebId_ = "";
      name_ = "";
      icon_ = "";
      description_ = "";
      image_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      priceType_ = 0;
      currencyType_ = 0;
      price_ = "";
      gateType_ = 0;
      gateValue_ = "";
      sort_ = 0;
      status_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.sticker.StickerFacadeOuterClass.internal_static_com_kikitrade_activity_facade_sticker_UpsertStickerRequest_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.sticker.UpsertStickerRequest getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.sticker.UpsertStickerRequest.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.sticker.UpsertStickerRequest build() {
      com.kikitrade.activity.facade.sticker.UpsertStickerRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.sticker.UpsertStickerRequest buildPartial() {
      com.kikitrade.activity.facade.sticker.UpsertStickerRequest result = new com.kikitrade.activity.facade.sticker.UpsertStickerRequest(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.kikitrade.activity.facade.sticker.UpsertStickerRequest result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.celebId_ = celebId_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.name_ = name_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.icon_ = icon_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.description_ = description_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        image_.makeImmutable();
        result.image_ = image_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.priceType_ = priceType_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.currencyType_ = currencyType_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.price_ = price_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.gateType_ = gateType_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.gateValue_ = gateValue_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.sort_ = sort_;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.status_ = status_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.sticker.UpsertStickerRequest) {
        return mergeFrom((com.kikitrade.activity.facade.sticker.UpsertStickerRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.sticker.UpsertStickerRequest other) {
      if (other == com.kikitrade.activity.facade.sticker.UpsertStickerRequest.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getCelebId().isEmpty()) {
        celebId_ = other.celebId_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getIcon().isEmpty()) {
        icon_ = other.icon_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (!other.getDescription().isEmpty()) {
        description_ = other.description_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (!other.image_.isEmpty()) {
        if (image_.isEmpty()) {
          image_ = other.image_;
          bitField0_ |= 0x00000020;
        } else {
          ensureImageIsMutable();
          image_.addAll(other.image_);
        }
        onChanged();
      }
      if (other.priceType_ != 0) {
        setPriceTypeValue(other.getPriceTypeValue());
      }
      if (other.currencyType_ != 0) {
        setCurrencyTypeValue(other.getCurrencyTypeValue());
      }
      if (!other.getPrice().isEmpty()) {
        price_ = other.price_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (other.gateType_ != 0) {
        setGateTypeValue(other.getGateTypeValue());
      }
      if (!other.getGateValue().isEmpty()) {
        gateValue_ = other.gateValue_;
        bitField0_ |= 0x00000400;
        onChanged();
      }
      if (other.getSort() != 0) {
        setSort(other.getSort());
      }
      if (other.status_ != 0) {
        setStatusValue(other.getStatusValue());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              celebId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              name_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              icon_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              description_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              java.lang.String s = input.readStringRequireUtf8();
              ensureImageIsMutable();
              image_.add(s);
              break;
            } // case 50
            case 56: {
              priceType_ = input.readEnum();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              currencyType_ = input.readEnum();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 74: {
              price_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 80: {
              gateType_ = input.readEnum();
              bitField0_ |= 0x00000200;
              break;
            } // case 80
            case 90: {
              gateValue_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 96: {
              sort_ = input.readInt32();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            case 104: {
              status_ = input.readEnum();
              bitField0_ |= 0x00001000;
              break;
            } // case 104
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object celebId_ = "";
    /**
     * <pre>
     * 所属艺人id
     * </pre>
     *
     * <code>string celebId = 2;</code>
     * @return The celebId.
     */
    public java.lang.String getCelebId() {
      java.lang.Object ref = celebId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        celebId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 所属艺人id
     * </pre>
     *
     * <code>string celebId = 2;</code>
     * @return The bytes for celebId.
     */
    public com.google.protobuf.ByteString
        getCelebIdBytes() {
      java.lang.Object ref = celebId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        celebId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 所属艺人id
     * </pre>
     *
     * <code>string celebId = 2;</code>
     * @param value The celebId to set.
     * @return This builder for chaining.
     */
    public Builder setCelebId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      celebId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 所属艺人id
     * </pre>
     *
     * <code>string celebId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearCelebId() {
      celebId_ = getDefaultInstance().getCelebId();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 所属艺人id
     * </pre>
     *
     * <code>string celebId = 2;</code>
     * @param value The bytes for celebId to set.
     * @return This builder for chaining.
     */
    public Builder setCelebIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      celebId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <pre>
     * 名称
     * </pre>
     *
     * <code>string name = 3;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 名称
     * </pre>
     *
     * <code>string name = 3;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 名称
     * </pre>
     *
     * <code>string name = 3;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 名称
     * </pre>
     *
     * <code>string name = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      name_ = getDefaultInstance().getName();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 名称
     * </pre>
     *
     * <code>string name = 3;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      name_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object icon_ = "";
    /**
     * <pre>
     * 图标
     * </pre>
     *
     * <code>string icon = 4;</code>
     * @return The icon.
     */
    public java.lang.String getIcon() {
      java.lang.Object ref = icon_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        icon_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 图标
     * </pre>
     *
     * <code>string icon = 4;</code>
     * @return The bytes for icon.
     */
    public com.google.protobuf.ByteString
        getIconBytes() {
      java.lang.Object ref = icon_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        icon_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 图标
     * </pre>
     *
     * <code>string icon = 4;</code>
     * @param value The icon to set.
     * @return This builder for chaining.
     */
    public Builder setIcon(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      icon_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 图标
     * </pre>
     *
     * <code>string icon = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearIcon() {
      icon_ = getDefaultInstance().getIcon();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 图标
     * </pre>
     *
     * <code>string icon = 4;</code>
     * @param value The bytes for icon to set.
     * @return This builder for chaining.
     */
    public Builder setIconBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      icon_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object description_ = "";
    /**
     * <pre>
     * 描述
     * </pre>
     *
     * <code>string description = 5;</code>
     * @return The description.
     */
    public java.lang.String getDescription() {
      java.lang.Object ref = description_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        description_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 描述
     * </pre>
     *
     * <code>string description = 5;</code>
     * @return The bytes for description.
     */
    public com.google.protobuf.ByteString
        getDescriptionBytes() {
      java.lang.Object ref = description_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        description_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 描述
     * </pre>
     *
     * <code>string description = 5;</code>
     * @param value The description to set.
     * @return This builder for chaining.
     */
    public Builder setDescription(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      description_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 描述
     * </pre>
     *
     * <code>string description = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearDescription() {
      description_ = getDefaultInstance().getDescription();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 描述
     * </pre>
     *
     * <code>string description = 5;</code>
     * @param value The bytes for description to set.
     * @return This builder for chaining.
     */
    public Builder setDescriptionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      description_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringArrayList image_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    private void ensureImageIsMutable() {
      if (!image_.isModifiable()) {
        image_ = new com.google.protobuf.LazyStringArrayList(image_);
      }
      bitField0_ |= 0x00000020;
    }
    /**
     * <pre>
     * 具体的表情包
     * </pre>
     *
     * <code>repeated string image = 6;</code>
     * @return A list containing the image.
     */
    public com.google.protobuf.ProtocolStringList
        getImageList() {
      image_.makeImmutable();
      return image_;
    }
    /**
     * <pre>
     * 具体的表情包
     * </pre>
     *
     * <code>repeated string image = 6;</code>
     * @return The count of image.
     */
    public int getImageCount() {
      return image_.size();
    }
    /**
     * <pre>
     * 具体的表情包
     * </pre>
     *
     * <code>repeated string image = 6;</code>
     * @param index The index of the element to return.
     * @return The image at the given index.
     */
    public java.lang.String getImage(int index) {
      return image_.get(index);
    }
    /**
     * <pre>
     * 具体的表情包
     * </pre>
     *
     * <code>repeated string image = 6;</code>
     * @param index The index of the value to return.
     * @return The bytes of the image at the given index.
     */
    public com.google.protobuf.ByteString
        getImageBytes(int index) {
      return image_.getByteString(index);
    }
    /**
     * <pre>
     * 具体的表情包
     * </pre>
     *
     * <code>repeated string image = 6;</code>
     * @param index The index to set the value at.
     * @param value The image to set.
     * @return This builder for chaining.
     */
    public Builder setImage(
        int index, java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureImageIsMutable();
      image_.set(index, value);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 具体的表情包
     * </pre>
     *
     * <code>repeated string image = 6;</code>
     * @param value The image to add.
     * @return This builder for chaining.
     */
    public Builder addImage(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureImageIsMutable();
      image_.add(value);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 具体的表情包
     * </pre>
     *
     * <code>repeated string image = 6;</code>
     * @param values The image to add.
     * @return This builder for chaining.
     */
    public Builder addAllImage(
        java.lang.Iterable<java.lang.String> values) {
      ensureImageIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, image_);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 具体的表情包
     * </pre>
     *
     * <code>repeated string image = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearImage() {
      image_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
      bitField0_ = (bitField0_ & ~0x00000020);;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 具体的表情包
     * </pre>
     *
     * <code>repeated string image = 6;</code>
     * @param value The bytes of the image to add.
     * @return This builder for chaining.
     */
    public Builder addImageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ensureImageIsMutable();
      image_.add(value);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private int priceType_ = 0;
    /**
     * <pre>
     * 价格类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.PriceType priceType = 7;</code>
     * @return The enum numeric value on the wire for priceType.
     */
    @java.lang.Override public int getPriceTypeValue() {
      return priceType_;
    }
    /**
     * <pre>
     * 价格类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.PriceType priceType = 7;</code>
     * @param value The enum numeric value on the wire for priceType to set.
     * @return This builder for chaining.
     */
    public Builder setPriceTypeValue(int value) {
      priceType_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 价格类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.PriceType priceType = 7;</code>
     * @return The priceType.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.sticker.PriceType getPriceType() {
      com.kikitrade.activity.facade.sticker.PriceType result = com.kikitrade.activity.facade.sticker.PriceType.forNumber(priceType_);
      return result == null ? com.kikitrade.activity.facade.sticker.PriceType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 价格类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.PriceType priceType = 7;</code>
     * @param value The priceType to set.
     * @return This builder for chaining.
     */
    public Builder setPriceType(com.kikitrade.activity.facade.sticker.PriceType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000040;
      priceType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 价格类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.PriceType priceType = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearPriceType() {
      bitField0_ = (bitField0_ & ~0x00000040);
      priceType_ = 0;
      onChanged();
      return this;
    }

    private int currencyType_ = 0;
    /**
     * <pre>
     * 法币类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.CurrencyType currencyType = 8;</code>
     * @return The enum numeric value on the wire for currencyType.
     */
    @java.lang.Override public int getCurrencyTypeValue() {
      return currencyType_;
    }
    /**
     * <pre>
     * 法币类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.CurrencyType currencyType = 8;</code>
     * @param value The enum numeric value on the wire for currencyType to set.
     * @return This builder for chaining.
     */
    public Builder setCurrencyTypeValue(int value) {
      currencyType_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 法币类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.CurrencyType currencyType = 8;</code>
     * @return The currencyType.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.sticker.CurrencyType getCurrencyType() {
      com.kikitrade.activity.facade.sticker.CurrencyType result = com.kikitrade.activity.facade.sticker.CurrencyType.forNumber(currencyType_);
      return result == null ? com.kikitrade.activity.facade.sticker.CurrencyType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 法币类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.CurrencyType currencyType = 8;</code>
     * @param value The currencyType to set.
     * @return This builder for chaining.
     */
    public Builder setCurrencyType(com.kikitrade.activity.facade.sticker.CurrencyType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000080;
      currencyType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 法币类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.CurrencyType currencyType = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurrencyType() {
      bitField0_ = (bitField0_ & ~0x00000080);
      currencyType_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object price_ = "";
    /**
     * <pre>
     * 价格
     * </pre>
     *
     * <code>string price = 9;</code>
     * @return The price.
     */
    public java.lang.String getPrice() {
      java.lang.Object ref = price_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        price_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 价格
     * </pre>
     *
     * <code>string price = 9;</code>
     * @return The bytes for price.
     */
    public com.google.protobuf.ByteString
        getPriceBytes() {
      java.lang.Object ref = price_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        price_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 价格
     * </pre>
     *
     * <code>string price = 9;</code>
     * @param value The price to set.
     * @return This builder for chaining.
     */
    public Builder setPrice(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      price_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 价格
     * </pre>
     *
     * <code>string price = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrice() {
      price_ = getDefaultInstance().getPrice();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 价格
     * </pre>
     *
     * <code>string price = 9;</code>
     * @param value The bytes for price to set.
     * @return This builder for chaining.
     */
    public Builder setPriceBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      price_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private int gateType_ = 0;
    /**
     * <pre>
     * 门槛类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.StickerGateType gateType = 10;</code>
     * @return The enum numeric value on the wire for gateType.
     */
    @java.lang.Override public int getGateTypeValue() {
      return gateType_;
    }
    /**
     * <pre>
     * 门槛类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.StickerGateType gateType = 10;</code>
     * @param value The enum numeric value on the wire for gateType to set.
     * @return This builder for chaining.
     */
    public Builder setGateTypeValue(int value) {
      gateType_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门槛类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.StickerGateType gateType = 10;</code>
     * @return The gateType.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.sticker.StickerGateType getGateType() {
      com.kikitrade.activity.facade.sticker.StickerGateType result = com.kikitrade.activity.facade.sticker.StickerGateType.forNumber(gateType_);
      return result == null ? com.kikitrade.activity.facade.sticker.StickerGateType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 门槛类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.StickerGateType gateType = 10;</code>
     * @param value The gateType to set.
     * @return This builder for chaining.
     */
    public Builder setGateType(com.kikitrade.activity.facade.sticker.StickerGateType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000200;
      gateType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门槛类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.StickerGateType gateType = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearGateType() {
      bitField0_ = (bitField0_ & ~0x00000200);
      gateType_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object gateValue_ = "";
    /**
     * <pre>
     * 门槛值
     * </pre>
     *
     * <code>string gateValue = 11;</code>
     * @return The gateValue.
     */
    public java.lang.String getGateValue() {
      java.lang.Object ref = gateValue_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        gateValue_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 门槛值
     * </pre>
     *
     * <code>string gateValue = 11;</code>
     * @return The bytes for gateValue.
     */
    public com.google.protobuf.ByteString
        getGateValueBytes() {
      java.lang.Object ref = gateValue_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gateValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 门槛值
     * </pre>
     *
     * <code>string gateValue = 11;</code>
     * @param value The gateValue to set.
     * @return This builder for chaining.
     */
    public Builder setGateValue(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      gateValue_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门槛值
     * </pre>
     *
     * <code>string gateValue = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearGateValue() {
      gateValue_ = getDefaultInstance().getGateValue();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门槛值
     * </pre>
     *
     * <code>string gateValue = 11;</code>
     * @param value The bytes for gateValue to set.
     * @return This builder for chaining.
     */
    public Builder setGateValueBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      gateValue_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }

    private int sort_ ;
    /**
     * <pre>
     * 排序
     * </pre>
     *
     * <code>int32 sort = 12;</code>
     * @return The sort.
     */
    @java.lang.Override
    public int getSort() {
      return sort_;
    }
    /**
     * <pre>
     * 排序
     * </pre>
     *
     * <code>int32 sort = 12;</code>
     * @param value The sort to set.
     * @return This builder for chaining.
     */
    public Builder setSort(int value) {

      sort_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 排序
     * </pre>
     *
     * <code>int32 sort = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearSort() {
      bitField0_ = (bitField0_ & ~0x00000800);
      sort_ = 0;
      onChanged();
      return this;
    }

    private int status_ = 0;
    /**
     * <pre>
     * 状态
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.StickerStatus status = 13;</code>
     * @return The enum numeric value on the wire for status.
     */
    @java.lang.Override public int getStatusValue() {
      return status_;
    }
    /**
     * <pre>
     * 状态
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.StickerStatus status = 13;</code>
     * @param value The enum numeric value on the wire for status to set.
     * @return This builder for chaining.
     */
    public Builder setStatusValue(int value) {
      status_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 状态
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.StickerStatus status = 13;</code>
     * @return The status.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.sticker.StickerStatus getStatus() {
      com.kikitrade.activity.facade.sticker.StickerStatus result = com.kikitrade.activity.facade.sticker.StickerStatus.forNumber(status_);
      return result == null ? com.kikitrade.activity.facade.sticker.StickerStatus.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 状态
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.StickerStatus status = 13;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(com.kikitrade.activity.facade.sticker.StickerStatus value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00001000;
      status_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 状态
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.sticker.StickerStatus status = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      bitField0_ = (bitField0_ & ~0x00001000);
      status_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.sticker.UpsertStickerRequest)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.sticker.UpsertStickerRequest)
  private static final com.kikitrade.activity.facade.sticker.UpsertStickerRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.sticker.UpsertStickerRequest();
  }

  public static com.kikitrade.activity.facade.sticker.UpsertStickerRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UpsertStickerRequest>
      PARSER = new com.google.protobuf.AbstractParser<UpsertStickerRequest>() {
    @java.lang.Override
    public UpsertStickerRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UpsertStickerRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UpsertStickerRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.sticker.UpsertStickerRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

