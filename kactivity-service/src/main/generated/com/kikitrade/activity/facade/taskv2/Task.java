// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Task.proto

package com.kikitrade.activity.facade.taskv2;

public final class Task {
  private Task() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_taskv2_TaskDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_taskv2_TaskDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_taskv2_RewardDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_taskv2_RewardDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_taskv2_SubTaskDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_taskv2_SubTaskDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_taskv2_IdVOResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_taskv2_IdVOResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_taskv2_CodeVOResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_taskv2_CodeVOResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_taskv2_CodeVO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_taskv2_CodeVO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_taskv2_EmptyRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_taskv2_EmptyRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\nTask.proto\022$com.kikitrade.activity.fac" +
      "ade.taskv2\"\273\006\n\007TaskDTO\022\n\n\002id\030\001 \001(\t\022\r\n\005ti" +
      "tle\030\002 \001(\t\022\014\n\004desc\030\003 \001(\t\022\024\n\014labelContent\030" +
      "\004 \001(\t\022\022\n\nlabelColor\030\005 \001(\t\022\021\n\tstartTime\030\006" +
      " \001(\t\022\017\n\007endTime\030\007 \001(\t\022\021\n\tlistImage\030\010 \001(\t" +
      "\022\023\n\013detailImage\030\t \001(\t\022\022\n\nshareImage\030\n \001(" +
      "\t\022\024\n\014shareContent\030\013 \001(\t\022>\n\005cycle\030\014 \001(\0162/" +
      ".com.kikitrade.activity.facade.taskv2.Ta" +
      "skCycle\022\r\n\005limit\030\r \001(\005\022\020\n\010limitVip\030\016 \001(\005" +
      "\022\027\n\017rewardFrequency\030\017 \001(\005\022H\n\014progressTyp" +
      "e\030\020 \001(\01622.com.kikitrade.activity.facade." +
      "taskv2.ProgressType\022D\n\nrewardForm\030\021 \001(\0162" +
      "0.com.kikitrade.activity.facade.taskv2.R" +
      "ewardForm\022F\n\013provideType\030\022 \001(\01621.com.kik" +
      "itrade.activity.facade.taskv2.ProvideTyp" +
      "e\022B\n\trewardDTO\030\023 \003(\0132/.com.kikitrade.act" +
      "ivity.facade.taskv2.RewardDTO\022D\n\nsubTask" +
      "DTO\030\024 \003(\01320.com.kikitrade.activity.facad" +
      "e.taskv2.SubTaskDTO\022B\n\006status\030\025 \001(\01622.co" +
      "m.kikitrade.activity.facade.taskv2.Commo" +
      "nStatus\022\016\n\006saasId\030\026 \001(\t\022\014\n\004code\030\027 \001(\t\022\026\n" +
      "\016twitterSubject\030\030 \001(\t\022\021\n\ttwitterTo\030\031 \001(\t" +
      "\"\346\001\n\tRewardDTO\022\030\n\020consecutiveTimes\030\001 \001(\005" +
      "\022>\n\004type\030\002 \001(\01620.com.kikitrade.activity." +
      "facade.taskv2.RewardType\022\016\n\006amount\030\003 \001(\t" +
      "\022\020\n\010currency\030\004 \001(\t\022\r\n\005image\030\005 \001(\t\022\014\n\004des" +
      "c\030\006 \001(\t\022@\n\010vipLevel\030\007 \001(\0162..com.kikitrad" +
      "e.activity.facade.taskv2.VipLevel\"_\n\nSub" +
      "TaskDTO\022\n\n\002id\030\001 \001(\t\022\r\n\005title\030\002 \001(\t\022\014\n\004co" +
      "de\030\003 \001(\t\022\013\n\003url\030\004 \001(\t\022\r\n\005order\030\005 \001(\005\022\014\n\004" +
      "icon\030\006 \001(\t\"K\n\014IdVOResponse\022\017\n\007success\030\001 " +
      "\001(\010\022\017\n\007message\030\002 \001(\t\022\n\n\002id\030\003 \001(\t\022\r\n\005subI" +
      "d\030\004 \003(\t\"n\n\016CodeVOResponse\022\017\n\007success\030\001 \001" +
      "(\010\022\017\n\007message\030\002 \001(\t\022:\n\004data\030\003 \003(\0132,.com." +
      "kikitrade.activity.facade.taskv2.CodeVO\"" +
      "$\n\006CodeVO\022\014\n\004code\030\001 \001(\t\022\014\n\004desc\030\002 \001(\t\"\016\n" +
      "\014EmptyRequest*\037\n\010VipLevel\022\n\n\006NORMAL\020\000\022\007\n" +
      "\003VIP\020\001*9\n\tTaskCycle\022\010\n\004once\020\000\022\t\n\005daily\020\001" +
      "\022\n\n\006weekly\020\002\022\013\n\007monthly\020\003*#\n\014ProgressTyp" +
      "e\022\007\n\003add\020\000\022\n\n\006series\020\001*3\n\nRewardForm\022\010\n\004" +
      "none\020\000\022\t\n\005fixed\020\001\022\020\n\014fixed_random\020\002*\'\n\013P" +
      "rovideType\022\010\n\004auto\020\000\022\016\n\nclaim_task\020\001* \n\n" +
      "RewardType\022\t\n\005POINT\020\000\022\007\n\003NFT\020\001*\'\n\014Common" +
      "Status\022\n\n\006ACTIVE\020\000\022\013\n\007DISABLE\020\0012\354\001\n\nTask" +
      "Facade\022i\n\004save\022-.com.kikitrade.activity." +
      "facade.taskv2.TaskDTO\0322.com.kikitrade.ac" +
      "tivity.facade.taskv2.IdVOResponse\022s\n\007get" +
      "Code\0222.com.kikitrade.activity.facade.tas" +
      "kv2.EmptyRequest\0324.com.kikitrade.activit" +
      "y.facade.taskv2.CodeVOResponseB(\n$com.ki" +
      "kitrade.activity.facade.taskv2P\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_kikitrade_activity_facade_taskv2_TaskDTO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_activity_facade_taskv2_TaskDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_taskv2_TaskDTO_descriptor,
        new java.lang.String[] { "Id", "Title", "Desc", "LabelContent", "LabelColor", "StartTime", "EndTime", "ListImage", "DetailImage", "ShareImage", "ShareContent", "Cycle", "Limit", "LimitVip", "RewardFrequency", "ProgressType", "RewardForm", "ProvideType", "RewardDTO", "SubTaskDTO", "Status", "SaasId", "Code", "TwitterSubject", "TwitterTo", });
    internal_static_com_kikitrade_activity_facade_taskv2_RewardDTO_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_activity_facade_taskv2_RewardDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_taskv2_RewardDTO_descriptor,
        new java.lang.String[] { "ConsecutiveTimes", "Type", "Amount", "Currency", "Image", "Desc", "VipLevel", });
    internal_static_com_kikitrade_activity_facade_taskv2_SubTaskDTO_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_kikitrade_activity_facade_taskv2_SubTaskDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_taskv2_SubTaskDTO_descriptor,
        new java.lang.String[] { "Id", "Title", "Code", "Url", "Order", "Icon", });
    internal_static_com_kikitrade_activity_facade_taskv2_IdVOResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_kikitrade_activity_facade_taskv2_IdVOResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_taskv2_IdVOResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "Id", "SubId", });
    internal_static_com_kikitrade_activity_facade_taskv2_CodeVOResponse_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_kikitrade_activity_facade_taskv2_CodeVOResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_taskv2_CodeVOResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "Data", });
    internal_static_com_kikitrade_activity_facade_taskv2_CodeVO_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_kikitrade_activity_facade_taskv2_CodeVO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_taskv2_CodeVO_descriptor,
        new java.lang.String[] { "Code", "Desc", });
    internal_static_com_kikitrade_activity_facade_taskv2_EmptyRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_kikitrade_activity_facade_taskv2_EmptyRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_taskv2_EmptyRequest_descriptor,
        new java.lang.String[] { });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
