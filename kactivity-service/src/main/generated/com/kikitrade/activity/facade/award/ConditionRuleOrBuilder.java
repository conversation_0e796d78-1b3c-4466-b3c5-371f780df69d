// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface ConditionRuleOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.ConditionRule)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string name = 1;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <code>string name = 1;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <code>string alias = 2;</code>
   * @return The alias.
   */
  java.lang.String getAlias();
  /**
   * <code>string alias = 2;</code>
   * @return The bytes for alias.
   */
  com.google.protobuf.ByteString
      getAliasBytes();

  /**
   * <code>string filter = 3;</code>
   * @return The filter.
   */
  java.lang.String getFilter();
  /**
   * <code>string filter = 3;</code>
   * @return The bytes for filter.
   */
  com.google.protobuf.ByteString
      getFilterBytes();

  /**
   * <code>string value = 4;</code>
   * @return The value.
   */
  java.lang.String getValue();
  /**
   * <code>string value = 4;</code>
   * @return The bytes for value.
   */
  com.google.protobuf.ByteString
      getValueBytes();
}
