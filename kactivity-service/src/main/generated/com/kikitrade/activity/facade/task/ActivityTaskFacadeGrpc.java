package com.kikitrade.activity.facade.task;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.58.0)",
    comments = "Source: TaskFacade.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class ActivityTaskFacadeGrpc {

  private ActivityTaskFacadeGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.kikitrade.activity.facade.task.ActivityTaskFacade";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.task.TaskDTO,
      com.kikitrade.activity.facade.task.CommonResponse> getSaveMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "save",
      requestType = com.kikitrade.activity.facade.task.TaskDTO.class,
      responseType = com.kikitrade.activity.facade.task.CommonResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.task.TaskDTO,
      com.kikitrade.activity.facade.task.CommonResponse> getSaveMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.task.TaskDTO, com.kikitrade.activity.facade.task.CommonResponse> getSaveMethod;
    if ((getSaveMethod = ActivityTaskFacadeGrpc.getSaveMethod) == null) {
      synchronized (ActivityTaskFacadeGrpc.class) {
        if ((getSaveMethod = ActivityTaskFacadeGrpc.getSaveMethod) == null) {
          ActivityTaskFacadeGrpc.getSaveMethod = getSaveMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.task.TaskDTO, com.kikitrade.activity.facade.task.CommonResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "save"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.task.TaskDTO.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.task.CommonResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityTaskFacadeMethodDescriptorSupplier("save"))
              .build();
        }
      }
    }
    return getSaveMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static ActivityTaskFacadeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ActivityTaskFacadeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ActivityTaskFacadeStub>() {
        @java.lang.Override
        public ActivityTaskFacadeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ActivityTaskFacadeStub(channel, callOptions);
        }
      };
    return ActivityTaskFacadeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static ActivityTaskFacadeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ActivityTaskFacadeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ActivityTaskFacadeBlockingStub>() {
        @java.lang.Override
        public ActivityTaskFacadeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ActivityTaskFacadeBlockingStub(channel, callOptions);
        }
      };
    return ActivityTaskFacadeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static ActivityTaskFacadeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ActivityTaskFacadeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ActivityTaskFacadeFutureStub>() {
        @java.lang.Override
        public ActivityTaskFacadeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ActivityTaskFacadeFutureStub(channel, callOptions);
        }
      };
    return ActivityTaskFacadeFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     * <pre>
     **
     *保存任务
     * </pre>
     */
    default void save(com.kikitrade.activity.facade.task.TaskDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.task.CommonResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSaveMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service ActivityTaskFacade.
   */
  public static abstract class ActivityTaskFacadeImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return ActivityTaskFacadeGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service ActivityTaskFacade.
   */
  public static final class ActivityTaskFacadeStub
      extends io.grpc.stub.AbstractAsyncStub<ActivityTaskFacadeStub> {
    private ActivityTaskFacadeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ActivityTaskFacadeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ActivityTaskFacadeStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *保存任务
     * </pre>
     */
    public void save(com.kikitrade.activity.facade.task.TaskDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.task.CommonResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSaveMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service ActivityTaskFacade.
   */
  public static final class ActivityTaskFacadeBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<ActivityTaskFacadeBlockingStub> {
    private ActivityTaskFacadeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ActivityTaskFacadeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ActivityTaskFacadeBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *保存任务
     * </pre>
     */
    public com.kikitrade.activity.facade.task.CommonResponse save(com.kikitrade.activity.facade.task.TaskDTO request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSaveMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service ActivityTaskFacade.
   */
  public static final class ActivityTaskFacadeFutureStub
      extends io.grpc.stub.AbstractFutureStub<ActivityTaskFacadeFutureStub> {
    private ActivityTaskFacadeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ActivityTaskFacadeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ActivityTaskFacadeFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *保存任务
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.task.CommonResponse> save(
        com.kikitrade.activity.facade.task.TaskDTO request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSaveMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_SAVE = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_SAVE:
          serviceImpl.save((com.kikitrade.activity.facade.task.TaskDTO) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.task.CommonResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getSaveMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.task.TaskDTO,
              com.kikitrade.activity.facade.task.CommonResponse>(
                service, METHODID_SAVE)))
        .build();
  }

  private static abstract class ActivityTaskFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    ActivityTaskFacadeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.kikitrade.activity.facade.task.ActivityTaskFacadeOutClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("ActivityTaskFacade");
    }
  }

  private static final class ActivityTaskFacadeFileDescriptorSupplier
      extends ActivityTaskFacadeBaseDescriptorSupplier {
    ActivityTaskFacadeFileDescriptorSupplier() {}
  }

  private static final class ActivityTaskFacadeMethodDescriptorSupplier
      extends ActivityTaskFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    ActivityTaskFacadeMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (ActivityTaskFacadeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new ActivityTaskFacadeFileDescriptorSupplier())
              .addMethod(getSaveMethod())
              .build();
        }
      }
    }
    return result;
  }
}
