// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: LuckFortuneFacade.proto

package com.kikitrade.activity.facade.luck;

public interface LuckFortuneRuleDTOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *kyc等级
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.luck.KycLevel kycLevel = 1;</code>
   * @return The enum numeric value on the wire for kycLevel.
   */
  int getKycLevelValue();
  /**
   * <pre>
   *kyc等级
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.luck.KycLevel kycLevel = 1;</code>
   * @return The kycLevel.
   */
  com.kikitrade.activity.facade.luck.KycLevel getKycLevel();

  /**
   * <pre>
   *用户类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.luck.UserType userType = 2;</code>
   * @return The enum numeric value on the wire for userType.
   */
  int getUserTypeValue();
  /**
   * <pre>
   *用户类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.luck.UserType userType = 2;</code>
   * @return The userType.
   */
  com.kikitrade.activity.facade.luck.UserType getUserType();

  /**
   * <pre>
   *发放红包最小金额
   * </pre>
   *
   * <code>string releaseMin = 3;</code>
   * @return The releaseMin.
   */
  java.lang.String getReleaseMin();
  /**
   * <pre>
   *发放红包最小金额
   * </pre>
   *
   * <code>string releaseMin = 3;</code>
   * @return The bytes for releaseMin.
   */
  com.google.protobuf.ByteString
      getReleaseMinBytes();

  /**
   * <pre>
   *发放红包最大金额
   * </pre>
   *
   * <code>string releaseMax = 4;</code>
   * @return The releaseMax.
   */
  java.lang.String getReleaseMax();
  /**
   * <pre>
   *发放红包最大金额
   * </pre>
   *
   * <code>string releaseMax = 4;</code>
   * @return The bytes for releaseMax.
   */
  com.google.protobuf.ByteString
      getReleaseMaxBytes();

  /**
   * <pre>
   *单个红包最大数量
   * </pre>
   *
   * <code>int32 releaseNumMax = 5;</code>
   * @return The releaseNumMax.
   */
  int getReleaseNumMax();

  /**
   * <pre>
   *24小时发放累积限额
   * </pre>
   *
   * <code>string releaseAccountDays = 6;</code>
   * @return The releaseAccountDays.
   */
  java.lang.String getReleaseAccountDays();
  /**
   * <pre>
   *24小时发放累积限额
   * </pre>
   *
   * <code>string releaseAccountDays = 6;</code>
   * @return The bytes for releaseAccountDays.
   */
  com.google.protobuf.ByteString
      getReleaseAccountDaysBytes();

  /**
   * <pre>
   *30天发放累积限额
   * </pre>
   *
   * <code>string releaseAccountMonths = 7;</code>
   * @return The releaseAccountMonths.
   */
  java.lang.String getReleaseAccountMonths();
  /**
   * <pre>
   *30天发放累积限额
   * </pre>
   *
   * <code>string releaseAccountMonths = 7;</code>
   * @return The bytes for releaseAccountMonths.
   */
  com.google.protobuf.ByteString
      getReleaseAccountMonthsBytes();

  /**
   * <pre>
   *领取红包的最小金额
   * </pre>
   *
   * <code>string receiveMin = 8;</code>
   * @return The receiveMin.
   */
  java.lang.String getReceiveMin();
  /**
   * <pre>
   *领取红包的最小金额
   * </pre>
   *
   * <code>string receiveMin = 8;</code>
   * @return The bytes for receiveMin.
   */
  com.google.protobuf.ByteString
      getReceiveMinBytes();

  /**
   * <pre>
   *领取红包的最小金额
   * </pre>
   *
   * <code>string receiveMax = 9;</code>
   * @return The receiveMax.
   */
  java.lang.String getReceiveMax();
  /**
   * <pre>
   *领取红包的最小金额
   * </pre>
   *
   * <code>string receiveMax = 9;</code>
   * @return The bytes for receiveMax.
   */
  com.google.protobuf.ByteString
      getReceiveMaxBytes();

  /**
   * <pre>
   *24小时领取累积限额
   * </pre>
   *
   * <code>string receiveAccountDays = 10;</code>
   * @return The receiveAccountDays.
   */
  java.lang.String getReceiveAccountDays();
  /**
   * <pre>
   *24小时领取累积限额
   * </pre>
   *
   * <code>string receiveAccountDays = 10;</code>
   * @return The bytes for receiveAccountDays.
   */
  com.google.protobuf.ByteString
      getReceiveAccountDaysBytes();

  /**
   * <pre>
   *30天领取累积限额
   * </pre>
   *
   * <code>string receiveAccountMonths = 11;</code>
   * @return The receiveAccountMonths.
   */
  java.lang.String getReceiveAccountMonths();
  /**
   * <pre>
   *30天领取累积限额
   * </pre>
   *
   * <code>string receiveAccountMonths = 11;</code>
   * @return The bytes for receiveAccountMonths.
   */
  com.google.protobuf.ByteString
      getReceiveAccountMonthsBytes();

  /**
   * <pre>
   *24小时领取累积数量限制
   * </pre>
   *
   * <code>int32 receiveNumDays = 12;</code>
   * @return The receiveNumDays.
   */
  int getReceiveNumDays();

  /**
   * <pre>
   *30天领取累积数量限制
   * </pre>
   *
   * <code>int32 receiveNumMonths = 13;</code>
   * @return The receiveNumMonths.
   */
  int getReceiveNumMonths();

  /**
   * <pre>
   *红包规则id
   * </pre>
   *
   * <code>string id = 14;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   *红包规则id
   * </pre>
   *
   * <code>string id = 14;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();
}
