// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: LuckFortuneFacade.proto

package com.kikitrade.activity.facade.luck;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.luck.KycLevel}
 */
public enum KycLevel
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *未认证
   * </pre>
   *
   * <code>L0 = 0;</code>
   */
  L0(0),
  /**
   * <code>L1 = 3;</code>
   */
  L1(3),
  /**
   * <code>L2 = 6;</code>
   */
  L2(6),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *未认证
   * </pre>
   *
   * <code>L0 = 0;</code>
   */
  public static final int L0_VALUE = 0;
  /**
   * <code>L1 = 3;</code>
   */
  public static final int L1_VALUE = 3;
  /**
   * <code>L2 = 6;</code>
   */
  public static final int L2_VALUE = 6;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static KycLevel valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static KycLevel forNumber(int value) {
    switch (value) {
      case 0: return L0;
      case 3: return L1;
      case 6: return L2;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<KycLevel>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      KycLevel> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<KycLevel>() {
          public KycLevel findValueByNumber(int number) {
            return KycLevel.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.luck.LuckFortuneFacadeOuterClass.getDescriptor().getEnumTypes().get(0);
  }

  private static final KycLevel[] VALUES = values();

  public static KycLevel valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private KycLevel(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.luck.KycLevel)
}

