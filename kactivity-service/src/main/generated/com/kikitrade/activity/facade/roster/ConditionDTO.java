// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityRosterRule.proto

package com.kikitrade.activity.facade.roster;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.roster.ConditionDTO}
 */
public final class ConditionDTO extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.roster.ConditionDTO)
    ConditionDTOOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ConditionDTO.newBuilder() to construct.
  private ConditionDTO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ConditionDTO() {
    id_ = "";
    code_ = "";
    status_ = 0;
    remark_ = "";
    conditions_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ConditionDTO();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionDTO_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionDTO_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.roster.ConditionDTO.class, com.kikitrade.activity.facade.roster.ConditionDTO.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CODE_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object code_ = "";
  /**
   * <code>string code = 2;</code>
   * @return The code.
   */
  @java.lang.Override
  public java.lang.String getCode() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      code_ = s;
      return s;
    }
  }
  /**
   * <code>string code = 2;</code>
   * @return The bytes for code.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCodeBytes() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      code_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATUS_FIELD_NUMBER = 3;
  private int status_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.roster.Status status = 3;</code>
   * @return The enum numeric value on the wire for status.
   */
  @java.lang.Override public int getStatusValue() {
    return status_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.roster.Status status = 3;</code>
   * @return The status.
   */
  @java.lang.Override public com.kikitrade.activity.facade.roster.Status getStatus() {
    com.kikitrade.activity.facade.roster.Status result = com.kikitrade.activity.facade.roster.Status.forNumber(status_);
    return result == null ? com.kikitrade.activity.facade.roster.Status.UNRECOGNIZED : result;
  }

  public static final int REMARK_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object remark_ = "";
  /**
   * <code>string remark = 4;</code>
   * @return The remark.
   */
  @java.lang.Override
  public java.lang.String getRemark() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      remark_ = s;
      return s;
    }
  }
  /**
   * <code>string remark = 4;</code>
   * @return The bytes for remark.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRemarkBytes() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      remark_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CONDITIONS_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.roster.Condition> conditions_;
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.roster.Condition> getConditionsList() {
    return conditions_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.roster.ConditionOrBuilder> 
      getConditionsOrBuilderList() {
    return conditions_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
   */
  @java.lang.Override
  public int getConditionsCount() {
    return conditions_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.roster.Condition getConditions(int index) {
    return conditions_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.roster.ConditionOrBuilder getConditionsOrBuilder(
      int index) {
    return conditions_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(code_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, code_);
    }
    if (status_ != com.kikitrade.activity.facade.roster.Status.ACTIVE.getNumber()) {
      output.writeEnum(3, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(remark_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, remark_);
    }
    for (int i = 0; i < conditions_.size(); i++) {
      output.writeMessage(5, conditions_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(code_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, code_);
    }
    if (status_ != com.kikitrade.activity.facade.roster.Status.ACTIVE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(3, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(remark_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, remark_);
    }
    for (int i = 0; i < conditions_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, conditions_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.roster.ConditionDTO)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.roster.ConditionDTO other = (com.kikitrade.activity.facade.roster.ConditionDTO) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getCode()
        .equals(other.getCode())) return false;
    if (status_ != other.status_) return false;
    if (!getRemark()
        .equals(other.getRemark())) return false;
    if (!getConditionsList()
        .equals(other.getConditionsList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCode().hashCode();
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + status_;
    hash = (37 * hash) + REMARK_FIELD_NUMBER;
    hash = (53 * hash) + getRemark().hashCode();
    if (getConditionsCount() > 0) {
      hash = (37 * hash) + CONDITIONS_FIELD_NUMBER;
      hash = (53 * hash) + getConditionsList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.roster.ConditionDTO parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.roster.ConditionDTO parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionDTO parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.roster.ConditionDTO parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionDTO parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.roster.ConditionDTO parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionDTO parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.roster.ConditionDTO parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.roster.ConditionDTO parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.roster.ConditionDTO parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.roster.ConditionDTO parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.roster.ConditionDTO parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.roster.ConditionDTO prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.roster.ConditionDTO}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.roster.ConditionDTO)
      com.kikitrade.activity.facade.roster.ConditionDTOOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionDTO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionDTO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.roster.ConditionDTO.class, com.kikitrade.activity.facade.roster.ConditionDTO.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.roster.ConditionDTO.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      code_ = "";
      status_ = 0;
      remark_ = "";
      if (conditionsBuilder_ == null) {
        conditions_ = java.util.Collections.emptyList();
      } else {
        conditions_ = null;
        conditionsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000010);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.internal_static_com_kikitrade_activity_facade_roster_ConditionDTO_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.roster.ConditionDTO getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.roster.ConditionDTO.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.roster.ConditionDTO build() {
      com.kikitrade.activity.facade.roster.ConditionDTO result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.roster.ConditionDTO buildPartial() {
      com.kikitrade.activity.facade.roster.ConditionDTO result = new com.kikitrade.activity.facade.roster.ConditionDTO(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.roster.ConditionDTO result) {
      if (conditionsBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          conditions_ = java.util.Collections.unmodifiableList(conditions_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.conditions_ = conditions_;
      } else {
        result.conditions_ = conditionsBuilder_.build();
      }
    }

    private void buildPartial0(com.kikitrade.activity.facade.roster.ConditionDTO result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.code_ = code_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.status_ = status_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.remark_ = remark_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.roster.ConditionDTO) {
        return mergeFrom((com.kikitrade.activity.facade.roster.ConditionDTO)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.roster.ConditionDTO other) {
      if (other == com.kikitrade.activity.facade.roster.ConditionDTO.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getCode().isEmpty()) {
        code_ = other.code_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.status_ != 0) {
        setStatusValue(other.getStatusValue());
      }
      if (!other.getRemark().isEmpty()) {
        remark_ = other.remark_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (conditionsBuilder_ == null) {
        if (!other.conditions_.isEmpty()) {
          if (conditions_.isEmpty()) {
            conditions_ = other.conditions_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureConditionsIsMutable();
            conditions_.addAll(other.conditions_);
          }
          onChanged();
        }
      } else {
        if (!other.conditions_.isEmpty()) {
          if (conditionsBuilder_.isEmpty()) {
            conditionsBuilder_.dispose();
            conditionsBuilder_ = null;
            conditions_ = other.conditions_;
            bitField0_ = (bitField0_ & ~0x00000010);
            conditionsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getConditionsFieldBuilder() : null;
          } else {
            conditionsBuilder_.addAllMessages(other.conditions_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              code_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              status_ = input.readEnum();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              remark_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              com.kikitrade.activity.facade.roster.Condition m =
                  input.readMessage(
                      com.kikitrade.activity.facade.roster.Condition.parser(),
                      extensionRegistry);
              if (conditionsBuilder_ == null) {
                ensureConditionsIsMutable();
                conditions_.add(m);
              } else {
                conditionsBuilder_.addMessage(m);
              }
              break;
            } // case 42
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object code_ = "";
    /**
     * <code>string code = 2;</code>
     * @return The code.
     */
    public java.lang.String getCode() {
      java.lang.Object ref = code_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        code_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string code = 2;</code>
     * @return The bytes for code.
     */
    public com.google.protobuf.ByteString
        getCodeBytes() {
      java.lang.Object ref = code_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        code_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string code = 2;</code>
     * @param value The code to set.
     * @return This builder for chaining.
     */
    public Builder setCode(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      code_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string code = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearCode() {
      code_ = getDefaultInstance().getCode();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string code = 2;</code>
     * @param value The bytes for code to set.
     * @return This builder for chaining.
     */
    public Builder setCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      code_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private int status_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.roster.Status status = 3;</code>
     * @return The enum numeric value on the wire for status.
     */
    @java.lang.Override public int getStatusValue() {
      return status_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.roster.Status status = 3;</code>
     * @param value The enum numeric value on the wire for status to set.
     * @return This builder for chaining.
     */
    public Builder setStatusValue(int value) {
      status_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.roster.Status status = 3;</code>
     * @return The status.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.roster.Status getStatus() {
      com.kikitrade.activity.facade.roster.Status result = com.kikitrade.activity.facade.roster.Status.forNumber(status_);
      return result == null ? com.kikitrade.activity.facade.roster.Status.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.roster.Status status = 3;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(com.kikitrade.activity.facade.roster.Status value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000004;
      status_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.roster.Status status = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      bitField0_ = (bitField0_ & ~0x00000004);
      status_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object remark_ = "";
    /**
     * <code>string remark = 4;</code>
     * @return The remark.
     */
    public java.lang.String getRemark() {
      java.lang.Object ref = remark_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        remark_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string remark = 4;</code>
     * @return The bytes for remark.
     */
    public com.google.protobuf.ByteString
        getRemarkBytes() {
      java.lang.Object ref = remark_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        remark_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string remark = 4;</code>
     * @param value The remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemark(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      remark_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>string remark = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRemark() {
      remark_ = getDefaultInstance().getRemark();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>string remark = 4;</code>
     * @param value The bytes for remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemarkBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      remark_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.util.List<com.kikitrade.activity.facade.roster.Condition> conditions_ =
      java.util.Collections.emptyList();
    private void ensureConditionsIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        conditions_ = new java.util.ArrayList<com.kikitrade.activity.facade.roster.Condition>(conditions_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.roster.Condition, com.kikitrade.activity.facade.roster.Condition.Builder, com.kikitrade.activity.facade.roster.ConditionOrBuilder> conditionsBuilder_;

    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.roster.Condition> getConditionsList() {
      if (conditionsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(conditions_);
      } else {
        return conditionsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public int getConditionsCount() {
      if (conditionsBuilder_ == null) {
        return conditions_.size();
      } else {
        return conditionsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public com.kikitrade.activity.facade.roster.Condition getConditions(int index) {
      if (conditionsBuilder_ == null) {
        return conditions_.get(index);
      } else {
        return conditionsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public Builder setConditions(
        int index, com.kikitrade.activity.facade.roster.Condition value) {
      if (conditionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionsIsMutable();
        conditions_.set(index, value);
        onChanged();
      } else {
        conditionsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public Builder setConditions(
        int index, com.kikitrade.activity.facade.roster.Condition.Builder builderForValue) {
      if (conditionsBuilder_ == null) {
        ensureConditionsIsMutable();
        conditions_.set(index, builderForValue.build());
        onChanged();
      } else {
        conditionsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public Builder addConditions(com.kikitrade.activity.facade.roster.Condition value) {
      if (conditionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionsIsMutable();
        conditions_.add(value);
        onChanged();
      } else {
        conditionsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public Builder addConditions(
        int index, com.kikitrade.activity.facade.roster.Condition value) {
      if (conditionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionsIsMutable();
        conditions_.add(index, value);
        onChanged();
      } else {
        conditionsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public Builder addConditions(
        com.kikitrade.activity.facade.roster.Condition.Builder builderForValue) {
      if (conditionsBuilder_ == null) {
        ensureConditionsIsMutable();
        conditions_.add(builderForValue.build());
        onChanged();
      } else {
        conditionsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public Builder addConditions(
        int index, com.kikitrade.activity.facade.roster.Condition.Builder builderForValue) {
      if (conditionsBuilder_ == null) {
        ensureConditionsIsMutable();
        conditions_.add(index, builderForValue.build());
        onChanged();
      } else {
        conditionsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public Builder addAllConditions(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.roster.Condition> values) {
      if (conditionsBuilder_ == null) {
        ensureConditionsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, conditions_);
        onChanged();
      } else {
        conditionsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public Builder clearConditions() {
      if (conditionsBuilder_ == null) {
        conditions_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        conditionsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public Builder removeConditions(int index) {
      if (conditionsBuilder_ == null) {
        ensureConditionsIsMutable();
        conditions_.remove(index);
        onChanged();
      } else {
        conditionsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public com.kikitrade.activity.facade.roster.Condition.Builder getConditionsBuilder(
        int index) {
      return getConditionsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public com.kikitrade.activity.facade.roster.ConditionOrBuilder getConditionsOrBuilder(
        int index) {
      if (conditionsBuilder_ == null) {
        return conditions_.get(index);  } else {
        return conditionsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.roster.ConditionOrBuilder> 
         getConditionsOrBuilderList() {
      if (conditionsBuilder_ != null) {
        return conditionsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(conditions_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public com.kikitrade.activity.facade.roster.Condition.Builder addConditionsBuilder() {
      return getConditionsFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.roster.Condition.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public com.kikitrade.activity.facade.roster.Condition.Builder addConditionsBuilder(
        int index) {
      return getConditionsFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.roster.Condition.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.roster.Condition conditions = 5;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.roster.Condition.Builder> 
         getConditionsBuilderList() {
      return getConditionsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.roster.Condition, com.kikitrade.activity.facade.roster.Condition.Builder, com.kikitrade.activity.facade.roster.ConditionOrBuilder> 
        getConditionsFieldBuilder() {
      if (conditionsBuilder_ == null) {
        conditionsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.roster.Condition, com.kikitrade.activity.facade.roster.Condition.Builder, com.kikitrade.activity.facade.roster.ConditionOrBuilder>(
                conditions_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        conditions_ = null;
      }
      return conditionsBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.roster.ConditionDTO)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.roster.ConditionDTO)
  private static final com.kikitrade.activity.facade.roster.ConditionDTO DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.roster.ConditionDTO();
  }

  public static com.kikitrade.activity.facade.roster.ConditionDTO getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ConditionDTO>
      PARSER = new com.google.protobuf.AbstractParser<ConditionDTO>() {
    @java.lang.Override
    public ConditionDTO parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ConditionDTO> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ConditionDTO> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.roster.ConditionDTO getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

