// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PropFacade.proto

package com.kikitrade.activity.facade.prop;

public interface GetPropRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.prop.GetPropRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string saasId = 2;</code>
   * @return The saasId.
   */
  java.lang.String getSaasId();
  /**
   * <code>string saasId = 2;</code>
   * @return The bytes for saasId.
   */
  com.google.protobuf.ByteString
      getSaasIdBytes();
}
