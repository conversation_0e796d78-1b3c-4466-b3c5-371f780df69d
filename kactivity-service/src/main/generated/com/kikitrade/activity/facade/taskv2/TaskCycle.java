// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Task.proto

package com.kikitrade.activity.facade.taskv2;

/**
 * <pre>
 **
 *任务周期
 * </pre>
 *
 * Protobuf enum {@code com.kikitrade.activity.facade.taskv2.TaskCycle}
 */
public enum TaskCycle
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *一次性
   * </pre>
   *
   * <code>once = 0;</code>
   */
  once(0),
  /**
   * <pre>
   *每日任务
   * </pre>
   *
   * <code>daily = 1;</code>
   */
  daily(1),
  /**
   * <pre>
   *每周任务
   * </pre>
   *
   * <code>weekly = 2;</code>
   */
  weekly(2),
  /**
   * <pre>
   *每月任务
   * </pre>
   *
   * <code>monthly = 3;</code>
   */
  monthly(3),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *一次性
   * </pre>
   *
   * <code>once = 0;</code>
   */
  public static final int once_VALUE = 0;
  /**
   * <pre>
   *每日任务
   * </pre>
   *
   * <code>daily = 1;</code>
   */
  public static final int daily_VALUE = 1;
  /**
   * <pre>
   *每周任务
   * </pre>
   *
   * <code>weekly = 2;</code>
   */
  public static final int weekly_VALUE = 2;
  /**
   * <pre>
   *每月任务
   * </pre>
   *
   * <code>monthly = 3;</code>
   */
  public static final int monthly_VALUE = 3;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static TaskCycle valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static TaskCycle forNumber(int value) {
    switch (value) {
      case 0: return once;
      case 1: return daily;
      case 2: return weekly;
      case 3: return monthly;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<TaskCycle>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      TaskCycle> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<TaskCycle>() {
          public TaskCycle findValueByNumber(int number) {
            return TaskCycle.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.getDescriptor().getEnumTypes().get(1);
  }

  private static final TaskCycle[] VALUES = values();

  public static TaskCycle valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private TaskCycle(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.taskv2.TaskCycle)
}

