// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityRewardFacade.proto

package com.kikitrade.activity.facade.reward;

public interface ManualRewardRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.reward.ManualRewardRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string customerId = 1;</code>
   * @return The customerId.
   */
  java.lang.String getCustomerId();
  /**
   * <code>string customerId = 1;</code>
   * @return The bytes for customerId.
   */
  com.google.protobuf.ByteString
      getCustomerIdBytes();

  /**
   * <code>.com.kikitrade.activity.facade.reward.Decimal amount = 2;</code>
   * @return Whether the amount field is set.
   */
  boolean hasAmount();
  /**
   * <code>.com.kikitrade.activity.facade.reward.Decimal amount = 2;</code>
   * @return The amount.
   */
  com.kikitrade.activity.facade.reward.Decimal getAmount();
  /**
   * <code>.com.kikitrade.activity.facade.reward.Decimal amount = 2;</code>
   */
  com.kikitrade.activity.facade.reward.DecimalOrBuilder getAmountOrBuilder();

  /**
   * <code>string currency = 3;</code>
   * @return The currency.
   */
  java.lang.String getCurrency();
  /**
   * <code>string currency = 3;</code>
   * @return The bytes for currency.
   */
  com.google.protobuf.ByteString
      getCurrencyBytes();

  /**
   * <code>.com.kikitrade.activity.facade.reward.TransactionTypeEnum type = 4;</code>
   * @return The enum numeric value on the wire for type.
   */
  int getTypeValue();
  /**
   * <code>.com.kikitrade.activity.facade.reward.TransactionTypeEnum type = 4;</code>
   * @return The type.
   */
  com.kikitrade.activity.facade.reward.TransactionTypeEnum getType();

  /**
   * <code>int64 rewardId = 5;</code>
   * @return The rewardId.
   */
  long getRewardId();
}
