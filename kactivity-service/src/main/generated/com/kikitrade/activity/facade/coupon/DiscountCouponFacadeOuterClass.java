// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: DiscountCouponFacade.proto

package com.kikitrade.activity.facade.coupon;

public final class DiscountCouponFacadeOuterClass {
  private DiscountCouponFacadeOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_coupon_DiscountCouponRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_coupon_DiscountCouponRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_coupon_DiscountCouponResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_coupon_DiscountCouponResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\032DiscountCouponFacade.proto\022$com.kikitr" +
      "ade.activity.facade.coupon\032\037google/proto" +
      "buf/timestamp.proto\"\370\001\n\025DiscountCouponRe" +
      "quest\022\n\n\002id\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\r\n\005image" +
      "\030\003 \001(\t\022\033\n\023shopifyDiscountCode\030\004 \001(\t\022\020\n\010v" +
      "alidDay\030\005 \001(\005\022H\n\014discountType\030\006 \001(\01622.co" +
      "m.kikitrade.activity.facade.coupon.Disco" +
      "untType\022\025\n\rdiscountValue\030\007 \001(\001\022\017\n\007applyT" +
      "o\030\010 \001(\t\022\025\n\rminLimitPrice\030\t \001(\001\"F\n\026Discou" +
      "ntCouponResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007mes" +
      "sage\030\002 \001(\t\022\n\n\002id\030\003 \001(\t*$\n\014DiscountType\022\010" +
      "\n\004RATE\020\000\022\n\n\006REDUCE\020\0012\252\001\n\024DiscountCouponF" +
      "acade\022\221\001\n\024upsertDiscountCoupon\022;.com.kik" +
      "itrade.activity.facade.coupon.DiscountCo" +
      "uponRequest\032<.com.kikitrade.activity.fac" +
      "ade.coupon.DiscountCouponResponseB(\n$com" +
      ".kikitrade.activity.facade.couponP\001b\006pro" +
      "to3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.TimestampProto.getDescriptor(),
        });
    internal_static_com_kikitrade_activity_facade_coupon_DiscountCouponRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_activity_facade_coupon_DiscountCouponRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_coupon_DiscountCouponRequest_descriptor,
        new java.lang.String[] { "Id", "Name", "Image", "ShopifyDiscountCode", "ValidDay", "DiscountType", "DiscountValue", "ApplyTo", "MinLimitPrice", });
    internal_static_com_kikitrade_activity_facade_coupon_DiscountCouponResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_activity_facade_coupon_DiscountCouponResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_coupon_DiscountCouponResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "Id", });
    com.google.protobuf.TimestampProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
