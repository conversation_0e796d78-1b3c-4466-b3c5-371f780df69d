// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityRosterRule.proto

package com.kikitrade.activity.facade.roster;

public final class ActivityRosterRule {
  private ActivityRosterRule() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_roster_ConditionParameterResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_roster_ConditionParameterResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_roster_ConditionParameter_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_roster_ConditionParameter_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_roster_ConditionVO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_roster_ConditionVO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_roster_Condition_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_roster_Condition_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_roster_EmptyRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_roster_EmptyRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_roster_ConditionRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_roster_ConditionRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_roster_ConditionDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_roster_ConditionDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_roster_CommonResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_roster_CommonResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\030ActivityRosterRule.proto\022$com.kikitrad" +
      "e.activity.facade.roster\"r\n\032ConditionPar" +
      "ameterResponse\022T\n\022conditionParameter\030\001 \003" +
      "(\01328.com.kikitrade.activity.facade.roste" +
      "r.ConditionParameter\"h\n\022ConditionParamet" +
      "er\022\014\n\004code\030\001 \001(\t\022D\n\tcondition\030\002 \003(\01321.co" +
      "m.kikitrade.activity.facade.roster.Condi" +
      "tionVO\"\202\001\n\013ConditionVO\022\014\n\004name\030\001 \001(\t\022E\n\013" +
      "filterTypes\030\002 \003(\01620.com.kikitrade.activi" +
      "ty.facade.roster.FilterType\022\r\n\005alisa\030\003 \001" +
      "(\t\022\017\n\007dynamic\030\004 \001(\010\"o\n\tCondition\022\014\n\004name" +
      "\030\001 \001(\t\022E\n\013filterTypes\030\002 \001(\01620.com.kikitr" +
      "ade.activity.facade.roster.FilterType\022\r\n" +
      "\005value\030\003 \001(\t\"\016\n\014EmptyRequest\" \n\020Conditio" +
      "nRequest\022\014\n\004code\030\001 \001(\t\"\273\001\n\014ConditionDTO\022" +
      "\n\n\002id\030\001 \001(\t\022\014\n\004code\030\002 \001(\t\022<\n\006status\030\003 \001(" +
      "\0162,.com.kikitrade.activity.facade.roster" +
      ".Status\022\016\n\006remark\030\004 \001(\t\022C\n\nconditions\030\005 " +
      "\003(\0132/.com.kikitrade.activity.facade.rost" +
      "er.Condition\"2\n\016CommonResponse\022\017\n\007succes" +
      "s\030\001 \001(\010\022\017\n\007message\030\002 \001(\t* \n\006Status\022\n\n\006AC" +
      "TIVE\020\000\022\n\n\006UNABLE\020\001*\215\001\n\nFilterType\022\n\n\006EQU" +
      "ALS\020\000\022\016\n\nNOT_EQUALS\020\001\022\020\n\014GREATER_THAN\020\002\022" +
      "\r\n\tLESS_THAN\020\003\022\027\n\023GREATER_THAN_EQUALS\020\004\022" +
      "\024\n\020LESS_THAN_EQUALS\020\005\022\006\n\002IN\020\006\022\013\n\007BETWEEN" +
      "\020\0072\241\002\n\030ActivityRosterRuleFacade\022\211\001\n\021getC" +
      "onditionNames\0222.com.kikitrade.activity.f" +
      "acade.roster.EmptyRequest\<EMAIL>" +
      "e.activity.facade.roster.ConditionParame" +
      "terResponse\022y\n\rsaveCondition\0222.com.kikit" +
      "rade.activity.facade.roster.ConditionDTO" +
      "\0324.com.kikitrade.activity.facade.roster." +
      "CommonResponseB(\n$com.kikitrade.activity" +
      ".facade.rosterP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_kikitrade_activity_facade_roster_ConditionParameterResponse_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_activity_facade_roster_ConditionParameterResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_roster_ConditionParameterResponse_descriptor,
        new java.lang.String[] { "ConditionParameter", });
    internal_static_com_kikitrade_activity_facade_roster_ConditionParameter_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_activity_facade_roster_ConditionParameter_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_roster_ConditionParameter_descriptor,
        new java.lang.String[] { "Code", "Condition", });
    internal_static_com_kikitrade_activity_facade_roster_ConditionVO_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_kikitrade_activity_facade_roster_ConditionVO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_roster_ConditionVO_descriptor,
        new java.lang.String[] { "Name", "FilterTypes", "Alisa", "Dynamic", });
    internal_static_com_kikitrade_activity_facade_roster_Condition_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_kikitrade_activity_facade_roster_Condition_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_roster_Condition_descriptor,
        new java.lang.String[] { "Name", "FilterTypes", "Value", });
    internal_static_com_kikitrade_activity_facade_roster_EmptyRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_kikitrade_activity_facade_roster_EmptyRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_roster_EmptyRequest_descriptor,
        new java.lang.String[] { });
    internal_static_com_kikitrade_activity_facade_roster_ConditionRequest_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_kikitrade_activity_facade_roster_ConditionRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_roster_ConditionRequest_descriptor,
        new java.lang.String[] { "Code", });
    internal_static_com_kikitrade_activity_facade_roster_ConditionDTO_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_kikitrade_activity_facade_roster_ConditionDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_roster_ConditionDTO_descriptor,
        new java.lang.String[] { "Id", "Code", "Status", "Remark", "Conditions", });
    internal_static_com_kikitrade_activity_facade_roster_CommonResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_kikitrade_activity_facade_roster_CommonResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_roster_CommonResponse_descriptor,
        new java.lang.String[] { "Success", "Message", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
