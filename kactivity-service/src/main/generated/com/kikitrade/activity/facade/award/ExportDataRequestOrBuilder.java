// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface ExportDataRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.ExportDataRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *活动id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   *活动id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string conditionId = 2;</code>
   * @return The conditionId.
   */
  java.lang.String getConditionId();
  /**
   * <code>string conditionId = 2;</code>
   * @return The bytes for conditionId.
   */
  com.google.protobuf.ByteString
      getConditionIdBytes();

  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
   */
  java.util.List<com.kikitrade.activity.facade.award.Condition> 
      getConditionsList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
   */
  com.kikitrade.activity.facade.award.Condition getConditions(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
   */
  int getConditionsCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.award.ConditionOrBuilder> 
      getConditionsOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
   */
  com.kikitrade.activity.facade.award.ConditionOrBuilder getConditionsOrBuilder(
      int index);
}
