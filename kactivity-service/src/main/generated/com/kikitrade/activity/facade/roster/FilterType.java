// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityRosterRule.proto

package com.kikitrade.activity.facade.roster;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.roster.FilterType}
 */
public enum FilterType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *等于
   * </pre>
   *
   * <code>EQUALS = 0;</code>
   */
  EQUALS(0),
  /**
   * <pre>
   *不等于
   * </pre>
   *
   * <code>NOT_EQUALS = 1;</code>
   */
  NOT_EQUALS(1),
  /**
   * <pre>
   *大于
   * </pre>
   *
   * <code>GREATER_THAN = 2;</code>
   */
  GREATER_THAN(2),
  /**
   * <pre>
   *小于
   * </pre>
   *
   * <code>LESS_THAN = 3;</code>
   */
  LESS_THAN(3),
  /**
   * <pre>
   *大于等于
   * </pre>
   *
   * <code>GREATER_THAN_EQUALS = 4;</code>
   */
  GREATER_THAN_EQUALS(4),
  /**
   * <pre>
   *小于等于
   * </pre>
   *
   * <code>LESS_THAN_EQUALS = 5;</code>
   */
  LESS_THAN_EQUALS(5),
  /**
   * <pre>
   *IN
   * </pre>
   *
   * <code>IN = 6;</code>
   */
  IN(6),
  /**
   * <pre>
   *BETWEEN
   * </pre>
   *
   * <code>BETWEEN = 7;</code>
   */
  BETWEEN(7),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *等于
   * </pre>
   *
   * <code>EQUALS = 0;</code>
   */
  public static final int EQUALS_VALUE = 0;
  /**
   * <pre>
   *不等于
   * </pre>
   *
   * <code>NOT_EQUALS = 1;</code>
   */
  public static final int NOT_EQUALS_VALUE = 1;
  /**
   * <pre>
   *大于
   * </pre>
   *
   * <code>GREATER_THAN = 2;</code>
   */
  public static final int GREATER_THAN_VALUE = 2;
  /**
   * <pre>
   *小于
   * </pre>
   *
   * <code>LESS_THAN = 3;</code>
   */
  public static final int LESS_THAN_VALUE = 3;
  /**
   * <pre>
   *大于等于
   * </pre>
   *
   * <code>GREATER_THAN_EQUALS = 4;</code>
   */
  public static final int GREATER_THAN_EQUALS_VALUE = 4;
  /**
   * <pre>
   *小于等于
   * </pre>
   *
   * <code>LESS_THAN_EQUALS = 5;</code>
   */
  public static final int LESS_THAN_EQUALS_VALUE = 5;
  /**
   * <pre>
   *IN
   * </pre>
   *
   * <code>IN = 6;</code>
   */
  public static final int IN_VALUE = 6;
  /**
   * <pre>
   *BETWEEN
   * </pre>
   *
   * <code>BETWEEN = 7;</code>
   */
  public static final int BETWEEN_VALUE = 7;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static FilterType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static FilterType forNumber(int value) {
    switch (value) {
      case 0: return EQUALS;
      case 1: return NOT_EQUALS;
      case 2: return GREATER_THAN;
      case 3: return LESS_THAN;
      case 4: return GREATER_THAN_EQUALS;
      case 5: return LESS_THAN_EQUALS;
      case 6: return IN;
      case 7: return BETWEEN;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<FilterType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      FilterType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<FilterType>() {
          public FilterType findValueByNumber(int number) {
            return FilterType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.roster.ActivityRosterRuleFacadeOutClass.getDescriptor().getEnumTypes().get(1);
  }

  private static final FilterType[] VALUES = values();

  public static FilterType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private FilterType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.roster.FilterType)
}

