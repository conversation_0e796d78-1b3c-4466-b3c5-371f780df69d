// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityRewardFacade.proto

package com.kikitrade.activity.facade.reward;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.reward.TransactionTypeEnum}
 */
public enum TransactionTypeEnum
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>REWARD = 0;</code>
   */
  REWARD(0),
  /**
   * <pre>
   *发放积分
   * </pre>
   *
   * <code>POINT = 1;</code>
   */
  POINT(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>REWARD = 0;</code>
   */
  public static final int REWARD_VALUE = 0;
  /**
   * <pre>
   *发放积分
   * </pre>
   *
   * <code>POINT = 1;</code>
   */
  public static final int POINT_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static TransactionTypeEnum valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static TransactionTypeEnum forNumber(int value) {
    switch (value) {
      case 0: return REWARD;
      case 1: return POINT;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<TransactionTypeEnum>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      TransactionTypeEnum> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<TransactionTypeEnum>() {
          public TransactionTypeEnum findValueByNumber(int number) {
            return TransactionTypeEnum.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.reward.ActivityRewardFacadeOuterClass.getDescriptor().getEnumTypes().get(0);
  }

  private static final TransactionTypeEnum[] VALUES = values();

  public static TransactionTypeEnum valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private TransactionTypeEnum(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.reward.TransactionTypeEnum)
}

