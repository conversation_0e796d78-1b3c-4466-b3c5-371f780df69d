// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.award.LotteryItem}
 */
public final class LotteryItem extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.award.LotteryItem)
    LotteryItemOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LotteryItem.newBuilder() to construct.
  private LotteryItem(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LotteryItem() {
    name_ = "";
    currency_ = "";
    amount_ = "";
    percent_ = "";
    awardType_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LotteryItem();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_LotteryItem_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_LotteryItem_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.award.LotteryItem.class, com.kikitrade.activity.facade.award.LotteryItem.Builder.class);
  }

  public static final int NAME_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object name_ = "";
  /**
   * <pre>
   *奖励名称
   * </pre>
   *
   * <code>string name = 1;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *奖励名称
   * </pre>
   *
   * <code>string name = 1;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CURRENCY_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object currency_ = "";
  /**
   * <pre>
   *奖励类型
   * </pre>
   *
   * <code>string currency = 2;</code>
   * @return The currency.
   */
  @java.lang.Override
  public java.lang.String getCurrency() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      currency_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *奖励类型
   * </pre>
   *
   * <code>string currency = 2;</code>
   * @return The bytes for currency.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCurrencyBytes() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      currency_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AMOUNT_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object amount_ = "";
  /**
   * <pre>
   *投放奖励
   * </pre>
   *
   * <code>string amount = 3;</code>
   * @return The amount.
   */
  @java.lang.Override
  public java.lang.String getAmount() {
    java.lang.Object ref = amount_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      amount_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *投放奖励
   * </pre>
   *
   * <code>string amount = 3;</code>
   * @return The bytes for amount.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAmountBytes() {
    java.lang.Object ref = amount_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      amount_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NUM_FIELD_NUMBER = 4;
  private int num_ = 0;
  /**
   * <pre>
   *投放数量
   * </pre>
   *
   * <code>int32 num = 4;</code>
   * @return The num.
   */
  @java.lang.Override
  public int getNum() {
    return num_;
  }

  public static final int PERCENT_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object percent_ = "";
  /**
   * <pre>
   *中奖概率
   * </pre>
   *
   * <code>string percent = 5;</code>
   * @return The percent.
   */
  @java.lang.Override
  public java.lang.String getPercent() {
    java.lang.Object ref = percent_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      percent_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *中奖概率
   * </pre>
   *
   * <code>string percent = 5;</code>
   * @return The bytes for percent.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPercentBytes() {
    java.lang.Object ref = percent_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      percent_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ISLOW_FIELD_NUMBER = 6;
  private boolean isLow_ = false;
  /**
   * <pre>
   *是否兜底
   * </pre>
   *
   * <code>bool isLow = 6;</code>
   * @return The isLow.
   */
  @java.lang.Override
  public boolean getIsLow() {
    return isLow_;
  }

  public static final int AWARDTYPE_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object awardType_ = "";
  /**
   * <pre>
   *POINT、TOKEN
   * </pre>
   *
   * <code>string awardType = 7;</code>
   * @return The awardType.
   */
  @java.lang.Override
  public java.lang.String getAwardType() {
    java.lang.Object ref = awardType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      awardType_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *POINT、TOKEN
   * </pre>
   *
   * <code>string awardType = 7;</code>
   * @return The bytes for awardType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAwardTypeBytes() {
    java.lang.Object ref = awardType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      awardType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REMAINNUM_FIELD_NUMBER = 8;
  private int remainNum_ = 0;
  /**
   * <pre>
   *剩余数量
   * </pre>
   *
   * <code>int32 remainNum = 8;</code>
   * @return The remainNum.
   */
  @java.lang.Override
  public int getRemainNum() {
    return remainNum_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(currency_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, currency_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amount_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, amount_);
    }
    if (num_ != 0) {
      output.writeInt32(4, num_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(percent_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, percent_);
    }
    if (isLow_ != false) {
      output.writeBool(6, isLow_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(awardType_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, awardType_);
    }
    if (remainNum_ != 0) {
      output.writeInt32(8, remainNum_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(currency_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, currency_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amount_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, amount_);
    }
    if (num_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, num_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(percent_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, percent_);
    }
    if (isLow_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(6, isLow_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(awardType_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, awardType_);
    }
    if (remainNum_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, remainNum_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.award.LotteryItem)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.award.LotteryItem other = (com.kikitrade.activity.facade.award.LotteryItem) obj;

    if (!getName()
        .equals(other.getName())) return false;
    if (!getCurrency()
        .equals(other.getCurrency())) return false;
    if (!getAmount()
        .equals(other.getAmount())) return false;
    if (getNum()
        != other.getNum()) return false;
    if (!getPercent()
        .equals(other.getPercent())) return false;
    if (getIsLow()
        != other.getIsLow()) return false;
    if (!getAwardType()
        .equals(other.getAwardType())) return false;
    if (getRemainNum()
        != other.getRemainNum()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + CURRENCY_FIELD_NUMBER;
    hash = (53 * hash) + getCurrency().hashCode();
    hash = (37 * hash) + AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getAmount().hashCode();
    hash = (37 * hash) + NUM_FIELD_NUMBER;
    hash = (53 * hash) + getNum();
    hash = (37 * hash) + PERCENT_FIELD_NUMBER;
    hash = (53 * hash) + getPercent().hashCode();
    hash = (37 * hash) + ISLOW_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIsLow());
    hash = (37 * hash) + AWARDTYPE_FIELD_NUMBER;
    hash = (53 * hash) + getAwardType().hashCode();
    hash = (37 * hash) + REMAINNUM_FIELD_NUMBER;
    hash = (53 * hash) + getRemainNum();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.award.LotteryItem parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.LotteryItem parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.LotteryItem parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.LotteryItem parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.LotteryItem parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.LotteryItem parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.LotteryItem parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.LotteryItem parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.award.LotteryItem parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.award.LotteryItem parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.LotteryItem parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.LotteryItem parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.award.LotteryItem prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.award.LotteryItem}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.award.LotteryItem)
      com.kikitrade.activity.facade.award.LotteryItemOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_LotteryItem_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_LotteryItem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.award.LotteryItem.class, com.kikitrade.activity.facade.award.LotteryItem.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.award.LotteryItem.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      name_ = "";
      currency_ = "";
      amount_ = "";
      num_ = 0;
      percent_ = "";
      isLow_ = false;
      awardType_ = "";
      remainNum_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_LotteryItem_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.LotteryItem getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.award.LotteryItem.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.LotteryItem build() {
      com.kikitrade.activity.facade.award.LotteryItem result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.LotteryItem buildPartial() {
      com.kikitrade.activity.facade.award.LotteryItem result = new com.kikitrade.activity.facade.award.LotteryItem(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.kikitrade.activity.facade.award.LotteryItem result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.name_ = name_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.currency_ = currency_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.amount_ = amount_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.num_ = num_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.percent_ = percent_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.isLow_ = isLow_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.awardType_ = awardType_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.remainNum_ = remainNum_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.award.LotteryItem) {
        return mergeFrom((com.kikitrade.activity.facade.award.LotteryItem)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.award.LotteryItem other) {
      if (other == com.kikitrade.activity.facade.award.LotteryItem.getDefaultInstance()) return this;
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getCurrency().isEmpty()) {
        currency_ = other.currency_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getAmount().isEmpty()) {
        amount_ = other.amount_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.getNum() != 0) {
        setNum(other.getNum());
      }
      if (!other.getPercent().isEmpty()) {
        percent_ = other.percent_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (other.getIsLow() != false) {
        setIsLow(other.getIsLow());
      }
      if (!other.getAwardType().isEmpty()) {
        awardType_ = other.awardType_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (other.getRemainNum() != 0) {
        setRemainNum(other.getRemainNum());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              name_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              currency_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              amount_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              num_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 42: {
              percent_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 48: {
              isLow_ = input.readBool();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 58: {
              awardType_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 64: {
              remainNum_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object name_ = "";
    /**
     * <pre>
     *奖励名称
     * </pre>
     *
     * <code>string name = 1;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *奖励名称
     * </pre>
     *
     * <code>string name = 1;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *奖励名称
     * </pre>
     *
     * <code>string name = 1;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖励名称
     * </pre>
     *
     * <code>string name = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      name_ = getDefaultInstance().getName();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖励名称
     * </pre>
     *
     * <code>string name = 1;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      name_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object currency_ = "";
    /**
     * <pre>
     *奖励类型
     * </pre>
     *
     * <code>string currency = 2;</code>
     * @return The currency.
     */
    public java.lang.String getCurrency() {
      java.lang.Object ref = currency_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        currency_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *奖励类型
     * </pre>
     *
     * <code>string currency = 2;</code>
     * @return The bytes for currency.
     */
    public com.google.protobuf.ByteString
        getCurrencyBytes() {
      java.lang.Object ref = currency_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        currency_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *奖励类型
     * </pre>
     *
     * <code>string currency = 2;</code>
     * @param value The currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrency(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      currency_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖励类型
     * </pre>
     *
     * <code>string currency = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurrency() {
      currency_ = getDefaultInstance().getCurrency();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *奖励类型
     * </pre>
     *
     * <code>string currency = 2;</code>
     * @param value The bytes for currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrencyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      currency_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object amount_ = "";
    /**
     * <pre>
     *投放奖励
     * </pre>
     *
     * <code>string amount = 3;</code>
     * @return The amount.
     */
    public java.lang.String getAmount() {
      java.lang.Object ref = amount_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        amount_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *投放奖励
     * </pre>
     *
     * <code>string amount = 3;</code>
     * @return The bytes for amount.
     */
    public com.google.protobuf.ByteString
        getAmountBytes() {
      java.lang.Object ref = amount_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        amount_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *投放奖励
     * </pre>
     *
     * <code>string amount = 3;</code>
     * @param value The amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmount(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      amount_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *投放奖励
     * </pre>
     *
     * <code>string amount = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmount() {
      amount_ = getDefaultInstance().getAmount();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *投放奖励
     * </pre>
     *
     * <code>string amount = 3;</code>
     * @param value The bytes for amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmountBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      amount_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private int num_ ;
    /**
     * <pre>
     *投放数量
     * </pre>
     *
     * <code>int32 num = 4;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }
    /**
     * <pre>
     *投放数量
     * </pre>
     *
     * <code>int32 num = 4;</code>
     * @param value The num to set.
     * @return This builder for chaining.
     */
    public Builder setNum(int value) {

      num_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *投放数量
     * </pre>
     *
     * <code>int32 num = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearNum() {
      bitField0_ = (bitField0_ & ~0x00000008);
      num_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object percent_ = "";
    /**
     * <pre>
     *中奖概率
     * </pre>
     *
     * <code>string percent = 5;</code>
     * @return The percent.
     */
    public java.lang.String getPercent() {
      java.lang.Object ref = percent_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        percent_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *中奖概率
     * </pre>
     *
     * <code>string percent = 5;</code>
     * @return The bytes for percent.
     */
    public com.google.protobuf.ByteString
        getPercentBytes() {
      java.lang.Object ref = percent_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        percent_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *中奖概率
     * </pre>
     *
     * <code>string percent = 5;</code>
     * @param value The percent to set.
     * @return This builder for chaining.
     */
    public Builder setPercent(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      percent_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *中奖概率
     * </pre>
     *
     * <code>string percent = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearPercent() {
      percent_ = getDefaultInstance().getPercent();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *中奖概率
     * </pre>
     *
     * <code>string percent = 5;</code>
     * @param value The bytes for percent to set.
     * @return This builder for chaining.
     */
    public Builder setPercentBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      percent_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private boolean isLow_ ;
    /**
     * <pre>
     *是否兜底
     * </pre>
     *
     * <code>bool isLow = 6;</code>
     * @return The isLow.
     */
    @java.lang.Override
    public boolean getIsLow() {
      return isLow_;
    }
    /**
     * <pre>
     *是否兜底
     * </pre>
     *
     * <code>bool isLow = 6;</code>
     * @param value The isLow to set.
     * @return This builder for chaining.
     */
    public Builder setIsLow(boolean value) {

      isLow_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *是否兜底
     * </pre>
     *
     * <code>bool isLow = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsLow() {
      bitField0_ = (bitField0_ & ~0x00000020);
      isLow_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object awardType_ = "";
    /**
     * <pre>
     *POINT、TOKEN
     * </pre>
     *
     * <code>string awardType = 7;</code>
     * @return The awardType.
     */
    public java.lang.String getAwardType() {
      java.lang.Object ref = awardType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        awardType_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *POINT、TOKEN
     * </pre>
     *
     * <code>string awardType = 7;</code>
     * @return The bytes for awardType.
     */
    public com.google.protobuf.ByteString
        getAwardTypeBytes() {
      java.lang.Object ref = awardType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        awardType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *POINT、TOKEN
     * </pre>
     *
     * <code>string awardType = 7;</code>
     * @param value The awardType to set.
     * @return This builder for chaining.
     */
    public Builder setAwardType(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      awardType_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *POINT、TOKEN
     * </pre>
     *
     * <code>string awardType = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearAwardType() {
      awardType_ = getDefaultInstance().getAwardType();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *POINT、TOKEN
     * </pre>
     *
     * <code>string awardType = 7;</code>
     * @param value The bytes for awardType to set.
     * @return This builder for chaining.
     */
    public Builder setAwardTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      awardType_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private int remainNum_ ;
    /**
     * <pre>
     *剩余数量
     * </pre>
     *
     * <code>int32 remainNum = 8;</code>
     * @return The remainNum.
     */
    @java.lang.Override
    public int getRemainNum() {
      return remainNum_;
    }
    /**
     * <pre>
     *剩余数量
     * </pre>
     *
     * <code>int32 remainNum = 8;</code>
     * @param value The remainNum to set.
     * @return This builder for chaining.
     */
    public Builder setRemainNum(int value) {

      remainNum_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *剩余数量
     * </pre>
     *
     * <code>int32 remainNum = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearRemainNum() {
      bitField0_ = (bitField0_ & ~0x00000080);
      remainNum_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.award.LotteryItem)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.award.LotteryItem)
  private static final com.kikitrade.activity.facade.award.LotteryItem DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.award.LotteryItem();
  }

  public static com.kikitrade.activity.facade.award.LotteryItem getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LotteryItem>
      PARSER = new com.google.protobuf.AbstractParser<LotteryItem>() {
    @java.lang.Override
    public LotteryItem parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<LotteryItem> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LotteryItem> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.award.LotteryItem getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

