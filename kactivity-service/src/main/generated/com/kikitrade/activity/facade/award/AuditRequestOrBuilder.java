// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface AuditRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.AuditRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>.com.kikitrade.activity.facade.award.AuditTypeEnum auditType = 2;</code>
   * @return The enum numeric value on the wire for auditType.
   */
  int getAuditTypeValue();
  /**
   * <code>.com.kikitrade.activity.facade.award.AuditTypeEnum auditType = 2;</code>
   * @return The auditType.
   */
  com.kikitrade.activity.facade.award.AuditTypeEnum getAuditType();
}
