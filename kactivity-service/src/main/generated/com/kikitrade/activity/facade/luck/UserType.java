// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: LuckFortuneFacade.proto

package com.kikitrade.activity.facade.luck;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.luck.UserType}
 */
public enum UserType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *普通用户
   * </pre>
   *
   * <code>COMMON = 0;</code>
   */
  COMMON(0),
  /**
   * <pre>
   *KOL
   * </pre>
   *
   * <code>KOL = 2;</code>
   */
  KOL(2),
  /**
   * <pre>
   *自媒体、新闻媒体
   * </pre>
   *
   * <code>MEDIA = 3;</code>
   */
  MEDIA(3),
  /**
   * <code>ORGANIZATION = 4;</code>
   */
  ORGANIZATION(4),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *普通用户
   * </pre>
   *
   * <code>COMMON = 0;</code>
   */
  public static final int COMMON_VALUE = 0;
  /**
   * <pre>
   *KOL
   * </pre>
   *
   * <code>KOL = 2;</code>
   */
  public static final int KOL_VALUE = 2;
  /**
   * <pre>
   *自媒体、新闻媒体
   * </pre>
   *
   * <code>MEDIA = 3;</code>
   */
  public static final int MEDIA_VALUE = 3;
  /**
   * <code>ORGANIZATION = 4;</code>
   */
  public static final int ORGANIZATION_VALUE = 4;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static UserType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static UserType forNumber(int value) {
    switch (value) {
      case 0: return COMMON;
      case 2: return KOL;
      case 3: return MEDIA;
      case 4: return ORGANIZATION;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<UserType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      UserType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<UserType>() {
          public UserType findValueByNumber(int number) {
            return UserType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.luck.LuckFortuneFacadeOuterClass.getDescriptor().getEnumTypes().get(1);
  }

  private static final UserType[] VALUES = values();

  public static UserType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private UserType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.luck.UserType)
}

