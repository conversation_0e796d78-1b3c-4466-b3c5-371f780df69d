// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: TaskFacade.proto

package com.kikitrade.activity.facade.task;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.task.CycleType}
 */
public enum CycleType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>CYCLE = 0;</code>
   */
  CYCLE(0),
  /**
   * <code>ONCE = 1;</code>
   */
  ONCE(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>CYCLE = 0;</code>
   */
  public static final int CYCLE_VALUE = 0;
  /**
   * <code>ONCE = 1;</code>
   */
  public static final int ONCE_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static CycleType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static CycleType forNumber(int value) {
    switch (value) {
      case 0: return CYCLE;
      case 1: return ONCE;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<CycleType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      CycleType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<CycleType>() {
          public CycleType findValueByNumber(int number) {
            return CycleType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.task.ActivityTaskFacadeOutClass.getDescriptor().getEnumTypes().get(1);
  }

  private static final CycleType[] VALUES = values();

  public static CycleType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private CycleType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.task.CycleType)
}

