/*
* Licensed to the Apache Software Foundation (ASF) under one or more
* contributor license agreements.  See the NOTICE file distributed with
* this work for additional information regarding copyright ownership.
* The ASF licenses this file to You under the Apache License, Version 2.0
* (the "License"); you may not use this file except in compliance with
* the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

    package com.kikitrade.activity.facade.sticker;

import org.apache.dubbo.common.stream.StreamObserver;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.PathResolver;
import org.apache.dubbo.rpc.RpcException;
import org.apache.dubbo.rpc.ServerService;
import org.apache.dubbo.rpc.TriRpcStatus;
import org.apache.dubbo.rpc.model.MethodDescriptor;
import org.apache.dubbo.rpc.model.ServiceDescriptor;
import org.apache.dubbo.rpc.model.StubMethodDescriptor;
import org.apache.dubbo.rpc.model.StubServiceDescriptor;
import org.apache.dubbo.rpc.stub.BiStreamMethodHandler;
import org.apache.dubbo.rpc.stub.ServerStreamMethodHandler;
import org.apache.dubbo.rpc.stub.StubInvocationUtil;
import org.apache.dubbo.rpc.stub.StubInvoker;
import org.apache.dubbo.rpc.stub.StubMethodHandler;
import org.apache.dubbo.rpc.stub.StubSuppliers;
import org.apache.dubbo.rpc.stub.UnaryStubMethodHandler;

import com.google.protobuf.Message;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.concurrent.CompletableFuture;

public final class DubboStickerFacadeTriple {

    public static final String SERVICE_NAME = StickerFacade.SERVICE_NAME;

    private static final StubServiceDescriptor serviceDescriptor = new StubServiceDescriptor(SERVICE_NAME,StickerFacade.class);

    static {
        org.apache.dubbo.rpc.protocol.tri.service.SchemaDescriptorRegistry.addSchemaDescriptor(SERVICE_NAME,StickerFacadeOuterClass.getDescriptor());
        StubSuppliers.addSupplier(SERVICE_NAME, DubboStickerFacadeTriple::newStub);
        StubSuppliers.addSupplier(StickerFacade.JAVA_SERVICE_NAME,  DubboStickerFacadeTriple::newStub);
        StubSuppliers.addDescriptor(SERVICE_NAME, serviceDescriptor);
        StubSuppliers.addDescriptor(StickerFacade.JAVA_SERVICE_NAME, serviceDescriptor);
    }

    @SuppressWarnings("all")
    public static StickerFacade newStub(Invoker<?> invoker) {
        return new StickerFacadeStub((Invoker<StickerFacade>)invoker);
    }

    private static final StubMethodDescriptor upsertStickerMethod = new StubMethodDescriptor("upsertSticker",
    com.kikitrade.activity.facade.sticker.UpsertStickerRequest.class, com.kikitrade.activity.facade.sticker.UpsertStickerResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.sticker.UpsertStickerRequest::parseFrom,
    com.kikitrade.activity.facade.sticker.UpsertStickerResponse::parseFrom);

    private static final StubMethodDescriptor upsertStickerAsyncMethod = new StubMethodDescriptor("upsertSticker",
    com.kikitrade.activity.facade.sticker.UpsertStickerRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.sticker.UpsertStickerRequest::parseFrom,
    com.kikitrade.activity.facade.sticker.UpsertStickerResponse::parseFrom);

    private static final StubMethodDescriptor upsertStickerProxyAsyncMethod = new StubMethodDescriptor("upsertStickerAsync",
    com.kikitrade.activity.facade.sticker.UpsertStickerRequest.class, com.kikitrade.activity.facade.sticker.UpsertStickerResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.sticker.UpsertStickerRequest::parseFrom,
    com.kikitrade.activity.facade.sticker.UpsertStickerResponse::parseFrom);





    public static class StickerFacadeStub implements StickerFacade{
        private final Invoker<StickerFacade> invoker;

        public StickerFacadeStub(Invoker<StickerFacade> invoker) {
            this.invoker = invoker;
        }

        @Override
        public com.kikitrade.activity.facade.sticker.UpsertStickerResponse upsertSticker(com.kikitrade.activity.facade.sticker.UpsertStickerRequest request){
            return StubInvocationUtil.unaryCall(invoker, upsertStickerMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.sticker.UpsertStickerResponse> upsertStickerAsync(com.kikitrade.activity.facade.sticker.UpsertStickerRequest request){
            return StubInvocationUtil.unaryCall(invoker, upsertStickerAsyncMethod, request);
        }

        @Override
        public void upsertSticker(com.kikitrade.activity.facade.sticker.UpsertStickerRequest request, StreamObserver<com.kikitrade.activity.facade.sticker.UpsertStickerResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, upsertStickerMethod , request, responseObserver);
        }



    }

    public static abstract class StickerFacadeImplBase implements StickerFacade, ServerService<StickerFacade> {

        private <T, R> BiConsumer<T, StreamObserver<R>> syncToAsync(java.util.function.Function<T, R> syncFun) {
            return new BiConsumer<T, StreamObserver<R>>() {
                @Override
                public void accept(T t, StreamObserver<R> observer) {
                    try {
                        R ret = syncFun.apply(t);
                        observer.onNext(ret);
                        observer.onCompleted();
                    } catch (Throwable e) {
                        observer.onError(e);
                    }
                }
            };
        }

        @Override
        public final Invoker<StickerFacade> getInvoker(URL url) {
            PathResolver pathResolver = url.getOrDefaultFrameworkModel()
            .getExtensionLoader(PathResolver.class)
            .getDefaultExtension();
            Map<String,StubMethodHandler<?, ?>> handlers = new HashMap<>();

            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/upsertSticker" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/upsertStickerAsync" );

            BiConsumer<com.kikitrade.activity.facade.sticker.UpsertStickerRequest, StreamObserver<com.kikitrade.activity.facade.sticker.UpsertStickerResponse>> upsertStickerFunc = this::upsertSticker;
            handlers.put(upsertStickerMethod.getMethodName(), new UnaryStubMethodHandler<>(upsertStickerFunc));
            BiConsumer<com.kikitrade.activity.facade.sticker.UpsertStickerRequest, StreamObserver<com.kikitrade.activity.facade.sticker.UpsertStickerResponse>> upsertStickerAsyncFunc = syncToAsync(this::upsertSticker);
            handlers.put(upsertStickerProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(upsertStickerAsyncFunc));




            return new StubInvoker<>(this, url, StickerFacade.class, handlers);
        }


        @Override
        public com.kikitrade.activity.facade.sticker.UpsertStickerResponse upsertSticker(com.kikitrade.activity.facade.sticker.UpsertStickerRequest request){
            throw unimplementedMethodException(upsertStickerMethod);
        }





        @Override
        public final ServiceDescriptor getServiceDescriptor() {
            return serviceDescriptor;
        }
        private RpcException unimplementedMethodException(StubMethodDescriptor methodDescriptor) {
            return TriRpcStatus.UNIMPLEMENTED.withDescription(String.format("Method %s is unimplemented",
                "/" + serviceDescriptor.getInterfaceName() + "/" + methodDescriptor.getMethodName())).asException();
        }
    }

}
