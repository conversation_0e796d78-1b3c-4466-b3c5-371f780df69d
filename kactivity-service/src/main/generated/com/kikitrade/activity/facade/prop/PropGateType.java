// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PropFacade.proto

package com.kikitrade.activity.facade.prop;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.prop.PropGateType}
 */
public enum PropGateType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>None = 0;</code>
   */
  None(0),
  /**
   * <pre>
   *会员
   * </pre>
   *
   * <code>Membership = 1;</code>
   */
  Membership(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>None = 0;</code>
   */
  public static final int None_VALUE = 0;
  /**
   * <pre>
   *会员
   * </pre>
   *
   * <code>Membership = 1;</code>
   */
  public static final int Membership_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static PropGateType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static PropGateType forNumber(int value) {
    switch (value) {
      case 0: return None;
      case 1: return Membership;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<PropGateType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      PropGateType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<PropGateType>() {
          public PropGateType findValueByNumber(int number) {
            return PropGateType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.prop.PropFacadeOuterClass.getDescriptor().getEnumTypes().get(3);
  }

  private static final PropGateType[] VALUES = values();

  public static PropGateType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private PropGateType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.prop.PropGateType)
}

