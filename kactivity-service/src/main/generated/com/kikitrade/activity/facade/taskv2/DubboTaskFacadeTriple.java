/*
* Licensed to the Apache Software Foundation (ASF) under one or more
* contributor license agreements.  See the NOTICE file distributed with
* this work for additional information regarding copyright ownership.
* The ASF licenses this file to You under the Apache License, Version 2.0
* (the "License"); you may not use this file except in compliance with
* the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

    package com.kikitrade.activity.facade.taskv2;

import org.apache.dubbo.common.stream.StreamObserver;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.PathResolver;
import org.apache.dubbo.rpc.RpcException;
import org.apache.dubbo.rpc.ServerService;
import org.apache.dubbo.rpc.TriRpcStatus;
import org.apache.dubbo.rpc.model.MethodDescriptor;
import org.apache.dubbo.rpc.model.ServiceDescriptor;
import org.apache.dubbo.rpc.model.StubMethodDescriptor;
import org.apache.dubbo.rpc.model.StubServiceDescriptor;
import org.apache.dubbo.rpc.stub.BiStreamMethodHandler;
import org.apache.dubbo.rpc.stub.ServerStreamMethodHandler;
import org.apache.dubbo.rpc.stub.StubInvocationUtil;
import org.apache.dubbo.rpc.stub.StubInvoker;
import org.apache.dubbo.rpc.stub.StubMethodHandler;
import org.apache.dubbo.rpc.stub.StubSuppliers;
import org.apache.dubbo.rpc.stub.UnaryStubMethodHandler;

import com.google.protobuf.Message;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.concurrent.CompletableFuture;

public final class DubboTaskFacadeTriple {

    public static final String SERVICE_NAME = TaskFacade.SERVICE_NAME;

    private static final StubServiceDescriptor serviceDescriptor = new StubServiceDescriptor(SERVICE_NAME,TaskFacade.class);

    static {
        org.apache.dubbo.rpc.protocol.tri.service.SchemaDescriptorRegistry.addSchemaDescriptor(SERVICE_NAME,TaskFacadeOutClass.getDescriptor());
        StubSuppliers.addSupplier(SERVICE_NAME, DubboTaskFacadeTriple::newStub);
        StubSuppliers.addSupplier(TaskFacade.JAVA_SERVICE_NAME,  DubboTaskFacadeTriple::newStub);
        StubSuppliers.addDescriptor(SERVICE_NAME, serviceDescriptor);
        StubSuppliers.addDescriptor(TaskFacade.JAVA_SERVICE_NAME, serviceDescriptor);
    }

    @SuppressWarnings("all")
    public static TaskFacade newStub(Invoker<?> invoker) {
        return new TaskFacadeStub((Invoker<TaskFacade>)invoker);
    }

    /**
         * <pre>
         * 任务id
         * </pre>
         */
    private static final StubMethodDescriptor saveMethod = new StubMethodDescriptor("save",
    com.kikitrade.activity.facade.taskv2.TaskDTO.class, com.kikitrade.activity.facade.taskv2.IdVOResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.taskv2.TaskDTO::parseFrom,
    com.kikitrade.activity.facade.taskv2.IdVOResponse::parseFrom);

    private static final StubMethodDescriptor saveAsyncMethod = new StubMethodDescriptor("save",
    com.kikitrade.activity.facade.taskv2.TaskDTO.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.taskv2.TaskDTO::parseFrom,
    com.kikitrade.activity.facade.taskv2.IdVOResponse::parseFrom);

    private static final StubMethodDescriptor saveProxyAsyncMethod = new StubMethodDescriptor("saveAsync",
    com.kikitrade.activity.facade.taskv2.TaskDTO.class, com.kikitrade.activity.facade.taskv2.IdVOResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.taskv2.TaskDTO::parseFrom,
    com.kikitrade.activity.facade.taskv2.IdVOResponse::parseFrom);

    /**
         * <pre>
         * quest name
         * </pre>
         */
    private static final StubMethodDescriptor getCodeMethod = new StubMethodDescriptor("getCode",
    com.kikitrade.activity.facade.taskv2.EmptyRequest.class, com.kikitrade.activity.facade.taskv2.CodeVOResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.taskv2.EmptyRequest::parseFrom,
    com.kikitrade.activity.facade.taskv2.CodeVOResponse::parseFrom);

    private static final StubMethodDescriptor getCodeAsyncMethod = new StubMethodDescriptor("getCode",
    com.kikitrade.activity.facade.taskv2.EmptyRequest.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.taskv2.EmptyRequest::parseFrom,
    com.kikitrade.activity.facade.taskv2.CodeVOResponse::parseFrom);

    private static final StubMethodDescriptor getCodeProxyAsyncMethod = new StubMethodDescriptor("getCodeAsync",
    com.kikitrade.activity.facade.taskv2.EmptyRequest.class, com.kikitrade.activity.facade.taskv2.CodeVOResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.taskv2.EmptyRequest::parseFrom,
    com.kikitrade.activity.facade.taskv2.CodeVOResponse::parseFrom);





    public static class TaskFacadeStub implements TaskFacade{
        private final Invoker<TaskFacade> invoker;

        public TaskFacadeStub(Invoker<TaskFacade> invoker) {
            this.invoker = invoker;
        }

            /**
         * <pre>
         * 任务id
         * </pre>
         */
        @Override
        public com.kikitrade.activity.facade.taskv2.IdVOResponse save(com.kikitrade.activity.facade.taskv2.TaskDTO request){
            return StubInvocationUtil.unaryCall(invoker, saveMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.taskv2.IdVOResponse> saveAsync(com.kikitrade.activity.facade.taskv2.TaskDTO request){
            return StubInvocationUtil.unaryCall(invoker, saveAsyncMethod, request);
        }

            /**
         * <pre>
         * 任务id
         * </pre>
         */
        @Override
        public void save(com.kikitrade.activity.facade.taskv2.TaskDTO request, StreamObserver<com.kikitrade.activity.facade.taskv2.IdVOResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, saveMethod , request, responseObserver);
        }
            /**
         * <pre>
         * quest name
         * </pre>
         */
        @Override
        public com.kikitrade.activity.facade.taskv2.CodeVOResponse getCode(com.kikitrade.activity.facade.taskv2.EmptyRequest request){
            return StubInvocationUtil.unaryCall(invoker, getCodeMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.taskv2.CodeVOResponse> getCodeAsync(com.kikitrade.activity.facade.taskv2.EmptyRequest request){
            return StubInvocationUtil.unaryCall(invoker, getCodeAsyncMethod, request);
        }

            /**
         * <pre>
         * quest name
         * </pre>
         */
        @Override
        public void getCode(com.kikitrade.activity.facade.taskv2.EmptyRequest request, StreamObserver<com.kikitrade.activity.facade.taskv2.CodeVOResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, getCodeMethod , request, responseObserver);
        }



    }

    public static abstract class TaskFacadeImplBase implements TaskFacade, ServerService<TaskFacade> {

        private <T, R> BiConsumer<T, StreamObserver<R>> syncToAsync(java.util.function.Function<T, R> syncFun) {
            return new BiConsumer<T, StreamObserver<R>>() {
                @Override
                public void accept(T t, StreamObserver<R> observer) {
                    try {
                        R ret = syncFun.apply(t);
                        observer.onNext(ret);
                        observer.onCompleted();
                    } catch (Throwable e) {
                        observer.onError(e);
                    }
                }
            };
        }

        @Override
        public final Invoker<TaskFacade> getInvoker(URL url) {
            PathResolver pathResolver = url.getOrDefaultFrameworkModel()
            .getExtensionLoader(PathResolver.class)
            .getDefaultExtension();
            Map<String,StubMethodHandler<?, ?>> handlers = new HashMap<>();

            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/save" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/saveAsync" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/getCode" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/getCodeAsync" );

            BiConsumer<com.kikitrade.activity.facade.taskv2.TaskDTO, StreamObserver<com.kikitrade.activity.facade.taskv2.IdVOResponse>> saveFunc = this::save;
            handlers.put(saveMethod.getMethodName(), new UnaryStubMethodHandler<>(saveFunc));
            BiConsumer<com.kikitrade.activity.facade.taskv2.TaskDTO, StreamObserver<com.kikitrade.activity.facade.taskv2.IdVOResponse>> saveAsyncFunc = syncToAsync(this::save);
            handlers.put(saveProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(saveAsyncFunc));
            BiConsumer<com.kikitrade.activity.facade.taskv2.EmptyRequest, StreamObserver<com.kikitrade.activity.facade.taskv2.CodeVOResponse>> getCodeFunc = this::getCode;
            handlers.put(getCodeMethod.getMethodName(), new UnaryStubMethodHandler<>(getCodeFunc));
            BiConsumer<com.kikitrade.activity.facade.taskv2.EmptyRequest, StreamObserver<com.kikitrade.activity.facade.taskv2.CodeVOResponse>> getCodeAsyncFunc = syncToAsync(this::getCode);
            handlers.put(getCodeProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(getCodeAsyncFunc));




            return new StubInvoker<>(this, url, TaskFacade.class, handlers);
        }


        @Override
        public com.kikitrade.activity.facade.taskv2.IdVOResponse save(com.kikitrade.activity.facade.taskv2.TaskDTO request){
            throw unimplementedMethodException(saveMethod);
        }

        @Override
        public com.kikitrade.activity.facade.taskv2.CodeVOResponse getCode(com.kikitrade.activity.facade.taskv2.EmptyRequest request){
            throw unimplementedMethodException(getCodeMethod);
        }





        @Override
        public final ServiceDescriptor getServiceDescriptor() {
            return serviceDescriptor;
        }
        private RpcException unimplementedMethodException(StubMethodDescriptor methodDescriptor) {
            return TriRpcStatus.UNIMPLEMENTED.withDescription(String.format("Method %s is unimplemented",
                "/" + serviceDescriptor.getInterfaceName() + "/" + methodDescriptor.getMethodName())).asException();
        }
    }

}
