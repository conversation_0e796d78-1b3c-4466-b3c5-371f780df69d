// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: LuckFortuneFacade.proto

package com.kikitrade.activity.facade.luck;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO}
 */
public final class LuckFortuneRuleDTO extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO)
    LuckFortuneRuleDTOOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LuckFortuneRuleDTO.newBuilder() to construct.
  private LuckFortuneRuleDTO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LuckFortuneRuleDTO() {
    kycLevel_ = 0;
    userType_ = 0;
    releaseMin_ = "";
    releaseMax_ = "";
    releaseAccountDays_ = "";
    releaseAccountMonths_ = "";
    receiveMin_ = "";
    receiveMax_ = "";
    receiveAccountDays_ = "";
    receiveAccountMonths_ = "";
    id_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LuckFortuneRuleDTO();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.luck.LuckFortuneFacadeOuterClass.internal_static_com_kikitrade_activity_facade_luck_LuckFortuneRuleDTO_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.luck.LuckFortuneFacadeOuterClass.internal_static_com_kikitrade_activity_facade_luck_LuckFortuneRuleDTO_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO.class, com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO.Builder.class);
  }

  public static final int KYCLEVEL_FIELD_NUMBER = 1;
  private int kycLevel_ = 0;
  /**
   * <pre>
   *kyc等级
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.luck.KycLevel kycLevel = 1;</code>
   * @return The enum numeric value on the wire for kycLevel.
   */
  @java.lang.Override public int getKycLevelValue() {
    return kycLevel_;
  }
  /**
   * <pre>
   *kyc等级
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.luck.KycLevel kycLevel = 1;</code>
   * @return The kycLevel.
   */
  @java.lang.Override public com.kikitrade.activity.facade.luck.KycLevel getKycLevel() {
    com.kikitrade.activity.facade.luck.KycLevel result = com.kikitrade.activity.facade.luck.KycLevel.forNumber(kycLevel_);
    return result == null ? com.kikitrade.activity.facade.luck.KycLevel.UNRECOGNIZED : result;
  }

  public static final int USERTYPE_FIELD_NUMBER = 2;
  private int userType_ = 0;
  /**
   * <pre>
   *用户类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.luck.UserType userType = 2;</code>
   * @return The enum numeric value on the wire for userType.
   */
  @java.lang.Override public int getUserTypeValue() {
    return userType_;
  }
  /**
   * <pre>
   *用户类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.luck.UserType userType = 2;</code>
   * @return The userType.
   */
  @java.lang.Override public com.kikitrade.activity.facade.luck.UserType getUserType() {
    com.kikitrade.activity.facade.luck.UserType result = com.kikitrade.activity.facade.luck.UserType.forNumber(userType_);
    return result == null ? com.kikitrade.activity.facade.luck.UserType.UNRECOGNIZED : result;
  }

  public static final int RELEASEMIN_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object releaseMin_ = "";
  /**
   * <pre>
   *发放红包最小金额
   * </pre>
   *
   * <code>string releaseMin = 3;</code>
   * @return The releaseMin.
   */
  @java.lang.Override
  public java.lang.String getReleaseMin() {
    java.lang.Object ref = releaseMin_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      releaseMin_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *发放红包最小金额
   * </pre>
   *
   * <code>string releaseMin = 3;</code>
   * @return The bytes for releaseMin.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getReleaseMinBytes() {
    java.lang.Object ref = releaseMin_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      releaseMin_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RELEASEMAX_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object releaseMax_ = "";
  /**
   * <pre>
   *发放红包最大金额
   * </pre>
   *
   * <code>string releaseMax = 4;</code>
   * @return The releaseMax.
   */
  @java.lang.Override
  public java.lang.String getReleaseMax() {
    java.lang.Object ref = releaseMax_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      releaseMax_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *发放红包最大金额
   * </pre>
   *
   * <code>string releaseMax = 4;</code>
   * @return The bytes for releaseMax.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getReleaseMaxBytes() {
    java.lang.Object ref = releaseMax_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      releaseMax_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RELEASENUMMAX_FIELD_NUMBER = 5;
  private int releaseNumMax_ = 0;
  /**
   * <pre>
   *单个红包最大数量
   * </pre>
   *
   * <code>int32 releaseNumMax = 5;</code>
   * @return The releaseNumMax.
   */
  @java.lang.Override
  public int getReleaseNumMax() {
    return releaseNumMax_;
  }

  public static final int RELEASEACCOUNTDAYS_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object releaseAccountDays_ = "";
  /**
   * <pre>
   *24小时发放累积限额
   * </pre>
   *
   * <code>string releaseAccountDays = 6;</code>
   * @return The releaseAccountDays.
   */
  @java.lang.Override
  public java.lang.String getReleaseAccountDays() {
    java.lang.Object ref = releaseAccountDays_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      releaseAccountDays_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *24小时发放累积限额
   * </pre>
   *
   * <code>string releaseAccountDays = 6;</code>
   * @return The bytes for releaseAccountDays.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getReleaseAccountDaysBytes() {
    java.lang.Object ref = releaseAccountDays_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      releaseAccountDays_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RELEASEACCOUNTMONTHS_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object releaseAccountMonths_ = "";
  /**
   * <pre>
   *30天发放累积限额
   * </pre>
   *
   * <code>string releaseAccountMonths = 7;</code>
   * @return The releaseAccountMonths.
   */
  @java.lang.Override
  public java.lang.String getReleaseAccountMonths() {
    java.lang.Object ref = releaseAccountMonths_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      releaseAccountMonths_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *30天发放累积限额
   * </pre>
   *
   * <code>string releaseAccountMonths = 7;</code>
   * @return The bytes for releaseAccountMonths.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getReleaseAccountMonthsBytes() {
    java.lang.Object ref = releaseAccountMonths_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      releaseAccountMonths_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RECEIVEMIN_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object receiveMin_ = "";
  /**
   * <pre>
   *领取红包的最小金额
   * </pre>
   *
   * <code>string receiveMin = 8;</code>
   * @return The receiveMin.
   */
  @java.lang.Override
  public java.lang.String getReceiveMin() {
    java.lang.Object ref = receiveMin_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      receiveMin_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *领取红包的最小金额
   * </pre>
   *
   * <code>string receiveMin = 8;</code>
   * @return The bytes for receiveMin.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getReceiveMinBytes() {
    java.lang.Object ref = receiveMin_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      receiveMin_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RECEIVEMAX_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object receiveMax_ = "";
  /**
   * <pre>
   *领取红包的最小金额
   * </pre>
   *
   * <code>string receiveMax = 9;</code>
   * @return The receiveMax.
   */
  @java.lang.Override
  public java.lang.String getReceiveMax() {
    java.lang.Object ref = receiveMax_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      receiveMax_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *领取红包的最小金额
   * </pre>
   *
   * <code>string receiveMax = 9;</code>
   * @return The bytes for receiveMax.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getReceiveMaxBytes() {
    java.lang.Object ref = receiveMax_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      receiveMax_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RECEIVEACCOUNTDAYS_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object receiveAccountDays_ = "";
  /**
   * <pre>
   *24小时领取累积限额
   * </pre>
   *
   * <code>string receiveAccountDays = 10;</code>
   * @return The receiveAccountDays.
   */
  @java.lang.Override
  public java.lang.String getReceiveAccountDays() {
    java.lang.Object ref = receiveAccountDays_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      receiveAccountDays_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *24小时领取累积限额
   * </pre>
   *
   * <code>string receiveAccountDays = 10;</code>
   * @return The bytes for receiveAccountDays.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getReceiveAccountDaysBytes() {
    java.lang.Object ref = receiveAccountDays_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      receiveAccountDays_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RECEIVEACCOUNTMONTHS_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private volatile java.lang.Object receiveAccountMonths_ = "";
  /**
   * <pre>
   *30天领取累积限额
   * </pre>
   *
   * <code>string receiveAccountMonths = 11;</code>
   * @return The receiveAccountMonths.
   */
  @java.lang.Override
  public java.lang.String getReceiveAccountMonths() {
    java.lang.Object ref = receiveAccountMonths_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      receiveAccountMonths_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *30天领取累积限额
   * </pre>
   *
   * <code>string receiveAccountMonths = 11;</code>
   * @return The bytes for receiveAccountMonths.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getReceiveAccountMonthsBytes() {
    java.lang.Object ref = receiveAccountMonths_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      receiveAccountMonths_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RECEIVENUMDAYS_FIELD_NUMBER = 12;
  private int receiveNumDays_ = 0;
  /**
   * <pre>
   *24小时领取累积数量限制
   * </pre>
   *
   * <code>int32 receiveNumDays = 12;</code>
   * @return The receiveNumDays.
   */
  @java.lang.Override
  public int getReceiveNumDays() {
    return receiveNumDays_;
  }

  public static final int RECEIVENUMMONTHS_FIELD_NUMBER = 13;
  private int receiveNumMonths_ = 0;
  /**
   * <pre>
   *30天领取累积数量限制
   * </pre>
   *
   * <code>int32 receiveNumMonths = 13;</code>
   * @return The receiveNumMonths.
   */
  @java.lang.Override
  public int getReceiveNumMonths() {
    return receiveNumMonths_;
  }

  public static final int ID_FIELD_NUMBER = 14;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <pre>
   *红包规则id
   * </pre>
   *
   * <code>string id = 14;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *红包规则id
   * </pre>
   *
   * <code>string id = 14;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (kycLevel_ != com.kikitrade.activity.facade.luck.KycLevel.L0.getNumber()) {
      output.writeEnum(1, kycLevel_);
    }
    if (userType_ != com.kikitrade.activity.facade.luck.UserType.COMMON.getNumber()) {
      output.writeEnum(2, userType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(releaseMin_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, releaseMin_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(releaseMax_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, releaseMax_);
    }
    if (releaseNumMax_ != 0) {
      output.writeInt32(5, releaseNumMax_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(releaseAccountDays_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, releaseAccountDays_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(releaseAccountMonths_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, releaseAccountMonths_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(receiveMin_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, receiveMin_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(receiveMax_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, receiveMax_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(receiveAccountDays_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, receiveAccountDays_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(receiveAccountMonths_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, receiveAccountMonths_);
    }
    if (receiveNumDays_ != 0) {
      output.writeInt32(12, receiveNumDays_);
    }
    if (receiveNumMonths_ != 0) {
      output.writeInt32(13, receiveNumMonths_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 14, id_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (kycLevel_ != com.kikitrade.activity.facade.luck.KycLevel.L0.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(1, kycLevel_);
    }
    if (userType_ != com.kikitrade.activity.facade.luck.UserType.COMMON.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, userType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(releaseMin_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, releaseMin_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(releaseMax_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, releaseMax_);
    }
    if (releaseNumMax_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, releaseNumMax_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(releaseAccountDays_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, releaseAccountDays_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(releaseAccountMonths_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, releaseAccountMonths_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(receiveMin_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, receiveMin_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(receiveMax_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, receiveMax_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(receiveAccountDays_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, receiveAccountDays_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(receiveAccountMonths_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, receiveAccountMonths_);
    }
    if (receiveNumDays_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(12, receiveNumDays_);
    }
    if (receiveNumMonths_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(13, receiveNumMonths_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, id_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO other = (com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO) obj;

    if (kycLevel_ != other.kycLevel_) return false;
    if (userType_ != other.userType_) return false;
    if (!getReleaseMin()
        .equals(other.getReleaseMin())) return false;
    if (!getReleaseMax()
        .equals(other.getReleaseMax())) return false;
    if (getReleaseNumMax()
        != other.getReleaseNumMax()) return false;
    if (!getReleaseAccountDays()
        .equals(other.getReleaseAccountDays())) return false;
    if (!getReleaseAccountMonths()
        .equals(other.getReleaseAccountMonths())) return false;
    if (!getReceiveMin()
        .equals(other.getReceiveMin())) return false;
    if (!getReceiveMax()
        .equals(other.getReceiveMax())) return false;
    if (!getReceiveAccountDays()
        .equals(other.getReceiveAccountDays())) return false;
    if (!getReceiveAccountMonths()
        .equals(other.getReceiveAccountMonths())) return false;
    if (getReceiveNumDays()
        != other.getReceiveNumDays()) return false;
    if (getReceiveNumMonths()
        != other.getReceiveNumMonths()) return false;
    if (!getId()
        .equals(other.getId())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + KYCLEVEL_FIELD_NUMBER;
    hash = (53 * hash) + kycLevel_;
    hash = (37 * hash) + USERTYPE_FIELD_NUMBER;
    hash = (53 * hash) + userType_;
    hash = (37 * hash) + RELEASEMIN_FIELD_NUMBER;
    hash = (53 * hash) + getReleaseMin().hashCode();
    hash = (37 * hash) + RELEASEMAX_FIELD_NUMBER;
    hash = (53 * hash) + getReleaseMax().hashCode();
    hash = (37 * hash) + RELEASENUMMAX_FIELD_NUMBER;
    hash = (53 * hash) + getReleaseNumMax();
    hash = (37 * hash) + RELEASEACCOUNTDAYS_FIELD_NUMBER;
    hash = (53 * hash) + getReleaseAccountDays().hashCode();
    hash = (37 * hash) + RELEASEACCOUNTMONTHS_FIELD_NUMBER;
    hash = (53 * hash) + getReleaseAccountMonths().hashCode();
    hash = (37 * hash) + RECEIVEMIN_FIELD_NUMBER;
    hash = (53 * hash) + getReceiveMin().hashCode();
    hash = (37 * hash) + RECEIVEMAX_FIELD_NUMBER;
    hash = (53 * hash) + getReceiveMax().hashCode();
    hash = (37 * hash) + RECEIVEACCOUNTDAYS_FIELD_NUMBER;
    hash = (53 * hash) + getReceiveAccountDays().hashCode();
    hash = (37 * hash) + RECEIVEACCOUNTMONTHS_FIELD_NUMBER;
    hash = (53 * hash) + getReceiveAccountMonths().hashCode();
    hash = (37 * hash) + RECEIVENUMDAYS_FIELD_NUMBER;
    hash = (53 * hash) + getReceiveNumDays();
    hash = (37 * hash) + RECEIVENUMMONTHS_FIELD_NUMBER;
    hash = (53 * hash) + getReceiveNumMonths();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO)
      com.kikitrade.activity.facade.luck.LuckFortuneRuleDTOOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.luck.LuckFortuneFacadeOuterClass.internal_static_com_kikitrade_activity_facade_luck_LuckFortuneRuleDTO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.luck.LuckFortuneFacadeOuterClass.internal_static_com_kikitrade_activity_facade_luck_LuckFortuneRuleDTO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO.class, com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      kycLevel_ = 0;
      userType_ = 0;
      releaseMin_ = "";
      releaseMax_ = "";
      releaseNumMax_ = 0;
      releaseAccountDays_ = "";
      releaseAccountMonths_ = "";
      receiveMin_ = "";
      receiveMax_ = "";
      receiveAccountDays_ = "";
      receiveAccountMonths_ = "";
      receiveNumDays_ = 0;
      receiveNumMonths_ = 0;
      id_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.luck.LuckFortuneFacadeOuterClass.internal_static_com_kikitrade_activity_facade_luck_LuckFortuneRuleDTO_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO build() {
      com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO buildPartial() {
      com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO result = new com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.kycLevel_ = kycLevel_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.userType_ = userType_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.releaseMin_ = releaseMin_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.releaseMax_ = releaseMax_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.releaseNumMax_ = releaseNumMax_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.releaseAccountDays_ = releaseAccountDays_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.releaseAccountMonths_ = releaseAccountMonths_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.receiveMin_ = receiveMin_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.receiveMax_ = receiveMax_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.receiveAccountDays_ = receiveAccountDays_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.receiveAccountMonths_ = receiveAccountMonths_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.receiveNumDays_ = receiveNumDays_;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.receiveNumMonths_ = receiveNumMonths_;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.id_ = id_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO) {
        return mergeFrom((com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO other) {
      if (other == com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO.getDefaultInstance()) return this;
      if (other.kycLevel_ != 0) {
        setKycLevelValue(other.getKycLevelValue());
      }
      if (other.userType_ != 0) {
        setUserTypeValue(other.getUserTypeValue());
      }
      if (!other.getReleaseMin().isEmpty()) {
        releaseMin_ = other.releaseMin_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getReleaseMax().isEmpty()) {
        releaseMax_ = other.releaseMax_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.getReleaseNumMax() != 0) {
        setReleaseNumMax(other.getReleaseNumMax());
      }
      if (!other.getReleaseAccountDays().isEmpty()) {
        releaseAccountDays_ = other.releaseAccountDays_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (!other.getReleaseAccountMonths().isEmpty()) {
        releaseAccountMonths_ = other.releaseAccountMonths_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (!other.getReceiveMin().isEmpty()) {
        receiveMin_ = other.receiveMin_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (!other.getReceiveMax().isEmpty()) {
        receiveMax_ = other.receiveMax_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (!other.getReceiveAccountDays().isEmpty()) {
        receiveAccountDays_ = other.receiveAccountDays_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (!other.getReceiveAccountMonths().isEmpty()) {
        receiveAccountMonths_ = other.receiveAccountMonths_;
        bitField0_ |= 0x00000400;
        onChanged();
      }
      if (other.getReceiveNumDays() != 0) {
        setReceiveNumDays(other.getReceiveNumDays());
      }
      if (other.getReceiveNumMonths() != 0) {
        setReceiveNumMonths(other.getReceiveNumMonths());
      }
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00002000;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              kycLevel_ = input.readEnum();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              userType_ = input.readEnum();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              releaseMin_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              releaseMax_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              releaseNumMax_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              releaseAccountDays_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              releaseAccountMonths_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              receiveMin_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 74: {
              receiveMax_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 82: {
              receiveAccountDays_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 90: {
              receiveAccountMonths_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 96: {
              receiveNumDays_ = input.readInt32();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            case 104: {
              receiveNumMonths_ = input.readInt32();
              bitField0_ |= 0x00001000;
              break;
            } // case 104
            case 114: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00002000;
              break;
            } // case 114
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int kycLevel_ = 0;
    /**
     * <pre>
     *kyc等级
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.luck.KycLevel kycLevel = 1;</code>
     * @return The enum numeric value on the wire for kycLevel.
     */
    @java.lang.Override public int getKycLevelValue() {
      return kycLevel_;
    }
    /**
     * <pre>
     *kyc等级
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.luck.KycLevel kycLevel = 1;</code>
     * @param value The enum numeric value on the wire for kycLevel to set.
     * @return This builder for chaining.
     */
    public Builder setKycLevelValue(int value) {
      kycLevel_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *kyc等级
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.luck.KycLevel kycLevel = 1;</code>
     * @return The kycLevel.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.luck.KycLevel getKycLevel() {
      com.kikitrade.activity.facade.luck.KycLevel result = com.kikitrade.activity.facade.luck.KycLevel.forNumber(kycLevel_);
      return result == null ? com.kikitrade.activity.facade.luck.KycLevel.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *kyc等级
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.luck.KycLevel kycLevel = 1;</code>
     * @param value The kycLevel to set.
     * @return This builder for chaining.
     */
    public Builder setKycLevel(com.kikitrade.activity.facade.luck.KycLevel value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000001;
      kycLevel_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *kyc等级
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.luck.KycLevel kycLevel = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearKycLevel() {
      bitField0_ = (bitField0_ & ~0x00000001);
      kycLevel_ = 0;
      onChanged();
      return this;
    }

    private int userType_ = 0;
    /**
     * <pre>
     *用户类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.luck.UserType userType = 2;</code>
     * @return The enum numeric value on the wire for userType.
     */
    @java.lang.Override public int getUserTypeValue() {
      return userType_;
    }
    /**
     * <pre>
     *用户类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.luck.UserType userType = 2;</code>
     * @param value The enum numeric value on the wire for userType to set.
     * @return This builder for chaining.
     */
    public Builder setUserTypeValue(int value) {
      userType_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *用户类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.luck.UserType userType = 2;</code>
     * @return The userType.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.luck.UserType getUserType() {
      com.kikitrade.activity.facade.luck.UserType result = com.kikitrade.activity.facade.luck.UserType.forNumber(userType_);
      return result == null ? com.kikitrade.activity.facade.luck.UserType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *用户类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.luck.UserType userType = 2;</code>
     * @param value The userType to set.
     * @return This builder for chaining.
     */
    public Builder setUserType(com.kikitrade.activity.facade.luck.UserType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000002;
      userType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *用户类型
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.luck.UserType userType = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearUserType() {
      bitField0_ = (bitField0_ & ~0x00000002);
      userType_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object releaseMin_ = "";
    /**
     * <pre>
     *发放红包最小金额
     * </pre>
     *
     * <code>string releaseMin = 3;</code>
     * @return The releaseMin.
     */
    public java.lang.String getReleaseMin() {
      java.lang.Object ref = releaseMin_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        releaseMin_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *发放红包最小金额
     * </pre>
     *
     * <code>string releaseMin = 3;</code>
     * @return The bytes for releaseMin.
     */
    public com.google.protobuf.ByteString
        getReleaseMinBytes() {
      java.lang.Object ref = releaseMin_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        releaseMin_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *发放红包最小金额
     * </pre>
     *
     * <code>string releaseMin = 3;</code>
     * @param value The releaseMin to set.
     * @return This builder for chaining.
     */
    public Builder setReleaseMin(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      releaseMin_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *发放红包最小金额
     * </pre>
     *
     * <code>string releaseMin = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearReleaseMin() {
      releaseMin_ = getDefaultInstance().getReleaseMin();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *发放红包最小金额
     * </pre>
     *
     * <code>string releaseMin = 3;</code>
     * @param value The bytes for releaseMin to set.
     * @return This builder for chaining.
     */
    public Builder setReleaseMinBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      releaseMin_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object releaseMax_ = "";
    /**
     * <pre>
     *发放红包最大金额
     * </pre>
     *
     * <code>string releaseMax = 4;</code>
     * @return The releaseMax.
     */
    public java.lang.String getReleaseMax() {
      java.lang.Object ref = releaseMax_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        releaseMax_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *发放红包最大金额
     * </pre>
     *
     * <code>string releaseMax = 4;</code>
     * @return The bytes for releaseMax.
     */
    public com.google.protobuf.ByteString
        getReleaseMaxBytes() {
      java.lang.Object ref = releaseMax_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        releaseMax_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *发放红包最大金额
     * </pre>
     *
     * <code>string releaseMax = 4;</code>
     * @param value The releaseMax to set.
     * @return This builder for chaining.
     */
    public Builder setReleaseMax(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      releaseMax_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *发放红包最大金额
     * </pre>
     *
     * <code>string releaseMax = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearReleaseMax() {
      releaseMax_ = getDefaultInstance().getReleaseMax();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *发放红包最大金额
     * </pre>
     *
     * <code>string releaseMax = 4;</code>
     * @param value The bytes for releaseMax to set.
     * @return This builder for chaining.
     */
    public Builder setReleaseMaxBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      releaseMax_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private int releaseNumMax_ ;
    /**
     * <pre>
     *单个红包最大数量
     * </pre>
     *
     * <code>int32 releaseNumMax = 5;</code>
     * @return The releaseNumMax.
     */
    @java.lang.Override
    public int getReleaseNumMax() {
      return releaseNumMax_;
    }
    /**
     * <pre>
     *单个红包最大数量
     * </pre>
     *
     * <code>int32 releaseNumMax = 5;</code>
     * @param value The releaseNumMax to set.
     * @return This builder for chaining.
     */
    public Builder setReleaseNumMax(int value) {

      releaseNumMax_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *单个红包最大数量
     * </pre>
     *
     * <code>int32 releaseNumMax = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearReleaseNumMax() {
      bitField0_ = (bitField0_ & ~0x00000010);
      releaseNumMax_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object releaseAccountDays_ = "";
    /**
     * <pre>
     *24小时发放累积限额
     * </pre>
     *
     * <code>string releaseAccountDays = 6;</code>
     * @return The releaseAccountDays.
     */
    public java.lang.String getReleaseAccountDays() {
      java.lang.Object ref = releaseAccountDays_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        releaseAccountDays_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *24小时发放累积限额
     * </pre>
     *
     * <code>string releaseAccountDays = 6;</code>
     * @return The bytes for releaseAccountDays.
     */
    public com.google.protobuf.ByteString
        getReleaseAccountDaysBytes() {
      java.lang.Object ref = releaseAccountDays_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        releaseAccountDays_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *24小时发放累积限额
     * </pre>
     *
     * <code>string releaseAccountDays = 6;</code>
     * @param value The releaseAccountDays to set.
     * @return This builder for chaining.
     */
    public Builder setReleaseAccountDays(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      releaseAccountDays_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *24小时发放累积限额
     * </pre>
     *
     * <code>string releaseAccountDays = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearReleaseAccountDays() {
      releaseAccountDays_ = getDefaultInstance().getReleaseAccountDays();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *24小时发放累积限额
     * </pre>
     *
     * <code>string releaseAccountDays = 6;</code>
     * @param value The bytes for releaseAccountDays to set.
     * @return This builder for chaining.
     */
    public Builder setReleaseAccountDaysBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      releaseAccountDays_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object releaseAccountMonths_ = "";
    /**
     * <pre>
     *30天发放累积限额
     * </pre>
     *
     * <code>string releaseAccountMonths = 7;</code>
     * @return The releaseAccountMonths.
     */
    public java.lang.String getReleaseAccountMonths() {
      java.lang.Object ref = releaseAccountMonths_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        releaseAccountMonths_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *30天发放累积限额
     * </pre>
     *
     * <code>string releaseAccountMonths = 7;</code>
     * @return The bytes for releaseAccountMonths.
     */
    public com.google.protobuf.ByteString
        getReleaseAccountMonthsBytes() {
      java.lang.Object ref = releaseAccountMonths_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        releaseAccountMonths_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *30天发放累积限额
     * </pre>
     *
     * <code>string releaseAccountMonths = 7;</code>
     * @param value The releaseAccountMonths to set.
     * @return This builder for chaining.
     */
    public Builder setReleaseAccountMonths(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      releaseAccountMonths_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *30天发放累积限额
     * </pre>
     *
     * <code>string releaseAccountMonths = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearReleaseAccountMonths() {
      releaseAccountMonths_ = getDefaultInstance().getReleaseAccountMonths();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *30天发放累积限额
     * </pre>
     *
     * <code>string releaseAccountMonths = 7;</code>
     * @param value The bytes for releaseAccountMonths to set.
     * @return This builder for chaining.
     */
    public Builder setReleaseAccountMonthsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      releaseAccountMonths_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object receiveMin_ = "";
    /**
     * <pre>
     *领取红包的最小金额
     * </pre>
     *
     * <code>string receiveMin = 8;</code>
     * @return The receiveMin.
     */
    public java.lang.String getReceiveMin() {
      java.lang.Object ref = receiveMin_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        receiveMin_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *领取红包的最小金额
     * </pre>
     *
     * <code>string receiveMin = 8;</code>
     * @return The bytes for receiveMin.
     */
    public com.google.protobuf.ByteString
        getReceiveMinBytes() {
      java.lang.Object ref = receiveMin_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        receiveMin_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *领取红包的最小金额
     * </pre>
     *
     * <code>string receiveMin = 8;</code>
     * @param value The receiveMin to set.
     * @return This builder for chaining.
     */
    public Builder setReceiveMin(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      receiveMin_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *领取红包的最小金额
     * </pre>
     *
     * <code>string receiveMin = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearReceiveMin() {
      receiveMin_ = getDefaultInstance().getReceiveMin();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *领取红包的最小金额
     * </pre>
     *
     * <code>string receiveMin = 8;</code>
     * @param value The bytes for receiveMin to set.
     * @return This builder for chaining.
     */
    public Builder setReceiveMinBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      receiveMin_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private java.lang.Object receiveMax_ = "";
    /**
     * <pre>
     *领取红包的最小金额
     * </pre>
     *
     * <code>string receiveMax = 9;</code>
     * @return The receiveMax.
     */
    public java.lang.String getReceiveMax() {
      java.lang.Object ref = receiveMax_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        receiveMax_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *领取红包的最小金额
     * </pre>
     *
     * <code>string receiveMax = 9;</code>
     * @return The bytes for receiveMax.
     */
    public com.google.protobuf.ByteString
        getReceiveMaxBytes() {
      java.lang.Object ref = receiveMax_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        receiveMax_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *领取红包的最小金额
     * </pre>
     *
     * <code>string receiveMax = 9;</code>
     * @param value The receiveMax to set.
     * @return This builder for chaining.
     */
    public Builder setReceiveMax(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      receiveMax_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *领取红包的最小金额
     * </pre>
     *
     * <code>string receiveMax = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearReceiveMax() {
      receiveMax_ = getDefaultInstance().getReceiveMax();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *领取红包的最小金额
     * </pre>
     *
     * <code>string receiveMax = 9;</code>
     * @param value The bytes for receiveMax to set.
     * @return This builder for chaining.
     */
    public Builder setReceiveMaxBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      receiveMax_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private java.lang.Object receiveAccountDays_ = "";
    /**
     * <pre>
     *24小时领取累积限额
     * </pre>
     *
     * <code>string receiveAccountDays = 10;</code>
     * @return The receiveAccountDays.
     */
    public java.lang.String getReceiveAccountDays() {
      java.lang.Object ref = receiveAccountDays_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        receiveAccountDays_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *24小时领取累积限额
     * </pre>
     *
     * <code>string receiveAccountDays = 10;</code>
     * @return The bytes for receiveAccountDays.
     */
    public com.google.protobuf.ByteString
        getReceiveAccountDaysBytes() {
      java.lang.Object ref = receiveAccountDays_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        receiveAccountDays_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *24小时领取累积限额
     * </pre>
     *
     * <code>string receiveAccountDays = 10;</code>
     * @param value The receiveAccountDays to set.
     * @return This builder for chaining.
     */
    public Builder setReceiveAccountDays(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      receiveAccountDays_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *24小时领取累积限额
     * </pre>
     *
     * <code>string receiveAccountDays = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearReceiveAccountDays() {
      receiveAccountDays_ = getDefaultInstance().getReceiveAccountDays();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *24小时领取累积限额
     * </pre>
     *
     * <code>string receiveAccountDays = 10;</code>
     * @param value The bytes for receiveAccountDays to set.
     * @return This builder for chaining.
     */
    public Builder setReceiveAccountDaysBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      receiveAccountDays_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private java.lang.Object receiveAccountMonths_ = "";
    /**
     * <pre>
     *30天领取累积限额
     * </pre>
     *
     * <code>string receiveAccountMonths = 11;</code>
     * @return The receiveAccountMonths.
     */
    public java.lang.String getReceiveAccountMonths() {
      java.lang.Object ref = receiveAccountMonths_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        receiveAccountMonths_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *30天领取累积限额
     * </pre>
     *
     * <code>string receiveAccountMonths = 11;</code>
     * @return The bytes for receiveAccountMonths.
     */
    public com.google.protobuf.ByteString
        getReceiveAccountMonthsBytes() {
      java.lang.Object ref = receiveAccountMonths_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        receiveAccountMonths_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *30天领取累积限额
     * </pre>
     *
     * <code>string receiveAccountMonths = 11;</code>
     * @param value The receiveAccountMonths to set.
     * @return This builder for chaining.
     */
    public Builder setReceiveAccountMonths(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      receiveAccountMonths_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *30天领取累积限额
     * </pre>
     *
     * <code>string receiveAccountMonths = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearReceiveAccountMonths() {
      receiveAccountMonths_ = getDefaultInstance().getReceiveAccountMonths();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *30天领取累积限额
     * </pre>
     *
     * <code>string receiveAccountMonths = 11;</code>
     * @param value The bytes for receiveAccountMonths to set.
     * @return This builder for chaining.
     */
    public Builder setReceiveAccountMonthsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      receiveAccountMonths_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }

    private int receiveNumDays_ ;
    /**
     * <pre>
     *24小时领取累积数量限制
     * </pre>
     *
     * <code>int32 receiveNumDays = 12;</code>
     * @return The receiveNumDays.
     */
    @java.lang.Override
    public int getReceiveNumDays() {
      return receiveNumDays_;
    }
    /**
     * <pre>
     *24小时领取累积数量限制
     * </pre>
     *
     * <code>int32 receiveNumDays = 12;</code>
     * @param value The receiveNumDays to set.
     * @return This builder for chaining.
     */
    public Builder setReceiveNumDays(int value) {

      receiveNumDays_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *24小时领取累积数量限制
     * </pre>
     *
     * <code>int32 receiveNumDays = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearReceiveNumDays() {
      bitField0_ = (bitField0_ & ~0x00000800);
      receiveNumDays_ = 0;
      onChanged();
      return this;
    }

    private int receiveNumMonths_ ;
    /**
     * <pre>
     *30天领取累积数量限制
     * </pre>
     *
     * <code>int32 receiveNumMonths = 13;</code>
     * @return The receiveNumMonths.
     */
    @java.lang.Override
    public int getReceiveNumMonths() {
      return receiveNumMonths_;
    }
    /**
     * <pre>
     *30天领取累积数量限制
     * </pre>
     *
     * <code>int32 receiveNumMonths = 13;</code>
     * @param value The receiveNumMonths to set.
     * @return This builder for chaining.
     */
    public Builder setReceiveNumMonths(int value) {

      receiveNumMonths_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *30天领取累积数量限制
     * </pre>
     *
     * <code>int32 receiveNumMonths = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearReceiveNumMonths() {
      bitField0_ = (bitField0_ & ~0x00001000);
      receiveNumMonths_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object id_ = "";
    /**
     * <pre>
     *红包规则id
     * </pre>
     *
     * <code>string id = 14;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *红包规则id
     * </pre>
     *
     * <code>string id = 14;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *红包规则id
     * </pre>
     *
     * <code>string id = 14;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *红包规则id
     * </pre>
     *
     * <code>string id = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00002000);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *红包规则id
     * </pre>
     *
     * <code>string id = 14;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO)
  private static final com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO();
  }

  public static com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LuckFortuneRuleDTO>
      PARSER = new com.google.protobuf.AbstractParser<LuckFortuneRuleDTO>() {
    @java.lang.Override
    public LuckFortuneRuleDTO parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<LuckFortuneRuleDTO> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LuckFortuneRuleDTO> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

