// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.award.ConditionResponse}
 */
public final class ConditionResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.award.ConditionResponse)
    ConditionResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ConditionResponse.newBuilder() to construct.
  private ConditionResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ConditionResponse() {
    condition_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ConditionResponse();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ConditionResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ConditionResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.award.ConditionResponse.class, com.kikitrade.activity.facade.award.ConditionResponse.Builder.class);
  }

  public static final int CONDITION_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.award.ConditionVO> condition_;
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.award.ConditionVO> getConditionList() {
    return condition_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.award.ConditionVOOrBuilder> 
      getConditionOrBuilderList() {
    return condition_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
   */
  @java.lang.Override
  public int getConditionCount() {
    return condition_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.ConditionVO getCondition(int index) {
    return condition_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.ConditionVOOrBuilder getConditionOrBuilder(
      int index) {
    return condition_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < condition_.size(); i++) {
      output.writeMessage(1, condition_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < condition_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, condition_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.award.ConditionResponse)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.award.ConditionResponse other = (com.kikitrade.activity.facade.award.ConditionResponse) obj;

    if (!getConditionList()
        .equals(other.getConditionList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getConditionCount() > 0) {
      hash = (37 * hash) + CONDITION_FIELD_NUMBER;
      hash = (53 * hash) + getConditionList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.award.ConditionResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ConditionResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ConditionResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ConditionResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ConditionResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ConditionResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ConditionResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.ConditionResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.award.ConditionResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.award.ConditionResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ConditionResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.ConditionResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.award.ConditionResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.award.ConditionResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.award.ConditionResponse)
      com.kikitrade.activity.facade.award.ConditionResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ConditionResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ConditionResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.award.ConditionResponse.class, com.kikitrade.activity.facade.award.ConditionResponse.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.award.ConditionResponse.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (conditionBuilder_ == null) {
        condition_ = java.util.Collections.emptyList();
      } else {
        condition_ = null;
        conditionBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ConditionResponse_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ConditionResponse getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.award.ConditionResponse.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ConditionResponse build() {
      com.kikitrade.activity.facade.award.ConditionResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ConditionResponse buildPartial() {
      com.kikitrade.activity.facade.award.ConditionResponse result = new com.kikitrade.activity.facade.award.ConditionResponse(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.award.ConditionResponse result) {
      if (conditionBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          condition_ = java.util.Collections.unmodifiableList(condition_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.condition_ = condition_;
      } else {
        result.condition_ = conditionBuilder_.build();
      }
    }

    private void buildPartial0(com.kikitrade.activity.facade.award.ConditionResponse result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.award.ConditionResponse) {
        return mergeFrom((com.kikitrade.activity.facade.award.ConditionResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.award.ConditionResponse other) {
      if (other == com.kikitrade.activity.facade.award.ConditionResponse.getDefaultInstance()) return this;
      if (conditionBuilder_ == null) {
        if (!other.condition_.isEmpty()) {
          if (condition_.isEmpty()) {
            condition_ = other.condition_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureConditionIsMutable();
            condition_.addAll(other.condition_);
          }
          onChanged();
        }
      } else {
        if (!other.condition_.isEmpty()) {
          if (conditionBuilder_.isEmpty()) {
            conditionBuilder_.dispose();
            conditionBuilder_ = null;
            condition_ = other.condition_;
            bitField0_ = (bitField0_ & ~0x00000001);
            conditionBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getConditionFieldBuilder() : null;
          } else {
            conditionBuilder_.addAllMessages(other.condition_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.kikitrade.activity.facade.award.ConditionVO m =
                  input.readMessage(
                      com.kikitrade.activity.facade.award.ConditionVO.parser(),
                      extensionRegistry);
              if (conditionBuilder_ == null) {
                ensureConditionIsMutable();
                condition_.add(m);
              } else {
                conditionBuilder_.addMessage(m);
              }
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<com.kikitrade.activity.facade.award.ConditionVO> condition_ =
      java.util.Collections.emptyList();
    private void ensureConditionIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        condition_ = new java.util.ArrayList<com.kikitrade.activity.facade.award.ConditionVO>(condition_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.ConditionVO, com.kikitrade.activity.facade.award.ConditionVO.Builder, com.kikitrade.activity.facade.award.ConditionVOOrBuilder> conditionBuilder_;

    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.ConditionVO> getConditionList() {
      if (conditionBuilder_ == null) {
        return java.util.Collections.unmodifiableList(condition_);
      } else {
        return conditionBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public int getConditionCount() {
      if (conditionBuilder_ == null) {
        return condition_.size();
      } else {
        return conditionBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public com.kikitrade.activity.facade.award.ConditionVO getCondition(int index) {
      if (conditionBuilder_ == null) {
        return condition_.get(index);
      } else {
        return conditionBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public Builder setCondition(
        int index, com.kikitrade.activity.facade.award.ConditionVO value) {
      if (conditionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionIsMutable();
        condition_.set(index, value);
        onChanged();
      } else {
        conditionBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public Builder setCondition(
        int index, com.kikitrade.activity.facade.award.ConditionVO.Builder builderForValue) {
      if (conditionBuilder_ == null) {
        ensureConditionIsMutable();
        condition_.set(index, builderForValue.build());
        onChanged();
      } else {
        conditionBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public Builder addCondition(com.kikitrade.activity.facade.award.ConditionVO value) {
      if (conditionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionIsMutable();
        condition_.add(value);
        onChanged();
      } else {
        conditionBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public Builder addCondition(
        int index, com.kikitrade.activity.facade.award.ConditionVO value) {
      if (conditionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionIsMutable();
        condition_.add(index, value);
        onChanged();
      } else {
        conditionBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public Builder addCondition(
        com.kikitrade.activity.facade.award.ConditionVO.Builder builderForValue) {
      if (conditionBuilder_ == null) {
        ensureConditionIsMutable();
        condition_.add(builderForValue.build());
        onChanged();
      } else {
        conditionBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public Builder addCondition(
        int index, com.kikitrade.activity.facade.award.ConditionVO.Builder builderForValue) {
      if (conditionBuilder_ == null) {
        ensureConditionIsMutable();
        condition_.add(index, builderForValue.build());
        onChanged();
      } else {
        conditionBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public Builder addAllCondition(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.award.ConditionVO> values) {
      if (conditionBuilder_ == null) {
        ensureConditionIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, condition_);
        onChanged();
      } else {
        conditionBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public Builder clearCondition() {
      if (conditionBuilder_ == null) {
        condition_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        conditionBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public Builder removeCondition(int index) {
      if (conditionBuilder_ == null) {
        ensureConditionIsMutable();
        condition_.remove(index);
        onChanged();
      } else {
        conditionBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public com.kikitrade.activity.facade.award.ConditionVO.Builder getConditionBuilder(
        int index) {
      return getConditionFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public com.kikitrade.activity.facade.award.ConditionVOOrBuilder getConditionOrBuilder(
        int index) {
      if (conditionBuilder_ == null) {
        return condition_.get(index);  } else {
        return conditionBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.award.ConditionVOOrBuilder> 
         getConditionOrBuilderList() {
      if (conditionBuilder_ != null) {
        return conditionBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(condition_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public com.kikitrade.activity.facade.award.ConditionVO.Builder addConditionBuilder() {
      return getConditionFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.award.ConditionVO.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public com.kikitrade.activity.facade.award.ConditionVO.Builder addConditionBuilder(
        int index) {
      return getConditionFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.award.ConditionVO.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ConditionVO condition = 1;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.ConditionVO.Builder> 
         getConditionBuilderList() {
      return getConditionFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.ConditionVO, com.kikitrade.activity.facade.award.ConditionVO.Builder, com.kikitrade.activity.facade.award.ConditionVOOrBuilder> 
        getConditionFieldBuilder() {
      if (conditionBuilder_ == null) {
        conditionBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.award.ConditionVO, com.kikitrade.activity.facade.award.ConditionVO.Builder, com.kikitrade.activity.facade.award.ConditionVOOrBuilder>(
                condition_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        condition_ = null;
      }
      return conditionBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.award.ConditionResponse)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.award.ConditionResponse)
  private static final com.kikitrade.activity.facade.award.ConditionResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.award.ConditionResponse();
  }

  public static com.kikitrade.activity.facade.award.ConditionResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ConditionResponse>
      PARSER = new com.google.protobuf.AbstractParser<ConditionResponse>() {
    @java.lang.Override
    public ConditionResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ConditionResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ConditionResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.award.ConditionResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

