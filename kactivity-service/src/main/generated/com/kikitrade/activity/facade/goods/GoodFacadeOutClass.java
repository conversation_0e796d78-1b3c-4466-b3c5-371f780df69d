// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Goods.proto

package com.kikitrade.activity.facade.goods;

public final class GoodFacadeOutClass {
  private GoodFacadeOutClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_goods_GoodsDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_goods_GoodsDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_goods_GoodsSaveResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_goods_GoodsSaveResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013Goods.proto\022#com.kikitrade.activity.fa" +
      "cade.goods\"\210\004\n\010GoodsDTO\022\n\n\002id\030\001 \001(\t\022\014\n\004n" +
      "ame\030\002 \001(\t\022@\n\006status\030\003 \001(\01620.com.kikitrad" +
      "e.activity.facade.goods.GoodsStatus\022\014\n\004d" +
      "esc\030\004 \001(\t\022<\n\004type\030\005 \001(\0162..com.kikitrade." +
      "activity.facade.goods.GoodsType\022\021\n\tstart" +
      "Time\030\006 \001(\t\022\017\n\007endTime\030\007 \001(\t\022>\n\005chain\030\010 \001" +
      "(\0162/.com.kikitrade.activity.facade.goods" +
      ".BlockChain\022\r\n\005price\030\t \001(\t\022\r\n\005stock\030\n \001(" +
      "\005\022\021\n\tlistImage\030\013 \001(\t\022\023\n\013detailImage\030\014 \001(" +
      "\t\022\022\n\nshareImage\030\r \001(\t\022\021\n\tlabelName\030\016 \001(\t" +
      "\022\022\n\nlabelColor\030\017 \001(\t\022\r\n\005outId\030\020 \001(\t\022\022\n\np" +
      "reQuestId\030\021 \001(\t\022\016\n\006saasId\030\022 \001(\t\022<\n\004test\030" +
      "\023 \003(\0162..com.kikitrade.activity.facade.go" +
      "ods.GoodsType\"A\n\021GoodsSaveResponse\022\017\n\007su" +
      "ccess\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\022\n\n\002id\030\003 \001(\t" +
      "*&\n\013GoodsStatus\022\n\n\006ONLINE\020\000\022\013\n\007OFFLINE\020\001" +
      "*#\n\tGoodsType\022\007\n\003NFT\020\000\022\r\n\tGAME_ITEM\020\001*\036\n" +
      "\nBlockChain\022\007\n\003evm\020\000\022\007\n\003sui\020\0012|\n\013GoodsFa" +
      "cade\022m\n\004save\022-.com.kikitrade.activity.fa" +
      "cade.goods.GoodsDTO\0326.com.kikitrade.acti" +
      "vity.facade.goods.GoodsSaveResponseB;\n#c" +
      "om.kikitrade.activity.facade.goodsB\022Good" +
      "FacadeOutClassP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_kikitrade_activity_facade_goods_GoodsDTO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_activity_facade_goods_GoodsDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_goods_GoodsDTO_descriptor,
        new java.lang.String[] { "Id", "Name", "Status", "Desc", "Type", "StartTime", "EndTime", "Chain", "Price", "Stock", "ListImage", "DetailImage", "ShareImage", "LabelName", "LabelColor", "OutId", "PreQuestId", "SaasId", "Test", });
    internal_static_com_kikitrade_activity_facade_goods_GoodsSaveResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_activity_facade_goods_GoodsSaveResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_goods_GoodsSaveResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "Id", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
