// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PropFacade.proto

package com.kikitrade.activity.facade.prop;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.prop.PropGetReply}
 */
public final class PropGetReply extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.prop.PropGetReply)
    PropGetReplyOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PropGetReply.newBuilder() to construct.
  private PropGetReply(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PropGetReply() {
    message_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PropGetReply();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.prop.PropFacadeOuterClass.internal_static_com_kikitrade_activity_facade_prop_PropGetReply_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.prop.PropFacadeOuterClass.internal_static_com_kikitrade_activity_facade_prop_PropGetReply_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.prop.PropGetReply.class, com.kikitrade.activity.facade.prop.PropGetReply.Builder.class);
  }

  private int bitField0_;
  public static final int SUCCESS_FIELD_NUMBER = 1;
  private boolean success_ = false;
  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  @java.lang.Override
  public boolean getSuccess() {
    return success_;
  }

  public static final int MESSAGE_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object message_ = "";
  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  @java.lang.Override
  public java.lang.String getMessage() {
    java.lang.Object ref = message_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      message_ = s;
      return s;
    }
  }
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMessageBytes() {
    java.lang.Object ref = message_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      message_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PROP_FIELD_NUMBER = 3;
  private com.kikitrade.activity.facade.prop.Prop prop_;
  /**
   * <code>.com.kikitrade.activity.facade.prop.Prop prop = 3;</code>
   * @return Whether the prop field is set.
   */
  @java.lang.Override
  public boolean hasProp() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>.com.kikitrade.activity.facade.prop.Prop prop = 3;</code>
   * @return The prop.
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.prop.Prop getProp() {
    return prop_ == null ? com.kikitrade.activity.facade.prop.Prop.getDefaultInstance() : prop_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.prop.Prop prop = 3;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.prop.PropOrBuilder getPropOrBuilder() {
    return prop_ == null ? com.kikitrade.activity.facade.prop.Prop.getDefaultInstance() : prop_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (success_ != false) {
      output.writeBool(1, success_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(message_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, message_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(3, getProp());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (success_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(1, success_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(message_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, message_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getProp());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.prop.PropGetReply)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.prop.PropGetReply other = (com.kikitrade.activity.facade.prop.PropGetReply) obj;

    if (getSuccess()
        != other.getSuccess()) return false;
    if (!getMessage()
        .equals(other.getMessage())) return false;
    if (hasProp() != other.hasProp()) return false;
    if (hasProp()) {
      if (!getProp()
          .equals(other.getProp())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SUCCESS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getSuccess());
    hash = (37 * hash) + MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getMessage().hashCode();
    if (hasProp()) {
      hash = (37 * hash) + PROP_FIELD_NUMBER;
      hash = (53 * hash) + getProp().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.prop.PropGetReply parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.prop.PropGetReply parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.prop.PropGetReply parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.prop.PropGetReply parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.prop.PropGetReply parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.prop.PropGetReply parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.prop.PropGetReply parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.prop.PropGetReply parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.prop.PropGetReply parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.prop.PropGetReply parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.prop.PropGetReply parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.prop.PropGetReply parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.prop.PropGetReply prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.prop.PropGetReply}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.prop.PropGetReply)
      com.kikitrade.activity.facade.prop.PropGetReplyOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.prop.PropFacadeOuterClass.internal_static_com_kikitrade_activity_facade_prop_PropGetReply_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.prop.PropFacadeOuterClass.internal_static_com_kikitrade_activity_facade_prop_PropGetReply_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.prop.PropGetReply.class, com.kikitrade.activity.facade.prop.PropGetReply.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.prop.PropGetReply.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getPropFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      success_ = false;
      message_ = "";
      prop_ = null;
      if (propBuilder_ != null) {
        propBuilder_.dispose();
        propBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.prop.PropFacadeOuterClass.internal_static_com_kikitrade_activity_facade_prop_PropGetReply_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.prop.PropGetReply getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.prop.PropGetReply.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.prop.PropGetReply build() {
      com.kikitrade.activity.facade.prop.PropGetReply result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.prop.PropGetReply buildPartial() {
      com.kikitrade.activity.facade.prop.PropGetReply result = new com.kikitrade.activity.facade.prop.PropGetReply(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.kikitrade.activity.facade.prop.PropGetReply result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.success_ = success_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.message_ = message_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.prop_ = propBuilder_ == null
            ? prop_
            : propBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.prop.PropGetReply) {
        return mergeFrom((com.kikitrade.activity.facade.prop.PropGetReply)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.prop.PropGetReply other) {
      if (other == com.kikitrade.activity.facade.prop.PropGetReply.getDefaultInstance()) return this;
      if (other.getSuccess() != false) {
        setSuccess(other.getSuccess());
      }
      if (!other.getMessage().isEmpty()) {
        message_ = other.message_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasProp()) {
        mergeProp(other.getProp());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              success_ = input.readBool();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              message_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  getPropFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private boolean success_ ;
    /**
     * <code>bool success = 1;</code>
     * @return The success.
     */
    @java.lang.Override
    public boolean getSuccess() {
      return success_;
    }
    /**
     * <code>bool success = 1;</code>
     * @param value The success to set.
     * @return This builder for chaining.
     */
    public Builder setSuccess(boolean value) {

      success_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>bool success = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSuccess() {
      bitField0_ = (bitField0_ & ~0x00000001);
      success_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object message_ = "";
    /**
     * <code>string message = 2;</code>
     * @return The message.
     */
    public java.lang.String getMessage() {
      java.lang.Object ref = message_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        message_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string message = 2;</code>
     * @return The bytes for message.
     */
    public com.google.protobuf.ByteString
        getMessageBytes() {
      java.lang.Object ref = message_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        message_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string message = 2;</code>
     * @param value The message to set.
     * @return This builder for chaining.
     */
    public Builder setMessage(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      message_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string message = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMessage() {
      message_ = getDefaultInstance().getMessage();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string message = 2;</code>
     * @param value The bytes for message to set.
     * @return This builder for chaining.
     */
    public Builder setMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      message_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private com.kikitrade.activity.facade.prop.Prop prop_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.kikitrade.activity.facade.prop.Prop, com.kikitrade.activity.facade.prop.Prop.Builder, com.kikitrade.activity.facade.prop.PropOrBuilder> propBuilder_;
    /**
     * <code>.com.kikitrade.activity.facade.prop.Prop prop = 3;</code>
     * @return Whether the prop field is set.
     */
    public boolean hasProp() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.Prop prop = 3;</code>
     * @return The prop.
     */
    public com.kikitrade.activity.facade.prop.Prop getProp() {
      if (propBuilder_ == null) {
        return prop_ == null ? com.kikitrade.activity.facade.prop.Prop.getDefaultInstance() : prop_;
      } else {
        return propBuilder_.getMessage();
      }
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.Prop prop = 3;</code>
     */
    public Builder setProp(com.kikitrade.activity.facade.prop.Prop value) {
      if (propBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        prop_ = value;
      } else {
        propBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.Prop prop = 3;</code>
     */
    public Builder setProp(
        com.kikitrade.activity.facade.prop.Prop.Builder builderForValue) {
      if (propBuilder_ == null) {
        prop_ = builderForValue.build();
      } else {
        propBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.Prop prop = 3;</code>
     */
    public Builder mergeProp(com.kikitrade.activity.facade.prop.Prop value) {
      if (propBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          prop_ != null &&
          prop_ != com.kikitrade.activity.facade.prop.Prop.getDefaultInstance()) {
          getPropBuilder().mergeFrom(value);
        } else {
          prop_ = value;
        }
      } else {
        propBuilder_.mergeFrom(value);
      }
      if (prop_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.Prop prop = 3;</code>
     */
    public Builder clearProp() {
      bitField0_ = (bitField0_ & ~0x00000004);
      prop_ = null;
      if (propBuilder_ != null) {
        propBuilder_.dispose();
        propBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.Prop prop = 3;</code>
     */
    public com.kikitrade.activity.facade.prop.Prop.Builder getPropBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return getPropFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.Prop prop = 3;</code>
     */
    public com.kikitrade.activity.facade.prop.PropOrBuilder getPropOrBuilder() {
      if (propBuilder_ != null) {
        return propBuilder_.getMessageOrBuilder();
      } else {
        return prop_ == null ?
            com.kikitrade.activity.facade.prop.Prop.getDefaultInstance() : prop_;
      }
    }
    /**
     * <code>.com.kikitrade.activity.facade.prop.Prop prop = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.kikitrade.activity.facade.prop.Prop, com.kikitrade.activity.facade.prop.Prop.Builder, com.kikitrade.activity.facade.prop.PropOrBuilder> 
        getPropFieldBuilder() {
      if (propBuilder_ == null) {
        propBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.kikitrade.activity.facade.prop.Prop, com.kikitrade.activity.facade.prop.Prop.Builder, com.kikitrade.activity.facade.prop.PropOrBuilder>(
                getProp(),
                getParentForChildren(),
                isClean());
        prop_ = null;
      }
      return propBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.prop.PropGetReply)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.prop.PropGetReply)
  private static final com.kikitrade.activity.facade.prop.PropGetReply DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.prop.PropGetReply();
  }

  public static com.kikitrade.activity.facade.prop.PropGetReply getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PropGetReply>
      PARSER = new com.google.protobuf.AbstractParser<PropGetReply>() {
    @java.lang.Override
    public PropGetReply parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PropGetReply> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PropGetReply> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.prop.PropGetReply getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

