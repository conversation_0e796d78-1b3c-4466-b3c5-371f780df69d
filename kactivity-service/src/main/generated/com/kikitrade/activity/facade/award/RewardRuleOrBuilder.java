// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface RewardRuleOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.RewardRule)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string level = 1;</code>
   * @return The level.
   */
  java.lang.String getLevel();
  /**
   * <code>string level = 1;</code>
   * @return The bytes for level.
   */
  com.google.protobuf.ByteString
      getLevelBytes();

  /**
   * <code>string min = 2;</code>
   * @return The min.
   */
  java.lang.String getMin();
  /**
   * <code>string min = 2;</code>
   * @return The bytes for min.
   */
  com.google.protobuf.ByteString
      getMinBytes();

  /**
   * <code>string max = 3;</code>
   * @return The max.
   */
  java.lang.String getMax();
  /**
   * <code>string max = 3;</code>
   * @return The bytes for max.
   */
  com.google.protobuf.ByteString
      getMaxBytes();

  /**
   * <code>string side = 4;</code>
   * @return The side.
   */
  java.lang.String getSide();
  /**
   * <code>string side = 4;</code>
   * @return The bytes for side.
   */
  com.google.protobuf.ByteString
      getSideBytes();

  /**
   * <code>string userType = 5;</code>
   * @return The userType.
   */
  java.lang.String getUserType();
  /**
   * <code>string userType = 5;</code>
   * @return The bytes for userType.
   */
  com.google.protobuf.ByteString
      getUserTypeBytes();

  /**
   * <code>string awardType = 6;</code>
   * @return The awardType.
   */
  java.lang.String getAwardType();
  /**
   * <code>string awardType = 6;</code>
   * @return The bytes for awardType.
   */
  com.google.protobuf.ByteString
      getAwardTypeBytes();

  /**
   * <code>string awardAmount = 7;</code>
   * @return The awardAmount.
   */
  java.lang.String getAwardAmount();
  /**
   * <code>string awardAmount = 7;</code>
   * @return The bytes for awardAmount.
   */
  com.google.protobuf.ByteString
      getAwardAmountBytes();

  /**
   * <code>string award = 8;</code>
   * @return The award.
   */
  java.lang.String getAward();
  /**
   * <code>string award = 8;</code>
   * @return The bytes for award.
   */
  com.google.protobuf.ByteString
      getAwardBytes();

  /**
   * <code>string vipLevel = 9;</code>
   * @return The vipLevel.
   */
  java.lang.String getVipLevel();
  /**
   * <code>string vipLevel = 9;</code>
   * @return The bytes for vipLevel.
   */
  com.google.protobuf.ByteString
      getVipLevelBytes();
}
