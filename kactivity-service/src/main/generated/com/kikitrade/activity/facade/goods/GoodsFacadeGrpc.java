package com.kikitrade.activity.facade.goods;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.58.0)",
    comments = "Source: Goods.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class GoodsFacadeGrpc {

  private GoodsFacadeGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.kikitrade.activity.facade.goods.GoodsFacade";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.goods.GoodsDTO,
      com.kikitrade.activity.facade.goods.GoodsSaveResponse> getSaveMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "save",
      requestType = com.kikitrade.activity.facade.goods.GoodsDTO.class,
      responseType = com.kikitrade.activity.facade.goods.GoodsSaveResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.goods.GoodsDTO,
      com.kikitrade.activity.facade.goods.GoodsSaveResponse> getSaveMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.goods.GoodsDTO, com.kikitrade.activity.facade.goods.GoodsSaveResponse> getSaveMethod;
    if ((getSaveMethod = GoodsFacadeGrpc.getSaveMethod) == null) {
      synchronized (GoodsFacadeGrpc.class) {
        if ((getSaveMethod = GoodsFacadeGrpc.getSaveMethod) == null) {
          GoodsFacadeGrpc.getSaveMethod = getSaveMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.goods.GoodsDTO, com.kikitrade.activity.facade.goods.GoodsSaveResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "save"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.goods.GoodsDTO.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.goods.GoodsSaveResponse.getDefaultInstance()))
              .setSchemaDescriptor(new GoodsFacadeMethodDescriptorSupplier("save"))
              .build();
        }
      }
    }
    return getSaveMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static GoodsFacadeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<GoodsFacadeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<GoodsFacadeStub>() {
        @java.lang.Override
        public GoodsFacadeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new GoodsFacadeStub(channel, callOptions);
        }
      };
    return GoodsFacadeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static GoodsFacadeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<GoodsFacadeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<GoodsFacadeBlockingStub>() {
        @java.lang.Override
        public GoodsFacadeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new GoodsFacadeBlockingStub(channel, callOptions);
        }
      };
    return GoodsFacadeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static GoodsFacadeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<GoodsFacadeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<GoodsFacadeFutureStub>() {
        @java.lang.Override
        public GoodsFacadeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new GoodsFacadeFutureStub(channel, callOptions);
        }
      };
    return GoodsFacadeFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     */
    default void save(com.kikitrade.activity.facade.goods.GoodsDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.goods.GoodsSaveResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSaveMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service GoodsFacade.
   */
  public static abstract class GoodsFacadeImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return GoodsFacadeGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service GoodsFacade.
   */
  public static final class GoodsFacadeStub
      extends io.grpc.stub.AbstractAsyncStub<GoodsFacadeStub> {
    private GoodsFacadeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected GoodsFacadeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new GoodsFacadeStub(channel, callOptions);
    }

    /**
     */
    public void save(com.kikitrade.activity.facade.goods.GoodsDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.goods.GoodsSaveResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSaveMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service GoodsFacade.
   */
  public static final class GoodsFacadeBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<GoodsFacadeBlockingStub> {
    private GoodsFacadeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected GoodsFacadeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new GoodsFacadeBlockingStub(channel, callOptions);
    }

    /**
     */
    public com.kikitrade.activity.facade.goods.GoodsSaveResponse save(com.kikitrade.activity.facade.goods.GoodsDTO request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSaveMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service GoodsFacade.
   */
  public static final class GoodsFacadeFutureStub
      extends io.grpc.stub.AbstractFutureStub<GoodsFacadeFutureStub> {
    private GoodsFacadeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected GoodsFacadeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new GoodsFacadeFutureStub(channel, callOptions);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.goods.GoodsSaveResponse> save(
        com.kikitrade.activity.facade.goods.GoodsDTO request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSaveMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_SAVE = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_SAVE:
          serviceImpl.save((com.kikitrade.activity.facade.goods.GoodsDTO) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.goods.GoodsSaveResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getSaveMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.goods.GoodsDTO,
              com.kikitrade.activity.facade.goods.GoodsSaveResponse>(
                service, METHODID_SAVE)))
        .build();
  }

  private static abstract class GoodsFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    GoodsFacadeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.kikitrade.activity.facade.goods.GoodFacadeOutClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("GoodsFacade");
    }
  }

  private static final class GoodsFacadeFileDescriptorSupplier
      extends GoodsFacadeBaseDescriptorSupplier {
    GoodsFacadeFileDescriptorSupplier() {}
  }

  private static final class GoodsFacadeMethodDescriptorSupplier
      extends GoodsFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    GoodsFacadeMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (GoodsFacadeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new GoodsFacadeFileDescriptorSupplier())
              .addMethod(getSaveMethod())
              .build();
        }
      }
    }
    return result;
  }
}
