// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Task.proto

package com.kikitrade.activity.facade.taskv2;

/**
 * <pre>
 **
 *奖品计算方式
 * </pre>
 *
 * Protobuf enum {@code com.kikitrade.activity.facade.taskv2.ProgressType}
 */
public enum ProgressType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *进度累积任务
   * </pre>
   *
   * <code>add = 0;</code>
   */
  add(0),
  /**
   * <pre>
   *连续任务
   * </pre>
   *
   * <code>series = 1;</code>
   */
  series(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *进度累积任务
   * </pre>
   *
   * <code>add = 0;</code>
   */
  public static final int add_VALUE = 0;
  /**
   * <pre>
   *连续任务
   * </pre>
   *
   * <code>series = 1;</code>
   */
  public static final int series_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static ProgressType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static ProgressType forNumber(int value) {
    switch (value) {
      case 0: return add;
      case 1: return series;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<ProgressType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      ProgressType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<ProgressType>() {
          public ProgressType findValueByNumber(int number) {
            return ProgressType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.getDescriptor().getEnumTypes().get(2);
  }

  private static final ProgressType[] VALUES = values();

  public static ProgressType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private ProgressType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.taskv2.ProgressType)
}

