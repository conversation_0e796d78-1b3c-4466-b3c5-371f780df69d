// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Lottery.proto

package com.kikitrade.activity.facade.lottery;

public interface RewardVOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.lottery.RewardVO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string name = 1;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <code>string name = 1;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <code>int32 multiple = 2;</code>
   * @return The multiple.
   */
  int getMultiple();
}
