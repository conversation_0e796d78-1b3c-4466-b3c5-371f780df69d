// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface LotteryResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.LotteryResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>.com.kikitrade.activity.facade.award.LotteryVO lotteryVO = 3;</code>
   * @return Whether the lotteryVO field is set.
   */
  boolean hasLotteryVO();
  /**
   * <code>.com.kikitrade.activity.facade.award.LotteryVO lotteryVO = 3;</code>
   * @return The lotteryVO.
   */
  com.kikitrade.activity.facade.award.LotteryVO getLotteryVO();
  /**
   * <code>.com.kikitrade.activity.facade.award.LotteryVO lotteryVO = 3;</code>
   */
  com.kikitrade.activity.facade.award.LotteryVOOrBuilder getLotteryVOOrBuilder();
}
