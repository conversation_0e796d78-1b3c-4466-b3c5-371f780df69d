// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface LotteryVOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.LotteryVO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *奖池基本信息
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   * @return Whether the lottery field is set.
   */
  boolean hasLottery();
  /**
   * <pre>
   *奖池基本信息
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   * @return The lottery.
   */
  com.kikitrade.activity.facade.award.Lottery getLottery();
  /**
   * <pre>
   *奖池基本信息
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   */
  com.kikitrade.activity.facade.award.LotteryOrBuilder getLotteryOrBuilder();

  /**
   * <pre>
   *奖池奖品
   * </pre>
   *
   * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
   */
  java.util.List<com.kikitrade.activity.facade.award.LotteryItem> 
      getItemList();
  /**
   * <pre>
   *奖池奖品
   * </pre>
   *
   * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
   */
  com.kikitrade.activity.facade.award.LotteryItem getItem(int index);
  /**
   * <pre>
   *奖池奖品
   * </pre>
   *
   * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
   */
  int getItemCount();
  /**
   * <pre>
   *奖池奖品
   * </pre>
   *
   * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.award.LotteryItemOrBuilder> 
      getItemOrBuilderList();
  /**
   * <pre>
   *奖池奖品
   * </pre>
   *
   * <code>repeated .com.kikitrade.activity.facade.award.LotteryItem item = 2;</code>
   */
  com.kikitrade.activity.facade.award.LotteryItemOrBuilder getItemOrBuilder(
      int index);
}
