// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityRewardFacade.proto

package com.kikitrade.activity.facade.reward;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.reward.ManualRewardRequest}
 */
public final class ManualRewardRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.reward.ManualRewardRequest)
    ManualRewardRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ManualRewardRequest.newBuilder() to construct.
  private ManualRewardRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ManualRewardRequest() {
    customerId_ = "";
    currency_ = "";
    type_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ManualRewardRequest();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.reward.ActivityRewardFacadeOuterClass.internal_static_com_kikitrade_activity_facade_reward_ManualRewardRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.reward.ActivityRewardFacadeOuterClass.internal_static_com_kikitrade_activity_facade_reward_ManualRewardRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.reward.ManualRewardRequest.class, com.kikitrade.activity.facade.reward.ManualRewardRequest.Builder.class);
  }

  private int bitField0_;
  public static final int CUSTOMERID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object customerId_ = "";
  /**
   * <code>string customerId = 1;</code>
   * @return The customerId.
   */
  @java.lang.Override
  public java.lang.String getCustomerId() {
    java.lang.Object ref = customerId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      customerId_ = s;
      return s;
    }
  }
  /**
   * <code>string customerId = 1;</code>
   * @return The bytes for customerId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCustomerIdBytes() {
    java.lang.Object ref = customerId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      customerId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AMOUNT_FIELD_NUMBER = 2;
  private com.kikitrade.activity.facade.reward.Decimal amount_;
  /**
   * <code>.com.kikitrade.activity.facade.reward.Decimal amount = 2;</code>
   * @return Whether the amount field is set.
   */
  @java.lang.Override
  public boolean hasAmount() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>.com.kikitrade.activity.facade.reward.Decimal amount = 2;</code>
   * @return The amount.
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.reward.Decimal getAmount() {
    return amount_ == null ? com.kikitrade.activity.facade.reward.Decimal.getDefaultInstance() : amount_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.reward.Decimal amount = 2;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.reward.DecimalOrBuilder getAmountOrBuilder() {
    return amount_ == null ? com.kikitrade.activity.facade.reward.Decimal.getDefaultInstance() : amount_;
  }

  public static final int CURRENCY_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object currency_ = "";
  /**
   * <code>string currency = 3;</code>
   * @return The currency.
   */
  @java.lang.Override
  public java.lang.String getCurrency() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      currency_ = s;
      return s;
    }
  }
  /**
   * <code>string currency = 3;</code>
   * @return The bytes for currency.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCurrencyBytes() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      currency_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TYPE_FIELD_NUMBER = 4;
  private int type_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.reward.TransactionTypeEnum type = 4;</code>
   * @return The enum numeric value on the wire for type.
   */
  @java.lang.Override public int getTypeValue() {
    return type_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.reward.TransactionTypeEnum type = 4;</code>
   * @return The type.
   */
  @java.lang.Override public com.kikitrade.activity.facade.reward.TransactionTypeEnum getType() {
    com.kikitrade.activity.facade.reward.TransactionTypeEnum result = com.kikitrade.activity.facade.reward.TransactionTypeEnum.forNumber(type_);
    return result == null ? com.kikitrade.activity.facade.reward.TransactionTypeEnum.UNRECOGNIZED : result;
  }

  public static final int REWARDID_FIELD_NUMBER = 5;
  private long rewardId_ = 0L;
  /**
   * <code>int64 rewardId = 5;</code>
   * @return The rewardId.
   */
  @java.lang.Override
  public long getRewardId() {
    return rewardId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(customerId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, customerId_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(2, getAmount());
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(currency_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, currency_);
    }
    if (type_ != com.kikitrade.activity.facade.reward.TransactionTypeEnum.REWARD.getNumber()) {
      output.writeEnum(4, type_);
    }
    if (rewardId_ != 0L) {
      output.writeInt64(5, rewardId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(customerId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, customerId_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getAmount());
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(currency_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, currency_);
    }
    if (type_ != com.kikitrade.activity.facade.reward.TransactionTypeEnum.REWARD.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(4, type_);
    }
    if (rewardId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, rewardId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.reward.ManualRewardRequest)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.reward.ManualRewardRequest other = (com.kikitrade.activity.facade.reward.ManualRewardRequest) obj;

    if (!getCustomerId()
        .equals(other.getCustomerId())) return false;
    if (hasAmount() != other.hasAmount()) return false;
    if (hasAmount()) {
      if (!getAmount()
          .equals(other.getAmount())) return false;
    }
    if (!getCurrency()
        .equals(other.getCurrency())) return false;
    if (type_ != other.type_) return false;
    if (getRewardId()
        != other.getRewardId()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CUSTOMERID_FIELD_NUMBER;
    hash = (53 * hash) + getCustomerId().hashCode();
    if (hasAmount()) {
      hash = (37 * hash) + AMOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getAmount().hashCode();
    }
    hash = (37 * hash) + CURRENCY_FIELD_NUMBER;
    hash = (53 * hash) + getCurrency().hashCode();
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + type_;
    hash = (37 * hash) + REWARDID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getRewardId());
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.reward.ManualRewardRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.reward.ManualRewardRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.reward.ManualRewardRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.reward.ManualRewardRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.reward.ManualRewardRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.reward.ManualRewardRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.reward.ManualRewardRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.reward.ManualRewardRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.reward.ManualRewardRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.reward.ManualRewardRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.reward.ManualRewardRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.reward.ManualRewardRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.reward.ManualRewardRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.reward.ManualRewardRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.reward.ManualRewardRequest)
      com.kikitrade.activity.facade.reward.ManualRewardRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.reward.ActivityRewardFacadeOuterClass.internal_static_com_kikitrade_activity_facade_reward_ManualRewardRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.reward.ActivityRewardFacadeOuterClass.internal_static_com_kikitrade_activity_facade_reward_ManualRewardRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.reward.ManualRewardRequest.class, com.kikitrade.activity.facade.reward.ManualRewardRequest.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.reward.ManualRewardRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getAmountFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      customerId_ = "";
      amount_ = null;
      if (amountBuilder_ != null) {
        amountBuilder_.dispose();
        amountBuilder_ = null;
      }
      currency_ = "";
      type_ = 0;
      rewardId_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.reward.ActivityRewardFacadeOuterClass.internal_static_com_kikitrade_activity_facade_reward_ManualRewardRequest_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.reward.ManualRewardRequest getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.reward.ManualRewardRequest.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.reward.ManualRewardRequest build() {
      com.kikitrade.activity.facade.reward.ManualRewardRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.reward.ManualRewardRequest buildPartial() {
      com.kikitrade.activity.facade.reward.ManualRewardRequest result = new com.kikitrade.activity.facade.reward.ManualRewardRequest(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.kikitrade.activity.facade.reward.ManualRewardRequest result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.customerId_ = customerId_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.amount_ = amountBuilder_ == null
            ? amount_
            : amountBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.currency_ = currency_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.type_ = type_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.rewardId_ = rewardId_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.reward.ManualRewardRequest) {
        return mergeFrom((com.kikitrade.activity.facade.reward.ManualRewardRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.reward.ManualRewardRequest other) {
      if (other == com.kikitrade.activity.facade.reward.ManualRewardRequest.getDefaultInstance()) return this;
      if (!other.getCustomerId().isEmpty()) {
        customerId_ = other.customerId_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasAmount()) {
        mergeAmount(other.getAmount());
      }
      if (!other.getCurrency().isEmpty()) {
        currency_ = other.currency_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.type_ != 0) {
        setTypeValue(other.getTypeValue());
      }
      if (other.getRewardId() != 0L) {
        setRewardId(other.getRewardId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              customerId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              input.readMessage(
                  getAmountFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              currency_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              type_ = input.readEnum();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              rewardId_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object customerId_ = "";
    /**
     * <code>string customerId = 1;</code>
     * @return The customerId.
     */
    public java.lang.String getCustomerId() {
      java.lang.Object ref = customerId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        customerId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string customerId = 1;</code>
     * @return The bytes for customerId.
     */
    public com.google.protobuf.ByteString
        getCustomerIdBytes() {
      java.lang.Object ref = customerId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        customerId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string customerId = 1;</code>
     * @param value The customerId to set.
     * @return This builder for chaining.
     */
    public Builder setCustomerId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      customerId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string customerId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearCustomerId() {
      customerId_ = getDefaultInstance().getCustomerId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string customerId = 1;</code>
     * @param value The bytes for customerId to set.
     * @return This builder for chaining.
     */
    public Builder setCustomerIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      customerId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private com.kikitrade.activity.facade.reward.Decimal amount_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.kikitrade.activity.facade.reward.Decimal, com.kikitrade.activity.facade.reward.Decimal.Builder, com.kikitrade.activity.facade.reward.DecimalOrBuilder> amountBuilder_;
    /**
     * <code>.com.kikitrade.activity.facade.reward.Decimal amount = 2;</code>
     * @return Whether the amount field is set.
     */
    public boolean hasAmount() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.com.kikitrade.activity.facade.reward.Decimal amount = 2;</code>
     * @return The amount.
     */
    public com.kikitrade.activity.facade.reward.Decimal getAmount() {
      if (amountBuilder_ == null) {
        return amount_ == null ? com.kikitrade.activity.facade.reward.Decimal.getDefaultInstance() : amount_;
      } else {
        return amountBuilder_.getMessage();
      }
    }
    /**
     * <code>.com.kikitrade.activity.facade.reward.Decimal amount = 2;</code>
     */
    public Builder setAmount(com.kikitrade.activity.facade.reward.Decimal value) {
      if (amountBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        amount_ = value;
      } else {
        amountBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.reward.Decimal amount = 2;</code>
     */
    public Builder setAmount(
        com.kikitrade.activity.facade.reward.Decimal.Builder builderForValue) {
      if (amountBuilder_ == null) {
        amount_ = builderForValue.build();
      } else {
        amountBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.reward.Decimal amount = 2;</code>
     */
    public Builder mergeAmount(com.kikitrade.activity.facade.reward.Decimal value) {
      if (amountBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          amount_ != null &&
          amount_ != com.kikitrade.activity.facade.reward.Decimal.getDefaultInstance()) {
          getAmountBuilder().mergeFrom(value);
        } else {
          amount_ = value;
        }
      } else {
        amountBuilder_.mergeFrom(value);
      }
      if (amount_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.reward.Decimal amount = 2;</code>
     */
    public Builder clearAmount() {
      bitField0_ = (bitField0_ & ~0x00000002);
      amount_ = null;
      if (amountBuilder_ != null) {
        amountBuilder_.dispose();
        amountBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.reward.Decimal amount = 2;</code>
     */
    public com.kikitrade.activity.facade.reward.Decimal.Builder getAmountBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return getAmountFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.kikitrade.activity.facade.reward.Decimal amount = 2;</code>
     */
    public com.kikitrade.activity.facade.reward.DecimalOrBuilder getAmountOrBuilder() {
      if (amountBuilder_ != null) {
        return amountBuilder_.getMessageOrBuilder();
      } else {
        return amount_ == null ?
            com.kikitrade.activity.facade.reward.Decimal.getDefaultInstance() : amount_;
      }
    }
    /**
     * <code>.com.kikitrade.activity.facade.reward.Decimal amount = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.kikitrade.activity.facade.reward.Decimal, com.kikitrade.activity.facade.reward.Decimal.Builder, com.kikitrade.activity.facade.reward.DecimalOrBuilder> 
        getAmountFieldBuilder() {
      if (amountBuilder_ == null) {
        amountBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.kikitrade.activity.facade.reward.Decimal, com.kikitrade.activity.facade.reward.Decimal.Builder, com.kikitrade.activity.facade.reward.DecimalOrBuilder>(
                getAmount(),
                getParentForChildren(),
                isClean());
        amount_ = null;
      }
      return amountBuilder_;
    }

    private java.lang.Object currency_ = "";
    /**
     * <code>string currency = 3;</code>
     * @return The currency.
     */
    public java.lang.String getCurrency() {
      java.lang.Object ref = currency_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        currency_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string currency = 3;</code>
     * @return The bytes for currency.
     */
    public com.google.protobuf.ByteString
        getCurrencyBytes() {
      java.lang.Object ref = currency_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        currency_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string currency = 3;</code>
     * @param value The currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrency(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      currency_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>string currency = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurrency() {
      currency_ = getDefaultInstance().getCurrency();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>string currency = 3;</code>
     * @param value The bytes for currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrencyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      currency_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private int type_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.reward.TransactionTypeEnum type = 4;</code>
     * @return The enum numeric value on the wire for type.
     */
    @java.lang.Override public int getTypeValue() {
      return type_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.reward.TransactionTypeEnum type = 4;</code>
     * @param value The enum numeric value on the wire for type to set.
     * @return This builder for chaining.
     */
    public Builder setTypeValue(int value) {
      type_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.reward.TransactionTypeEnum type = 4;</code>
     * @return The type.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.reward.TransactionTypeEnum getType() {
      com.kikitrade.activity.facade.reward.TransactionTypeEnum result = com.kikitrade.activity.facade.reward.TransactionTypeEnum.forNumber(type_);
      return result == null ? com.kikitrade.activity.facade.reward.TransactionTypeEnum.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.reward.TransactionTypeEnum type = 4;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(com.kikitrade.activity.facade.reward.TransactionTypeEnum value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000008;
      type_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.reward.TransactionTypeEnum type = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000008);
      type_ = 0;
      onChanged();
      return this;
    }

    private long rewardId_ ;
    /**
     * <code>int64 rewardId = 5;</code>
     * @return The rewardId.
     */
    @java.lang.Override
    public long getRewardId() {
      return rewardId_;
    }
    /**
     * <code>int64 rewardId = 5;</code>
     * @param value The rewardId to set.
     * @return This builder for chaining.
     */
    public Builder setRewardId(long value) {

      rewardId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>int64 rewardId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewardId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      rewardId_ = 0L;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.reward.ManualRewardRequest)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.reward.ManualRewardRequest)
  private static final com.kikitrade.activity.facade.reward.ManualRewardRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.reward.ManualRewardRequest();
  }

  public static com.kikitrade.activity.facade.reward.ManualRewardRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ManualRewardRequest>
      PARSER = new com.google.protobuf.AbstractParser<ManualRewardRequest>() {
    @java.lang.Override
    public ManualRewardRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ManualRewardRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ManualRewardRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.reward.ManualRewardRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

