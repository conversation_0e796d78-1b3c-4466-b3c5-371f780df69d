package com.kikitrade.activity.facade.luck;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.58.0)",
    comments = "Source: LuckFortuneFacade.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class LuckFortuneFacadeGrpc {

  private LuckFortuneFacadeGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.kikitrade.activity.facade.luck.LuckFortuneFacade";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO,
      com.kikitrade.activity.facade.luck.LuckCommonResponse> getSaveMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "save",
      requestType = com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO.class,
      responseType = com.kikitrade.activity.facade.luck.LuckCommonResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO,
      com.kikitrade.activity.facade.luck.LuckCommonResponse> getSaveMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO, com.kikitrade.activity.facade.luck.LuckCommonResponse> getSaveMethod;
    if ((getSaveMethod = LuckFortuneFacadeGrpc.getSaveMethod) == null) {
      synchronized (LuckFortuneFacadeGrpc.class) {
        if ((getSaveMethod = LuckFortuneFacadeGrpc.getSaveMethod) == null) {
          LuckFortuneFacadeGrpc.getSaveMethod = getSaveMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO, com.kikitrade.activity.facade.luck.LuckCommonResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "save"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.luck.LuckCommonResponse.getDefaultInstance()))
              .setSchemaDescriptor(new LuckFortuneFacadeMethodDescriptorSupplier("save"))
              .build();
        }
      }
    }
    return getSaveMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static LuckFortuneFacadeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<LuckFortuneFacadeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<LuckFortuneFacadeStub>() {
        @java.lang.Override
        public LuckFortuneFacadeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new LuckFortuneFacadeStub(channel, callOptions);
        }
      };
    return LuckFortuneFacadeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static LuckFortuneFacadeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<LuckFortuneFacadeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<LuckFortuneFacadeBlockingStub>() {
        @java.lang.Override
        public LuckFortuneFacadeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new LuckFortuneFacadeBlockingStub(channel, callOptions);
        }
      };
    return LuckFortuneFacadeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static LuckFortuneFacadeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<LuckFortuneFacadeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<LuckFortuneFacadeFutureStub>() {
        @java.lang.Override
        public LuckFortuneFacadeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new LuckFortuneFacadeFutureStub(channel, callOptions);
        }
      };
    return LuckFortuneFacadeFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     * <pre>
     **
     *保存红包规则
     * </pre>
     */
    default void save(com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.luck.LuckCommonResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSaveMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service LuckFortuneFacade.
   */
  public static abstract class LuckFortuneFacadeImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return LuckFortuneFacadeGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service LuckFortuneFacade.
   */
  public static final class LuckFortuneFacadeStub
      extends io.grpc.stub.AbstractAsyncStub<LuckFortuneFacadeStub> {
    private LuckFortuneFacadeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected LuckFortuneFacadeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new LuckFortuneFacadeStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *保存红包规则
     * </pre>
     */
    public void save(com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.luck.LuckCommonResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSaveMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service LuckFortuneFacade.
   */
  public static final class LuckFortuneFacadeBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<LuckFortuneFacadeBlockingStub> {
    private LuckFortuneFacadeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected LuckFortuneFacadeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new LuckFortuneFacadeBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *保存红包规则
     * </pre>
     */
    public com.kikitrade.activity.facade.luck.LuckCommonResponse save(com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSaveMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service LuckFortuneFacade.
   */
  public static final class LuckFortuneFacadeFutureStub
      extends io.grpc.stub.AbstractFutureStub<LuckFortuneFacadeFutureStub> {
    private LuckFortuneFacadeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected LuckFortuneFacadeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new LuckFortuneFacadeFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *保存红包规则
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.luck.LuckCommonResponse> save(
        com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSaveMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_SAVE = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_SAVE:
          serviceImpl.save((com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.luck.LuckCommonResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getSaveMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO,
              com.kikitrade.activity.facade.luck.LuckCommonResponse>(
                service, METHODID_SAVE)))
        .build();
  }

  private static abstract class LuckFortuneFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    LuckFortuneFacadeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.kikitrade.activity.facade.luck.LuckFortuneFacadeOuterClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("LuckFortuneFacade");
    }
  }

  private static final class LuckFortuneFacadeFileDescriptorSupplier
      extends LuckFortuneFacadeBaseDescriptorSupplier {
    LuckFortuneFacadeFileDescriptorSupplier() {}
  }

  private static final class LuckFortuneFacadeMethodDescriptorSupplier
      extends LuckFortuneFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    LuckFortuneFacadeMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (LuckFortuneFacadeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new LuckFortuneFacadeFileDescriptorSupplier())
              .addMethod(getSaveMethod())
              .build();
        }
      }
    }
    return result;
  }
}
