/*
* Licensed to the Apache Software Foundation (ASF) under one or more
* contributor license agreements.  See the NOTICE file distributed with
* this work for additional information regarding copyright ownership.
* The ASF licenses this file to You under the Apache License, Version 2.0
* (the "License"); you may not use this file except in compliance with
* the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

    package com.kikitrade.activity.facade.award;

import org.apache.dubbo.common.stream.StreamObserver;
import com.google.protobuf.Message;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.concurrent.CompletableFuture;

public interface ActivityFacade extends org.apache.dubbo.rpc.model.DubboStub {

    String JAVA_SERVICE_NAME = "com.kikitrade.activity.facade.award.ActivityFacade";
    String SERVICE_NAME = "com.kikitrade.activity.facade.award.ActivityFacade";

    com.kikitrade.activity.facade.award.ActivityResponse saveOrUpdateActivity(com.kikitrade.activity.facade.award.ActivityDTO request);

    default CompletableFuture<com.kikitrade.activity.facade.award.ActivityResponse> saveOrUpdateActivityAsync(com.kikitrade.activity.facade.award.ActivityDTO request){
        return CompletableFuture.completedFuture(saveOrUpdateActivity(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void saveOrUpdateActivity(com.kikitrade.activity.facade.award.ActivityDTO request, StreamObserver<com.kikitrade.activity.facade.award.ActivityResponse> responseObserver){
        saveOrUpdateActivityAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

    com.kikitrade.activity.facade.award.ActivityBatchResponse saveOrUpdateBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request);

    default CompletableFuture<com.kikitrade.activity.facade.award.ActivityBatchResponse> saveOrUpdateBatchAsync(com.kikitrade.activity.facade.award.ActivityBatchDTO request){
        return CompletableFuture.completedFuture(saveOrUpdateBatch(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void saveOrUpdateBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse> responseObserver){
        saveOrUpdateBatchAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

        /**
         * <pre>
         * 自定义
         * </pre>
         */
    com.kikitrade.activity.facade.award.ActivityBatchResponse deleteBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request);

    default CompletableFuture<com.kikitrade.activity.facade.award.ActivityBatchResponse> deleteBatchAsync(com.kikitrade.activity.facade.award.ActivityBatchDTO request){
        return CompletableFuture.completedFuture(deleteBatch(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void deleteBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse> responseObserver){
        deleteBatchAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

        /**
         * <pre>
         * 普通
         * </pre>
         */
    com.kikitrade.activity.facade.award.ActivityBatchListResponse queryBatchForList(com.kikitrade.activity.facade.award.ActivityBatchRequest request);

    default CompletableFuture<com.kikitrade.activity.facade.award.ActivityBatchListResponse> queryBatchForListAsync(com.kikitrade.activity.facade.award.ActivityBatchRequest request){
        return CompletableFuture.completedFuture(queryBatchForList(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void queryBatchForList(com.kikitrade.activity.facade.award.ActivityBatchRequest request, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchListResponse> responseObserver){
        queryBatchForListAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

    com.kikitrade.activity.facade.award.ActivityBatch queryDetail(com.kikitrade.activity.facade.award.ActivityBatchDetailRequest request);

    default CompletableFuture<com.kikitrade.activity.facade.award.ActivityBatch> queryDetailAsync(com.kikitrade.activity.facade.award.ActivityBatchDetailRequest request){
        return CompletableFuture.completedFuture(queryDetail(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void queryDetail(com.kikitrade.activity.facade.award.ActivityBatchDetailRequest request, StreamObserver<com.kikitrade.activity.facade.award.ActivityBatch> responseObserver){
        queryDetailAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

    com.kikitrade.activity.facade.award.AuditResponse audit(com.kikitrade.activity.facade.award.AuditRequest request);

    default CompletableFuture<com.kikitrade.activity.facade.award.AuditResponse> auditAsync(com.kikitrade.activity.facade.award.AuditRequest request){
        return CompletableFuture.completedFuture(audit(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void audit(com.kikitrade.activity.facade.award.AuditRequest request, StreamObserver<com.kikitrade.activity.facade.award.AuditResponse> responseObserver){
        auditAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

    com.kikitrade.activity.facade.award.AwardListResponse queryRewardList(com.kikitrade.activity.facade.award.AwardRequest request);

    default CompletableFuture<com.kikitrade.activity.facade.award.AwardListResponse> queryRewardListAsync(com.kikitrade.activity.facade.award.AwardRequest request){
        return CompletableFuture.completedFuture(queryRewardList(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void queryRewardList(com.kikitrade.activity.facade.award.AwardRequest request, StreamObserver<com.kikitrade.activity.facade.award.AwardListResponse> responseObserver){
        queryRewardListAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

    com.kikitrade.activity.facade.award.ModifyAwardResponse deleteReward(com.kikitrade.activity.facade.award.AwardDTO request);

    default CompletableFuture<com.kikitrade.activity.facade.award.ModifyAwardResponse> deleteRewardAsync(com.kikitrade.activity.facade.award.AwardDTO request){
        return CompletableFuture.completedFuture(deleteReward(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void deleteReward(com.kikitrade.activity.facade.award.AwardDTO request, StreamObserver<com.kikitrade.activity.facade.award.ModifyAwardResponse> responseObserver){
        deleteRewardAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

    com.kikitrade.activity.facade.award.UploadResponse uploadFile(com.kikitrade.activity.facade.award.UploadRequest request);

    default CompletableFuture<com.kikitrade.activity.facade.award.UploadResponse> uploadFileAsync(com.kikitrade.activity.facade.award.UploadRequest request){
        return CompletableFuture.completedFuture(uploadFile(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void uploadFile(com.kikitrade.activity.facade.award.UploadRequest request, StreamObserver<com.kikitrade.activity.facade.award.UploadResponse> responseObserver){
        uploadFileAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

    com.kikitrade.activity.facade.award.ExportDataResponse exportData(com.kikitrade.activity.facade.award.ExportDataRequest request);

    default CompletableFuture<com.kikitrade.activity.facade.award.ExportDataResponse> exportDataAsync(com.kikitrade.activity.facade.award.ExportDataRequest request){
        return CompletableFuture.completedFuture(exportData(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void exportData(com.kikitrade.activity.facade.award.ExportDataRequest request, StreamObserver<com.kikitrade.activity.facade.award.ExportDataResponse> responseObserver){
        exportDataAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

    com.kikitrade.activity.facade.award.ImportDataResponse importData(com.kikitrade.activity.facade.award.ImportDataRequest request);

    default CompletableFuture<com.kikitrade.activity.facade.award.ImportDataResponse> importDataAsync(com.kikitrade.activity.facade.award.ImportDataRequest request){
        return CompletableFuture.completedFuture(importData(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void importData(com.kikitrade.activity.facade.award.ImportDataRequest request, StreamObserver<com.kikitrade.activity.facade.award.ImportDataResponse> responseObserver){
        importDataAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

    com.kikitrade.activity.facade.award.LotteryResponse saveLottery(com.kikitrade.activity.facade.award.LotteryDTO request);

    default CompletableFuture<com.kikitrade.activity.facade.award.LotteryResponse> saveLotteryAsync(com.kikitrade.activity.facade.award.LotteryDTO request){
        return CompletableFuture.completedFuture(saveLottery(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void saveLottery(com.kikitrade.activity.facade.award.LotteryDTO request, StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse> responseObserver){
        saveLotteryAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

    com.kikitrade.activity.facade.award.LotteryResponse deleteLottery(com.kikitrade.activity.facade.award.LotteryDeleteDTO request);

    default CompletableFuture<com.kikitrade.activity.facade.award.LotteryResponse> deleteLotteryAsync(com.kikitrade.activity.facade.award.LotteryDeleteDTO request){
        return CompletableFuture.completedFuture(deleteLottery(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void deleteLottery(com.kikitrade.activity.facade.award.LotteryDeleteDTO request, StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse> responseObserver){
        deleteLotteryAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

    com.kikitrade.activity.facade.award.LotteryListResponse lotteryList(com.kikitrade.activity.facade.award.LotteryRequest request);

    default CompletableFuture<com.kikitrade.activity.facade.award.LotteryListResponse> lotteryListAsync(com.kikitrade.activity.facade.award.LotteryRequest request){
        return CompletableFuture.completedFuture(lotteryList(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void lotteryList(com.kikitrade.activity.facade.award.LotteryRequest request, StreamObserver<com.kikitrade.activity.facade.award.LotteryListResponse> responseObserver){
        lotteryListAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

    com.kikitrade.activity.facade.award.LotteryVO lotteryDetail(com.kikitrade.activity.facade.award.LotteryDetailRequest request);

    default CompletableFuture<com.kikitrade.activity.facade.award.LotteryVO> lotteryDetailAsync(com.kikitrade.activity.facade.award.LotteryDetailRequest request){
        return CompletableFuture.completedFuture(lotteryDetail(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void lotteryDetail(com.kikitrade.activity.facade.award.LotteryDetailRequest request, StreamObserver<com.kikitrade.activity.facade.award.LotteryVO> responseObserver){
        lotteryDetailAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

        /**
         * <pre>
         *  子活动类型
         * </pre>
         */
    com.kikitrade.activity.facade.award.ConditionCode getConditionCodes(com.kikitrade.activity.facade.award.EmptyRequest request);

    default CompletableFuture<com.kikitrade.activity.facade.award.ConditionCode> getConditionCodesAsync(com.kikitrade.activity.facade.award.EmptyRequest request){
        return CompletableFuture.completedFuture(getConditionCodes(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void getConditionCodes(com.kikitrade.activity.facade.award.EmptyRequest request, StreamObserver<com.kikitrade.activity.facade.award.ConditionCode> responseObserver){
        getConditionCodesAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }

        /**
         * <pre>
         * 下载地址
         * </pre>
         */
    com.kikitrade.activity.facade.award.ConditionResponse getCondition(com.kikitrade.activity.facade.award.ConditionRequest request);

    default CompletableFuture<com.kikitrade.activity.facade.award.ConditionResponse> getConditionAsync(com.kikitrade.activity.facade.award.ConditionRequest request){
        return CompletableFuture.completedFuture(getCondition(request));
    }

    /**
    * This server stream type unary method is <b>only</b> used for generated stub to support async unary method.
    * It will not be called if you are NOT using Dubbo3 generated triple stub and <b>DO NOT</b> implement this method.
    */
    default void getCondition(com.kikitrade.activity.facade.award.ConditionRequest request, StreamObserver<com.kikitrade.activity.facade.award.ConditionResponse> responseObserver){
        getConditionAsync(request).whenComplete((r, t) -> {
            if (t != null) {
                responseObserver.onError(t);
            } else {
                responseObserver.onNext(r);
                responseObserver.onCompleted();
            }
        });
    }






}
