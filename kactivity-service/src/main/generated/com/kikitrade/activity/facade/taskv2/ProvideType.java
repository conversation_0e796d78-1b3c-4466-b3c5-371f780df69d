// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Task.proto

package com.kikitrade.activity.facade.taskv2;

/**
 * <pre>
 **
 *奖品发放时机
 * </pre>
 *
 * Protobuf enum {@code com.kikitrade.activity.facade.taskv2.ProvideType}
 */
public enum ProvideType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *自动发放
   * </pre>
   *
   * <code>auto = 0;</code>
   */
  auto(0),
  /**
   * <pre>
   *任务周期内手动领取
   * </pre>
   *
   * <code>claim_task = 1;</code>
   */
  claim_task(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *自动发放
   * </pre>
   *
   * <code>auto = 0;</code>
   */
  public static final int auto_VALUE = 0;
  /**
   * <pre>
   *任务周期内手动领取
   * </pre>
   *
   * <code>claim_task = 1;</code>
   */
  public static final int claim_task_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static ProvideType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static ProvideType forNumber(int value) {
    switch (value) {
      case 0: return auto;
      case 1: return claim_task;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<ProvideType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      ProvideType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<ProvideType>() {
          public ProvideType findValueByNumber(int number) {
            return ProvideType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.getDescriptor().getEnumTypes().get(4);
  }

  private static final ProvideType[] VALUES = values();

  public static ProvideType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private ProvideType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.taskv2.ProvideType)
}

