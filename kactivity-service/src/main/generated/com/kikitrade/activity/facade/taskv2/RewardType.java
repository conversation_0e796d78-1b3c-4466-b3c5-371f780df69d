// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Task.proto

package com.kikitrade.activity.facade.taskv2;

/**
 * <pre>
 **
 *奖品类型
 * </pre>
 *
 * Protobuf enum {@code com.kikitrade.activity.facade.taskv2.RewardType}
 */
public enum RewardType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>POINT = 0;</code>
   */
  POINT(0),
  /**
   * <code>NFT = 1;</code>
   */
  NFT(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>POINT = 0;</code>
   */
  public static final int POINT_VALUE = 0;
  /**
   * <code>NFT = 1;</code>
   */
  public static final int NFT_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static RewardType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static RewardType forNumber(int value) {
    switch (value) {
      case 0: return POINT;
      case 1: return NFT;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<RewardType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      RewardType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<RewardType>() {
          public RewardType findValueByNumber(int number) {
            return RewardType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.taskv2.TaskFacadeOutClass.getDescriptor().getEnumTypes().get(5);
  }

  private static final RewardType[] VALUES = values();

  public static RewardType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private RewardType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.taskv2.RewardType)
}

