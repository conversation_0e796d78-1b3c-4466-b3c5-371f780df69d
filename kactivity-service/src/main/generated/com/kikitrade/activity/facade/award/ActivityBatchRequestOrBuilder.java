// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface ActivityBatchRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.ActivityBatchRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string batchName = 2;</code>
   * @return The batchName.
   */
  java.lang.String getBatchName();
  /**
   * <code>string batchName = 2;</code>
   * @return The bytes for batchName.
   */
  com.google.protobuf.ByteString
      getBatchNameBytes();

  /**
   * <code>int32 pageNo = 3;</code>
   * @return The pageNo.
   */
  int getPageNo();

  /**
   * <code>int32 pageSize = 4;</code>
   * @return The pageSize.
   */
  int getPageSize();

  /**
   * <code>.com.kikitrade.activity.facade.award.BatchStatusEnum status = 5;</code>
   * @return The enum numeric value on the wire for status.
   */
  int getStatusValue();
  /**
   * <code>.com.kikitrade.activity.facade.award.BatchStatusEnum status = 5;</code>
   * @return The status.
   */
  com.kikitrade.activity.facade.award.BatchStatusEnum getStatus();

  /**
   * <code>string activityId = 6;</code>
   * @return The activityId.
   */
  java.lang.String getActivityId();
  /**
   * <code>string activityId = 6;</code>
   * @return The bytes for activityId.
   */
  com.google.protobuf.ByteString
      getActivityIdBytes();
}
