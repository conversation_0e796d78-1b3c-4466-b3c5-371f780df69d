// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityRosterRule.proto

package com.kikitrade.activity.facade.roster;

public interface ConditionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.roster.Condition)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string name = 1;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <code>string name = 1;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <code>.com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
   * @return The enum numeric value on the wire for filterTypes.
   */
  int getFilterTypesValue();
  /**
   * <code>.com.kikitrade.activity.facade.roster.FilterType filterTypes = 2;</code>
   * @return The filterTypes.
   */
  com.kikitrade.activity.facade.roster.FilterType getFilterTypes();

  /**
   * <code>string value = 3;</code>
   * @return The value.
   */
  java.lang.String getValue();
  /**
   * <code>string value = 3;</code>
   * @return The bytes for value.
   */
  com.google.protobuf.ByteString
      getValueBytes();
}
