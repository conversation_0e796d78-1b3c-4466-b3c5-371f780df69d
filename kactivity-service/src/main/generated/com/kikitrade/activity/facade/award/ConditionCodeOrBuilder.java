// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface ConditionCodeOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.ConditionCode)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated string code = 1;</code>
   * @return A list containing the code.
   */
  java.util.List<java.lang.String>
      getCodeList();
  /**
   * <code>repeated string code = 1;</code>
   * @return The count of code.
   */
  int getCodeCount();
  /**
   * <code>repeated string code = 1;</code>
   * @param index The index of the element to return.
   * @return The code at the given index.
   */
  java.lang.String getCode(int index);
  /**
   * <code>repeated string code = 1;</code>
   * @param index The index of the value to return.
   * @return The bytes of the code at the given index.
   */
  com.google.protobuf.ByteString
      getCodeBytes(int index);
}
