// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.award.ModifyAwardResponse}
 */
public final class ModifyAwardResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.award.ModifyAwardResponse)
    ModifyAwardResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ModifyAwardResponse.newBuilder() to construct.
  private ModifyAwardResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ModifyAwardResponse() {
    message_ = "";
    detail_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ModifyAwardResponse();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ModifyAwardResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ModifyAwardResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.award.ModifyAwardResponse.class, com.kikitrade.activity.facade.award.ModifyAwardResponse.Builder.class);
  }

  public static final int SUCCESS_FIELD_NUMBER = 1;
  private boolean success_ = false;
  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  @java.lang.Override
  public boolean getSuccess() {
    return success_;
  }

  public static final int MESSAGE_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object message_ = "";
  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  @java.lang.Override
  public java.lang.String getMessage() {
    java.lang.Object ref = message_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      message_ = s;
      return s;
    }
  }
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMessageBytes() {
    java.lang.Object ref = message_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      message_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DETAIL_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.award.ModifyDetail> detail_;
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.award.ModifyDetail> getDetailList() {
    return detail_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.award.ModifyDetailOrBuilder> 
      getDetailOrBuilderList() {
    return detail_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
   */
  @java.lang.Override
  public int getDetailCount() {
    return detail_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.ModifyDetail getDetail(int index) {
    return detail_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.ModifyDetailOrBuilder getDetailOrBuilder(
      int index) {
    return detail_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (success_ != false) {
      output.writeBool(1, success_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(message_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, message_);
    }
    for (int i = 0; i < detail_.size(); i++) {
      output.writeMessage(3, detail_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (success_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(1, success_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(message_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, message_);
    }
    for (int i = 0; i < detail_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, detail_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.award.ModifyAwardResponse)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.award.ModifyAwardResponse other = (com.kikitrade.activity.facade.award.ModifyAwardResponse) obj;

    if (getSuccess()
        != other.getSuccess()) return false;
    if (!getMessage()
        .equals(other.getMessage())) return false;
    if (!getDetailList()
        .equals(other.getDetailList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SUCCESS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getSuccess());
    hash = (37 * hash) + MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getMessage().hashCode();
    if (getDetailCount() > 0) {
      hash = (37 * hash) + DETAIL_FIELD_NUMBER;
      hash = (53 * hash) + getDetailList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.award.ModifyAwardResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ModifyAwardResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ModifyAwardResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ModifyAwardResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ModifyAwardResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ModifyAwardResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ModifyAwardResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.ModifyAwardResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.award.ModifyAwardResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.award.ModifyAwardResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ModifyAwardResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.ModifyAwardResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.award.ModifyAwardResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.award.ModifyAwardResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.award.ModifyAwardResponse)
      com.kikitrade.activity.facade.award.ModifyAwardResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ModifyAwardResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ModifyAwardResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.award.ModifyAwardResponse.class, com.kikitrade.activity.facade.award.ModifyAwardResponse.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.award.ModifyAwardResponse.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      success_ = false;
      message_ = "";
      if (detailBuilder_ == null) {
        detail_ = java.util.Collections.emptyList();
      } else {
        detail_ = null;
        detailBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ModifyAwardResponse_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ModifyAwardResponse getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.award.ModifyAwardResponse.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ModifyAwardResponse build() {
      com.kikitrade.activity.facade.award.ModifyAwardResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ModifyAwardResponse buildPartial() {
      com.kikitrade.activity.facade.award.ModifyAwardResponse result = new com.kikitrade.activity.facade.award.ModifyAwardResponse(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.award.ModifyAwardResponse result) {
      if (detailBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          detail_ = java.util.Collections.unmodifiableList(detail_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.detail_ = detail_;
      } else {
        result.detail_ = detailBuilder_.build();
      }
    }

    private void buildPartial0(com.kikitrade.activity.facade.award.ModifyAwardResponse result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.success_ = success_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.message_ = message_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.award.ModifyAwardResponse) {
        return mergeFrom((com.kikitrade.activity.facade.award.ModifyAwardResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.award.ModifyAwardResponse other) {
      if (other == com.kikitrade.activity.facade.award.ModifyAwardResponse.getDefaultInstance()) return this;
      if (other.getSuccess() != false) {
        setSuccess(other.getSuccess());
      }
      if (!other.getMessage().isEmpty()) {
        message_ = other.message_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (detailBuilder_ == null) {
        if (!other.detail_.isEmpty()) {
          if (detail_.isEmpty()) {
            detail_ = other.detail_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureDetailIsMutable();
            detail_.addAll(other.detail_);
          }
          onChanged();
        }
      } else {
        if (!other.detail_.isEmpty()) {
          if (detailBuilder_.isEmpty()) {
            detailBuilder_.dispose();
            detailBuilder_ = null;
            detail_ = other.detail_;
            bitField0_ = (bitField0_ & ~0x00000004);
            detailBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getDetailFieldBuilder() : null;
          } else {
            detailBuilder_.addAllMessages(other.detail_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              success_ = input.readBool();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              message_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              com.kikitrade.activity.facade.award.ModifyDetail m =
                  input.readMessage(
                      com.kikitrade.activity.facade.award.ModifyDetail.parser(),
                      extensionRegistry);
              if (detailBuilder_ == null) {
                ensureDetailIsMutable();
                detail_.add(m);
              } else {
                detailBuilder_.addMessage(m);
              }
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private boolean success_ ;
    /**
     * <code>bool success = 1;</code>
     * @return The success.
     */
    @java.lang.Override
    public boolean getSuccess() {
      return success_;
    }
    /**
     * <code>bool success = 1;</code>
     * @param value The success to set.
     * @return This builder for chaining.
     */
    public Builder setSuccess(boolean value) {

      success_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>bool success = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSuccess() {
      bitField0_ = (bitField0_ & ~0x00000001);
      success_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object message_ = "";
    /**
     * <code>string message = 2;</code>
     * @return The message.
     */
    public java.lang.String getMessage() {
      java.lang.Object ref = message_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        message_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string message = 2;</code>
     * @return The bytes for message.
     */
    public com.google.protobuf.ByteString
        getMessageBytes() {
      java.lang.Object ref = message_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        message_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string message = 2;</code>
     * @param value The message to set.
     * @return This builder for chaining.
     */
    public Builder setMessage(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      message_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string message = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMessage() {
      message_ = getDefaultInstance().getMessage();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string message = 2;</code>
     * @param value The bytes for message to set.
     * @return This builder for chaining.
     */
    public Builder setMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      message_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.util.List<com.kikitrade.activity.facade.award.ModifyDetail> detail_ =
      java.util.Collections.emptyList();
    private void ensureDetailIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        detail_ = new java.util.ArrayList<com.kikitrade.activity.facade.award.ModifyDetail>(detail_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.ModifyDetail, com.kikitrade.activity.facade.award.ModifyDetail.Builder, com.kikitrade.activity.facade.award.ModifyDetailOrBuilder> detailBuilder_;

    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.ModifyDetail> getDetailList() {
      if (detailBuilder_ == null) {
        return java.util.Collections.unmodifiableList(detail_);
      } else {
        return detailBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public int getDetailCount() {
      if (detailBuilder_ == null) {
        return detail_.size();
      } else {
        return detailBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public com.kikitrade.activity.facade.award.ModifyDetail getDetail(int index) {
      if (detailBuilder_ == null) {
        return detail_.get(index);
      } else {
        return detailBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public Builder setDetail(
        int index, com.kikitrade.activity.facade.award.ModifyDetail value) {
      if (detailBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetailIsMutable();
        detail_.set(index, value);
        onChanged();
      } else {
        detailBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public Builder setDetail(
        int index, com.kikitrade.activity.facade.award.ModifyDetail.Builder builderForValue) {
      if (detailBuilder_ == null) {
        ensureDetailIsMutable();
        detail_.set(index, builderForValue.build());
        onChanged();
      } else {
        detailBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public Builder addDetail(com.kikitrade.activity.facade.award.ModifyDetail value) {
      if (detailBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetailIsMutable();
        detail_.add(value);
        onChanged();
      } else {
        detailBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public Builder addDetail(
        int index, com.kikitrade.activity.facade.award.ModifyDetail value) {
      if (detailBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetailIsMutable();
        detail_.add(index, value);
        onChanged();
      } else {
        detailBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public Builder addDetail(
        com.kikitrade.activity.facade.award.ModifyDetail.Builder builderForValue) {
      if (detailBuilder_ == null) {
        ensureDetailIsMutable();
        detail_.add(builderForValue.build());
        onChanged();
      } else {
        detailBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public Builder addDetail(
        int index, com.kikitrade.activity.facade.award.ModifyDetail.Builder builderForValue) {
      if (detailBuilder_ == null) {
        ensureDetailIsMutable();
        detail_.add(index, builderForValue.build());
        onChanged();
      } else {
        detailBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public Builder addAllDetail(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.award.ModifyDetail> values) {
      if (detailBuilder_ == null) {
        ensureDetailIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, detail_);
        onChanged();
      } else {
        detailBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public Builder clearDetail() {
      if (detailBuilder_ == null) {
        detail_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        detailBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public Builder removeDetail(int index) {
      if (detailBuilder_ == null) {
        ensureDetailIsMutable();
        detail_.remove(index);
        onChanged();
      } else {
        detailBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public com.kikitrade.activity.facade.award.ModifyDetail.Builder getDetailBuilder(
        int index) {
      return getDetailFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public com.kikitrade.activity.facade.award.ModifyDetailOrBuilder getDetailOrBuilder(
        int index) {
      if (detailBuilder_ == null) {
        return detail_.get(index);  } else {
        return detailBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.award.ModifyDetailOrBuilder> 
         getDetailOrBuilderList() {
      if (detailBuilder_ != null) {
        return detailBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(detail_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public com.kikitrade.activity.facade.award.ModifyDetail.Builder addDetailBuilder() {
      return getDetailFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.award.ModifyDetail.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public com.kikitrade.activity.facade.award.ModifyDetail.Builder addDetailBuilder(
        int index) {
      return getDetailFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.award.ModifyDetail.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.ModifyDetail detail = 3;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.ModifyDetail.Builder> 
         getDetailBuilderList() {
      return getDetailFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.ModifyDetail, com.kikitrade.activity.facade.award.ModifyDetail.Builder, com.kikitrade.activity.facade.award.ModifyDetailOrBuilder> 
        getDetailFieldBuilder() {
      if (detailBuilder_ == null) {
        detailBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.award.ModifyDetail, com.kikitrade.activity.facade.award.ModifyDetail.Builder, com.kikitrade.activity.facade.award.ModifyDetailOrBuilder>(
                detail_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        detail_ = null;
      }
      return detailBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.award.ModifyAwardResponse)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.award.ModifyAwardResponse)
  private static final com.kikitrade.activity.facade.award.ModifyAwardResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.award.ModifyAwardResponse();
  }

  public static com.kikitrade.activity.facade.award.ModifyAwardResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ModifyAwardResponse>
      PARSER = new com.google.protobuf.AbstractParser<ModifyAwardResponse>() {
    @java.lang.Override
    public ModifyAwardResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ModifyAwardResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ModifyAwardResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.award.ModifyAwardResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

