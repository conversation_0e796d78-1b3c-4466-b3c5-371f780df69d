// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface LotteryRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.LotteryRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string valid = 1;</code>
   * @return The valid.
   */
  java.lang.String getValid();
  /**
   * <code>string valid = 1;</code>
   * @return The bytes for valid.
   */
  com.google.protobuf.ByteString
      getValidBytes();

  /**
   * <code>int32 offset = 2;</code>
   * @return The offset.
   */
  int getOffset();

  /**
   * <code>int32 limit = 3;</code>
   * @return The limit.
   */
  int getLimit();
}
