// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface AwardListResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.AwardListResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>int64 pageNo = 2;</code>
   * @return The pageNo.
   */
  long getPageNo();

  /**
   * <code>int64 pageSize = 3;</code>
   * @return The pageSize.
   */
  long getPageSize();

  /**
   * <code>int64 total = 4;</code>
   * @return The total.
   */
  long getTotal();
}
