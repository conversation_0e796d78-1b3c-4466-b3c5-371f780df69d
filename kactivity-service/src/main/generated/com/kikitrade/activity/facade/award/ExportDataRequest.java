// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.award.ExportDataRequest}
 */
public final class ExportDataRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.award.ExportDataRequest)
    ExportDataRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ExportDataRequest.newBuilder() to construct.
  private ExportDataRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ExportDataRequest() {
    id_ = "";
    conditionId_ = "";
    conditions_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ExportDataRequest();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ExportDataRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ExportDataRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.award.ExportDataRequest.class, com.kikitrade.activity.facade.award.ExportDataRequest.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <pre>
   *活动id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *活动id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CONDITIONID_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object conditionId_ = "";
  /**
   * <code>string conditionId = 2;</code>
   * @return The conditionId.
   */
  @java.lang.Override
  public java.lang.String getConditionId() {
    java.lang.Object ref = conditionId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      conditionId_ = s;
      return s;
    }
  }
  /**
   * <code>string conditionId = 2;</code>
   * @return The bytes for conditionId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getConditionIdBytes() {
    java.lang.Object ref = conditionId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      conditionId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CONDITIONS_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.award.Condition> conditions_;
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.award.Condition> getConditionsList() {
    return conditions_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.award.ConditionOrBuilder> 
      getConditionsOrBuilderList() {
    return conditions_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
   */
  @java.lang.Override
  public int getConditionsCount() {
    return conditions_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.Condition getConditions(int index) {
    return conditions_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.ConditionOrBuilder getConditionsOrBuilder(
      int index) {
    return conditions_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(conditionId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, conditionId_);
    }
    for (int i = 0; i < conditions_.size(); i++) {
      output.writeMessage(3, conditions_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(conditionId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, conditionId_);
    }
    for (int i = 0; i < conditions_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, conditions_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.award.ExportDataRequest)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.award.ExportDataRequest other = (com.kikitrade.activity.facade.award.ExportDataRequest) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getConditionId()
        .equals(other.getConditionId())) return false;
    if (!getConditionsList()
        .equals(other.getConditionsList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + CONDITIONID_FIELD_NUMBER;
    hash = (53 * hash) + getConditionId().hashCode();
    if (getConditionsCount() > 0) {
      hash = (37 * hash) + CONDITIONS_FIELD_NUMBER;
      hash = (53 * hash) + getConditionsList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.award.ExportDataRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ExportDataRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ExportDataRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ExportDataRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ExportDataRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ExportDataRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ExportDataRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.ExportDataRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.award.ExportDataRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.award.ExportDataRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ExportDataRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.ExportDataRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.award.ExportDataRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.award.ExportDataRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.award.ExportDataRequest)
      com.kikitrade.activity.facade.award.ExportDataRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ExportDataRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ExportDataRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.award.ExportDataRequest.class, com.kikitrade.activity.facade.award.ExportDataRequest.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.award.ExportDataRequest.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      conditionId_ = "";
      if (conditionsBuilder_ == null) {
        conditions_ = java.util.Collections.emptyList();
      } else {
        conditions_ = null;
        conditionsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ExportDataRequest_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ExportDataRequest getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.award.ExportDataRequest.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ExportDataRequest build() {
      com.kikitrade.activity.facade.award.ExportDataRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ExportDataRequest buildPartial() {
      com.kikitrade.activity.facade.award.ExportDataRequest result = new com.kikitrade.activity.facade.award.ExportDataRequest(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.award.ExportDataRequest result) {
      if (conditionsBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          conditions_ = java.util.Collections.unmodifiableList(conditions_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.conditions_ = conditions_;
      } else {
        result.conditions_ = conditionsBuilder_.build();
      }
    }

    private void buildPartial0(com.kikitrade.activity.facade.award.ExportDataRequest result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.conditionId_ = conditionId_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.award.ExportDataRequest) {
        return mergeFrom((com.kikitrade.activity.facade.award.ExportDataRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.award.ExportDataRequest other) {
      if (other == com.kikitrade.activity.facade.award.ExportDataRequest.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getConditionId().isEmpty()) {
        conditionId_ = other.conditionId_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (conditionsBuilder_ == null) {
        if (!other.conditions_.isEmpty()) {
          if (conditions_.isEmpty()) {
            conditions_ = other.conditions_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureConditionsIsMutable();
            conditions_.addAll(other.conditions_);
          }
          onChanged();
        }
      } else {
        if (!other.conditions_.isEmpty()) {
          if (conditionsBuilder_.isEmpty()) {
            conditionsBuilder_.dispose();
            conditionsBuilder_ = null;
            conditions_ = other.conditions_;
            bitField0_ = (bitField0_ & ~0x00000004);
            conditionsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getConditionsFieldBuilder() : null;
          } else {
            conditionsBuilder_.addAllMessages(other.conditions_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              conditionId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              com.kikitrade.activity.facade.award.Condition m =
                  input.readMessage(
                      com.kikitrade.activity.facade.award.Condition.parser(),
                      extensionRegistry);
              if (conditionsBuilder_ == null) {
                ensureConditionsIsMutable();
                conditions_.add(m);
              } else {
                conditionsBuilder_.addMessage(m);
              }
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <pre>
     *活动id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *活动id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *活动id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *活动id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *活动id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object conditionId_ = "";
    /**
     * <code>string conditionId = 2;</code>
     * @return The conditionId.
     */
    public java.lang.String getConditionId() {
      java.lang.Object ref = conditionId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        conditionId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string conditionId = 2;</code>
     * @return The bytes for conditionId.
     */
    public com.google.protobuf.ByteString
        getConditionIdBytes() {
      java.lang.Object ref = conditionId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        conditionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string conditionId = 2;</code>
     * @param value The conditionId to set.
     * @return This builder for chaining.
     */
    public Builder setConditionId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      conditionId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string conditionId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearConditionId() {
      conditionId_ = getDefaultInstance().getConditionId();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string conditionId = 2;</code>
     * @param value The bytes for conditionId to set.
     * @return This builder for chaining.
     */
    public Builder setConditionIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      conditionId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.util.List<com.kikitrade.activity.facade.award.Condition> conditions_ =
      java.util.Collections.emptyList();
    private void ensureConditionsIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        conditions_ = new java.util.ArrayList<com.kikitrade.activity.facade.award.Condition>(conditions_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.Condition, com.kikitrade.activity.facade.award.Condition.Builder, com.kikitrade.activity.facade.award.ConditionOrBuilder> conditionsBuilder_;

    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.Condition> getConditionsList() {
      if (conditionsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(conditions_);
      } else {
        return conditionsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public int getConditionsCount() {
      if (conditionsBuilder_ == null) {
        return conditions_.size();
      } else {
        return conditionsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public com.kikitrade.activity.facade.award.Condition getConditions(int index) {
      if (conditionsBuilder_ == null) {
        return conditions_.get(index);
      } else {
        return conditionsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public Builder setConditions(
        int index, com.kikitrade.activity.facade.award.Condition value) {
      if (conditionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionsIsMutable();
        conditions_.set(index, value);
        onChanged();
      } else {
        conditionsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public Builder setConditions(
        int index, com.kikitrade.activity.facade.award.Condition.Builder builderForValue) {
      if (conditionsBuilder_ == null) {
        ensureConditionsIsMutable();
        conditions_.set(index, builderForValue.build());
        onChanged();
      } else {
        conditionsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public Builder addConditions(com.kikitrade.activity.facade.award.Condition value) {
      if (conditionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionsIsMutable();
        conditions_.add(value);
        onChanged();
      } else {
        conditionsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public Builder addConditions(
        int index, com.kikitrade.activity.facade.award.Condition value) {
      if (conditionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConditionsIsMutable();
        conditions_.add(index, value);
        onChanged();
      } else {
        conditionsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public Builder addConditions(
        com.kikitrade.activity.facade.award.Condition.Builder builderForValue) {
      if (conditionsBuilder_ == null) {
        ensureConditionsIsMutable();
        conditions_.add(builderForValue.build());
        onChanged();
      } else {
        conditionsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public Builder addConditions(
        int index, com.kikitrade.activity.facade.award.Condition.Builder builderForValue) {
      if (conditionsBuilder_ == null) {
        ensureConditionsIsMutable();
        conditions_.add(index, builderForValue.build());
        onChanged();
      } else {
        conditionsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public Builder addAllConditions(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.award.Condition> values) {
      if (conditionsBuilder_ == null) {
        ensureConditionsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, conditions_);
        onChanged();
      } else {
        conditionsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public Builder clearConditions() {
      if (conditionsBuilder_ == null) {
        conditions_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        conditionsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public Builder removeConditions(int index) {
      if (conditionsBuilder_ == null) {
        ensureConditionsIsMutable();
        conditions_.remove(index);
        onChanged();
      } else {
        conditionsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public com.kikitrade.activity.facade.award.Condition.Builder getConditionsBuilder(
        int index) {
      return getConditionsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public com.kikitrade.activity.facade.award.ConditionOrBuilder getConditionsOrBuilder(
        int index) {
      if (conditionsBuilder_ == null) {
        return conditions_.get(index);  } else {
        return conditionsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.award.ConditionOrBuilder> 
         getConditionsOrBuilderList() {
      if (conditionsBuilder_ != null) {
        return conditionsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(conditions_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public com.kikitrade.activity.facade.award.Condition.Builder addConditionsBuilder() {
      return getConditionsFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.award.Condition.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public com.kikitrade.activity.facade.award.Condition.Builder addConditionsBuilder(
        int index) {
      return getConditionsFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.award.Condition.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.Condition conditions = 3;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.Condition.Builder> 
         getConditionsBuilderList() {
      return getConditionsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.Condition, com.kikitrade.activity.facade.award.Condition.Builder, com.kikitrade.activity.facade.award.ConditionOrBuilder> 
        getConditionsFieldBuilder() {
      if (conditionsBuilder_ == null) {
        conditionsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.award.Condition, com.kikitrade.activity.facade.award.Condition.Builder, com.kikitrade.activity.facade.award.ConditionOrBuilder>(
                conditions_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        conditions_ = null;
      }
      return conditionsBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.award.ExportDataRequest)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.award.ExportDataRequest)
  private static final com.kikitrade.activity.facade.award.ExportDataRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.award.ExportDataRequest();
  }

  public static com.kikitrade.activity.facade.award.ExportDataRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ExportDataRequest>
      PARSER = new com.google.protobuf.AbstractParser<ExportDataRequest>() {
    @java.lang.Override
    public ExportDataRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ExportDataRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ExportDataRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.award.ExportDataRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

