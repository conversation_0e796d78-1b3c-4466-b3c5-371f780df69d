package com.kikitrade.activity.facade.reward;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.58.0)",
    comments = "Source: ActivityRewardFacade.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class ActivityRewardFacadeGrpc {

  private ActivityRewardFacadeGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.kikitrade.activity.facade.reward.ActivityRewardFacade";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.kikitrade.activity.facade.reward.ManualRewardRequest,
      com.kikitrade.activity.facade.reward.ManualRewardResponse> getManualRewardMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "manualReward",
      requestType = com.kikitrade.activity.facade.reward.ManualRewardRequest.class,
      responseType = com.kikitrade.activity.facade.reward.ManualRewardResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.activity.facade.reward.ManualRewardRequest,
      com.kikitrade.activity.facade.reward.ManualRewardResponse> getManualRewardMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.activity.facade.reward.ManualRewardRequest, com.kikitrade.activity.facade.reward.ManualRewardResponse> getManualRewardMethod;
    if ((getManualRewardMethod = ActivityRewardFacadeGrpc.getManualRewardMethod) == null) {
      synchronized (ActivityRewardFacadeGrpc.class) {
        if ((getManualRewardMethod = ActivityRewardFacadeGrpc.getManualRewardMethod) == null) {
          ActivityRewardFacadeGrpc.getManualRewardMethod = getManualRewardMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.activity.facade.reward.ManualRewardRequest, com.kikitrade.activity.facade.reward.ManualRewardResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "manualReward"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.reward.ManualRewardRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.activity.facade.reward.ManualRewardResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ActivityRewardFacadeMethodDescriptorSupplier("manualReward"))
              .build();
        }
      }
    }
    return getManualRewardMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static ActivityRewardFacadeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ActivityRewardFacadeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ActivityRewardFacadeStub>() {
        @java.lang.Override
        public ActivityRewardFacadeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ActivityRewardFacadeStub(channel, callOptions);
        }
      };
    return ActivityRewardFacadeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static ActivityRewardFacadeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ActivityRewardFacadeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ActivityRewardFacadeBlockingStub>() {
        @java.lang.Override
        public ActivityRewardFacadeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ActivityRewardFacadeBlockingStub(channel, callOptions);
        }
      };
    return ActivityRewardFacadeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static ActivityRewardFacadeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ActivityRewardFacadeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ActivityRewardFacadeFutureStub>() {
        @java.lang.Override
        public ActivityRewardFacadeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ActivityRewardFacadeFutureStub(channel, callOptions);
        }
      };
    return ActivityRewardFacadeFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     * <pre>
     **
     * 运营手工发奖
     * </pre>
     */
    default void manualReward(com.kikitrade.activity.facade.reward.ManualRewardRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.reward.ManualRewardResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getManualRewardMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service ActivityRewardFacade.
   */
  public static abstract class ActivityRewardFacadeImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return ActivityRewardFacadeGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service ActivityRewardFacade.
   */
  public static final class ActivityRewardFacadeStub
      extends io.grpc.stub.AbstractAsyncStub<ActivityRewardFacadeStub> {
    private ActivityRewardFacadeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ActivityRewardFacadeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ActivityRewardFacadeStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     * 运营手工发奖
     * </pre>
     */
    public void manualReward(com.kikitrade.activity.facade.reward.ManualRewardRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.reward.ManualRewardResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getManualRewardMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service ActivityRewardFacade.
   */
  public static final class ActivityRewardFacadeBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<ActivityRewardFacadeBlockingStub> {
    private ActivityRewardFacadeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ActivityRewardFacadeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ActivityRewardFacadeBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     * 运营手工发奖
     * </pre>
     */
    public com.kikitrade.activity.facade.reward.ManualRewardResponse manualReward(com.kikitrade.activity.facade.reward.ManualRewardRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getManualRewardMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service ActivityRewardFacade.
   */
  public static final class ActivityRewardFacadeFutureStub
      extends io.grpc.stub.AbstractFutureStub<ActivityRewardFacadeFutureStub> {
    private ActivityRewardFacadeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ActivityRewardFacadeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ActivityRewardFacadeFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     * 运营手工发奖
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.activity.facade.reward.ManualRewardResponse> manualReward(
        com.kikitrade.activity.facade.reward.ManualRewardRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getManualRewardMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_MANUAL_REWARD = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_MANUAL_REWARD:
          serviceImpl.manualReward((com.kikitrade.activity.facade.reward.ManualRewardRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.reward.ManualRewardResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getManualRewardMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.kikitrade.activity.facade.reward.ManualRewardRequest,
              com.kikitrade.activity.facade.reward.ManualRewardResponse>(
                service, METHODID_MANUAL_REWARD)))
        .build();
  }

  private static abstract class ActivityRewardFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    ActivityRewardFacadeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.kikitrade.activity.facade.reward.ActivityRewardFacadeOuterClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("ActivityRewardFacade");
    }
  }

  private static final class ActivityRewardFacadeFileDescriptorSupplier
      extends ActivityRewardFacadeBaseDescriptorSupplier {
    ActivityRewardFacadeFileDescriptorSupplier() {}
  }

  private static final class ActivityRewardFacadeMethodDescriptorSupplier
      extends ActivityRewardFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    ActivityRewardFacadeMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (ActivityRewardFacadeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new ActivityRewardFacadeFileDescriptorSupplier())
              .addMethod(getManualRewardMethod())
              .build();
        }
      }
    }
    return result;
  }
}
