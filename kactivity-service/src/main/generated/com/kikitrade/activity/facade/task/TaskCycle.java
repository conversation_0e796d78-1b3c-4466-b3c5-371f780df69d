// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: TaskFacade.proto

package com.kikitrade.activity.facade.task;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.task.TaskCycle}
 */
public enum TaskCycle
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>DAILY = 0;</code>
   */
  DAILY(0),
  /**
   * <code>WEEKLY = 1;</code>
   */
  WEEKLY(1),
  /**
   * <code>MONTHLY = 2;</code>
   */
  MONTHLY(2),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>DAILY = 0;</code>
   */
  public static final int DAILY_VALUE = 0;
  /**
   * <code>WEEKLY = 1;</code>
   */
  public static final int WEEKLY_VALUE = 1;
  /**
   * <code>MONTHLY = 2;</code>
   */
  public static final int MONTHLY_VALUE = 2;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static TaskCycle valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static TaskCycle forNumber(int value) {
    switch (value) {
      case 0: return DAILY;
      case 1: return WEEKLY;
      case 2: return MONTHLY;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<TaskCycle>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      TaskCycle> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<TaskCycle>() {
          public TaskCycle findValueByNumber(int number) {
            return TaskCycle.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.task.ActivityTaskFacadeOutClass.getDescriptor().getEnumTypes().get(4);
  }

  private static final TaskCycle[] VALUES = values();

  public static TaskCycle valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private TaskCycle(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.task.TaskCycle)
}

