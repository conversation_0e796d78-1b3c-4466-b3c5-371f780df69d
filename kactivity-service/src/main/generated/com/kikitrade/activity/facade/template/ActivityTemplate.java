// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityTemplate.proto

package com.kikitrade.activity.facade.template;

public final class ActivityTemplate {
  private ActivityTemplate() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_template_ActivityMaterial_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_template_ActivityMaterial_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_template_ActivityMaterialDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_template_ActivityMaterialDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_template_CommonResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_template_CommonResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\026ActivityTemplate.proto\022&com.kikitrade." +
      "activity.facade.template\032\037google/protobu" +
      "f/timestamp.proto\"/\n\020ActivityMaterial\022\014\n" +
      "\004code\030\001 \001(\t\022\r\n\005value\030\002 \001(\t\"\215\001\n\023ActivityM" +
      "aterialDTO\022\022\n\nactivityId\030\001 \001(\t\022\026\n\016templa" +
      "teStatus\030\002 \001(\t\022J\n\010material\030\003 \003(\01328.com.k" +
      "ikitrade.activity.facade.template.Activi" +
      "tyMaterial\"2\n\016CommonResponse\022\017\n\007success\030" +
      "\001 \001(\010\022\017\n\007message\030\002 \001(\t2\236\001\n\026ActivityTempl" +
      "ateFacade\022\203\001\n\014saveMaterial\022;.com.kikitra" +
      "de.activity.facade.template.ActivityMate" +
      "rialDTO\0326.com.kikitrade.activity.facade." +
      "template.CommonResponseB*\n&com.kikitrade" +
      ".activity.facade.templateP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.TimestampProto.getDescriptor(),
        });
    internal_static_com_kikitrade_activity_facade_template_ActivityMaterial_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_activity_facade_template_ActivityMaterial_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_template_ActivityMaterial_descriptor,
        new java.lang.String[] { "Code", "Value", });
    internal_static_com_kikitrade_activity_facade_template_ActivityMaterialDTO_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_activity_facade_template_ActivityMaterialDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_template_ActivityMaterialDTO_descriptor,
        new java.lang.String[] { "ActivityId", "TemplateStatus", "Material", });
    internal_static_com_kikitrade_activity_facade_template_CommonResponse_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_kikitrade_activity_facade_template_CommonResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_template_CommonResponse_descriptor,
        new java.lang.String[] { "Success", "Message", });
    com.google.protobuf.TimestampProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
