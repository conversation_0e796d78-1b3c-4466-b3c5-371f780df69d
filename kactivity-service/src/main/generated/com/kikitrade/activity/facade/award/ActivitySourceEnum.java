// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.award.ActivitySourceEnum}
 */
public enum ActivitySourceEnum
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>TASK = 0;</code>
   */
  TASK(0),
  /**
   * <code>OPERATE = 1;</code>
   */
  OPERATE(1),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>TASK = 0;</code>
   */
  public static final int TASK_VALUE = 0;
  /**
   * <code>OPERATE = 1;</code>
   */
  public static final int OPERATE_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static ActivitySourceEnum valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static ActivitySourceEnum forNumber(int value) {
    switch (value) {
      case 0: return TASK;
      case 1: return OPERATE;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<ActivitySourceEnum>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      ActivitySourceEnum> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<ActivitySourceEnum>() {
          public ActivitySourceEnum findValueByNumber(int number) {
            return ActivitySourceEnum.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.getDescriptor().getEnumTypes().get(0);
  }

  private static final ActivitySourceEnum[] VALUES = values();

  public static ActivitySourceEnum valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private ActivitySourceEnum(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.award.ActivitySourceEnum)
}

