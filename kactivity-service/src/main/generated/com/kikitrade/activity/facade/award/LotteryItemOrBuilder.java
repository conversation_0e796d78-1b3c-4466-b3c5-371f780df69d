// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface LotteryItemOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.LotteryItem)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *奖励名称
   * </pre>
   *
   * <code>string name = 1;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   *奖励名称
   * </pre>
   *
   * <code>string name = 1;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   *奖励类型
   * </pre>
   *
   * <code>string currency = 2;</code>
   * @return The currency.
   */
  java.lang.String getCurrency();
  /**
   * <pre>
   *奖励类型
   * </pre>
   *
   * <code>string currency = 2;</code>
   * @return The bytes for currency.
   */
  com.google.protobuf.ByteString
      getCurrencyBytes();

  /**
   * <pre>
   *投放奖励
   * </pre>
   *
   * <code>string amount = 3;</code>
   * @return The amount.
   */
  java.lang.String getAmount();
  /**
   * <pre>
   *投放奖励
   * </pre>
   *
   * <code>string amount = 3;</code>
   * @return The bytes for amount.
   */
  com.google.protobuf.ByteString
      getAmountBytes();

  /**
   * <pre>
   *投放数量
   * </pre>
   *
   * <code>int32 num = 4;</code>
   * @return The num.
   */
  int getNum();

  /**
   * <pre>
   *中奖概率
   * </pre>
   *
   * <code>string percent = 5;</code>
   * @return The percent.
   */
  java.lang.String getPercent();
  /**
   * <pre>
   *中奖概率
   * </pre>
   *
   * <code>string percent = 5;</code>
   * @return The bytes for percent.
   */
  com.google.protobuf.ByteString
      getPercentBytes();

  /**
   * <pre>
   *是否兜底
   * </pre>
   *
   * <code>bool isLow = 6;</code>
   * @return The isLow.
   */
  boolean getIsLow();

  /**
   * <pre>
   *POINT、TOKEN
   * </pre>
   *
   * <code>string awardType = 7;</code>
   * @return The awardType.
   */
  java.lang.String getAwardType();
  /**
   * <pre>
   *POINT、TOKEN
   * </pre>
   *
   * <code>string awardType = 7;</code>
   * @return The bytes for awardType.
   */
  com.google.protobuf.ByteString
      getAwardTypeBytes();

  /**
   * <pre>
   *剩余数量
   * </pre>
   *
   * <code>int32 remainNum = 8;</code>
   * @return The remainNum.
   */
  int getRemainNum();
}
