// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface ImportDataRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.ImportDataRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *活动id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   *活动id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string batchId = 2;</code>
   * @return The batchId.
   */
  java.lang.String getBatchId();
  /**
   * <code>string batchId = 2;</code>
   * @return The bytes for batchId.
   */
  com.google.protobuf.ByteString
      getBatchIdBytes();
}
