// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PropFacade.proto

package com.kikitrade.activity.facade.prop;

public interface PropsQueryReplyOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.prop.PropsQueryReply)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>repeated .com.kikitrade.activity.facade.prop.Prop props = 3;</code>
   */
  java.util.List<com.kikitrade.activity.facade.prop.Prop> 
      getPropsList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.prop.Prop props = 3;</code>
   */
  com.kikitrade.activity.facade.prop.Prop getProps(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.prop.Prop props = 3;</code>
   */
  int getPropsCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.prop.Prop props = 3;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.prop.PropOrBuilder> 
      getPropsOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.prop.Prop props = 3;</code>
   */
  com.kikitrade.activity.facade.prop.PropOrBuilder getPropsOrBuilder(
      int index);
}
