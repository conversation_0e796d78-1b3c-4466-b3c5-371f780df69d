/*
* Licensed to the Apache Software Foundation (ASF) under one or more
* contributor license agreements.  See the NOTICE file distributed with
* this work for additional information regarding copyright ownership.
* The ASF licenses this file to You under the Apache License, Version 2.0
* (the "License"); you may not use this file except in compliance with
* the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

    package com.kikitrade.activity.facade.template;

import org.apache.dubbo.common.stream.StreamObserver;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.PathResolver;
import org.apache.dubbo.rpc.RpcException;
import org.apache.dubbo.rpc.ServerService;
import org.apache.dubbo.rpc.TriRpcStatus;
import org.apache.dubbo.rpc.model.MethodDescriptor;
import org.apache.dubbo.rpc.model.ServiceDescriptor;
import org.apache.dubbo.rpc.model.StubMethodDescriptor;
import org.apache.dubbo.rpc.model.StubServiceDescriptor;
import org.apache.dubbo.rpc.stub.BiStreamMethodHandler;
import org.apache.dubbo.rpc.stub.ServerStreamMethodHandler;
import org.apache.dubbo.rpc.stub.StubInvocationUtil;
import org.apache.dubbo.rpc.stub.StubInvoker;
import org.apache.dubbo.rpc.stub.StubMethodHandler;
import org.apache.dubbo.rpc.stub.StubSuppliers;
import org.apache.dubbo.rpc.stub.UnaryStubMethodHandler;

import com.google.protobuf.Message;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.concurrent.CompletableFuture;

public final class DubboActivityTemplateFacadeTriple {

    public static final String SERVICE_NAME = ActivityTemplateFacade.SERVICE_NAME;

    private static final StubServiceDescriptor serviceDescriptor = new StubServiceDescriptor(SERVICE_NAME,ActivityTemplateFacade.class);

    static {
        org.apache.dubbo.rpc.protocol.tri.service.SchemaDescriptorRegistry.addSchemaDescriptor(SERVICE_NAME,ActivityTemplateFacadeOutClass.getDescriptor());
        StubSuppliers.addSupplier(SERVICE_NAME, DubboActivityTemplateFacadeTriple::newStub);
        StubSuppliers.addSupplier(ActivityTemplateFacade.JAVA_SERVICE_NAME,  DubboActivityTemplateFacadeTriple::newStub);
        StubSuppliers.addDescriptor(SERVICE_NAME, serviceDescriptor);
        StubSuppliers.addDescriptor(ActivityTemplateFacade.JAVA_SERVICE_NAME, serviceDescriptor);
    }

    @SuppressWarnings("all")
    public static ActivityTemplateFacade newStub(Invoker<?> invoker) {
        return new ActivityTemplateFacadeStub((Invoker<ActivityTemplateFacade>)invoker);
    }

    private static final StubMethodDescriptor saveMaterialMethod = new StubMethodDescriptor("saveMaterial",
    com.kikitrade.activity.facade.template.ActivityMaterialDTO.class, com.kikitrade.activity.facade.template.CommonResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.template.ActivityMaterialDTO::parseFrom,
    com.kikitrade.activity.facade.template.CommonResponse::parseFrom);

    private static final StubMethodDescriptor saveMaterialAsyncMethod = new StubMethodDescriptor("saveMaterial",
    com.kikitrade.activity.facade.template.ActivityMaterialDTO.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.template.ActivityMaterialDTO::parseFrom,
    com.kikitrade.activity.facade.template.CommonResponse::parseFrom);

    private static final StubMethodDescriptor saveMaterialProxyAsyncMethod = new StubMethodDescriptor("saveMaterialAsync",
    com.kikitrade.activity.facade.template.ActivityMaterialDTO.class, com.kikitrade.activity.facade.template.CommonResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.template.ActivityMaterialDTO::parseFrom,
    com.kikitrade.activity.facade.template.CommonResponse::parseFrom);





    public static class ActivityTemplateFacadeStub implements ActivityTemplateFacade{
        private final Invoker<ActivityTemplateFacade> invoker;

        public ActivityTemplateFacadeStub(Invoker<ActivityTemplateFacade> invoker) {
            this.invoker = invoker;
        }

        @Override
        public com.kikitrade.activity.facade.template.CommonResponse saveMaterial(com.kikitrade.activity.facade.template.ActivityMaterialDTO request){
            return StubInvocationUtil.unaryCall(invoker, saveMaterialMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.template.CommonResponse> saveMaterialAsync(com.kikitrade.activity.facade.template.ActivityMaterialDTO request){
            return StubInvocationUtil.unaryCall(invoker, saveMaterialAsyncMethod, request);
        }

        @Override
        public void saveMaterial(com.kikitrade.activity.facade.template.ActivityMaterialDTO request, StreamObserver<com.kikitrade.activity.facade.template.CommonResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, saveMaterialMethod , request, responseObserver);
        }



    }

    public static abstract class ActivityTemplateFacadeImplBase implements ActivityTemplateFacade, ServerService<ActivityTemplateFacade> {

        private <T, R> BiConsumer<T, StreamObserver<R>> syncToAsync(java.util.function.Function<T, R> syncFun) {
            return new BiConsumer<T, StreamObserver<R>>() {
                @Override
                public void accept(T t, StreamObserver<R> observer) {
                    try {
                        R ret = syncFun.apply(t);
                        observer.onNext(ret);
                        observer.onCompleted();
                    } catch (Throwable e) {
                        observer.onError(e);
                    }
                }
            };
        }

        @Override
        public final Invoker<ActivityTemplateFacade> getInvoker(URL url) {
            PathResolver pathResolver = url.getOrDefaultFrameworkModel()
            .getExtensionLoader(PathResolver.class)
            .getDefaultExtension();
            Map<String,StubMethodHandler<?, ?>> handlers = new HashMap<>();

            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/saveMaterial" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/saveMaterialAsync" );

            BiConsumer<com.kikitrade.activity.facade.template.ActivityMaterialDTO, StreamObserver<com.kikitrade.activity.facade.template.CommonResponse>> saveMaterialFunc = this::saveMaterial;
            handlers.put(saveMaterialMethod.getMethodName(), new UnaryStubMethodHandler<>(saveMaterialFunc));
            BiConsumer<com.kikitrade.activity.facade.template.ActivityMaterialDTO, StreamObserver<com.kikitrade.activity.facade.template.CommonResponse>> saveMaterialAsyncFunc = syncToAsync(this::saveMaterial);
            handlers.put(saveMaterialProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(saveMaterialAsyncFunc));




            return new StubInvoker<>(this, url, ActivityTemplateFacade.class, handlers);
        }


        @Override
        public com.kikitrade.activity.facade.template.CommonResponse saveMaterial(com.kikitrade.activity.facade.template.ActivityMaterialDTO request){
            throw unimplementedMethodException(saveMaterialMethod);
        }





        @Override
        public final ServiceDescriptor getServiceDescriptor() {
            return serviceDescriptor;
        }
        private RpcException unimplementedMethodException(StubMethodDescriptor methodDescriptor) {
            return TriRpcStatus.UNIMPLEMENTED.withDescription(String.format("Method %s is unimplemented",
                "/" + serviceDescriptor.getInterfaceName() + "/" + methodDescriptor.getMethodName())).asException();
        }
    }

}
