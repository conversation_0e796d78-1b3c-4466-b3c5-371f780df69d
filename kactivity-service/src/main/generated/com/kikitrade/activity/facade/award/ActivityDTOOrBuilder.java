// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface ActivityDTOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.ActivityDTO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *activityId 为空时新增，不为空时修改
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   *activityId 为空时新增，不为空时修改
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string activityName = 2;</code>
   * @return The activityName.
   */
  java.lang.String getActivityName();
  /**
   * <code>string activityName = 2;</code>
   * @return The bytes for activityName.
   */
  com.google.protobuf.ByteString
      getActivityNameBytes();

  /**
   * <code>.com.kikitrade.activity.facade.award.ActivityTypeEnum type = 3;</code>
   * @return The enum numeric value on the wire for type.
   */
  int getTypeValue();
  /**
   * <code>.com.kikitrade.activity.facade.award.ActivityTypeEnum type = 3;</code>
   * @return The type.
   */
  com.kikitrade.activity.facade.award.ActivityTypeEnum getType();

  /**
   * <code>string startTime = 4;</code>
   * @return The startTime.
   */
  java.lang.String getStartTime();
  /**
   * <code>string startTime = 4;</code>
   * @return The bytes for startTime.
   */
  com.google.protobuf.ByteString
      getStartTimeBytes();

  /**
   * <pre>
   *结束时间大于开始时间
   * </pre>
   *
   * <code>string endTime = 5;</code>
   * @return The endTime.
   */
  java.lang.String getEndTime();
  /**
   * <pre>
   *结束时间大于开始时间
   * </pre>
   *
   * <code>string endTime = 5;</code>
   * @return The bytes for endTime.
   */
  com.google.protobuf.ByteString
      getEndTimeBytes();

  /**
   * <code>string remark = 6;</code>
   * @return The remark.
   */
  java.lang.String getRemark();
  /**
   * <code>string remark = 6;</code>
   * @return The bytes for remark.
   */
  com.google.protobuf.ByteString
      getRemarkBytes();

  /**
   * <code>.com.kikitrade.activity.facade.award.ActivityStatusEnum status = 7;</code>
   * @return The enum numeric value on the wire for status.
   */
  int getStatusValue();
  /**
   * <code>.com.kikitrade.activity.facade.award.ActivityStatusEnum status = 7;</code>
   * @return The status.
   */
  com.kikitrade.activity.facade.award.ActivityStatusEnum getStatus();

  /**
   * <code>string activityArea = 8;</code>
   * @return The activityArea.
   */
  java.lang.String getActivityArea();
  /**
   * <code>string activityArea = 8;</code>
   * @return The bytes for activityArea.
   */
  com.google.protobuf.ByteString
      getActivityAreaBytes();

  /**
   * <code>bool autoCreateBatch = 9;</code>
   * @return The autoCreateBatch.
   */
  boolean getAutoCreateBatch();

  /**
   * <code>.com.kikitrade.activity.facade.award.BatchFrequency batchFrequency = 10;</code>
   * @return The enum numeric value on the wire for batchFrequency.
   */
  int getBatchFrequencyValue();
  /**
   * <code>.com.kikitrade.activity.facade.award.BatchFrequency batchFrequency = 10;</code>
   * @return The batchFrequency.
   */
  com.kikitrade.activity.facade.award.BatchFrequency getBatchFrequency();

  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
   */
  java.util.List<com.kikitrade.activity.facade.award.RewardRule> 
      getRewardRuleList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
   */
  com.kikitrade.activity.facade.award.RewardRule getRewardRule(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
   */
  int getRewardRuleCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.award.RewardRuleOrBuilder> 
      getRewardRuleOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 11;</code>
   */
  com.kikitrade.activity.facade.award.RewardRuleOrBuilder getRewardRuleOrBuilder(
      int index);

  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
   */
  java.util.List<com.kikitrade.activity.facade.award.ConditionRule> 
      getConditionRuleList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
   */
  com.kikitrade.activity.facade.award.ConditionRule getConditionRule(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
   */
  int getConditionRuleCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.award.ConditionRuleOrBuilder> 
      getConditionRuleOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.ConditionRule conditionRule = 12;</code>
   */
  com.kikitrade.activity.facade.award.ConditionRuleOrBuilder getConditionRuleOrBuilder(
      int index);

  /**
   * <code>bool autoApprove = 13;</code>
   * @return The autoApprove.
   */
  boolean getAutoApprove();

  /**
   * <code>string taskId = 14;</code>
   * @return The taskId.
   */
  java.lang.String getTaskId();
  /**
   * <code>string taskId = 14;</code>
   * @return The bytes for taskId.
   */
  com.google.protobuf.ByteString
      getTaskIdBytes();

  /**
   * <code>string conditionCode = 15;</code>
   * @return The conditionCode.
   */
  java.lang.String getConditionCode();
  /**
   * <code>string conditionCode = 15;</code>
   * @return The bytes for conditionCode.
   */
  com.google.protobuf.ByteString
      getConditionCodeBytes();

  /**
   * <pre>
   * 子活动类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.ActivitySubTypeEnum subType = 16;</code>
   * @return The enum numeric value on the wire for subType.
   */
  int getSubTypeValue();
  /**
   * <pre>
   * 子活动类型
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.ActivitySubTypeEnum subType = 16;</code>
   * @return The subType.
   */
  com.kikitrade.activity.facade.award.ActivitySubTypeEnum getSubType();
}
