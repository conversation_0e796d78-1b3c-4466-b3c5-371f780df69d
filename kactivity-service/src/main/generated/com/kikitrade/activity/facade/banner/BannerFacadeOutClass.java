// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Banner.proto

package com.kikitrade.activity.facade.banner;

public final class BannerFacadeOutClass {
  private BannerFacadeOutClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_banner_BannerDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_banner_BannerDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_activity_facade_banner_BannerSaveResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_activity_facade_banner_BannerSaveResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014Banner.proto\022$com.kikitrade.activity.f" +
      "acade.banner\"\335\002\n\tBannerDTO\022\n\n\002id\030\001 \001(\t\022\r" +
      "\n\005image\030\002 \001(\t\022\014\n\004name\030\003 \001(\t\022\014\n\004desc\030\004 \001(" +
      "\t\022\013\n\003url\030\005 \001(\t\022B\n\006source\030\006 \001(\01622.com.kik" +
      "itrade.activity.facade.banner.BannerSour" +
      "ce\022\020\n\010sourceId\030\007 \001(\t\022\r\n\005order\030\010 \001(\005\022D\n\007c" +
      "hannel\030\t \001(\01623.com.kikitrade.activity.fa" +
      "cade.banner.BannerChannel\022\r\n\005local\030\n \001(\t" +
      "\022\016\n\006saasId\030\013 \001(\t\022B\n\006status\030\014 \001(\01622.com.k" +
      "ikitrade.activity.facade.banner.CommonSt" +
      "atus\"B\n\022BannerSaveResponse\022\017\n\007success\030\001 " +
      "\001(\010\022\017\n\007message\030\002 \001(\t\022\n\n\002id\030\003 \001(\t*#\n\014Bann" +
      "erSource\022\010\n\004TASK\020\000\022\t\n\005GOODS\020\001*(\n\rBannerC" +
      "hannel\022\007\n\003APP\020\000\022\006\n\002H5\020\001\022\006\n\002PC\020\002*\'\n\014Commo" +
      "nStatus\022\n\n\006ACTIVE\020\000\022\013\n\007DISABLE\020\0012\201\001\n\014Ban" +
      "nerFacade\022q\n\004save\022/.com.kikitrade.activi" +
      "ty.facade.banner.BannerDTO\0328.com.kikitra" +
      "de.activity.facade.banner.BannerSaveResp" +
      "onseB>\n$com.kikitrade.activity.facade.ba" +
      "nnerB\024BannerFacadeOutClassP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_kikitrade_activity_facade_banner_BannerDTO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_activity_facade_banner_BannerDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_banner_BannerDTO_descriptor,
        new java.lang.String[] { "Id", "Image", "Name", "Desc", "Url", "Source", "SourceId", "Order", "Channel", "Local", "SaasId", "Status", });
    internal_static_com_kikitrade_activity_facade_banner_BannerSaveResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_activity_facade_banner_BannerSaveResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_activity_facade_banner_BannerSaveResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "Id", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
