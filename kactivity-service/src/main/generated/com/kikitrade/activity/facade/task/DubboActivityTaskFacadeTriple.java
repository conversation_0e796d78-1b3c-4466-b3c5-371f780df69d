/*
* Licensed to the Apache Software Foundation (ASF) under one or more
* contributor license agreements.  See the NOTICE file distributed with
* this work for additional information regarding copyright ownership.
* The ASF licenses this file to You under the Apache License, Version 2.0
* (the "License"); you may not use this file except in compliance with
* the License.  You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

    package com.kikitrade.activity.facade.task;

import org.apache.dubbo.common.stream.StreamObserver;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.PathResolver;
import org.apache.dubbo.rpc.RpcException;
import org.apache.dubbo.rpc.ServerService;
import org.apache.dubbo.rpc.TriRpcStatus;
import org.apache.dubbo.rpc.model.MethodDescriptor;
import org.apache.dubbo.rpc.model.ServiceDescriptor;
import org.apache.dubbo.rpc.model.StubMethodDescriptor;
import org.apache.dubbo.rpc.model.StubServiceDescriptor;
import org.apache.dubbo.rpc.stub.BiStreamMethodHandler;
import org.apache.dubbo.rpc.stub.ServerStreamMethodHandler;
import org.apache.dubbo.rpc.stub.StubInvocationUtil;
import org.apache.dubbo.rpc.stub.StubInvoker;
import org.apache.dubbo.rpc.stub.StubMethodHandler;
import org.apache.dubbo.rpc.stub.StubSuppliers;
import org.apache.dubbo.rpc.stub.UnaryStubMethodHandler;

import com.google.protobuf.Message;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.concurrent.CompletableFuture;

public final class DubboActivityTaskFacadeTriple {

    public static final String SERVICE_NAME = ActivityTaskFacade.SERVICE_NAME;

    private static final StubServiceDescriptor serviceDescriptor = new StubServiceDescriptor(SERVICE_NAME,ActivityTaskFacade.class);

    static {
        org.apache.dubbo.rpc.protocol.tri.service.SchemaDescriptorRegistry.addSchemaDescriptor(SERVICE_NAME,ActivityTaskFacadeOutClass.getDescriptor());
        StubSuppliers.addSupplier(SERVICE_NAME, DubboActivityTaskFacadeTriple::newStub);
        StubSuppliers.addSupplier(ActivityTaskFacade.JAVA_SERVICE_NAME,  DubboActivityTaskFacadeTriple::newStub);
        StubSuppliers.addDescriptor(SERVICE_NAME, serviceDescriptor);
        StubSuppliers.addDescriptor(ActivityTaskFacade.JAVA_SERVICE_NAME, serviceDescriptor);
    }

    @SuppressWarnings("all")
    public static ActivityTaskFacade newStub(Invoker<?> invoker) {
        return new ActivityTaskFacadeStub((Invoker<ActivityTaskFacade>)invoker);
    }

    private static final StubMethodDescriptor saveMethod = new StubMethodDescriptor("save",
    com.kikitrade.activity.facade.task.TaskDTO.class, com.kikitrade.activity.facade.task.CommonResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.task.TaskDTO::parseFrom,
    com.kikitrade.activity.facade.task.CommonResponse::parseFrom);

    private static final StubMethodDescriptor saveAsyncMethod = new StubMethodDescriptor("save",
    com.kikitrade.activity.facade.task.TaskDTO.class, java.util.concurrent.CompletableFuture.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.task.TaskDTO::parseFrom,
    com.kikitrade.activity.facade.task.CommonResponse::parseFrom);

    private static final StubMethodDescriptor saveProxyAsyncMethod = new StubMethodDescriptor("saveAsync",
    com.kikitrade.activity.facade.task.TaskDTO.class, com.kikitrade.activity.facade.task.CommonResponse.class, serviceDescriptor, MethodDescriptor.RpcType.UNARY,
    obj -> ((Message) obj).toByteArray(), obj -> ((Message) obj).toByteArray(), com.kikitrade.activity.facade.task.TaskDTO::parseFrom,
    com.kikitrade.activity.facade.task.CommonResponse::parseFrom);





    public static class ActivityTaskFacadeStub implements ActivityTaskFacade{
        private final Invoker<ActivityTaskFacade> invoker;

        public ActivityTaskFacadeStub(Invoker<ActivityTaskFacade> invoker) {
            this.invoker = invoker;
        }

        @Override
        public com.kikitrade.activity.facade.task.CommonResponse save(com.kikitrade.activity.facade.task.TaskDTO request){
            return StubInvocationUtil.unaryCall(invoker, saveMethod, request);
        }

        public CompletableFuture<com.kikitrade.activity.facade.task.CommonResponse> saveAsync(com.kikitrade.activity.facade.task.TaskDTO request){
            return StubInvocationUtil.unaryCall(invoker, saveAsyncMethod, request);
        }

        @Override
        public void save(com.kikitrade.activity.facade.task.TaskDTO request, StreamObserver<com.kikitrade.activity.facade.task.CommonResponse> responseObserver){
            StubInvocationUtil.unaryCall(invoker, saveMethod , request, responseObserver);
        }



    }

    public static abstract class ActivityTaskFacadeImplBase implements ActivityTaskFacade, ServerService<ActivityTaskFacade> {

        private <T, R> BiConsumer<T, StreamObserver<R>> syncToAsync(java.util.function.Function<T, R> syncFun) {
            return new BiConsumer<T, StreamObserver<R>>() {
                @Override
                public void accept(T t, StreamObserver<R> observer) {
                    try {
                        R ret = syncFun.apply(t);
                        observer.onNext(ret);
                        observer.onCompleted();
                    } catch (Throwable e) {
                        observer.onError(e);
                    }
                }
            };
        }

        @Override
        public final Invoker<ActivityTaskFacade> getInvoker(URL url) {
            PathResolver pathResolver = url.getOrDefaultFrameworkModel()
            .getExtensionLoader(PathResolver.class)
            .getDefaultExtension();
            Map<String,StubMethodHandler<?, ?>> handlers = new HashMap<>();

            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/save" );
            pathResolver.addNativeStub( "/" + SERVICE_NAME + "/saveAsync" );

            BiConsumer<com.kikitrade.activity.facade.task.TaskDTO, StreamObserver<com.kikitrade.activity.facade.task.CommonResponse>> saveFunc = this::save;
            handlers.put(saveMethod.getMethodName(), new UnaryStubMethodHandler<>(saveFunc));
            BiConsumer<com.kikitrade.activity.facade.task.TaskDTO, StreamObserver<com.kikitrade.activity.facade.task.CommonResponse>> saveAsyncFunc = syncToAsync(this::save);
            handlers.put(saveProxyAsyncMethod.getMethodName(), new UnaryStubMethodHandler<>(saveAsyncFunc));




            return new StubInvoker<>(this, url, ActivityTaskFacade.class, handlers);
        }


        @Override
        public com.kikitrade.activity.facade.task.CommonResponse save(com.kikitrade.activity.facade.task.TaskDTO request){
            throw unimplementedMethodException(saveMethod);
        }





        @Override
        public final ServiceDescriptor getServiceDescriptor() {
            return serviceDescriptor;
        }
        private RpcException unimplementedMethodException(StubMethodDescriptor methodDescriptor) {
            return TriRpcStatus.UNIMPLEMENTED.withDescription(String.format("Method %s is unimplemented",
                "/" + serviceDescriptor.getInterfaceName() + "/" + methodDescriptor.getMethodName())).asException();
        }
    }

}
