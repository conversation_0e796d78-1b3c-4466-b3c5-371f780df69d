// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * Protobuf type {@code com.kikitrade.activity.facade.award.ActivityBatch}
 */
public final class ActivityBatch extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.activity.facade.award.ActivityBatch)
    ActivityBatchOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ActivityBatch.newBuilder() to construct.
  private ActivityBatch(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ActivityBatch() {
    id_ = "";
    name_ = "";
    activityId_ = "";
    activityName_ = "";
    rewardType_ = "";
    amount_ = "";
    currency_ = "";
    remark_ = "";
    scheduledTime_ = "";
    saasId_ = "";
    prizeAmount_ = "";
    winners_ = "";
    amended_ = "";
    modified_ = "";
    status_ = 0;
    ossUrl_ = "";
    generateTime_ = "";
    sourceOssUrl_ = "";
    rewardRule_ = java.util.Collections.emptyList();
    activityType_ = "";
    source_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ActivityBatch();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ActivityBatch_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ActivityBatch_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.activity.facade.award.ActivityBatch.class, com.kikitrade.activity.facade.award.ActivityBatch.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object name_ = "";
  /**
   * <code>string name = 2;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACTIVITYID_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object activityId_ = "";
  /**
   * <code>string activityId = 3;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public java.lang.String getActivityId() {
    java.lang.Object ref = activityId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      activityId_ = s;
      return s;
    }
  }
  /**
   * <code>string activityId = 3;</code>
   * @return The bytes for activityId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getActivityIdBytes() {
    java.lang.Object ref = activityId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      activityId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACTIVITYNAME_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object activityName_ = "";
  /**
   * <code>string activityName = 4;</code>
   * @return The activityName.
   */
  @java.lang.Override
  public java.lang.String getActivityName() {
    java.lang.Object ref = activityName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      activityName_ = s;
      return s;
    }
  }
  /**
   * <code>string activityName = 4;</code>
   * @return The bytes for activityName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getActivityNameBytes() {
    java.lang.Object ref = activityName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      activityName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REWARDTYPE_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object rewardType_ = "";
  /**
   * <pre>
   *数字货币、道具
   * </pre>
   *
   * <code>string rewardType = 5;</code>
   * @return The rewardType.
   */
  @java.lang.Override
  public java.lang.String getRewardType() {
    java.lang.Object ref = rewardType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      rewardType_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *数字货币、道具
   * </pre>
   *
   * <code>string rewardType = 5;</code>
   * @return The bytes for rewardType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardTypeBytes() {
    java.lang.Object ref = rewardType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      rewardType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AMOUNT_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object amount_ = "";
  /**
   * <pre>
   *货币金额
   * </pre>
   *
   * <code>string amount = 6;</code>
   * @return The amount.
   */
  @java.lang.Override
  public java.lang.String getAmount() {
    java.lang.Object ref = amount_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      amount_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *货币金额
   * </pre>
   *
   * <code>string amount = 6;</code>
   * @return The bytes for amount.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAmountBytes() {
    java.lang.Object ref = amount_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      amount_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CURRENCY_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object currency_ = "";
  /**
   * <pre>
   *货币单位
   * </pre>
   *
   * <code>string currency = 7;</code>
   * @return The currency.
   */
  @java.lang.Override
  public java.lang.String getCurrency() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      currency_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *货币单位
   * </pre>
   *
   * <code>string currency = 7;</code>
   * @return The bytes for currency.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCurrencyBytes() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      currency_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REMARK_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object remark_ = "";
  /**
   * <pre>
   *批次描述
   * </pre>
   *
   * <code>string remark = 8;</code>
   * @return The remark.
   */
  @java.lang.Override
  public java.lang.String getRemark() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      remark_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *批次描述
   * </pre>
   *
   * <code>string remark = 8;</code>
   * @return The bytes for remark.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRemarkBytes() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      remark_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SCHEDULED_FIELD_NUMBER = 9;
  private boolean scheduled_ = false;
  /**
   * <pre>
   *是否立即发奖
   * </pre>
   *
   * <code>bool scheduled = 9;</code>
   * @return The scheduled.
   */
  @java.lang.Override
  public boolean getScheduled() {
    return scheduled_;
  }

  public static final int SCHEDULEDTIME_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object scheduledTime_ = "";
  /**
   * <pre>
   *发奖时间
   * </pre>
   *
   * <code>string scheduledTime = 10;</code>
   * @return The scheduledTime.
   */
  @java.lang.Override
  public java.lang.String getScheduledTime() {
    java.lang.Object ref = scheduledTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      scheduledTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *发奖时间
   * </pre>
   *
   * <code>string scheduledTime = 10;</code>
   * @return The bytes for scheduledTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getScheduledTimeBytes() {
    java.lang.Object ref = scheduledTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      scheduledTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SAASID_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private volatile java.lang.Object saasId_ = "";
  /**
   * <pre>
   *saasId
   * </pre>
   *
   * <code>string saasId = 11;</code>
   * @return The saasId.
   */
  @java.lang.Override
  public java.lang.String getSaasId() {
    java.lang.Object ref = saasId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      saasId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *saasId
   * </pre>
   *
   * <code>string saasId = 11;</code>
   * @return The bytes for saasId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSaasIdBytes() {
    java.lang.Object ref = saasId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      saasId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PRIZEAMOUNT_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private volatile java.lang.Object prizeAmount_ = "";
  /**
   * <code>string prizeAmount = 12;</code>
   * @return The prizeAmount.
   */
  @java.lang.Override
  public java.lang.String getPrizeAmount() {
    java.lang.Object ref = prizeAmount_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      prizeAmount_ = s;
      return s;
    }
  }
  /**
   * <code>string prizeAmount = 12;</code>
   * @return The bytes for prizeAmount.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPrizeAmountBytes() {
    java.lang.Object ref = prizeAmount_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      prizeAmount_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int WINNERS_FIELD_NUMBER = 13;
  @SuppressWarnings("serial")
  private volatile java.lang.Object winners_ = "";
  /**
   * <code>string winners = 13;</code>
   * @return The winners.
   */
  @java.lang.Override
  public java.lang.String getWinners() {
    java.lang.Object ref = winners_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      winners_ = s;
      return s;
    }
  }
  /**
   * <code>string winners = 13;</code>
   * @return The bytes for winners.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getWinnersBytes() {
    java.lang.Object ref = winners_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      winners_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AMENDED_FIELD_NUMBER = 14;
  @SuppressWarnings("serial")
  private volatile java.lang.Object amended_ = "";
  /**
   * <code>string amended = 14;</code>
   * @return The amended.
   */
  @java.lang.Override
  public java.lang.String getAmended() {
    java.lang.Object ref = amended_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      amended_ = s;
      return s;
    }
  }
  /**
   * <code>string amended = 14;</code>
   * @return The bytes for amended.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAmendedBytes() {
    java.lang.Object ref = amended_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      amended_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MODIFIED_FIELD_NUMBER = 15;
  @SuppressWarnings("serial")
  private volatile java.lang.Object modified_ = "";
  /**
   * <code>string modified = 15;</code>
   * @return The modified.
   */
  @java.lang.Override
  public java.lang.String getModified() {
    java.lang.Object ref = modified_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      modified_ = s;
      return s;
    }
  }
  /**
   * <code>string modified = 15;</code>
   * @return The bytes for modified.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getModifiedBytes() {
    java.lang.Object ref = modified_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      modified_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATUS_FIELD_NUMBER = 16;
  private int status_ = 0;
  /**
   * <code>.com.kikitrade.activity.facade.award.BatchStatusEnum status = 16;</code>
   * @return The enum numeric value on the wire for status.
   */
  @java.lang.Override public int getStatusValue() {
    return status_;
  }
  /**
   * <code>.com.kikitrade.activity.facade.award.BatchStatusEnum status = 16;</code>
   * @return The status.
   */
  @java.lang.Override public com.kikitrade.activity.facade.award.BatchStatusEnum getStatus() {
    com.kikitrade.activity.facade.award.BatchStatusEnum result = com.kikitrade.activity.facade.award.BatchStatusEnum.forNumber(status_);
    return result == null ? com.kikitrade.activity.facade.award.BatchStatusEnum.UNRECOGNIZED : result;
  }

  public static final int OSSURL_FIELD_NUMBER = 17;
  @SuppressWarnings("serial")
  private volatile java.lang.Object ossUrl_ = "";
  /**
   * <pre>
   *下载地址
   * </pre>
   *
   * <code>string ossUrl = 17;</code>
   * @return The ossUrl.
   */
  @java.lang.Override
  public java.lang.String getOssUrl() {
    java.lang.Object ref = ossUrl_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      ossUrl_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *下载地址
   * </pre>
   *
   * <code>string ossUrl = 17;</code>
   * @return The bytes for ossUrl.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOssUrlBytes() {
    java.lang.Object ref = ossUrl_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      ossUrl_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int GENERATETIME_FIELD_NUMBER = 18;
  @SuppressWarnings("serial")
  private volatile java.lang.Object generateTime_ = "";
  /**
   * <code>string generateTime = 18;</code>
   * @return The generateTime.
   */
  @java.lang.Override
  public java.lang.String getGenerateTime() {
    java.lang.Object ref = generateTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      generateTime_ = s;
      return s;
    }
  }
  /**
   * <code>string generateTime = 18;</code>
   * @return The bytes for generateTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getGenerateTimeBytes() {
    java.lang.Object ref = generateTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      generateTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SOURCEOSSURL_FIELD_NUMBER = 19;
  @SuppressWarnings("serial")
  private volatile java.lang.Object sourceOssUrl_ = "";
  /**
   * <code>string sourceOssUrl = 19;</code>
   * @return The sourceOssUrl.
   */
  @java.lang.Override
  public java.lang.String getSourceOssUrl() {
    java.lang.Object ref = sourceOssUrl_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      sourceOssUrl_ = s;
      return s;
    }
  }
  /**
   * <code>string sourceOssUrl = 19;</code>
   * @return The bytes for sourceOssUrl.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSourceOssUrlBytes() {
    java.lang.Object ref = sourceOssUrl_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      sourceOssUrl_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REWARDRULE_FIELD_NUMBER = 20;
  @SuppressWarnings("serial")
  private java.util.List<com.kikitrade.activity.facade.award.RewardRule> rewardRule_;
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.activity.facade.award.RewardRule> getRewardRuleList() {
    return rewardRule_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.activity.facade.award.RewardRuleOrBuilder> 
      getRewardRuleOrBuilderList() {
    return rewardRule_;
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
   */
  @java.lang.Override
  public int getRewardRuleCount() {
    return rewardRule_.size();
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.RewardRule getRewardRule(int index) {
    return rewardRule_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
   */
  @java.lang.Override
  public com.kikitrade.activity.facade.award.RewardRuleOrBuilder getRewardRuleOrBuilder(
      int index) {
    return rewardRule_.get(index);
  }

  public static final int ACTIVITYTYPE_FIELD_NUMBER = 21;
  @SuppressWarnings("serial")
  private volatile java.lang.Object activityType_ = "";
  /**
   * <code>string activityType = 21;</code>
   * @return The activityType.
   */
  @java.lang.Override
  public java.lang.String getActivityType() {
    java.lang.Object ref = activityType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      activityType_ = s;
      return s;
    }
  }
  /**
   * <code>string activityType = 21;</code>
   * @return The bytes for activityType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getActivityTypeBytes() {
    java.lang.Object ref = activityType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      activityType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SOURCE_FIELD_NUMBER = 22;
  private int source_ = 0;
  /**
   * <pre>
   *TASK,OPERATE
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.ActivitySourceEnum source = 22;</code>
   * @return The enum numeric value on the wire for source.
   */
  @java.lang.Override public int getSourceValue() {
    return source_;
  }
  /**
   * <pre>
   *TASK,OPERATE
   * </pre>
   *
   * <code>.com.kikitrade.activity.facade.award.ActivitySourceEnum source = 22;</code>
   * @return The source.
   */
  @java.lang.Override public com.kikitrade.activity.facade.award.ActivitySourceEnum getSource() {
    com.kikitrade.activity.facade.award.ActivitySourceEnum result = com.kikitrade.activity.facade.award.ActivitySourceEnum.forNumber(source_);
    return result == null ? com.kikitrade.activity.facade.award.ActivitySourceEnum.UNRECOGNIZED : result;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, activityId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityName_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, activityName_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(rewardType_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, rewardType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amount_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, amount_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(currency_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, currency_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(remark_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, remark_);
    }
    if (scheduled_ != false) {
      output.writeBool(9, scheduled_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(scheduledTime_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, scheduledTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(saasId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, saasId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(prizeAmount_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 12, prizeAmount_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(winners_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 13, winners_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amended_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 14, amended_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(modified_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 15, modified_);
    }
    if (status_ != com.kikitrade.activity.facade.award.BatchStatusEnum.ALL.getNumber()) {
      output.writeEnum(16, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(ossUrl_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 17, ossUrl_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(generateTime_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 18, generateTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sourceOssUrl_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 19, sourceOssUrl_);
    }
    for (int i = 0; i < rewardRule_.size(); i++) {
      output.writeMessage(20, rewardRule_.get(i));
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityType_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 21, activityType_);
    }
    if (source_ != com.kikitrade.activity.facade.award.ActivitySourceEnum.TASK.getNumber()) {
      output.writeEnum(22, source_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, activityId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityName_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, activityName_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(rewardType_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, rewardType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amount_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, amount_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(currency_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, currency_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(remark_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, remark_);
    }
    if (scheduled_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(9, scheduled_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(scheduledTime_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, scheduledTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(saasId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, saasId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(prizeAmount_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, prizeAmount_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(winners_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, winners_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amended_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, amended_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(modified_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, modified_);
    }
    if (status_ != com.kikitrade.activity.facade.award.BatchStatusEnum.ALL.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(16, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(ossUrl_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, ossUrl_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(generateTime_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(18, generateTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sourceOssUrl_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(19, sourceOssUrl_);
    }
    for (int i = 0; i < rewardRule_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(20, rewardRule_.get(i));
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(activityType_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(21, activityType_);
    }
    if (source_ != com.kikitrade.activity.facade.award.ActivitySourceEnum.TASK.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(22, source_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.activity.facade.award.ActivityBatch)) {
      return super.equals(obj);
    }
    com.kikitrade.activity.facade.award.ActivityBatch other = (com.kikitrade.activity.facade.award.ActivityBatch) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (!getActivityId()
        .equals(other.getActivityId())) return false;
    if (!getActivityName()
        .equals(other.getActivityName())) return false;
    if (!getRewardType()
        .equals(other.getRewardType())) return false;
    if (!getAmount()
        .equals(other.getAmount())) return false;
    if (!getCurrency()
        .equals(other.getCurrency())) return false;
    if (!getRemark()
        .equals(other.getRemark())) return false;
    if (getScheduled()
        != other.getScheduled()) return false;
    if (!getScheduledTime()
        .equals(other.getScheduledTime())) return false;
    if (!getSaasId()
        .equals(other.getSaasId())) return false;
    if (!getPrizeAmount()
        .equals(other.getPrizeAmount())) return false;
    if (!getWinners()
        .equals(other.getWinners())) return false;
    if (!getAmended()
        .equals(other.getAmended())) return false;
    if (!getModified()
        .equals(other.getModified())) return false;
    if (status_ != other.status_) return false;
    if (!getOssUrl()
        .equals(other.getOssUrl())) return false;
    if (!getGenerateTime()
        .equals(other.getGenerateTime())) return false;
    if (!getSourceOssUrl()
        .equals(other.getSourceOssUrl())) return false;
    if (!getRewardRuleList()
        .equals(other.getRewardRuleList())) return false;
    if (!getActivityType()
        .equals(other.getActivityType())) return false;
    if (source_ != other.source_) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
    hash = (53 * hash) + getActivityId().hashCode();
    hash = (37 * hash) + ACTIVITYNAME_FIELD_NUMBER;
    hash = (53 * hash) + getActivityName().hashCode();
    hash = (37 * hash) + REWARDTYPE_FIELD_NUMBER;
    hash = (53 * hash) + getRewardType().hashCode();
    hash = (37 * hash) + AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getAmount().hashCode();
    hash = (37 * hash) + CURRENCY_FIELD_NUMBER;
    hash = (53 * hash) + getCurrency().hashCode();
    hash = (37 * hash) + REMARK_FIELD_NUMBER;
    hash = (53 * hash) + getRemark().hashCode();
    hash = (37 * hash) + SCHEDULED_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getScheduled());
    hash = (37 * hash) + SCHEDULEDTIME_FIELD_NUMBER;
    hash = (53 * hash) + getScheduledTime().hashCode();
    hash = (37 * hash) + SAASID_FIELD_NUMBER;
    hash = (53 * hash) + getSaasId().hashCode();
    hash = (37 * hash) + PRIZEAMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getPrizeAmount().hashCode();
    hash = (37 * hash) + WINNERS_FIELD_NUMBER;
    hash = (53 * hash) + getWinners().hashCode();
    hash = (37 * hash) + AMENDED_FIELD_NUMBER;
    hash = (53 * hash) + getAmended().hashCode();
    hash = (37 * hash) + MODIFIED_FIELD_NUMBER;
    hash = (53 * hash) + getModified().hashCode();
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + status_;
    hash = (37 * hash) + OSSURL_FIELD_NUMBER;
    hash = (53 * hash) + getOssUrl().hashCode();
    hash = (37 * hash) + GENERATETIME_FIELD_NUMBER;
    hash = (53 * hash) + getGenerateTime().hashCode();
    hash = (37 * hash) + SOURCEOSSURL_FIELD_NUMBER;
    hash = (53 * hash) + getSourceOssUrl().hashCode();
    if (getRewardRuleCount() > 0) {
      hash = (37 * hash) + REWARDRULE_FIELD_NUMBER;
      hash = (53 * hash) + getRewardRuleList().hashCode();
    }
    hash = (37 * hash) + ACTIVITYTYPE_FIELD_NUMBER;
    hash = (53 * hash) + getActivityType().hashCode();
    hash = (37 * hash) + SOURCE_FIELD_NUMBER;
    hash = (53 * hash) + source_;
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.activity.facade.award.ActivityBatch parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatch parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatch parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatch parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatch parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatch parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatch parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatch parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.kikitrade.activity.facade.award.ActivityBatch parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.kikitrade.activity.facade.award.ActivityBatch parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatch parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.activity.facade.award.ActivityBatch parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.activity.facade.award.ActivityBatch prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.activity.facade.award.ActivityBatch}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.activity.facade.award.ActivityBatch)
      com.kikitrade.activity.facade.award.ActivityBatchOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ActivityBatch_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ActivityBatch_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.activity.facade.award.ActivityBatch.class, com.kikitrade.activity.facade.award.ActivityBatch.Builder.class);
    }

    // Construct using com.kikitrade.activity.facade.award.ActivityBatch.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      name_ = "";
      activityId_ = "";
      activityName_ = "";
      rewardType_ = "";
      amount_ = "";
      currency_ = "";
      remark_ = "";
      scheduled_ = false;
      scheduledTime_ = "";
      saasId_ = "";
      prizeAmount_ = "";
      winners_ = "";
      amended_ = "";
      modified_ = "";
      status_ = 0;
      ossUrl_ = "";
      generateTime_ = "";
      sourceOssUrl_ = "";
      if (rewardRuleBuilder_ == null) {
        rewardRule_ = java.util.Collections.emptyList();
      } else {
        rewardRule_ = null;
        rewardRuleBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00080000);
      activityType_ = "";
      source_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.internal_static_com_kikitrade_activity_facade_award_ActivityBatch_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ActivityBatch getDefaultInstanceForType() {
      return com.kikitrade.activity.facade.award.ActivityBatch.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ActivityBatch build() {
      com.kikitrade.activity.facade.award.ActivityBatch result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.activity.facade.award.ActivityBatch buildPartial() {
      com.kikitrade.activity.facade.award.ActivityBatch result = new com.kikitrade.activity.facade.award.ActivityBatch(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.kikitrade.activity.facade.award.ActivityBatch result) {
      if (rewardRuleBuilder_ == null) {
        if (((bitField0_ & 0x00080000) != 0)) {
          rewardRule_ = java.util.Collections.unmodifiableList(rewardRule_);
          bitField0_ = (bitField0_ & ~0x00080000);
        }
        result.rewardRule_ = rewardRule_;
      } else {
        result.rewardRule_ = rewardRuleBuilder_.build();
      }
    }

    private void buildPartial0(com.kikitrade.activity.facade.award.ActivityBatch result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.name_ = name_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.activityId_ = activityId_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.activityName_ = activityName_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.rewardType_ = rewardType_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.amount_ = amount_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.currency_ = currency_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.remark_ = remark_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.scheduled_ = scheduled_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.scheduledTime_ = scheduledTime_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.saasId_ = saasId_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.prizeAmount_ = prizeAmount_;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.winners_ = winners_;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.amended_ = amended_;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.modified_ = modified_;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.status_ = status_;
      }
      if (((from_bitField0_ & 0x00010000) != 0)) {
        result.ossUrl_ = ossUrl_;
      }
      if (((from_bitField0_ & 0x00020000) != 0)) {
        result.generateTime_ = generateTime_;
      }
      if (((from_bitField0_ & 0x00040000) != 0)) {
        result.sourceOssUrl_ = sourceOssUrl_;
      }
      if (((from_bitField0_ & 0x00100000) != 0)) {
        result.activityType_ = activityType_;
      }
      if (((from_bitField0_ & 0x00200000) != 0)) {
        result.source_ = source_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.activity.facade.award.ActivityBatch) {
        return mergeFrom((com.kikitrade.activity.facade.award.ActivityBatch)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.activity.facade.award.ActivityBatch other) {
      if (other == com.kikitrade.activity.facade.award.ActivityBatch.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getActivityId().isEmpty()) {
        activityId_ = other.activityId_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getActivityName().isEmpty()) {
        activityName_ = other.activityName_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (!other.getRewardType().isEmpty()) {
        rewardType_ = other.rewardType_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (!other.getAmount().isEmpty()) {
        amount_ = other.amount_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (!other.getCurrency().isEmpty()) {
        currency_ = other.currency_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (!other.getRemark().isEmpty()) {
        remark_ = other.remark_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (other.getScheduled() != false) {
        setScheduled(other.getScheduled());
      }
      if (!other.getScheduledTime().isEmpty()) {
        scheduledTime_ = other.scheduledTime_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (!other.getSaasId().isEmpty()) {
        saasId_ = other.saasId_;
        bitField0_ |= 0x00000400;
        onChanged();
      }
      if (!other.getPrizeAmount().isEmpty()) {
        prizeAmount_ = other.prizeAmount_;
        bitField0_ |= 0x00000800;
        onChanged();
      }
      if (!other.getWinners().isEmpty()) {
        winners_ = other.winners_;
        bitField0_ |= 0x00001000;
        onChanged();
      }
      if (!other.getAmended().isEmpty()) {
        amended_ = other.amended_;
        bitField0_ |= 0x00002000;
        onChanged();
      }
      if (!other.getModified().isEmpty()) {
        modified_ = other.modified_;
        bitField0_ |= 0x00004000;
        onChanged();
      }
      if (other.status_ != 0) {
        setStatusValue(other.getStatusValue());
      }
      if (!other.getOssUrl().isEmpty()) {
        ossUrl_ = other.ossUrl_;
        bitField0_ |= 0x00010000;
        onChanged();
      }
      if (!other.getGenerateTime().isEmpty()) {
        generateTime_ = other.generateTime_;
        bitField0_ |= 0x00020000;
        onChanged();
      }
      if (!other.getSourceOssUrl().isEmpty()) {
        sourceOssUrl_ = other.sourceOssUrl_;
        bitField0_ |= 0x00040000;
        onChanged();
      }
      if (rewardRuleBuilder_ == null) {
        if (!other.rewardRule_.isEmpty()) {
          if (rewardRule_.isEmpty()) {
            rewardRule_ = other.rewardRule_;
            bitField0_ = (bitField0_ & ~0x00080000);
          } else {
            ensureRewardRuleIsMutable();
            rewardRule_.addAll(other.rewardRule_);
          }
          onChanged();
        }
      } else {
        if (!other.rewardRule_.isEmpty()) {
          if (rewardRuleBuilder_.isEmpty()) {
            rewardRuleBuilder_.dispose();
            rewardRuleBuilder_ = null;
            rewardRule_ = other.rewardRule_;
            bitField0_ = (bitField0_ & ~0x00080000);
            rewardRuleBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getRewardRuleFieldBuilder() : null;
          } else {
            rewardRuleBuilder_.addAllMessages(other.rewardRule_);
          }
        }
      }
      if (!other.getActivityType().isEmpty()) {
        activityType_ = other.activityType_;
        bitField0_ |= 0x00100000;
        onChanged();
      }
      if (other.source_ != 0) {
        setSourceValue(other.getSourceValue());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              name_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              activityId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              activityName_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              rewardType_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              amount_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              currency_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              remark_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 72: {
              scheduled_ = input.readBool();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 82: {
              scheduledTime_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 90: {
              saasId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 98: {
              prizeAmount_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000800;
              break;
            } // case 98
            case 106: {
              winners_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00001000;
              break;
            } // case 106
            case 114: {
              amended_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00002000;
              break;
            } // case 114
            case 122: {
              modified_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00004000;
              break;
            } // case 122
            case 128: {
              status_ = input.readEnum();
              bitField0_ |= 0x00008000;
              break;
            } // case 128
            case 138: {
              ossUrl_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00010000;
              break;
            } // case 138
            case 146: {
              generateTime_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00020000;
              break;
            } // case 146
            case 154: {
              sourceOssUrl_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00040000;
              break;
            } // case 154
            case 162: {
              com.kikitrade.activity.facade.award.RewardRule m =
                  input.readMessage(
                      com.kikitrade.activity.facade.award.RewardRule.parser(),
                      extensionRegistry);
              if (rewardRuleBuilder_ == null) {
                ensureRewardRuleIsMutable();
                rewardRule_.add(m);
              } else {
                rewardRuleBuilder_.addMessage(m);
              }
              break;
            } // case 162
            case 170: {
              activityType_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00100000;
              break;
            } // case 170
            case 176: {
              source_ = input.readEnum();
              bitField0_ |= 0x00200000;
              break;
            } // case 176
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string name = 2;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      name_ = getDefaultInstance().getName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string name = 2;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      name_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object activityId_ = "";
    /**
     * <code>string activityId = 3;</code>
     * @return The activityId.
     */
    public java.lang.String getActivityId() {
      java.lang.Object ref = activityId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        activityId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string activityId = 3;</code>
     * @return The bytes for activityId.
     */
    public com.google.protobuf.ByteString
        getActivityIdBytes() {
      java.lang.Object ref = activityId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        activityId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string activityId = 3;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      activityId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>string activityId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      activityId_ = getDefaultInstance().getActivityId();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>string activityId = 3;</code>
     * @param value The bytes for activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      activityId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object activityName_ = "";
    /**
     * <code>string activityName = 4;</code>
     * @return The activityName.
     */
    public java.lang.String getActivityName() {
      java.lang.Object ref = activityName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        activityName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string activityName = 4;</code>
     * @return The bytes for activityName.
     */
    public com.google.protobuf.ByteString
        getActivityNameBytes() {
      java.lang.Object ref = activityName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        activityName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string activityName = 4;</code>
     * @param value The activityName to set.
     * @return This builder for chaining.
     */
    public Builder setActivityName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      activityName_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>string activityName = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityName() {
      activityName_ = getDefaultInstance().getActivityName();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>string activityName = 4;</code>
     * @param value The bytes for activityName to set.
     * @return This builder for chaining.
     */
    public Builder setActivityNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      activityName_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object rewardType_ = "";
    /**
     * <pre>
     *数字货币、道具
     * </pre>
     *
     * <code>string rewardType = 5;</code>
     * @return The rewardType.
     */
    public java.lang.String getRewardType() {
      java.lang.Object ref = rewardType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        rewardType_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *数字货币、道具
     * </pre>
     *
     * <code>string rewardType = 5;</code>
     * @return The bytes for rewardType.
     */
    public com.google.protobuf.ByteString
        getRewardTypeBytes() {
      java.lang.Object ref = rewardType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rewardType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *数字货币、道具
     * </pre>
     *
     * <code>string rewardType = 5;</code>
     * @param value The rewardType to set.
     * @return This builder for chaining.
     */
    public Builder setRewardType(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      rewardType_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *数字货币、道具
     * </pre>
     *
     * <code>string rewardType = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewardType() {
      rewardType_ = getDefaultInstance().getRewardType();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *数字货币、道具
     * </pre>
     *
     * <code>string rewardType = 5;</code>
     * @param value The bytes for rewardType to set.
     * @return This builder for chaining.
     */
    public Builder setRewardTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      rewardType_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private java.lang.Object amount_ = "";
    /**
     * <pre>
     *货币金额
     * </pre>
     *
     * <code>string amount = 6;</code>
     * @return The amount.
     */
    public java.lang.String getAmount() {
      java.lang.Object ref = amount_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        amount_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *货币金额
     * </pre>
     *
     * <code>string amount = 6;</code>
     * @return The bytes for amount.
     */
    public com.google.protobuf.ByteString
        getAmountBytes() {
      java.lang.Object ref = amount_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        amount_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *货币金额
     * </pre>
     *
     * <code>string amount = 6;</code>
     * @param value The amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmount(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      amount_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *货币金额
     * </pre>
     *
     * <code>string amount = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmount() {
      amount_ = getDefaultInstance().getAmount();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *货币金额
     * </pre>
     *
     * <code>string amount = 6;</code>
     * @param value The bytes for amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmountBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      amount_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object currency_ = "";
    /**
     * <pre>
     *货币单位
     * </pre>
     *
     * <code>string currency = 7;</code>
     * @return The currency.
     */
    public java.lang.String getCurrency() {
      java.lang.Object ref = currency_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        currency_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *货币单位
     * </pre>
     *
     * <code>string currency = 7;</code>
     * @return The bytes for currency.
     */
    public com.google.protobuf.ByteString
        getCurrencyBytes() {
      java.lang.Object ref = currency_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        currency_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *货币单位
     * </pre>
     *
     * <code>string currency = 7;</code>
     * @param value The currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrency(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      currency_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *货币单位
     * </pre>
     *
     * <code>string currency = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurrency() {
      currency_ = getDefaultInstance().getCurrency();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *货币单位
     * </pre>
     *
     * <code>string currency = 7;</code>
     * @param value The bytes for currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrencyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      currency_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object remark_ = "";
    /**
     * <pre>
     *批次描述
     * </pre>
     *
     * <code>string remark = 8;</code>
     * @return The remark.
     */
    public java.lang.String getRemark() {
      java.lang.Object ref = remark_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        remark_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *批次描述
     * </pre>
     *
     * <code>string remark = 8;</code>
     * @return The bytes for remark.
     */
    public com.google.protobuf.ByteString
        getRemarkBytes() {
      java.lang.Object ref = remark_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        remark_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *批次描述
     * </pre>
     *
     * <code>string remark = 8;</code>
     * @param value The remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemark(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      remark_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *批次描述
     * </pre>
     *
     * <code>string remark = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearRemark() {
      remark_ = getDefaultInstance().getRemark();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *批次描述
     * </pre>
     *
     * <code>string remark = 8;</code>
     * @param value The bytes for remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemarkBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      remark_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private boolean scheduled_ ;
    /**
     * <pre>
     *是否立即发奖
     * </pre>
     *
     * <code>bool scheduled = 9;</code>
     * @return The scheduled.
     */
    @java.lang.Override
    public boolean getScheduled() {
      return scheduled_;
    }
    /**
     * <pre>
     *是否立即发奖
     * </pre>
     *
     * <code>bool scheduled = 9;</code>
     * @param value The scheduled to set.
     * @return This builder for chaining.
     */
    public Builder setScheduled(boolean value) {

      scheduled_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *是否立即发奖
     * </pre>
     *
     * <code>bool scheduled = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearScheduled() {
      bitField0_ = (bitField0_ & ~0x00000100);
      scheduled_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object scheduledTime_ = "";
    /**
     * <pre>
     *发奖时间
     * </pre>
     *
     * <code>string scheduledTime = 10;</code>
     * @return The scheduledTime.
     */
    public java.lang.String getScheduledTime() {
      java.lang.Object ref = scheduledTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        scheduledTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *发奖时间
     * </pre>
     *
     * <code>string scheduledTime = 10;</code>
     * @return The bytes for scheduledTime.
     */
    public com.google.protobuf.ByteString
        getScheduledTimeBytes() {
      java.lang.Object ref = scheduledTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        scheduledTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *发奖时间
     * </pre>
     *
     * <code>string scheduledTime = 10;</code>
     * @param value The scheduledTime to set.
     * @return This builder for chaining.
     */
    public Builder setScheduledTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      scheduledTime_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *发奖时间
     * </pre>
     *
     * <code>string scheduledTime = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearScheduledTime() {
      scheduledTime_ = getDefaultInstance().getScheduledTime();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *发奖时间
     * </pre>
     *
     * <code>string scheduledTime = 10;</code>
     * @param value The bytes for scheduledTime to set.
     * @return This builder for chaining.
     */
    public Builder setScheduledTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      scheduledTime_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private java.lang.Object saasId_ = "";
    /**
     * <pre>
     *saasId
     * </pre>
     *
     * <code>string saasId = 11;</code>
     * @return The saasId.
     */
    public java.lang.String getSaasId() {
      java.lang.Object ref = saasId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        saasId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *saasId
     * </pre>
     *
     * <code>string saasId = 11;</code>
     * @return The bytes for saasId.
     */
    public com.google.protobuf.ByteString
        getSaasIdBytes() {
      java.lang.Object ref = saasId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        saasId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *saasId
     * </pre>
     *
     * <code>string saasId = 11;</code>
     * @param value The saasId to set.
     * @return This builder for chaining.
     */
    public Builder setSaasId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      saasId_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *saasId
     * </pre>
     *
     * <code>string saasId = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearSaasId() {
      saasId_ = getDefaultInstance().getSaasId();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *saasId
     * </pre>
     *
     * <code>string saasId = 11;</code>
     * @param value The bytes for saasId to set.
     * @return This builder for chaining.
     */
    public Builder setSaasIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      saasId_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }

    private java.lang.Object prizeAmount_ = "";
    /**
     * <code>string prizeAmount = 12;</code>
     * @return The prizeAmount.
     */
    public java.lang.String getPrizeAmount() {
      java.lang.Object ref = prizeAmount_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        prizeAmount_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string prizeAmount = 12;</code>
     * @return The bytes for prizeAmount.
     */
    public com.google.protobuf.ByteString
        getPrizeAmountBytes() {
      java.lang.Object ref = prizeAmount_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        prizeAmount_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string prizeAmount = 12;</code>
     * @param value The prizeAmount to set.
     * @return This builder for chaining.
     */
    public Builder setPrizeAmount(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      prizeAmount_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>string prizeAmount = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrizeAmount() {
      prizeAmount_ = getDefaultInstance().getPrizeAmount();
      bitField0_ = (bitField0_ & ~0x00000800);
      onChanged();
      return this;
    }
    /**
     * <code>string prizeAmount = 12;</code>
     * @param value The bytes for prizeAmount to set.
     * @return This builder for chaining.
     */
    public Builder setPrizeAmountBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      prizeAmount_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }

    private java.lang.Object winners_ = "";
    /**
     * <code>string winners = 13;</code>
     * @return The winners.
     */
    public java.lang.String getWinners() {
      java.lang.Object ref = winners_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        winners_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string winners = 13;</code>
     * @return The bytes for winners.
     */
    public com.google.protobuf.ByteString
        getWinnersBytes() {
      java.lang.Object ref = winners_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        winners_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string winners = 13;</code>
     * @param value The winners to set.
     * @return This builder for chaining.
     */
    public Builder setWinners(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      winners_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>string winners = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearWinners() {
      winners_ = getDefaultInstance().getWinners();
      bitField0_ = (bitField0_ & ~0x00001000);
      onChanged();
      return this;
    }
    /**
     * <code>string winners = 13;</code>
     * @param value The bytes for winners to set.
     * @return This builder for chaining.
     */
    public Builder setWinnersBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      winners_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }

    private java.lang.Object amended_ = "";
    /**
     * <code>string amended = 14;</code>
     * @return The amended.
     */
    public java.lang.String getAmended() {
      java.lang.Object ref = amended_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        amended_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string amended = 14;</code>
     * @return The bytes for amended.
     */
    public com.google.protobuf.ByteString
        getAmendedBytes() {
      java.lang.Object ref = amended_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        amended_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string amended = 14;</code>
     * @param value The amended to set.
     * @return This builder for chaining.
     */
    public Builder setAmended(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      amended_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>string amended = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmended() {
      amended_ = getDefaultInstance().getAmended();
      bitField0_ = (bitField0_ & ~0x00002000);
      onChanged();
      return this;
    }
    /**
     * <code>string amended = 14;</code>
     * @param value The bytes for amended to set.
     * @return This builder for chaining.
     */
    public Builder setAmendedBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      amended_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }

    private java.lang.Object modified_ = "";
    /**
     * <code>string modified = 15;</code>
     * @return The modified.
     */
    public java.lang.String getModified() {
      java.lang.Object ref = modified_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        modified_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string modified = 15;</code>
     * @return The bytes for modified.
     */
    public com.google.protobuf.ByteString
        getModifiedBytes() {
      java.lang.Object ref = modified_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        modified_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string modified = 15;</code>
     * @param value The modified to set.
     * @return This builder for chaining.
     */
    public Builder setModified(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      modified_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <code>string modified = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearModified() {
      modified_ = getDefaultInstance().getModified();
      bitField0_ = (bitField0_ & ~0x00004000);
      onChanged();
      return this;
    }
    /**
     * <code>string modified = 15;</code>
     * @param value The bytes for modified to set.
     * @return This builder for chaining.
     */
    public Builder setModifiedBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      modified_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }

    private int status_ = 0;
    /**
     * <code>.com.kikitrade.activity.facade.award.BatchStatusEnum status = 16;</code>
     * @return The enum numeric value on the wire for status.
     */
    @java.lang.Override public int getStatusValue() {
      return status_;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.BatchStatusEnum status = 16;</code>
     * @param value The enum numeric value on the wire for status to set.
     * @return This builder for chaining.
     */
    public Builder setStatusValue(int value) {
      status_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.BatchStatusEnum status = 16;</code>
     * @return The status.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.award.BatchStatusEnum getStatus() {
      com.kikitrade.activity.facade.award.BatchStatusEnum result = com.kikitrade.activity.facade.award.BatchStatusEnum.forNumber(status_);
      return result == null ? com.kikitrade.activity.facade.award.BatchStatusEnum.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.BatchStatusEnum status = 16;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(com.kikitrade.activity.facade.award.BatchStatusEnum value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00008000;
      status_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.activity.facade.award.BatchStatusEnum status = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      bitField0_ = (bitField0_ & ~0x00008000);
      status_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object ossUrl_ = "";
    /**
     * <pre>
     *下载地址
     * </pre>
     *
     * <code>string ossUrl = 17;</code>
     * @return The ossUrl.
     */
    public java.lang.String getOssUrl() {
      java.lang.Object ref = ossUrl_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ossUrl_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *下载地址
     * </pre>
     *
     * <code>string ossUrl = 17;</code>
     * @return The bytes for ossUrl.
     */
    public com.google.protobuf.ByteString
        getOssUrlBytes() {
      java.lang.Object ref = ossUrl_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ossUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *下载地址
     * </pre>
     *
     * <code>string ossUrl = 17;</code>
     * @param value The ossUrl to set.
     * @return This builder for chaining.
     */
    public Builder setOssUrl(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ossUrl_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *下载地址
     * </pre>
     *
     * <code>string ossUrl = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearOssUrl() {
      ossUrl_ = getDefaultInstance().getOssUrl();
      bitField0_ = (bitField0_ & ~0x00010000);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *下载地址
     * </pre>
     *
     * <code>string ossUrl = 17;</code>
     * @param value The bytes for ossUrl to set.
     * @return This builder for chaining.
     */
    public Builder setOssUrlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ossUrl_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }

    private java.lang.Object generateTime_ = "";
    /**
     * <code>string generateTime = 18;</code>
     * @return The generateTime.
     */
    public java.lang.String getGenerateTime() {
      java.lang.Object ref = generateTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        generateTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string generateTime = 18;</code>
     * @return The bytes for generateTime.
     */
    public com.google.protobuf.ByteString
        getGenerateTimeBytes() {
      java.lang.Object ref = generateTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        generateTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string generateTime = 18;</code>
     * @param value The generateTime to set.
     * @return This builder for chaining.
     */
    public Builder setGenerateTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      generateTime_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <code>string generateTime = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearGenerateTime() {
      generateTime_ = getDefaultInstance().getGenerateTime();
      bitField0_ = (bitField0_ & ~0x00020000);
      onChanged();
      return this;
    }
    /**
     * <code>string generateTime = 18;</code>
     * @param value The bytes for generateTime to set.
     * @return This builder for chaining.
     */
    public Builder setGenerateTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      generateTime_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }

    private java.lang.Object sourceOssUrl_ = "";
    /**
     * <code>string sourceOssUrl = 19;</code>
     * @return The sourceOssUrl.
     */
    public java.lang.String getSourceOssUrl() {
      java.lang.Object ref = sourceOssUrl_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sourceOssUrl_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string sourceOssUrl = 19;</code>
     * @return The bytes for sourceOssUrl.
     */
    public com.google.protobuf.ByteString
        getSourceOssUrlBytes() {
      java.lang.Object ref = sourceOssUrl_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sourceOssUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string sourceOssUrl = 19;</code>
     * @param value The sourceOssUrl to set.
     * @return This builder for chaining.
     */
    public Builder setSourceOssUrl(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      sourceOssUrl_ = value;
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <code>string sourceOssUrl = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearSourceOssUrl() {
      sourceOssUrl_ = getDefaultInstance().getSourceOssUrl();
      bitField0_ = (bitField0_ & ~0x00040000);
      onChanged();
      return this;
    }
    /**
     * <code>string sourceOssUrl = 19;</code>
     * @param value The bytes for sourceOssUrl to set.
     * @return This builder for chaining.
     */
    public Builder setSourceOssUrlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      sourceOssUrl_ = value;
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }

    private java.util.List<com.kikitrade.activity.facade.award.RewardRule> rewardRule_ =
      java.util.Collections.emptyList();
    private void ensureRewardRuleIsMutable() {
      if (!((bitField0_ & 0x00080000) != 0)) {
        rewardRule_ = new java.util.ArrayList<com.kikitrade.activity.facade.award.RewardRule>(rewardRule_);
        bitField0_ |= 0x00080000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.RewardRule, com.kikitrade.activity.facade.award.RewardRule.Builder, com.kikitrade.activity.facade.award.RewardRuleOrBuilder> rewardRuleBuilder_;

    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.RewardRule> getRewardRuleList() {
      if (rewardRuleBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rewardRule_);
      } else {
        return rewardRuleBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public int getRewardRuleCount() {
      if (rewardRuleBuilder_ == null) {
        return rewardRule_.size();
      } else {
        return rewardRuleBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public com.kikitrade.activity.facade.award.RewardRule getRewardRule(int index) {
      if (rewardRuleBuilder_ == null) {
        return rewardRule_.get(index);
      } else {
        return rewardRuleBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public Builder setRewardRule(
        int index, com.kikitrade.activity.facade.award.RewardRule value) {
      if (rewardRuleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardRuleIsMutable();
        rewardRule_.set(index, value);
        onChanged();
      } else {
        rewardRuleBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public Builder setRewardRule(
        int index, com.kikitrade.activity.facade.award.RewardRule.Builder builderForValue) {
      if (rewardRuleBuilder_ == null) {
        ensureRewardRuleIsMutable();
        rewardRule_.set(index, builderForValue.build());
        onChanged();
      } else {
        rewardRuleBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public Builder addRewardRule(com.kikitrade.activity.facade.award.RewardRule value) {
      if (rewardRuleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardRuleIsMutable();
        rewardRule_.add(value);
        onChanged();
      } else {
        rewardRuleBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public Builder addRewardRule(
        int index, com.kikitrade.activity.facade.award.RewardRule value) {
      if (rewardRuleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardRuleIsMutable();
        rewardRule_.add(index, value);
        onChanged();
      } else {
        rewardRuleBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public Builder addRewardRule(
        com.kikitrade.activity.facade.award.RewardRule.Builder builderForValue) {
      if (rewardRuleBuilder_ == null) {
        ensureRewardRuleIsMutable();
        rewardRule_.add(builderForValue.build());
        onChanged();
      } else {
        rewardRuleBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public Builder addRewardRule(
        int index, com.kikitrade.activity.facade.award.RewardRule.Builder builderForValue) {
      if (rewardRuleBuilder_ == null) {
        ensureRewardRuleIsMutable();
        rewardRule_.add(index, builderForValue.build());
        onChanged();
      } else {
        rewardRuleBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public Builder addAllRewardRule(
        java.lang.Iterable<? extends com.kikitrade.activity.facade.award.RewardRule> values) {
      if (rewardRuleBuilder_ == null) {
        ensureRewardRuleIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rewardRule_);
        onChanged();
      } else {
        rewardRuleBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public Builder clearRewardRule() {
      if (rewardRuleBuilder_ == null) {
        rewardRule_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00080000);
        onChanged();
      } else {
        rewardRuleBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public Builder removeRewardRule(int index) {
      if (rewardRuleBuilder_ == null) {
        ensureRewardRuleIsMutable();
        rewardRule_.remove(index);
        onChanged();
      } else {
        rewardRuleBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public com.kikitrade.activity.facade.award.RewardRule.Builder getRewardRuleBuilder(
        int index) {
      return getRewardRuleFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public com.kikitrade.activity.facade.award.RewardRuleOrBuilder getRewardRuleOrBuilder(
        int index) {
      if (rewardRuleBuilder_ == null) {
        return rewardRule_.get(index);  } else {
        return rewardRuleBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public java.util.List<? extends com.kikitrade.activity.facade.award.RewardRuleOrBuilder> 
         getRewardRuleOrBuilderList() {
      if (rewardRuleBuilder_ != null) {
        return rewardRuleBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rewardRule_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public com.kikitrade.activity.facade.award.RewardRule.Builder addRewardRuleBuilder() {
      return getRewardRuleFieldBuilder().addBuilder(
          com.kikitrade.activity.facade.award.RewardRule.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public com.kikitrade.activity.facade.award.RewardRule.Builder addRewardRuleBuilder(
        int index) {
      return getRewardRuleFieldBuilder().addBuilder(
          index, com.kikitrade.activity.facade.award.RewardRule.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.activity.facade.award.RewardRule rewardRule = 20;</code>
     */
    public java.util.List<com.kikitrade.activity.facade.award.RewardRule.Builder> 
         getRewardRuleBuilderList() {
      return getRewardRuleFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.activity.facade.award.RewardRule, com.kikitrade.activity.facade.award.RewardRule.Builder, com.kikitrade.activity.facade.award.RewardRuleOrBuilder> 
        getRewardRuleFieldBuilder() {
      if (rewardRuleBuilder_ == null) {
        rewardRuleBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.activity.facade.award.RewardRule, com.kikitrade.activity.facade.award.RewardRule.Builder, com.kikitrade.activity.facade.award.RewardRuleOrBuilder>(
                rewardRule_,
                ((bitField0_ & 0x00080000) != 0),
                getParentForChildren(),
                isClean());
        rewardRule_ = null;
      }
      return rewardRuleBuilder_;
    }

    private java.lang.Object activityType_ = "";
    /**
     * <code>string activityType = 21;</code>
     * @return The activityType.
     */
    public java.lang.String getActivityType() {
      java.lang.Object ref = activityType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        activityType_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string activityType = 21;</code>
     * @return The bytes for activityType.
     */
    public com.google.protobuf.ByteString
        getActivityTypeBytes() {
      java.lang.Object ref = activityType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        activityType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string activityType = 21;</code>
     * @param value The activityType to set.
     * @return This builder for chaining.
     */
    public Builder setActivityType(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      activityType_ = value;
      bitField0_ |= 0x00100000;
      onChanged();
      return this;
    }
    /**
     * <code>string activityType = 21;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityType() {
      activityType_ = getDefaultInstance().getActivityType();
      bitField0_ = (bitField0_ & ~0x00100000);
      onChanged();
      return this;
    }
    /**
     * <code>string activityType = 21;</code>
     * @param value The bytes for activityType to set.
     * @return This builder for chaining.
     */
    public Builder setActivityTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      activityType_ = value;
      bitField0_ |= 0x00100000;
      onChanged();
      return this;
    }

    private int source_ = 0;
    /**
     * <pre>
     *TASK,OPERATE
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.ActivitySourceEnum source = 22;</code>
     * @return The enum numeric value on the wire for source.
     */
    @java.lang.Override public int getSourceValue() {
      return source_;
    }
    /**
     * <pre>
     *TASK,OPERATE
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.ActivitySourceEnum source = 22;</code>
     * @param value The enum numeric value on the wire for source to set.
     * @return This builder for chaining.
     */
    public Builder setSourceValue(int value) {
      source_ = value;
      bitField0_ |= 0x00200000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *TASK,OPERATE
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.ActivitySourceEnum source = 22;</code>
     * @return The source.
     */
    @java.lang.Override
    public com.kikitrade.activity.facade.award.ActivitySourceEnum getSource() {
      com.kikitrade.activity.facade.award.ActivitySourceEnum result = com.kikitrade.activity.facade.award.ActivitySourceEnum.forNumber(source_);
      return result == null ? com.kikitrade.activity.facade.award.ActivitySourceEnum.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *TASK,OPERATE
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.ActivitySourceEnum source = 22;</code>
     * @param value The source to set.
     * @return This builder for chaining.
     */
    public Builder setSource(com.kikitrade.activity.facade.award.ActivitySourceEnum value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00200000;
      source_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *TASK,OPERATE
     * </pre>
     *
     * <code>.com.kikitrade.activity.facade.award.ActivitySourceEnum source = 22;</code>
     * @return This builder for chaining.
     */
    public Builder clearSource() {
      bitField0_ = (bitField0_ & ~0x00200000);
      source_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.activity.facade.award.ActivityBatch)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.activity.facade.award.ActivityBatch)
  private static final com.kikitrade.activity.facade.award.ActivityBatch DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.activity.facade.award.ActivityBatch();
  }

  public static com.kikitrade.activity.facade.award.ActivityBatch getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ActivityBatch>
      PARSER = new com.google.protobuf.AbstractParser<ActivityBatch>() {
    @java.lang.Override
    public ActivityBatch parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ActivityBatch> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ActivityBatch> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.activity.facade.award.ActivityBatch getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

