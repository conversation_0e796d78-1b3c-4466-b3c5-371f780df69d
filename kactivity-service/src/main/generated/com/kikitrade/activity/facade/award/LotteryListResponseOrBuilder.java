// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

public interface LotteryListResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.activity.facade.award.LotteryListResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   */
  java.util.List<com.kikitrade.activity.facade.award.Lottery> 
      getLotteryList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   */
  com.kikitrade.activity.facade.award.Lottery getLottery(int index);
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   */
  int getLotteryCount();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   */
  java.util.List<? extends com.kikitrade.activity.facade.award.LotteryOrBuilder> 
      getLotteryOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.activity.facade.award.Lottery lottery = 1;</code>
   */
  com.kikitrade.activity.facade.award.LotteryOrBuilder getLotteryOrBuilder(
      int index);
}
