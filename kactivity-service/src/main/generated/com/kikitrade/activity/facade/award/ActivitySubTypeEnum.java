// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ActivityFacade.proto

package com.kikitrade.activity.facade.award;

/**
 * Protobuf enum {@code com.kikitrade.activity.facade.award.ActivitySubTypeEnum}
 */
public enum ActivitySubTypeEnum
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>NONE = 0;</code>
   */
  NONE(0),
  /**
   * <pre>
   * 交易返佣
   * </pre>
   *
   * <code>TRADE_REBATE = 1;</code>
   */
  TRADE_REBATE(1),
  /**
   * <pre>
   * KYC1奖励
   * </pre>
   *
   * <code>KYC1 = 2;</code>
   */
  KYC1(2),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>NONE = 0;</code>
   */
  public static final int NONE_VALUE = 0;
  /**
   * <pre>
   * 交易返佣
   * </pre>
   *
   * <code>TRADE_REBATE = 1;</code>
   */
  public static final int TRADE_REBATE_VALUE = 1;
  /**
   * <pre>
   * KYC1奖励
   * </pre>
   *
   * <code>KYC1 = 2;</code>
   */
  public static final int KYC1_VALUE = 2;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static ActivitySubTypeEnum valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static ActivitySubTypeEnum forNumber(int value) {
    switch (value) {
      case 0: return NONE;
      case 1: return TRADE_REBATE;
      case 2: return KYC1;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<ActivitySubTypeEnum>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      ActivitySubTypeEnum> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<ActivitySubTypeEnum>() {
          public ActivitySubTypeEnum findValueByNumber(int number) {
            return ActivitySubTypeEnum.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.activity.facade.award.ActivityFacadeOuterClass.getDescriptor().getEnumTypes().get(2);
  }

  private static final ActivitySubTypeEnum[] VALUES = values();

  public static ActivitySubTypeEnum valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private ActivitySubTypeEnum(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.activity.facade.award.ActivitySubTypeEnum)
}

