package com.kikitrade.activity.service.remote.impl;

import com.kikitrade.activity.api.model.response.GoodsDetailResponse;
import com.kikitrade.activity.api.model.response.GoodsResponse;
import com.kikitrade.activity.dal.tablestore.model.Goods;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.goods.GiftPackService;
import com.kikitrade.activity.service.goods.GoodsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RemoteGoodsServiceImpl 测试类
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
@ExtendWith(MockitoExtension.class)
class RemoteGoodsServiceImplTest {

    @Mock
    private GoodsService goodsService;

    @Mock
    private GiftPackService giftPackService;

    @InjectMocks
    private RemoteGoodsServiceImpl remoteGoodsService;

    private Goods testGoods;
    private Goods testGiftPackGoods;

    @BeforeEach
    void setUp() {
        // 创建测试商品
        testGoods = new Goods();
        testGoods.setGoodsId("goods_001");
        testGoods.setSaasId("test_saas");
        testGoods.setName("测试商品");
        testGoods.setType(ActivityConstant.GoodsType.GAME_ITEM.getCode());
        testGoods.setStatus(ActivityConstant.GoodsStatus.ONLINE.getCode());
        testGoods.setEndTime(System.currentTimeMillis() + 86400000); // 24小时后过期
        testGoods.setOrder(1);

        // 创建测试礼包商品
        testGiftPackGoods = new Goods();
        testGiftPackGoods.setGoodsId("gift_pack_001");
        testGiftPackGoods.setSaasId("test_saas");
        testGiftPackGoods.setName("测试礼包");
        testGiftPackGoods.setType(ActivityConstant.GoodsType.GIFT_PACK.getCode());
        testGiftPackGoods.setStatus(ActivityConstant.GoodsStatus.ONLINE.getCode());
        testGiftPackGoods.setEndTime(System.currentTimeMillis() + 86400000);
        testGiftPackGoods.setOrder(2);
    }

    @Test
    void testGoods_NormalGoods() {
        // Given
        when(goodsService.findById("goods_001")).thenReturn(testGoods);
        when(giftPackService.isGiftPack(ActivityConstant.GoodsType.GAME_ITEM.getCode())).thenReturn(false);

        // When
        GoodsDetailResponse result = remoteGoodsService.goods("goods_001");

        // Then
        assertNotNull(result);
        assertEquals("goods_001", result.getGoodsId());
        assertEquals("测试商品", result.getName());
        assertEquals(ActivityConstant.GoodsType.GAME_ITEM.getCode(), result.getType());
        assertNull(result.getGiftPackItems()); // 普通商品不应该有礼包信息

        verify(goodsService).findById("goods_001");
        verify(giftPackService).isGiftPack(ActivityConstant.GoodsType.GAME_ITEM.getCode());
        verify(giftPackService, never()).getGiftPackItems(anyString(), anyString());
    }

    @Test
    void testGoods_GiftPackGoods() {
        // Given
        List<GoodsDetailResponse.GiftPackItem> giftPackItems = Arrays.asList(
            createGiftPackItem("item_001", "物品1", 2),
            createGiftPackItem("item_002", "物品2", 1)
        );

        when(goodsService.findById("gift_pack_001")).thenReturn(testGiftPackGoods);
        when(giftPackService.isGiftPack(ActivityConstant.GoodsType.GIFT_PACK.getCode())).thenReturn(true);
        when(giftPackService.getGiftPackItems("gift_pack_001", "test_saas")).thenReturn(giftPackItems);

        // When
        GoodsDetailResponse result = remoteGoodsService.goods("gift_pack_001");

        // Then
        assertNotNull(result);
        assertEquals("gift_pack_001", result.getGoodsId());
        assertEquals("测试礼包", result.getName());
        assertEquals(ActivityConstant.GoodsType.GIFT_PACK.getCode(), result.getType());
        assertNotNull(result.getGiftPackItems());
        assertEquals(2, result.getGiftPackItems().size());

        verify(goodsService).findById("gift_pack_001");
        verify(giftPackService).isGiftPack(ActivityConstant.GoodsType.GIFT_PACK.getCode());
        verify(giftPackService).getGiftPackItems("gift_pack_001", "test_saas");
    }

    @Test
    void testGoods_NotFound() {
        // Given
        when(goodsService.findById("not_exist")).thenReturn(null);

        // When
        GoodsDetailResponse result = remoteGoodsService.goods("not_exist");

        // Then
        assertNull(result);
        verify(goodsService).findById("not_exist");
        verify(giftPackService, never()).isGiftPack(anyString());
    }

    @Test
    void testFindBySceneCode_Success() {
        // Given
        List<Goods> goodsList = Arrays.asList(testGoods, testGiftPackGoods);
        
        when(goodsService.findBySceneCode("test_scene")).thenReturn(goodsList);
        when(giftPackService.isGiftPack(ActivityConstant.GoodsType.GAME_ITEM.getCode())).thenReturn(false);
        when(giftPackService.isGiftPack(ActivityConstant.GoodsType.GIFT_PACK.getCode())).thenReturn(true);
        when(giftPackService.batchGetGiftPackItems(Arrays.asList("gift_pack_001"), "test_saas"))
            .thenReturn(java.util.Map.of("gift_pack_001", Arrays.asList(
                createGiftPackItem("item_001", "物品1", 2)
            )));

        // When
        List<GoodsResponse> result = remoteGoodsService.findBySceneCode("test_scene");

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证排序（按order字段）
        assertEquals("goods_001", result.get(0).getGoodsId());
        assertEquals("gift_pack_001", result.get(1).getGoodsId());
        
        // 验证礼包商品包含物品信息
        assertNull(result.get(0).getGiftPackItems()); // 普通商品
        assertNotNull(result.get(1).getGiftPackItems()); // 礼包商品
        assertEquals(1, result.get(1).getGiftPackItems().size());

        verify(goodsService).findBySceneCode("test_scene");
        verify(giftPackService).batchGetGiftPackItems(Arrays.asList("gift_pack_001"), "test_saas");
    }

    @Test
    void testFindBySceneCode_EmptyResult() {
        // Given
        when(goodsService.findBySceneCode("empty_scene")).thenReturn(new ArrayList<>());

        // When
        List<GoodsResponse> result = remoteGoodsService.findBySceneCode("empty_scene");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(goodsService).findBySceneCode("empty_scene");
        verify(giftPackService, never()).batchGetGiftPackItems(anyList(), anyString());
    }

    private GoodsDetailResponse.GiftPackItem createGiftPackItem(String itemId, String itemName, Integer quantity) {
        GoodsDetailResponse.GiftPackItem item = new GoodsDetailResponse.GiftPackItem();
        item.setItemId(itemId);
        item.setItemName(itemName);
        item.setItemType("FIXED");
        item.setQuantity(quantity);
        item.setDescription("测试物品");
        return item;
    }
}
