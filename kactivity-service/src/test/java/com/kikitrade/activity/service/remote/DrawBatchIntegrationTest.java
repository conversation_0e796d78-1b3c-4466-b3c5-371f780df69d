package com.kikitrade.activity.service.remote;

import com.kikitrade.activity.api.model.request.reward.DrawBatchRequest;
import com.kikitrade.activity.api.model.response.reward.DrawBatchResponse;
import com.kikitrade.activity.dal.tablestore.builder.DrawHistoryBuilder;
import com.kikitrade.activity.service.remote.impl.RewardPlatformServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * 批量抽奖历史记录落库集成测试
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@SpringBootTest
@ActiveProfiles("test")
public class DrawBatchIntegrationTest {

    @Resource
    private RewardPlatformServiceImpl rewardPlatformService;
    
    @Resource
    private DrawHistoryBuilder drawHistoryBuilder;

    /**
     * 测试完整的批量抽奖流程，包括历史记录落库
     * 注意：此测试需要有效的奖池配置和抽奖券
     */
    @Test
    public void testDrawBatchWithHistoryRecords() {
        System.out.println("=== 批量抽奖历史记录落库集成测试 ===");
        
        // 准备测试请求
        DrawBatchRequest request = new DrawBatchRequest();
        request.setUserId("test-user-integration-" + System.currentTimeMillis());
        request.setSaasId("test-saas");
        request.setPrizePoolCode("TEST_POOL"); // 需要确保此奖池存在
        request.setDrawCount(3);
        
        System.out.println("测试参数:");
        System.out.println("- 用户ID: " + request.getUserId());
        System.out.println("- 奖池编码: " + request.getPrizePoolCode());
        System.out.println("- 抽奖次数: " + request.getDrawCount());
        
        try {
            // 执行批量抽奖
            System.out.println("\n开始执行批量抽奖...");
            DrawBatchResponse response = rewardPlatformService.drawBatch(request);
            
            System.out.println("抽奖响应:");
            System.out.println("- 成功状态: " + response.getSuccess());
            System.out.println("- 响应消息: " + response.getMessage());
            
            if (response.getSuccess()) {
                System.out.println("- 中奖结果数量: " + (response.getDrawResults() != null ? response.getDrawResults().size() : 0));
                
                if (response.getDrawResults() != null) {
                    for (int i = 0; i < response.getDrawResults().size(); i++) {
                        var result = response.getDrawResults().get(i);
                        System.out.println("  奖品" + (i + 1) + ": " + result.getPrizeName() + " x" + result.getQuantity());
                    }
                }
                
                // 验证历史记录是否正确保存
                System.out.println("\n验证历史记录保存情况...");
                try {
                    Thread.sleep(3000); // 等待数据同步
                    
                    // 查询用户的抽奖历史记录
                    var historyPage = drawHistoryBuilder.findByUserId(request.getUserId(), null, 10);
                    
                    System.out.println("历史记录查询结果:");
                    System.out.println("- 查询到的记录数量: " + historyPage.getRows().size());
                    
                    if (historyPage.getRows().size() >= request.getDrawCount()) {
                        System.out.println("✅ 历史记录数量验证通过");
                        
                        // 验证记录内容
                        boolean allRecordsValid = true;
                        for (var record : historyPage.getRows()) {
                            if (!request.getUserId().equals(record.getUserId()) ||
                                !request.getPrizePoolCode().equals(record.getPrizePoolCode()) ||
                                !request.getSaasId().equals(record.getSaasId())) {
                                allRecordsValid = false;
                                break;
                            }
                        }
                        
                        if (allRecordsValid) {
                            System.out.println("✅ 历史记录内容验证通过");
                            
                            // 显示部分记录详情
                            System.out.println("\n历史记录详情（前3条）:");
                            for (int i = 0; i < Math.min(3, historyPage.getRows().size()); i++) {
                                var record = historyPage.getRows().get(i);
                                System.out.println("记录" + (i + 1) + ":");
                                System.out.println("  - 事件ID: " + record.getEventId());
                                System.out.println("  - 批量交易ID: " + record.getBatchTransactionId());
                                System.out.println("  - 奖品名称: " + record.getPrizeName());
                                System.out.println("  - 奖品类型: " + record.getPrizeType());
                                System.out.println("  - 抽奖时间: " + record.getDrawTime());
                            }
                            
                        } else {
                            System.out.println("❌ 历史记录内容验证失败");
                        }
                    } else {
                        System.out.println("❌ 历史记录数量验证失败: 期望至少" + request.getDrawCount() + "条，实际" + historyPage.getRows().size() + "条");
                    }
                    
                } catch (Exception e) {
                    System.out.println("❌ 历史记录验证异常: " + e.getMessage());
                    e.printStackTrace();
                }
                
                System.out.println("\n✅ 批量抽奖历史记录落库集成测试完成");
                
            } else {
                System.out.println("❌ 批量抽奖失败: " + response.getErrorCode() + " - " + response.getMessage());
                System.out.println("可能原因:");
                System.out.println("1. 奖池配置不存在或未激活");
                System.out.println("2. 用户抽奖券不足");
                System.out.println("3. 奖池中无可用奖品");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 批量抽奖集成测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试批量抽奖的错误处理机制
     */
    @Test
    public void testDrawBatchErrorHandling() {
        System.out.println("=== 批量抽奖错误处理测试 ===");
        
        // 测试不存在的奖池
        DrawBatchRequest request = new DrawBatchRequest();
        request.setUserId("test-user-error-" + System.currentTimeMillis());
        request.setSaasId("test-saas");
        request.setPrizePoolCode("NON_EXISTENT_POOL");
        request.setDrawCount(1);
        
        System.out.println("测试不存在的奖池: " + request.getPrizePoolCode());
        
        try {
            DrawBatchResponse response = rewardPlatformService.drawBatch(request);
            
            System.out.println("错误处理响应:");
            System.out.println("- 成功状态: " + response.getSuccess());
            System.out.println("- 错误码: " + response.getErrorCode());
            System.out.println("- 错误消息: " + response.getMessage());
            
            if (!response.getSuccess() && "POOL_NOT_FOUND".equals(response.getErrorCode())) {
                System.out.println("✅ 奖池不存在错误处理验证通过");
            } else {
                System.out.println("❌ 奖池不存在错误处理验证失败");
            }
            
        } catch (Exception e) {
            System.out.println("错误处理测试异常: " + e.getMessage());
        }
    }
}
