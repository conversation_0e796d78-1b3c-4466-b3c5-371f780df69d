package com.kikitrade.activity.luck.api.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 */
@Data
public class LuckFortuneCheckDTO implements Serializable {

    /**
     * 用户
     */
    private ActivityCustomerDTO customer;

    /**
     * 红包数量
     */
    private Integer num;

    /**
     * 币种
     */
    private String currency;

    /**
     * 红包金额
     */
    private BigDecimal amount;

    /**
     * 红包分配类型：普通红包/随机红包
     */
    private String assignType;

    /**
     * usd价值
     */
    private BigDecimal usdCost;

    /**
     * 当前币种精度
     */
    private int keepDecimalForCoin;
}
