package com.kikitrade.activity.luck.api;

import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.luck.api.model.request.ActivityCustomerDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneOpenDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneReceiveDTO;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneHistoryReceiveVO;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneReceive;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneReceiveResponse;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneReceiveVO;

import java.util.List;

/**
 * 领取接口
 */
public interface RemoteLuckFortuneReceiveService {

    /**
     * 领取红包
     * @param luckFortuneReceiveDTO
     */
    Result<LuckFortuneReceiveResponse> receiveLuckFortune(LuckFortuneReceiveDTO luckFortuneReceiveDTO) throws Exception;

    /**
     * 开启红包
     * @param luckFortuneOpenDTO
     */
    Result openLuckFortune(LuckFortuneOpenDTO luckFortuneOpenDTO) throws Exception;

    /**
     * 最近收到的红包
     * @param customer
     * @return
     */
    List<LuckFortuneReceive> lastReceiveLuckFortune(ActivityCustomerDTO customer);

    /**
     * 历史收到的红包
     * @param customer
     */
    LuckFortuneHistoryReceiveVO historyReceiveLuckFortune(ActivityCustomerDTO customer, String type, Long startTime, Long endTime, int offset, int limit);

    /**
     * 查询我领取的某个红包
     * @param id
     * @param receiveId
     * @return
     */
    LuckFortuneReceiveVO findByReceiveId(String id, String receiveId, String refer) throws Exception;

    /**
     * 检测能否开启空投
     * @param luckFortuneOpenDTO
     * @return
     * @throws Exception
     */
    Result check(LuckFortuneOpenDTO luckFortuneOpenDTO) throws Exception;
}
