package com.kikitrade.activity.luck.api.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 */
@Data
public class LuckFortuneReleaseDTO implements Serializable {

    /**
     * 用户id
     */
    private ActivityCustomerDTO customer;

    /**
     * 红包数量
     */
    private Integer num;

    /**
     * 币种
     */
    private String currency;

    /**
     * 红包金额
     */
    private BigDecimal amount;

    /**
     * 封面
     */
    private String cover;

    /**
     * 红包分配类型：普通红包/随机红包
     */
    private String assignType;

    /**
     * 祝福语
     */
    private String greeting;

    /**
     * 有效时长
     */
    private Integer validTime;

    /**
     * 发放金额的usd价值
     */
    private BigDecimal usdCost;

    /**
     * 精度
     */
    private int keepDecimalForCoin;

    /**
     * 支付密码校验
     */
    private String tokenId;
}
