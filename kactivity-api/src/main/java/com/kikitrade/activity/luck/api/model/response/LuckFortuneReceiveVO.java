package com.kikitrade.activity.luck.api.model.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 */
@Data
public class LuckFortuneReceiveVO implements Serializable {

    /**
     * 空投id
     */
    private String id;

    /**
     * 红包分配类型
     */
    private String assignType;

    /**
     * 发放数量
     */
    private int num;

    /**
     * 领取到金额
     */
    private String amount;

    /**
     * 领取到币种
     */
    private String currency;

    /**
     * 封面id
     */
    private String cover;

    /**
     * 祝福语
     */
    private String greeting;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 发放人昵称
     */
    private String nickName;

    /**
     * 过期时间
     */
    private long expiredTime;

    /**
     * 已领取数量
     */
    private Integer receiveNum;

    /**
     * 领取记录
     */
    private List<LuckFortuneReceive> receiveList;

    /**
     * 用户昵称
     */
    private String releaseCustomerId;
}
