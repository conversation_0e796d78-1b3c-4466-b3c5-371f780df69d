package com.kikitrade.activity.luck.api.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LuckFortuneHistoryReceiveVO implements Serializable {

    /**
     * 游标
     */
    private int offset;

    private int limit;

    private long totalCount;

    /**
     * 发放的空投总数量
     */
    private long num;

    /**
     * 发放的空投总金额
     */
    private BigDecimal amount;


    private List<LuckFortuneReceive> rows;
}
