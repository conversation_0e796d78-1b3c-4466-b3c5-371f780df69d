package com.kikitrade.activity.luck.api.model.response;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 *
 */
@Data
@Builder
public class LuckFortuneReceive implements Serializable {

    /**
     * 领取标识
     */
    private String receiveCode;

    /**
     * 领取金额
     */
    private String amount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 领取时间
     */
    private long receiveTime;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 用户昵称
     */
    private String releaseNickName;

    /**
     * 祝福语
     */
    private String greeting;

    /**
     * 发放的红包id
     */
    private String id;

    /**
     * 红包状态
     */
    private Integer status;

    /**
     * 封皮
     */
    private String cover;

    /**
     * 领取到的红包id
     */
    private String receiveId;

    /**
     * 红包类型
     */
    private String assignType;

    /**
     * 领取人头像
     */
    private String receiveAvatar;

    /**
     * 剩余激活时间
     */
    private long remainTime;
}
