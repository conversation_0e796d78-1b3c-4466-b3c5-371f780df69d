package com.kikitrade.activity.luck.api.model.response;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 */
@Data
@Builder
public class AirdropRuleVO implements Serializable {

    /**
     * 发放的最大金额
     */
    private BigDecimal releaseMax;
    /**
     * 发放的最小金额
     */
    private BigDecimal releaseMin;
    /**
     * 发放的最大数量
     */
    private Integer releaseNumMax;
    /**
     * 发放的单个红包最小金额
     */
    private BigDecimal receiveMin;
    /**
     * 发放的单个红包最大金额
     */
    private BigDecimal receiveMax;
}
