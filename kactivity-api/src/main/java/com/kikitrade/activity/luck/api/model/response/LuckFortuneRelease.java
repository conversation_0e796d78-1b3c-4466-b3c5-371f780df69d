package com.kikitrade.activity.luck.api.model.response;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 */
@Data
@Builder
public class LuckFortuneRelease implements Serializable {

    /**
     * 主键, 红包id
     */
    private String id;

    /**
     * 红包分配方案：普通红包/拼手气红包
     */
    private String assignType;

    /**
     * 红包数量
     */
    private Integer num;

    /**
     * 已领取数量
     */
    private Integer receiveNum;

    /**
     * 红包金额
     */
    private String amount;

    /**
     * 红包币种
     */
    private String currency;

    /**
     * 封面
     */
    private String cover;

    /**
     * 祝福语
     */
    private String greeting;

    /**
     * 红包过期时间
     */
    private Long expiredTime;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 密钥
     */
    private String token;

    /**
     * 发放时间
     */
    private Long releaseTime;

    /**
     * 红包状态
     */
    private Integer status;

    /**
     * 领取进度
     */
    private ProcessVO process;

    /**
     * 领取记录
     */
    private List<LuckFortuneReceive> receiveList;

    /**
     * 空投标识
     */
    private String tag;
}
