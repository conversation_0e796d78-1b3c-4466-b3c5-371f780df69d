package com.kikitrade.activity.luck.api;

import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.luck.api.model.request.ActivityCustomerDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneReleaseDTO;
import com.kikitrade.activity.luck.api.model.response.AirdropRuleVO;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneHistoryReleaseVO;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneRelease;

/**
 * 发放接口
 */
public interface RemoteLuckFortuneReleaseService {

    /**
     * 查询规则
     * @param customer
     * @return
     */
    Result<AirdropRuleVO> rule(ActivityCustomerDTO customer) throws Exception;

    /**
     * 发放的空投简单详情,不包含变化值
     * @param id
     * @param customer
     * @return
     * @throws Exception
     */
    Result<LuckFortuneRelease> findReleaseAirdropSimpleById(String id, ActivityCustomerDTO customer) throws Exception;

    /**
     * 发放的空投详情
     * @param id
     * @param customer
     * @return
     * @throws Exception
     */
    Result<LuckFortuneRelease> findReleaseAirdropById(String id, String refer, ActivityCustomerDTO customer) throws Exception;

    /**
     * 发放红包
     * @param luckFortuneReleaseDTO
     * @return
     */
    Result<LuckFortuneRelease> releaseLuckFortune(LuckFortuneReleaseDTO luckFortuneReleaseDTO) throws Exception;

    /**
     * 发放的，时间倒叙
     * @param customer
     * @return
     */
    LuckFortuneHistoryReleaseVO historyReleaseLuckFortune(ActivityCustomerDTO customer, String type, Long startTime, Long endTime, int offset, int limit);
}
