package com.kikitrade.activity.luck.api.exception;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 业务异常
 */
@Getter
@Setter
@NoArgsConstructor
public class LuckFortuneException extends Exception {

    private static final long serialVersionUID = 1L;

    private LuckFortuneExceptionType type;
    private Exception e;

    public LuckFortuneException(LuckFortuneExceptionType type){
        super(type.getMessage());
        this.type = type;
    }

    public LuckFortuneException(LuckFortuneExceptionType type, String message){
        super(message == null ? "" : message);
        this.type = type;
    }
}
