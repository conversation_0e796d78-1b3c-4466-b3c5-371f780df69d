package com.kikitrade.activity.luck.api.exception;

import lombok.Getter;

@Getter
public enum LuckFortuneExceptionType {

    //没有发放红包权限
    LUCK_FORTUNE_RELEASE_PERMISSION("100000", "luck.fortune.release.permission"),
    //参数为空
    LUCK_FORTUNE_PARAM_NULL("100001", "luck.fortune.param.null"),
    //红包数量无效
    LUCK_FORTUNE_NUM_INVALID("100002", "luck.fortune.num.invalid"),
    //红包金额无效
    LUCK_FORTUNE_AMOUNT_INVALID("100003", "luck.fortune.amount.invalid"),
    //红包金额无效
    LUCK_FORTUNE_SINGLE_AMOUNT_INVALID("100004", "luck.fortune.single.amount.invalid"),
    //红包无效
    LUCK_FORTUNE_INVALID("100005", "luck.fortune.invalid"),
    //操作过于频繁
    LUCK_FORTUNE_OPERATION_FAST("100006", "luck.fortune.operation.faster"),
    //红包已经被领空
    LUCK_FORTUNE_EMPTY_ERROR("100007", "luck.fortune.empty"),
    //已经领取过
    LUCK_FORTUNE_RECEIVE_REPEAT_ERROR("10008", "luck.fortune.repeat"),
    //币种不存在
    LUCK_FORTUNE_CURRENCY_INVALID("100009", "luck.fortune.currency"),
    //红包已过期
    LUCK_FORTUNE_EXPIRED("100010", "luck.fortune.expired"),
    //红包发放金额达上限
    LUCK_FORTUNE_RELEASE_LIMIT_ERROR("100011", "luck.fortune.release.limit"),
    //钱包余额不足
    CUSTOMER_INSUFFICIENT_BALANCE("100012", "customer.insufficient.balance"),
    //领取达上限
    LUCK_FORTUNE_RECEIVE_LIMIT_ERROR("100013", "luck.fortune.receive.limit"),
    //领取次数达日上限,
    LUCK_FORTUNE_RECEIVE_LIMIT_DAY_ERROR("100015", "luck.fortune.receive.limit.day"),
    //领取次数月上限,
    LUCK_FORTUNE_RECEIVE_LIMIT_MONTH_ERROR("100016", "luck.fortune.receive.limit.month"),
    //领取金额达月上限
    LUCK_FORTUNE_RECEIVE_LIMIT_AMOUNT_MONTH_ERROR("100017", "luck.fortune.receive.limit.amount.month"),
    //领取金额达日上限
    LUCK_FORTUNE_RECEIVE_LIMIT_AMOUNT_DAY_ERROR("100018", "luck.fortune.receive.limit.amount.day"),
    //未参与空投新人活动
    LUCK_FORTUNE_ACTIVITY_INVALID("100014", "luck.fortune.activity.invalid"),
    //系统繁忙
    SYSTEM_ERROR("9999", "system.error"),
    ;

    private String code;
    private String message;

    LuckFortuneExceptionType(String code, String message){
        this.code = code;
        this.message = message;
    }
}
