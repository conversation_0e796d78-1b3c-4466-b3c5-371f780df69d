package com.kikitrade.activity.api.model.response;

import com.kikitrade.activity.api.model.QuestionSimpleVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/28 19:24
 */
@Data
public class QuestionListResponse implements Serializable {

    private Boolean result = Boolean.FALSE;

    private String message;

    private String code;

    @Schema(name = "questions", description = "questions list")
    private List<QuestionSimpleVO> questions = new ArrayList<>();
}
