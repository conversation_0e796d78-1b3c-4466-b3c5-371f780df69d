package com.kikitrade.activity.api.model.request.reward;

import com.kikitrade.activity.model.constant.ActivityConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 礼包权益授予请求
 * 对应技术规格书中的 grantClaimEntitlement 接口
 * 由上游服务（如任务系统）为用户创建领奖凭证
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GrantPackRequest implements Serializable {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * SaaS ID
     */
    private String saasId;

    /**
     * 奖励类型 (PROGRESS_CHEST, GRANTED_PACK)
     */
    private ActivityConstant.RewardTypeEnum rewardType;

    /**
     * 奖励来源ID (如 chestConfigId 或 packId)
     */
    private String rewardSourceId;

    /**
     * 奖励名称（用于展示）
     */
    private String rewardName;

    /**
     * 奖励图标URL（用于展示）
     */
    private String rewardIcon;

    /**
     * 奖励描述（用于展示）
     */
    private String rewardDescription;

    /**
     * 凭证来源渠道
     */
    private String sourceChannel;

    /**
     * 来源渠道的唯一交易ID (用于幂等性)
     */
    private String sourceTransactionId;

    /**
     * 凭证过期时间（可选，不设置则永不过期）
     */
    private Long expireTime;
}
