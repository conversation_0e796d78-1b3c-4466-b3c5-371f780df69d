package com.kikitrade.activity.api.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/22 20:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LotteryCountResponse implements Serializable {

    private Long latestCycleCount;

    private Long historyCount;
}
