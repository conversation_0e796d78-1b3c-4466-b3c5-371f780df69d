package com.kikitrade.activity.api.model.response;

import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/12/21 11:28 上午
 * @modify
 */
@Data
@Builder
public class InviteRewardsSumResponse implements Serializable {
    private ActivityResponseCode code;
    private Map<ActivityConstant.RewardBusinessType, Map<ActivityConstant.AwardTypeEnum, BigDecimal>> rewards;
}
