package com.kikitrade.activity.api.model.request.reward;

import lombok.Data;

import java.io.Serializable;

/**
 * 获取宝箱领取状态请求
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class GetClaimStatusRequest implements Serializable {

    /**
     * 用户ID (通过Token获取，可选)
     */
    private String userId;

    /**
     * SaaS ID
     */
    private String saasId;

    /**
     * 宝箱ID（可选，不传则返回所有宝箱状态）
     */
    private String chestId;

    /**
     * 宝箱类型过滤
     * DAILY: 每日进度宝箱
     * WEEKLY: 每周进度宝箱
     * MONTHLY: 每月进度宝箱
     * LEVEL: 等级进度宝箱
     * ACHIEVEMENT: 成就进度宝箱
     * ACTIVITY: 活动进度宝箱
     */
    private String chestType;

    /**
     * 状态过滤
     * LOCKED: 未解锁
     * UNLOCKED: 已解锁可领取
     * CLAIMED: 已领取
     * EXPIRED: 已过期
     */
    private String status;
}
