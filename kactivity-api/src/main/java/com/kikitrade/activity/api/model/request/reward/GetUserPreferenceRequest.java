package com.kikitrade.activity.api.model.request.reward;

import lombok.Data;

import java.io.Serializable;

/**
 * 获取用户偏好请求
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class GetUserPreferenceRequest implements Serializable {

    /**
     * 用户ID (通过Token获取，可选)
     */
    private String userId;

    /**
     * SaaS ID
     */
    private String saasId;

    /**
     * 偏好类型
     * 支持的类型：
     * - SELECTED_HERO: 选择的英雄
     * - VIP_LEVEL: VIP等级
     * - USER_LEVEL: 用户等级
     * - PREFERRED_REGION: 偏好地区
     */
    private String preferenceType;
}