package com.kikitrade.activity.api.model.request.reward;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 礼包发放请求
 * 对应技术规格书中的 issuePack 接口
 * 由外部业务系统调用，按ID为用户发放一个宝箱/礼包（直接发放，无需用户领取）
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IssuePackRequest implements Serializable {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * SaaS ID
     */
    private String saasId;

    /**
     * 礼包ID
     */
    private String packId;

    /**
     * 来源渠道/场景Code
     */
    private String channel;

    /**
     * 外部渠道的唯一交易ID (用于幂等性)
     */
    private String transactionId;

    /**
     * 发放描述
     */
    private String description;

    /**
     * 客户端IP（可选，用于风控）
     */
    private String clientIp;

    /**
     * 设备信息（可选，用于风控）
     */
    private String deviceInfo;
}
