package com.kikitrade.activity.api.model.response;

import com.kikitrade.activity.model.response.ActivityResponseCode;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/12 11:39
 */
@Data
public class VerifyResponse implements Serializable {

    /**
     * 校验是否通过
     */
    public boolean success;
    /**
     * 不通过的原因
     */
    private String reason;
    /**
     *
     */
    private String postUrl;
    private String accessToken;
    private String refreshToken;

    private ActivityResponseCode code;

    public static VerifyResponse response(ActivityResponseCode code) {
        VerifyResponse response = new VerifyResponse();
        response.setSuccess(code.isSuccess());
        response.setReason(code.getKey());
        response.setCode(code);
        return response;
    }

}
