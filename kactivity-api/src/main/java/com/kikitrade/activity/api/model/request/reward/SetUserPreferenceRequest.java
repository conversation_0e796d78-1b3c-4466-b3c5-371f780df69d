package com.kikitrade.activity.api.model.request.reward;

import lombok.Data;

import java.io.Serializable;

/**
 * 设置用户偏好请求
 * 通用的用户偏好设置接口，支持英雄选择、VIP等级等多种偏好类型
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class SetUserPreferenceRequest implements Serializable {

    /**
     * 用户ID (通过Token获取，可选)
     */
    private String userId;

    /**
     * SaaS ID
     */
    private String prizePoolCode;

    /**
     * 偏好类型
     * 支持的类型：
     * - SELECTED_HERO: 选择的英雄
     * - VIP_LEVEL: VIP等级
     * - USER_LEVEL: 用户等级
     * - PREFERRED_REGION: 偏好地区
     */
    private String preferenceType;

    /**
     * 偏好值
     */
    private String preferenceValue;
}