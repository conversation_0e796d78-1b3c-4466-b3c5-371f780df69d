package com.kikitrade.activity.api.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/28 19:24
 */
@Data
public class QuestionDTO implements Serializable {

    @Schema(name = "id", description = "question id")
    private String id;

    @Schema(name = "title", description = "question title")
    private String title;

    @Schema(name = "option1", description = "question option1")
    private String option1;

    @Schema(name = "option2", description = "question option2")
    private String option2;

    @Schema(name = "imgUrl", description = "imgUrl")
    private String imgUrl;

    @Schema(name = "categories", description = "categories")
    private List<String> categories;

    @Schema(name = "userOption", description = "userOption")
    private String userOption;

    @Schema(name = "correctOption", description = "correctOption")
    private String correctOption;

    @Schema(name = "isCorrect", description = "isCorrect")
    private Boolean isCorrect;
}
