package com.kikitrade.activity.api.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.kikitrade.activity.model.domain.Award;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/22 10:52
 */
@Data
public class TaskVO implements Serializable {

    private String taskId;
    private String saasId;
    /**
     * 任务所属平台
     */
    private String domain;
    private String title;
    private String desc;
    private int status;
    /**
     * 任务的url
     */
    private String url;
    private String code;
    private String connectUrl;
    private String reward;
    private String platform;
    private Integer rewardStatus;
    private Long startTime;
    private Long endTime;
    private Integer limit;
    private Map<String, String> attr;
    private List<Award> rewards;
}
