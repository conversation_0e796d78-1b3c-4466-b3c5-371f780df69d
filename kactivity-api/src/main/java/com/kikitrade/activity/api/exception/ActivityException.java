package com.kikitrade.activity.api.exception;

import com.kikitrade.activity.api.model.ActivityDTO;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ActivityException extends RuntimeException {

    private String code;
    private String message;
    private Throwable e;
    private ActivityDTO activity;

    public ActivityException() {
    }

    /**
     * groovy里面真正send的时候出现异常
     *
     * @param ct
     */
    public ActivityException(ActivityExceptionType type, ActivityDTO ct) {
        this(type);
        this.activity = ct;
    }

    public ActivityException(ActivityExceptionType type) {
        this.code = type.getCode();
        this.message = type.getMessage();
    }

    public ActivityException(ActivityExceptionType type, String message) {
        this.code = type.getCode();
        this.message = type.getMessage() + " " + message;
    }

    public ActivityException(ActivityExceptionType type, Throwable e) {
        this.code = type.getCode();
        this.message = type.getMessage();
        this.e = e;
    }

    public ActivityException(Throwable e) {
        this.code = ActivityExceptionType.UNKNOWN_REASON.getCode();
        this.message = e.getMessage();
        this.e = e;
    }

    public ActivityException(String message) {
        this.code = ActivityExceptionType.UNKNOWN_REASON.getCode();
        this.message = message;
    }

    public ActivityException(String code, String message, Throwable e) {
        this.code = code;
        this.message = message;
        this.e = e;
    }

}


