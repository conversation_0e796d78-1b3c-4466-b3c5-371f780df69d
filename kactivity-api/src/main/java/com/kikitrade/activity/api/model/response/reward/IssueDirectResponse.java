package com.kikitrade.activity.api.model.response.reward;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 直接奖励发放响应
 * 对应技术规格书中的 issueDirect 接口响应
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IssueDirectResponse implements Serializable {

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 发放的奖励列表
     */
    private List<IssuedReward> issuedRewards;

    /**
     * 错误码（失败时）
     */
    private String errorCode;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 发放时间
     */
    private Long issueTime;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 是否为重复请求（幂等性处理）
     */
    private Boolean isDuplicate;

    /**
     * 发放的奖励信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class IssuedReward implements Serializable {
        /**
         * 物品ID
         */
        private String itemId;

        /**
         * 物品类型
         */
        private String itemType;

        /**
         * 物品名称
         */
        private String itemName;

        /**
         * 数量
         */
        private Integer quantity;

        /**
         * 描述
         */
        private String description;

        /**
         * 图标URL
         */
        private String iconUrl;

        /**
         * 发放状态 (SUCCESS, FAILED)
         */
        private String issueStatus;

        /**
         * 发放失败原因（如果失败）
         */
        private String failureReason;
    }
}
