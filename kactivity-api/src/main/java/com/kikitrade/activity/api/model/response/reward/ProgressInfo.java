package com.kikitrade.activity.api.model.response.reward;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

@Data
@Builder
public class ProgressInfo implements Serializable {
    /**
     * 周期类型
     */
    private String cycleType;

    /**
     * 当前值
     */
    private Integer currentValue;

    /**
     * 周期结束时间
     */
    private Instant cycleEndTime;
}