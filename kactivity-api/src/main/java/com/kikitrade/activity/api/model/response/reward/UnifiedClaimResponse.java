package com.kikitrade.activity.api.model.response.reward;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 统一领奖响应
 * 按照技术规格书要求设计的统一领奖接口响应
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
public class UnifiedClaimResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 领奖凭证ID
     */
    private String claimId;

    /**
     * 获得的奖励列表
     */
    private List<RewardItem> rewards;

    /**
     * 领取时间
     */
    private Long claimTime;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 奖励物品信息
     */
    @Data
    @Builder
    public static class RewardItem implements Serializable {
        
        /**
         * 物品ID
         */
        private String itemId;
        
        /**
         * 物品名称
         */
        private String itemName;
        
        /**
         * 物品类型
         */
        private String itemType;
        
        /**
         * 数量
         */
        private Integer quantity;
        
        /**
         * 物品描述
         */
        private String description;
        
        /**
         * 物品图标URL
         */
        private String iconUrl;
    }
}
