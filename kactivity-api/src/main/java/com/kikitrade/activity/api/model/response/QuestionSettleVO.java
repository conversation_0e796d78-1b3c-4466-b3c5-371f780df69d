package com.kikitrade.activity.api.model.response;

import com.kikitrade.activity.api.model.QuestionDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/28 19:24
 */
@Data
public class QuestionSettleVO implements Serializable {

    @Schema(name = "correctCount", description = "correctCount")
    private String correctCount;
    
    @Schema(name = "correctScore", description = "correctScore")
    private String correctScore;
    
    @Schema(name = "errorCount", description = "errorCount")
    private String errorCount;

    @Schema(name = "errorScore", description = "errorScore")
    private String errorScore;

    @Schema(name = "spendTime", description = "spendTime")
    private Long spendTime;

    @Schema(name = "scoreAdditionMultiple", description = "scoreAdditionMultiple")
    private BigDecimal scoreAdditionMultiple;

    @Schema(name = "score", description = "score")
    private BigDecimal score;

    @Schema(name = "totalScore", description = "totalScore")
    private BigDecimal totalScore;

    @Schema(name = "usedSets", description = "usedSets")
    private Integer usedSets;

    @Schema(name = "rewardSets", description = "rewardSets")
    private Integer rewardSets;

    @Schema(name = "availableSets", description = "availableSets")
    private Integer availableSets;

    @Schema(name = "rewardExp", description = "rewardExp")
    private BigDecimal rewardExp;

    @Schema(name = "totalExp", description = "totalExp")
    private BigDecimal totalExp;

    @Schema(name = "questions", description = "questions")
    private List<QuestionDTO> questions;

}
