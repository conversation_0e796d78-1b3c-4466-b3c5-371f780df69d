package com.kikitrade.activity.api.model.request.reward;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 直接奖励发放请求
 * 对应技术规格书中的 issueDirect 接口
 * 由外部业务系统调用，直接为用户发放一组指定的奖励
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IssueDirectRequest implements Serializable {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * SaaS ID
     */
    private String saasId;

    /**
     * 奖励列表
     */
    private List<RewardItem> rewards;

    /**
     * 来源渠道
     */
    private String channel;

    /**
     * 场景Code
     */
    private String sceneCode;

    /**
     * 外部渠道的唯一交易ID (用于幂等性)
     */
    private String transactionId;

    /**
     * 发放描述
     */
    private String description;

    /**
     * 客户端IP（可选，用于风控）
     */
    private String clientIp;

    /**
     * 设备信息（可选，用于风控）
     */
    private String deviceInfo;

    /**
     * 奖励物品信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RewardItem implements Serializable {
        /**
         * 物品ID
         */
        private String itemId;

        /**
         * 物品类型 (ITEM, CURRENCY, POINTS等)
         */
        private String itemType;

        /**
         * 物品名称
         */
        private String itemName;

        /**
         * 数量
         */
        private Integer quantity;

        /**
         * 描述
         */
        private String description;

        /**
         * 图标URL
         */
        private String iconUrl;
    }
}
