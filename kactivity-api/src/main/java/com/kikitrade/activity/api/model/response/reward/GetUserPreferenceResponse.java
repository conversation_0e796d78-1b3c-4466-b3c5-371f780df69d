package com.kikitrade.activity.api.model.response.reward;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 获取用户偏好响应
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
public class GetUserPreferenceResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 偏好类型
     */
    private String preferenceType;

    /**
     * 偏好值
     */
    private String preferenceValue;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String message;
}