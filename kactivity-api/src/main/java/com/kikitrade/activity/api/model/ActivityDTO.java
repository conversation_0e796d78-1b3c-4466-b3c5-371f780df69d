package com.kikitrade.activity.api.model;

import com.kikitrade.activity.model.ActivityType;
import com.kikitrade.activity.model.constant.ActivityConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ActivityDTO
 *
 * <AUTHOR>
 * @create 2021/9/7 9:14 下午
 * @modify
 */
@Data
public class ActivityDTO implements Serializable {

    //活动ID（主键）
    private Integer id;  //id

    /**
     * {@link ActivityType#getCode()}
     */
    private Integer type;

    //活动名称
    private String name;


    //活动链接
    private String url;

    //活动详情
    private String content;

    private String locale;

    //开始时间
    private Date startTime;

    //结束时间
    private Date endTime;

    //报名是否需要审核
    private Integer isNeedAudit;


    //置顶顺序
    private Integer topOrder;

    //执行类型
    //0-自动
    //1-手动
    //2-定时

    private Integer executeType;

    //是否发送消息
    //0-不发送
    //1-发送
    private Integer isPush;

    //活动状态
    //0-新建
    //1-已发布
    //2-进行中
    //3-已结束
    //4-暂停
    //5-失效
    private Integer status;

    //参加次数
    private Integer applyTimes;


    //活动发布者用户ID
    private String publishUserId;

    /**
     * SassId
     */
    private String saasId;

    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;

    private List<ActivityActionDTO> actionConfig;

    private List<ActivityRuleDTO> ruleConfig;

    private List<ActivityContentDTO> activityContents;

    private String business_id;
    private String checkResult;

    private Integer batchStatus;

    private ActivityConstant.ActivitySubTypeEnum subType;   // 子活动类型
}
