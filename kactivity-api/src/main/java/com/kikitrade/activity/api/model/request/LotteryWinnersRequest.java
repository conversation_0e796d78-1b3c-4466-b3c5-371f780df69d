package com.kikitrade.activity.api.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/1 20:33
 * @description: 保存抽奖中奖者信息的请求对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LotteryWinnersRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private String saasId;

    /**
     * 抽奖活动代码
     */
    private String lotteryCode;

    /**
     * 活动期数编码，例如 week1, week2 等
     */
    private String poolCode;

    /**
     * 中奖者列表
     */
    private List<Winner> winners;

    /**
     * 中奖者信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Winner implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 用户地址，用于标识中奖者
         */
        private String address;

        /**
         * 中奖排名
         */
        private Integer order;
    }
}