package com.kikitrade.activity.api.model.request;

import com.kikitrade.activity.api.model.QuestionAnswerVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/28 19:24
 */
@Data
public class UserAnswerRequest implements Serializable {

    @Schema
    private String saas_id;
    @Schema
    private Long spendTimeMs;
    @Schema
    private List<QuestionAnswerVO> questions;
}
