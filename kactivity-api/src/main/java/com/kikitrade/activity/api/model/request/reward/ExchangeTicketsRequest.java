package com.kikitrade.activity.api.model.request.reward;

import lombok.Data;

import java.io.Serializable;

/**
 * 资产兑换抽奖券请求
 * 支持两阶段抽奖系统，用户使用积分、金币等资产兑换抽奖券
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class ExchangeTicketsRequest implements Serializable {

    /**
     * 用户ID (通过Token获取，可选)
     */
    private String customerId;

    /**
     * SaaS ID
     */
    private String saasId;

    /**
     * 奖池编码（兼容字段）
     */
    private String prizePoolCode;

    /**
     * 兑换类型
     */
    private String exchangeType;
    /**
     * 兑换的资产类型
     * POINTS: 积分
     */
    private String assetType;
    /**
     * 兑换方式
     */
    private String optionId;

    /**
     * 兑换场景
     * MANUAL: 手动兑换
     * AUTO: 自动兑换
     * ACTIVITY: 活动兑换
     */
    private String exchangeScene;

}