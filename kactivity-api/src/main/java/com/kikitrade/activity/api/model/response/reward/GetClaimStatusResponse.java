package com.kikitrade.activity.api.model.response.reward;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 获取宝箱领取状态响应
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
public class GetClaimStatusResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 宝箱状态列表
     */
    private List<ChestStatusInfo> chestStatuses;

    /**
     * 用户进度信息
     */
    private List<UserProgressInfo> userProgress;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 宝箱状态信息
     */
    @Data
    @Builder
    public static class ChestStatusInfo implements Serializable {
        
        /**
         * 宝箱ID
         */
        private String chestId;
        
        /**
         * 宝箱名称
         */
        private String chestName;
        
        /**
         * 宝箱类型
         */
        private String chestType;
        
        /**
         * 状态
         */
        private String status;
        
        /**
         * 当前进度值
         */
        private Integer currentProgress;
        
        /**
         * 所需进度值
         */
        private Integer requiredProgress;
        
        /**
         * 是否可以领取
         */
        private Boolean canClaim;
        
        /**
         * 上次领取时间
         */
        private Long lastClaimTime;
        
        /**
         * 已领取次数
         */
        private Integer claimCount;
        
        /**
         * 最大领取次数
         */
        private Integer maxClaimCount;
        
        /**
         * 奖品预览
         */
        private List<String> prizePreview;
    }

    /**
     * 用户进度信息
     */
    @Data
    @Builder
    public static class UserProgressInfo implements Serializable {
        
        /**
         * 进度类型
         */
        private String progressType;
        
        /**
         * 当前进度值
         */
        private Integer currentProgress;
        
        /**
         * 历史最高进度值
         */
        private Integer maxProgress;
        
        /**
         * 今日进度值
         */
        private Integer dailyProgress;
        
        /**
         * 本周进度值
         */
        private Integer weeklyProgress;
        
        /**
         * 本月进度值
         */
        private Integer monthlyProgress;
        
        /**
         * 上次更新时间
         */
        private Long lastUpdateTime;
    }
}
