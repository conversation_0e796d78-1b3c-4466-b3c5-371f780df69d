package com.kikitrade.activity.api.model.response;

import com.kikitrade.activity.model.constant.ActivityConstant;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @desc 任务结果查询
 * @date 2023/11/22 14:44
 */
@Data
public class TaskCompletedResult implements Serializable {

    private String customerId;
    private String taskId;
    private String cycle;
    private String targetId;
    private String event;
    private String completeTime;
    private String status;
    private Integer completeThreshold;
    private Integer progress = 0;
    private String expiredTime;
    private Integer consecutiveDays;
    private Integer completeDays;

    public Boolean isDone(){
        return ActivityConstant.TaskStatusEnum.DONE.name().equals(this.status);
    }
}
