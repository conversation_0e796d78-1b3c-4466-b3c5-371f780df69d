package com.kikitrade.activity.api.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class TaskSubmitDTO implements Serializable {

    private Integer datapointId;
    private Integer submissionId;
    private Long submittedAt;
    private Integer sahUid;
    private String userWalletAddress;
    private Map submittedContent;

}
