package com.kikitrade.activity.api.model.request.reward;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户消耗抽奖券进行批量抽奖请求
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class DrawBatchRequest implements Serializable {

    /**
     * 奖池编码
     */
    private String prizePoolCode;

    /**
     * 抽奖次数
     */
    private Integer drawCount;

    /**
     * 用户ID (通过Token获取，可选)
     */
    private String userId;

    /**
     * SaaS ID
     */
    private String saasId;
}