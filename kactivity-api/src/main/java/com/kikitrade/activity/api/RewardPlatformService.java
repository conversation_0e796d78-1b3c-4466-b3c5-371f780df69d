package com.kikitrade.activity.api;

import com.kikitrade.activity.api.model.request.reward.*;
import com.kikitrade.activity.api.model.response.reward.*;

/**
 * 奖励中台服务接口
 * 提供新的奖励中台功能，与现有RemoteLotteryService并行运行
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface RewardPlatformService {

    // ==================== 用户端API ====================

    /**
     * 用户兑换抽奖券
     * POST /api/lottery/exchange-tickets
     */
    ExchangeTicketsResponse exchangeTickets(ExchangeTicketsRequest request);

    /**
     * 用户消耗抽奖券进行批量抽奖
     * POST /api/lottery/draw-batch
     */
    DrawBatchResponse drawBatch(DrawBatchRequest request);

    // ==================== 用户偏好管理API ====================

    /**
     * 设置用户偏好（通用接口，支持英雄选择、VIP等级等多种偏好类型）
     * POST /api/user/preference/set
     */
    SetUserPreferenceResponse setUserPreference(SetUserPreferenceRequest request);

    /**
     * 获取用户偏好
     * GET /api/user/preference/get
     */
    GetUserPreferenceResponse getUserPreference(GetUserPreferenceRequest request);

    // ==================== 统一领奖系统API ====================

    /**
     * 统一领奖接口
     * POST /api/rewards/claim
     */
    UnifiedClaimResponse claimReward(UnifiedClaimRequest request);

    /**
     * 获取用户抽奖状态
     * GET /api/lottery/state
     */
    LotteryStateResponse getLotteryState(LotteryStateRequest request);

    // ==================== 内部服务API (Dubbo) ====================

    /**
     * 由上游服务（如任务系统）为用户创建领奖资格
     */
    GrantPackResponse grantPackEntitlement(GrantPackRequest request);

    /**
     * 由外部业务系统调用，按ID为用户发放一个宝箱/礼包（直接发放，无需用户领取）
     */
    IssuePackResponse issuePack(IssuePackRequest request);

    /**
     * 由外部业务系统调用，直接为用户发放一组指定的奖励
     */
    IssueDirectResponse issueDirect(IssueDirectRequest request);
}