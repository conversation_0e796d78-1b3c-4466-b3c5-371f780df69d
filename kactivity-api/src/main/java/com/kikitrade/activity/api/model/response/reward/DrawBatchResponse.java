package com.kikitrade.activity.api.model.response.reward;

import lombok.Data;
import lombok.Builder;

import java.io.Serializable;
import java.util.List;

/**
 * 批量抽奖响应
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
public class DrawBatchResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 抽奖结果列表
     */
    private List<DrawResult> drawResults;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 单次抽奖结果
     */
    @Data
    @Builder
    public static class DrawResult implements Serializable {

        /**
         * 奖品ID
         */
        private String prizeId;

        /**
         * 奖品名称
         */
        private String prizeName;

        /**
         * 奖品类型
         */
        private String prizeType;

        /**
         * 奖品数量
         */
        private Integer quantity;

        /**
         * 奖品图标
         */
        private String prizeIcon;
    }
}