package com.kikitrade.activity.api.model;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.kikitrade.activity.model.FiatDepositActivityConfig;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/9/7 9:17 下午
 * @modify
 */
public class ActivityRuleDTO implements Serializable {
    
    //activity id
    private Integer activity_id;

    //rule id
    private Integer rule_id;

    //rule_name for redis use
    private String rule_name;

    //规则优先级，哪些规则优先执行
    private Integer priority;

    //0-失效 1-生效
    private Integer status;

    //rule param json
    private String params;

    private JSONObject paramsJson;

    private List<FiatDepositActivityConfig> configList;

    /**
     * 把params解析成JSONObject格式，并返回
     *
     * @return
     */
    public JSONObject fetchParamsJson() {
        if (paramsJson == null && StringUtils.isNotBlank(params)) {
            try {
                paramsJson = JSONObject.parseObject(params);
            } catch (Exception e) {
                paramsJson = new JSONObject();
                e.printStackTrace();
            }
        }
        return paramsJson == null ? new JSONObject() : paramsJson;
    }

    /**
     * 解析出paramsObj中的configList字段，并返回
     *
     * @return
     */
    public List<FiatDepositActivityConfig> fetchConfigList() {
        if (configList == null) {
            fetchParamsJson();
            try {
                if (paramsJson != null && paramsJson.containsKey("configList")) {
                    configList = paramsJson.getObject("configList", new TypeReference<List<FiatDepositActivityConfig>>() {
                    });
                } else {
                    configList = new ArrayList<>();
                }
            } catch (Exception e) {
                e.printStackTrace();
                configList = new ArrayList<>();
            }
        }
        return configList;
    }

}