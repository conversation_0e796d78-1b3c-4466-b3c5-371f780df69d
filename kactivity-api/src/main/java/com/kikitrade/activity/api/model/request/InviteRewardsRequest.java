package com.kikitrade.activity.api.model.request;

import com.kikitrade.activity.model.constant.ActivityConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 邀请奖励明细查询对象
 * <AUTHOR>
 * @create 2022/12/13 2:59 下午
 * @modify
 */
@Data
public class InviteRewardsRequest implements Serializable {
    private String customerId;
    private ActivityConstant.SideEnum role;
    private Map<ActivityConstant.RewardBusinessType, QueryParam> queryMap;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class QueryParam implements Serializable {
        private List<ActivityConstant.AwardTypeEnum> rewardTypes;
        private int offset = 0;
        private int limit = 10;
    }

}
