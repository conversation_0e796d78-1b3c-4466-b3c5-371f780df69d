package com.kikitrade.activity.api.model.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/12/27 14:01
 */
@Data
public class TaskCodeDetailResponse implements Serializable {

    /**
     * 任务id
     */
    private String taskId;
    /**
     * 任务标题
     */
    private String title;
    /**
     * 任务描述
     */
    private String desc;
    /**
     * 任务图片
     */
    private Map<String, String> imageMap;
    private List<String> image;
    /**
     * 标签名称
     */
    private String labelName;
    /**
     * 标签颜色
     */
    private String labelColor;
    /**
     * 进度
     */
    private int process;
    /**
     * 任务节点信息
     */
    private List<NodeVO> nodes;

    private boolean checkIn;
}
