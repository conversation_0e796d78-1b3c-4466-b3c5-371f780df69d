package com.kikitrade.activity.api.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

@Data
@Builder
public class ActivityTaskVO implements Serializable {

    private Map<String,String> name;

    //任务名称国际化
    private String nameI18n;

    private Integer status;

    private BigDecimal amount;

    private Map<String,String> remark;

    //任务描述国际
    private String remarkI18n;

    private String url;

    private String callback;

    private String level;

    private Integer process;

    private Integer completeThreshold;

    private Long expiredTime;

    private Integer order;

    private String currentName;

    private String currentRemark;

    private String icon;

    private String event;

    private Date created;

}
