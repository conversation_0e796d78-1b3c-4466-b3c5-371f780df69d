package com.kikitrade.activity.api.model.response.reward;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 礼包权益授予响应
 * 对应技术规格书中的 grantClaimEntitlement 接口响应
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GrantPackResponse implements Serializable {

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 生成的领奖凭证ID
     */
    private String claimId;

    /**
     * 错误码（失败时）
     */
    private String errorCode;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 凭证创建时间
     */
    private Long createTime;

    /**
     * 凭证过期时间
     */
    private Long expireTime;

    /**
     * 是否为重复请求（幂等性处理）
     */
    private Boolean isDuplicate;
}
