package com.kikitrade.activity.api.model.request;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailDomainWhitelistRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * SaaS ID
     */
    @NotBlank(message = "saasId cannot be blank")
    private String saasId;

    /**
     * 场景代码
     */
    @NotBlank(message = "sceneCode cannot be blank")
    private String sceneCode;

    /**
     * 域名白名单列表
     */
    @NotNull(message = "domainList cannot be null")
    private List<String> domainList;
}