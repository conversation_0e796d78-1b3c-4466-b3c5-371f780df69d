package com.kikitrade.activity.api.model.request.reward;

import lombok.Data;

import java.io.Serializable;

/**
 * 获取可领取奖励列表请求
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class GetClaimableRewardsRequest implements Serializable {

    /**
     * 用户ID (通过Token获取，可选)
     */
    private String userId;

    /**
     * SaaS ID
     */
    private String saasId;

    /**
     * 奖励类型过滤
     * PROGRESS_CHEST: 进度宝箱
     * GRANTED_PACK: 授予的礼包
     * TASK_REWARD: 任务奖励
     * LEADERBOARD_REWARD: 排行榜奖励
     */
    private String rewardType;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 页码
     */
    private Integer pageNum;
}
