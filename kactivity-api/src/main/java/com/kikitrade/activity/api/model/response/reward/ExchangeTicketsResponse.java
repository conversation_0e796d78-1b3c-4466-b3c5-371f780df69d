package com.kikitrade.activity.api.model.response.reward;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 资产兑换抽奖券响应
 * 支持两阶段抽奖系统的详细兑换信息
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
public class ExchangeTicketsResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 获得的抽奖券数量（兼容字段）
     */
    private Integer ticketsObtained;

    /**
     * 兑换的抽奖券类型
     */
    private String ticketType;

    /**
     * 消耗的资产类型
     */
    private String assetType;

    /**
     * 兑换订单ID
     */
    private String exchangeOrderId;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String message;
}