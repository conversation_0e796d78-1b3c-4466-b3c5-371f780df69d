package com.kikitrade.activity.api.model.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/23 10:00
 * @description: 社媒平台授权URL响应
 */
public class SocialAuthUrlResponse implements Serializable {

    /**
     * 社媒平台名称
     */
    private String platform;

    /**
     * 授权URL
     */
    private String authUrl;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 排序
     */
    private Integer order;

    public SocialAuthUrlResponse() {
    }

    public SocialAuthUrlResponse(String platform, String authUrl, Boolean enabled, Integer order) {
        this.platform = platform;
        this.authUrl = authUrl;
        this.enabled = enabled;
        this.order = order;
    }

    public static SocialAuthUrlResponseBuilder builder() {
        return new SocialAuthUrlResponseBuilder();
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getAuthUrl() {
        return authUrl;
    }

    public void setAuthUrl(String authUrl) {
        this.authUrl = authUrl;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public static class SocialAuthUrlResponseBuilder {
        private String platform;
        private String platformName;
        private String authUrl;
        private String iconUrl;
        private Boolean enabled;
        private Integer order;

        public SocialAuthUrlResponseBuilder platform(String platform) {
            this.platform = platform;
            return this;
        }

        public SocialAuthUrlResponseBuilder platformName(String platformName) {
            this.platformName = platformName;
            return this;
        }

        public SocialAuthUrlResponseBuilder authUrl(String authUrl) {
            this.authUrl = authUrl;
            return this;
        }

        public SocialAuthUrlResponseBuilder iconUrl(String iconUrl) {
            this.iconUrl = iconUrl;
            return this;
        }

        public SocialAuthUrlResponseBuilder enabled(Boolean enabled) {
            this.enabled = enabled;
            return this;
        }

        public SocialAuthUrlResponseBuilder order(Integer order) {
            this.order = order;
            return this;
        }

        public SocialAuthUrlResponse build() {
            return new SocialAuthUrlResponse(platform, authUrl, enabled, order);
        }
    }
}
