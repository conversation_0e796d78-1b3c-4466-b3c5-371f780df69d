package com.kikitrade.activity.api.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/28 19:24
 */
@Data
public class QuestionSettleResponse implements Serializable {

    private Boolean result = Boolean.FALSE;

    private String message;

    private String code;

    @Schema(name = "settleVO", description = "settleVO")
    private QuestionSettleVO questionSettleVO;

}
