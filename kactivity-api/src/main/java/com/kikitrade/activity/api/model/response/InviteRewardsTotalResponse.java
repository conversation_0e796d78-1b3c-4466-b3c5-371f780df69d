package com.kikitrade.activity.api.model.response;

import com.kikitrade.activity.api.model.activity.InviteRewardDTO;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 邀请奖励总览返回对象
 * <AUTHOR>
 * @create 2022/12/13 2:35 下午
 * @modify
 */
@Data
@Builder
public class InviteRewardsTotalResponse implements Serializable {
    private ActivityResponseCode code;
    private InviteRewardDTO reward;
}
