package com.kikitrade.activity.api.model.request;

import com.kikitrade.activity.model.constant.ActivityConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/12/21 10:41 上午
 * @modify
 */
@Data
public class InviteRewardsSumRequest implements Serializable {
    private String customerId;
    private List<ActivityConstant.RewardBusinessType> rewardsType;
    private String saasId;
}
