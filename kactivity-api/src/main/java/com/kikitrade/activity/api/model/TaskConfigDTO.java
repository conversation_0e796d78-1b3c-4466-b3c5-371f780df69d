package com.kikitrade.activity.api.model;

import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.domain.Award;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/6 11:44
 */
@Data
@Slf4j
public class TaskConfigDTO implements Serializable {
    private String taskId;
    private String groupId;
    private Boolean isGroup;
    private String title;
    private Map<String, String> titleMap;
    private String desc;
    private Map<String, String> descMap;
    private String labelName;
    private String labelColor;
    /**
     * 任务图片
     */
    private Map<String, String> image;
    /**
     * 任务所属平台
     */
    private String saasId;
    /**
     * 任务状态
     */
    private ActivityConstant.CommonStatus status;
    /**
     * 任务开始时间
     */
    private Long startTime;
    /**
     * 任务结束时间
     */
    private Long endTime;
    /**
     * 任务事件code
     */
    private String code;
    private String showCode;
    /**
     * 任务周期内总次数，小于0表示无上限
     */
    private Integer limit;

    private Map<String, Integer> limitMap;
    /**
     * 奖励频率
     */
    private Integer rewardFrequency;
    /**
     * 任务周期
     */
    private ActivityTaskConstant.TaskCycleEnum cycle;
    /**
     * 进度计算方式
     */
    private ActivityTaskConstant.ProgressTypeEnum progressType;
    /**
     * 奖励方式
     */
    private ActivityTaskConstant.RewardForm rewardForm;
    /**
     * 发放方式
     */
    private ActivityTaskConstant.ProvideType provideType;
    /**
     * 任务详情页
     */
    private String url;
    /**
     * 任务授权页
     */
    private String connectUrl;
    private String connectUrlPc;
    /**
     * 奖品
     */
    private Map<String, List<Award>> reward;
    /**
     * 子任务
     */
    private List<String> subTaskId;
    /**
     * 任务排序
     */
    private Integer order;
    /**
     * 任务其他属性
     */
    private Map<String, String> attr;
    /**
     * 任务url domain
     */
    private String domain;
    /**
     * 允许的最低vip等级
     * 0：注册用户
     * 1：1级会员
     * 0+：大于等于注册用户
     * 1+：大于等于1级会员
     */
    private String vipLevel;
    /**
     * 前端需要显示的按钮，0:不显示 1:go 2:verify，默认0
     */
    private Integer btn;
    /**
     * 任务描述下链接
     */
    private Map<String, String> link;

    private Boolean showProgress;

    private Boolean showList;
    private String ledgerTitle;
    private String position;
    private String channel;

    private Boolean lastPost;

    private String clientType;
    /**
     * 任务免verify，直接完成
     */
    private Boolean skipVerification;

    /**
     * @see
     */
    private String type;
    /**
     * el表达式，(表达式不为空且结果为true)||(表达式为空)则继续执行任务
     */
    private String checkReward;
    private String postTaskId;
    private Integer postTaskReward;
    private String postTaskDesc;
    private String postTaskCode;

    private Boolean callNotify;

    private Boolean hiddenTaskCompleted;
    private Boolean onlyVerifySocial = false;

    /* ============= 上面为配置，下面为查询转换 ================= */
    private List<TaskConfigDTO> subTask;
    private List filters;
    private Boolean callRegister;
    private String taskStatusCondition;

    public Integer getLimit(String vipLevel){
        if(limitMap == null){
            return 0;
        }
        if(vipLevel == null){
            if(limitMap.get("NORMAL") == null){
                return 0;
            }
            return limitMap.get("NORMAL") < 0 ? Integer.MAX_VALUE : limitMap.get("NORMAL");
        }
        return limitMap.get(vipLevel) == null ? 0 : (limitMap.get(vipLevel) < 0 ? Integer.MAX_VALUE : limitMap.get(vipLevel));
    }
}
