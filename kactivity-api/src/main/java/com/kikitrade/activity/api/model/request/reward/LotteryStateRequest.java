package com.kikitrade.activity.api.model.request.reward;

import lombok.Data;

import java.io.Serializable;

/**
 * 获取用户抽奖状态请求
 * 用于查询用户在抽奖活动中的所有状态信息
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class LotteryStateRequest implements Serializable {

    /**
     * 用户ID (通过Token获取，可选)
     */
    private String userId;

    /**
     * SaaS ID
     */
    private String saasId;

    /**
     * 奖池编码（可选，不传则返回所有奖池状态）
     */
    private String prizePoolCode;
}
