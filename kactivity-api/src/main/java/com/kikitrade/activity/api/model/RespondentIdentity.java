package com.kikitrade.activity.api.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc Respondent Identity
 * @date 2024/8/29 9:24
 */
@Data
public class RespondentIdentity implements Serializable {

    @Schema(name = "saasId", description = "saasId")
    private String saasId;

    @Schema(name = "cid", description = "cid")
    private String cid;

    @Schema(name = "uid", description = "uid")
    private String uid;

    @Schema(name = "vipLevel", description = "vipLevel")
    private String vipLevel;

    @Schema(name = "authorization", description = "authorization")
    private String authorization;
}
