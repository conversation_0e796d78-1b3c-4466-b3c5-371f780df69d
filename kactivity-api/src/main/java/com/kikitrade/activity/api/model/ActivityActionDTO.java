package com.kikitrade.activity.api.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2021/9/7 9:15 下午
 * @modify
 */
@Data
public class ActivityActionDTO implements Serializable {

    //活动id
    private Integer activity_id;

    //action id
    private Integer action_id;

    //action_name for redis use
    private String action_name;

    //操作优先级，哪些操作优先执行
    private Integer priority;

    //0-失效 1-生效
    private Integer status;

    private String params;

}
