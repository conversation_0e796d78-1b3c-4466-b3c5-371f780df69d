package com.kikitrade.activity.api.exception;

import lombok.Getter;

@Getter
public enum ActivityExceptionType {

    ACCESS_PROHIBITED("1000", "access_prohibited"),
    OPERATION_TYPE_CHECK_FAILED("1001", "operation_type_check_failed"),
    NO_DATA_FOUND("1002", "no_data_found"),
    CAN_NOT_BE_NULL("1003", "{} can_not_be_null"),
    CAN_NOT_BE_DELETE("1004", "{} can_not_be_delete"),
    INCORRECT_STATUS("1005", "{} incorrect_status"),
    SKIP_EXECUTION("1006", "skip_execution"),
    TASK_PROCESSING("1007", "task_processing"),
    ALREADY_EXISTS("1008", "already_exists"),
    KOL_EXTRA_REWARD_CONFIG_RELOAD_FAILED("1009", "kol_extra_reward_config_reload_failed"),

    LOTTERY_PRODUCT_LIMIT("1010", "lottery.product.limit"),
    LOTTERY_PROCESS_FAIL("1011", "lottery.process.fail"),   // 抽奖失败
    LOTTERY_INSUFFICIENT_BALANCE("1012", "lottery.insufficient.balance"),//余额不足

    ORDER_PRICE_CHANGE("2001", "order.price.change"),
    STOCK_NOT_ENOUGH("2002", "stock.not.enough"),
    POINT_NOT_ENOUGH("2003", "point.not.enough"),
    ADDRESS_NOT_EXIST("2004", "address.not.exist"),

    AUTH_CODE_INVALID("2005", "auth.code.invalid"),
    AUTH_OTHER("2006", "auth.other"),

    CLAIM_REPEAT("3001", "claim.repeat"),

    EXECUTION_SUCCEED("0000", "success"),
    SYSTEM_PARAMETER_INVALID("9003", "system.parameter.invalid"),

    UNKNOWN_REASON("9999", "other_exception");
    private String code;
    private String message;

    ActivityExceptionType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getParaMsg(String target) {
        return this.message.replace("{}", target);
    }

}
