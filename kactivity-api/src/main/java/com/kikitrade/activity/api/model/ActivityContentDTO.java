package com.kikitrade.activity.api.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2021/9/7 9:18 下午
 * @modify
 */
@Data
public class ActivityContentDTO implements Serializable {

    //活动ID（主键）
    private Integer activityId;  //id

    //语言版本
    private String locale;

    //活动名称
    private String name;

    //活动链接
    private String url;

    //活动详情
    private String content;
}
