package com.kikitrade.activity.api;

import com.kikitrade.activity.api.exception.ActivityException;
import com.kikitrade.activity.api.model.request.GoodsOrderRequest;
import com.kikitrade.activity.api.model.response.GoodsDetailResponse;
import com.kikitrade.activity.api.model.response.GoodsResponse;
import com.kikitrade.framework.common.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/12/28 10:43
 */
public interface RemoteGoodsService {

    List<GoodsResponse> goodsList(int offset, int limit, String saasId, String exclude);

    GoodsDetailResponse goods(String goodsId);

    List<GoodsResponse> findBySceneCode(String sceneCode);

}
