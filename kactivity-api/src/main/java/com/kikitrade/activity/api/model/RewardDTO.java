package com.kikitrade.activity.api.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * RewardDTO model
 *
 * <AUTHOR>
 * @create 2021/9/8 11:05 上午
 * @modify
 */
@Data
public class RewardDTO implements Serializable {

    private String customerId;

    private String userName;

    private Integer activityId;

    private Date rewardTime;

    private String invitee;

    private String inviteeUserName;

    private Integer type;

    private String currency;

    private BigDecimal amount;

    private boolean autoExchange = false;

    private String secondStep;

    private String quoteCurrency;

    private BigDecimal quoteQuantity;

    private String remark;
    private Date created = new Date();
    private Date updated = new Date();
    private String businessId;
    private String orderNum;

    private String symbol;

    private String transactionId;

}
