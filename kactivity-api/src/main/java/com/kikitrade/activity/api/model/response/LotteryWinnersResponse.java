package com.kikitrade.activity.api.model.response;

import com.kikitrade.activity.api.model.request.LotteryWinnersRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 中奖者列表响应对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LotteryWinnersResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    List<LotteryWinner> lotteryWinners;

    /**
     * 中奖者信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LotteryWinner implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 用户地址，用于标识中奖者
         */
        private String poolCode;

        /**
         * 中奖排名
         */
        private List<LotteryWinnersRequest.Winner> poolWinners;
    }
}