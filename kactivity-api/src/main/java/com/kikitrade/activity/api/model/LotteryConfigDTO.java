package com.kikitrade.activity.api.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/21 14:16
 */
@Data
public class LotteryConfigDTO implements Serializable {
    private String id;
    private String type;
    private String code;
    private Integer amount;
    private Long startTime;
    private Long endTime;
    private Integer limitTimes;
    private String limitUnit;
    private String currency;
    private String nodes;
    private String awards;
    private String status;
    private String saasId;
    private Boolean isCumulate;
    private String cumulateInfo;
}
