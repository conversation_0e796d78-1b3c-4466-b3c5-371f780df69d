package com.kikitrade.activity.api.model.response;

import com.kikitrade.activity.api.model.activity.InviteRewardDTO;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 邀请奖励明细返回对象
 * <AUTHOR>
 * @create 2022/12/13 3:12 下午
 * @modify
 */
@Data
@Builder
public class InviteRewardsResponse implements Serializable {
    private ActivityResponseCode code;
    private Map<ActivityConstant.RewardBusinessType, InviteRewardDTO> rewards;
}
