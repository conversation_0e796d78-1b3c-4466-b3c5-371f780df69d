package com.kikitrade.activity.api.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/10/31 14:01
 */
@Data
public class CheckInStatusDTO implements Serializable {

    /**
     * 非连续签到今天的奖励
     */
    private String todayReward;
    private String todayRewardType;
    private String title;
    private String desc;
    private String icon;
    //今日是否签到
    private Boolean checkIn = false;
    //签到时间
    private Long checkInTime;
    //
    private Integer checkInDays = 0;
    //签到奖励
    private List<AwardDTO> checkInReward = null;
}
