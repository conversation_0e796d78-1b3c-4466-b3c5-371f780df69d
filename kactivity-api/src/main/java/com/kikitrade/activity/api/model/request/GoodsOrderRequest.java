package com.kikitrade.activity.api.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/1/3 18:11
 */
@Data
public class GoodsOrderRequest implements Serializable {

    private String customerId;
    private String goodsId;
    private String goodsType;
    private Map<String,String> address;
    private Integer quality;
    private BigDecimal totalPrice;
    private Long timestamp;
    private String saasId;
}
