package com.kikitrade.activity.api.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/28 19:24
 */
@Data
public class QuestionAnswerVO implements Serializable {

    @Schema(name = "id", description = "question id")
    private String id;

    @Schema(name = "title", description = "question title")
    private String title;

    @Schema(name = "option1", description = "question option1")
    private String option1;

    @Schema(name = "option2", description = "question option2")
    private String option2;

    @Schema(name = "userOption", description = "userOption")
    private String userOption;

}
