package com.kikitrade.activity.api.model.draw;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class ExchangeTicketsDTO implements Serializable {
    private String userId;
    private String saasId;
    private String prizePoolCode;
    private String exchangeType;
    private String transferOutAssetType;
    private String optionId;
    private String transferOutAmount;
    private Integer transferInCount;
    private String desc;
    private String businessId;
    private String businessType;
    private String transferInAssetType;
}