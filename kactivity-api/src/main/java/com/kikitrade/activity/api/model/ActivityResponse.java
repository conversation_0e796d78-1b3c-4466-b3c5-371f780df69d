package com.kikitrade.activity.api.model;

import com.kikitrade.framework.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * ActivityResponse model
 *
 * <AUTHOR>
 * @create 2021/9/6 7:55 下午
 * @modify
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Deprecated
public class ActivityResponse extends Response {
    private Object obj;
    private String msg;
}
