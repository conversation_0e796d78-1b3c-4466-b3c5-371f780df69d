package com.kikitrade.activity.api.model.activity;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Builder
@Data
public class RewardVO implements Serializable {

    /**
     * 奖励金额
     */
    private String amount;

    /**
     * 奖励币种
     */
    private String currency;

    /**
     * 用户名，电话 or 邮箱
     */
    private String userName;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 发奖时间
     */
    private Date rewardTime;

    /**
     * 获奖业务来源
     */
    private String businessType;

    /**
     * 奖励类型
     */
    private String rewardType;

    /**
     * 其他扩展信息
     */
    private Map<String,String> extendParam;
}
