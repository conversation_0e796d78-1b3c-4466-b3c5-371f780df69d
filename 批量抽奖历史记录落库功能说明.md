# 批量抽奖历史记录落库功能实现

## 功能概述

本次实现了批量抽奖的历史记录落库逻辑，严格按照业务需求文档中的"内存中准备，一次性落库"原则，确保操作的高性能和数据的一致性。

## 核心原则

**内存中准备，一次性落库** - 这个原则是为了保证操作的高性能和数据的一致性。

## 实现的组件

### 1. DrawHistoryBuilder 接口
- **位置**: `kactivity-dal/src/main/java/com/kikitrade/activity/dal/tablestore/builder/DrawHistoryBuilder.java`
- **功能**: 定义抽奖历史记录的数据访问层接口
- **主要方法**:
  - `insert(DrawHistory)` - 单条插入
  - `batchInsert(List<DrawHistory>)` - 批量插入（核心方法）
  - `findByUserId()` - 按用户ID查询
  - `findByBatchTransactionId()` - 按批量交易ID查询
  - `findByUpstreamTransactionId()` - 按上游交易ID查询

### 2. DrawHistoryBuilderImpl 实现类
- **位置**: `kactivity-dal/src/main/java/com/kikitrade/activity/dal/tablestore/builder/impl/DrawHistoryBuilderImpl.java`
- **功能**: 实现抽奖历史记录的数据访问逻辑
- **特点**:
  - 继承 `WideColumnStoreBuilder<DrawHistory>`
  - 使用阿里云OTS的 `batchPutRow` 方法实现批量插入
  - 支持原子性操作，要么全部成功，要么全部失败
  - 完善的异常处理和日志记录

### 3. 修改后的批量抽奖逻辑
- **位置**: `kactivity-service/src/main/java/com/kikitrade/activity/service/remote/impl/RewardPlatformServiceImpl.java`
- **修改的方法**: `drawBatch(DrawBatchRequest)`

## 详细业务逻辑步骤

### 第1步：准备阶段（在循环抽奖之前）

1. **生成 batch_transaction_id**:
   ```java
   String batchTransactionId = "draw-batch-" + UUID.randomUUID().toString();
   ```
   - 为本次批量操作生成全局唯一ID
   - 用于"捆绑"本次批量操作中产生的所有独立抽奖记录

2. **初始化内存列表**:
   ```java
   List<DrawHistory> historyRecordsToSave = new ArrayList<>();
   ```
   - 在内存中创建空列表，用于存放即将生成的DrawHistory对象

### 第2步：循环抽奖与记录生成（核心循环）

系统根据前端上送的 `drawCount`，执行循环：

```java
for (int i = 0; i < request.getDrawCount(); i++) {
    // A. 执行单次抽奖逻辑
    List<PrizeConfig> singleDrawResult = probabilityAlgorithmService.batchDraw(
        dynamicPool, prizePool.getProbabilityStrategy(), fallbackPrize, 1);
    
    PrizeConfig prize = singleDrawResult.get(0);
    
    // B. 生成 event_id
    String eventId = "evt-" + UUID.randomUUID().toString();
    
    // C. 构建历史记录对象
    DrawHistory drawHistory = new DrawHistory();
    drawHistory.setUserId(request.getUserId());
    drawHistory.setEventId(eventId);
    drawHistory.setUpstream_transaction_id(batchTransactionId);
    drawHistory.setBatchTransactionId(batchTransactionId);
    drawHistory.setPrizePoolCode(request.getPrizePoolCode());
    drawHistory.setPrizeId(Long.valueOf(prize.getConfigId()));
    drawHistory.setPrizeName(prize.getPrizeName());
    drawHistory.setDrawTime(currentTime);
    drawHistory.setPrizeType(prize.getPrizeType());
    drawHistory.setPrizeQuantity(prize.getRewardQuantity());
    drawHistory.setPrizeItemId(prize.getRewardItemId());
    drawHistory.setSaasId(request.getSaasId());
    
    // D. 添加到内存列表
    historyRecordsToSave.add(drawHistory);
}
```

### 第3步：数据库落库（在循环结束之后）

**执行时机**: 当且仅当整个抽奖循环成功完成，没有任何异常抛出时，才执行此步骤。

```java
// 批量插入历史记录
boolean historyInsertResult = drawHistoryBuilder.batchInsert(historyRecordsToSave);

if (!historyInsertResult) {
    throw new ActivityException("HISTORY_INSERT_FAILED", "抽奖历史记录保存失败");
}
```

**执行方式**:
- 调用阿里云OTS的 `BatchWriteRow` 接口
- 将整个 `historyRecordsToSave` 列表作为参数，一次性提交给OTS
- 配置为原子操作，列表中的所有行要么全部写入成功，要么全部失败

### 第4步：错误处理

#### 如果在循环中（第2步）发生失败:
- 循环会中断，程序会抛出异常
- 第3步的数据库落库操作将永远不会被执行
- 内存中的 `historyRecordsToSave` 列表会被垃圾回收
- 数据库中没有留下任何本次操作的痕迹
- API向前端返回失败

#### 如果在落库时（第3步）发生失败:
- 整个批量写入都会失败
- 数据库中同样没有留下任何本次操作的痕迹
- API向前端返回失败
- 用户可以安全地重试整个批量抽奖操作

## 数据模型

### DrawHistory 表结构
- **user_id** (分区键): 用户ID
- **event_id** (排序键): 抽奖事件唯一ID
- **upstream_transaction_id**: 上游交易ID
- **batch_transaction_id**: 批量抽奖的唯一交易ID
- **prize_pool_code**: 抽奖时所在的奖池编码
- **prize_id**: 中奖的奖品ID
- **prize_name**: 中奖的奖品名称
- **draw_time**: 抽奖时间
- **prize_type**: 奖品类型
- **prize_quantity**: 奖品数量
- **prize_item_id**: 奖品物品ID
- **create_time**: 创建时间
- **saas_id**: SaaS ID

## 测试

### 单元测试
- **DrawHistoryTest**: 测试DrawHistoryBuilder的基本功能
- **DrawBatchIntegrationTest**: 测试完整的批量抽奖流程

### 测试用例
1. 批量插入抽奖历史记录测试
2. 单条插入抽奖历史记录测试
3. 完整批量抽奖流程集成测试
4. 错误处理机制测试

## 性能优势

1. **网络请求优化**: 将N次独立的写入操作合并为1次网络请求，极大降低网络延迟
2. **数据库压力减少**: 减少对数据库的并发访问压力
3. **原子性保证**: BatchWriteRow支持原子操作，防止部分写入的脏数据问题
4. **内存效率**: 在内存中准备数据，避免频繁的数据库交互

## 使用方式

批量抽奖历史记录落库功能已集成到现有的 `drawBatch` 接口中，无需额外的API调用。当用户调用批量抽奖接口时，历史记录会自动保存。

```java
// 调用批量抽奖接口
DrawBatchRequest request = new DrawBatchRequest();
request.setUserId("user123");
request.setSaasId("saas456");
request.setPrizePoolCode("POOL_001");
request.setDrawCount(10);

DrawBatchResponse response = rewardPlatformService.drawBatch(request);
// 历史记录已自动保存到数据库
```

## 注意事项

1. 确保DrawHistory表已在阿里云OTS中创建
2. 确保相关的搜索索引已配置
3. 批量插入的记录数量建议控制在合理范围内（如100条以内）
4. 异常情况下，用户的抽奖券不会被消耗，可以安全重试
