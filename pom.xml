<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.kikitrade</groupId>
        <artifactId>kiki-framework</artifactId>
        <version>2.5.0-SNAPSHOT</version>
    </parent>
    <groupId>com.kikitrade</groupId>
    <artifactId>kactivity</artifactId>
    <version>3.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>kactivity</name>
    <description>The parent entry of all the kiki activity modules</description>
    <properties>
        <skipChecks>true</skipChecks>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.property.path>..</project.property.path>
        <spring.version>5.2.8.RELEASE</spring.version>
        <com.alibaba.nacos.version>2.2.1-RC</com.alibaba.nacos.version>
        <api.version>1.0.0-SNAPSHOT</api.version>
        <kactivity.version>3.0.0-SNAPSHOT</kactivity.version>
        <dtm.version>2.3.0-SNAPSHOT</dtm.version>
        <kaccounting.version>2.2.2-SNAPSHOT</kaccounting.version>
        <kfinancing.version>1.0.0-SNAPSHOT</kfinancing.version>
        <kpay.version>2.0.0-SNAPSHOT</kpay.version>
        <kmarket.version>2.0.0-SNAPSHOT</kmarket.version>
        <ktrade.version>2.0.0-SNAPSHOT</ktrade.version>
    </properties>
    <modules>
        <module>kactivity-api</module>
        <module>kactivity-web</module>
        <module>kactivity-service</module>
        <module>kactivity-dal</module>
        <module>kactivity-model</module>
    </modules>
    <dependencyManagement>

        <dependencies>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kactivity-api</artifactId>
                <version>${kactivity.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kactivity-service</artifactId>
                <version>${kactivity.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kactivity-web</artifactId>
                <version>${kactivity.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kactivity-model</artifactId>
                <version>${kactivity.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kactivity-dal</artifactId>
                <version>${kactivity.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kaccounting-api</artifactId>
                <version>${kaccounting.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kfinancing-api</artifactId>
                <version>${kfinancing.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kpay-api</artifactId>
                <version>${kpay.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kmarket-client</artifactId>
                <version>${kmarket.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aliyun</groupId>
                        <artifactId>aliyun-java-sdk-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kmarket-common</artifactId>
                <version>${kmarket.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>ktrade-api</artifactId>
                <version>${ktrade.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.persistence</groupId>
                <artifactId>javax.persistence-api</artifactId>
                <version>2.2</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${com.alibaba.nacos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-api</artifactId>
                <version>${com.alibaba.nacos.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.15.1</version>
            </dependency>

            <!-- oss使用aliyun-core 3.4.0不能升级为最新版本 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>4.5.10</version>
            </dependency>
            <dependency>
                <groupId>org.jdom</groupId>
                <artifactId>jdom2</artifactId>
                <version>2.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>31.0.1-jre</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>ksocial-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kiki-odps-spring-boot-starter</artifactId>
                <version>2.3.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>io.github.mouzt</groupId>
                <artifactId>bizlog-sdk</artifactId>
                <version>3.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kquota-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kquota-common</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>3.25.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.yaml</include>
                    <include>**/*.json</include>
                    <include>**/*.txt</include>
                    <include>**/*.png</include>
                    <include>log4j2.xml</include>
                    <include>dubbo.json</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.sql</include>
                    <include>**/*/*.xml</include>
                    <include>**/*.tld</include>
                    <include>**/*.ftl</include>
                    <include>**/*.vm</include>
                    <include>META-INF/**</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.sql</include>
                    <include>**/*.xml</include>
                    <include>**/*.tld</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>regex-property</id>
                        <goals>
                            <goal>regex-property</goal>
                        </goals>
                        <configuration>
                            <name>maven.test.skip</name>
                            <value>${project.artifactId}</value>
                            <regex>(kactivity-api)|(kactivity-dal)|(kactivity-model)|(kactivity-service)|(kactivity-web)</regex>
                            <replacement>true</replacement>
                            <failIfNoMatch>false</failIfNoMatch>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <project.profile.id>local</project.profile.id>
            </properties>
            <build>
                <filters>
                    <filter>${project.property.path}/config-local.properties</filter>
                </filters>
            </build>
            <repositories>
                <repository>
                    <id>snapshot</id>
                    <!--<url>http://nexus.marathon.l4lb.thisdcos.directory:8081/repository/maven-snapshots/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/maven-snapshots/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>public</id>
                    <!--<url>http://*************:8081/nexus/groups/public/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/maven-public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>releases</id>
                    <url>https://nexus.dipbit.xyz/repository/maven-releases/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </repository>

                <repository>
                    <id>thirdparty</id>
                    <url>https://nexus.dipbit.xyz/repository/thirdparty</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>bintray.com</id>
                    <!--<url>https://dl.bintray.com/spark-packages/maven/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/bintray.com/</url>
                </repository>
                <repository>
                    <id>maven-public</id>
                    <url>https://nexus.dipbit.xyz/repository/maven-public/</url>
                </repository>
                <repository>
                    <id>jahia.org</id>
                    <!-- <url>http://maven.jahia.org/maven2</url>-->
                    <url>https://nexus.dipbit.xyz/repository/jahia.org/</url>
                </repository>

                <repository>
                    <id>clojars.org</id>
                    <url>https://nexus.dipbit.xyz/repository/clojars.org/</url>
                    <!--<url>http://clojars.org/repo/</url>-->
                </repository>

                <repository>
                    <id>cloudera</id>
                    <!--<url>https://repository.cloudera.com/artifactory/cloudera-repos/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/cloudera/</url>
                </repository>

                <repository>
                    <id>spring-jcenter-cache</id>
                    <!--<url>http://repo.spring.io/jcenter-cache/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/spring-jcenter-cache</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>repo.maven.apache.org</id>
                    <!--<url>https://dl.bintray.com/spark-packages/maven/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/repo.maven.apache.org</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>spark-thirdpart</id>
                    <!--<url>https://dl.bintray.com/spark-packages/maven/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/spark-thirdpart</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>jitpack.io</id>
                    <url>https://jitpack.io</url>
                </repository>
                <repository>
                    <id>sonatype-nexus-staging</id>
                    <name>Sonatype Nexus Staging</name>
                    <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <distributionManagement>
                <repository>
                    <id>releases</id>
                    <name>Local Nexus Repository</name>
                    <url>https://nexus.dipbit.xyz/repository/maven-releases/</url>
                </repository>
                <snapshotRepository>
                    <id>snapshot</id>
                    <name>Local Nexus Repository</name>
                    <url>https://nexus.dipbit.xyz/repository/maven-snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
            <pluginRepositories>
                <pluginRepository>
                    <id>maven-public</id>
                    <url>https://nexus.dipbit.xyz/repository/maven-public/</url>
                </pluginRepository>
                <pluginRepository>
                    <id>repo.maven.apache.org</id>
                    <url>https://nexus.dipbit.xyz/repository/repo.maven.apache.org</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>
</project>
