package com.kikitrade.activity.model.exception;

import com.kikitrade.activity.model.response.ActivityResponseCode;
import lombok.Getter;

/**
 * Activity Base Exception
 *
 * <AUTHOR>
 * @create 2021/7/25 2:28 下午
 * @modify
 */
@Getter
public class ActivityException extends Exception {

    private ActivityResponseCode code;

    public ActivityException() {
    }

    public ActivityException(ActivityResponseCode code) {
        super(code.name());
        this.code = code;
    }

    public ActivityException(ActivityResponseCode code, Throwable cause) {
        super(cause);
        this.code = code;
    }

    public ActivityException(ActivityResponseCode code, String message) {
        super(message);
        this.code = code;
    }

    public ActivityException(Throwable cause) {
        super(cause);
    }
}
