package com.kikitrade.activity.model.constant;

/**
 * 兑换类型常量
 * 用于定义不同的抽奖券兑换类型
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class ExchangeTypeConstant {

    /**
     * 个人联盟积分兑换抽奖券
     */
    public static final String EXCHANGE_TICKETS_AND_WAITING_TRANSFER = "EXCHANGE_TICKETS_AND_WAITING_TRANSFER";

    /**
     * 普通积分兑换抽奖券
     */
    public static final String EXCHANGE_TICKETS_NORMAL = "EXCHANGE_TICKETS_NORMAL";

    /**
     * 金币兑换抽奖券
     */
    public static final String EXCHANGE_TICKETS_GOLD = "EXCHANGE_TICKETS_GOLD";

    /**
     * VIP专属兑换
     */
    public static final String EXCHANGE_TICKETS_VIP = "EXCHANGE_TICKETS_VIP";

    /**
     * 活动特殊兑换
     */
    public static final String EXCHANGE_TICKETS_ACTIVITY = "EXCHANGE_TICKETS_ACTIVITY";

    /**
     * 消耗抽奖券
     */
    public static final String EXCHANGE_TICKETS_CONSUME = "EXCHANGE_TICKETS_CONSUME";

    private ExchangeTypeConstant() {
        // 工具类，禁止实例化
    }
}
