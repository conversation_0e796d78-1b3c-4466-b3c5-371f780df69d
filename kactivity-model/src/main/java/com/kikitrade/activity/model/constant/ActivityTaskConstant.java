package com.kikitrade.activity.model.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/2/5 13:57
 */
public class ActivityTaskConstant {


    /**
     * 任务周期
     */
    @Getter
    public enum TaskCycleEnum {
        once,
        once_season,
        daily,
        weekly,
        monthly,
        once_daily
    }

    public enum ProgressTypeEnum{
        series,//进度连续计算
        add//进度累加计算
    }

    @Getter
    public enum ProvideType{
        auto(0, "自动发放"),//贯穿 task-reward-kmember
        claim_reward(1, "提交奖励后领取"),//贯穿 task-reward
        claim_reward_confirm(3, "提交奖励后等待第三方确认"),//贯穿 task-reward
        claim_task(2, "任务完成领取"),//贯穿 task
        ;

        private int code;
        private String desc;

        ProvideType(int code, String desc){
            this.code = code;
            this.desc = desc;
        }
    }

    @Getter
    public enum RewardForm{

        none(-1, "无奖励"),
        fixed(0, "指定进度对奖励频率的余数"),
        fixed_random(1, "在 fixed 的基础上，支持多个奖励随机中一个")
        ;

        private int code;
        private String desc;

        RewardForm(int code, String desc){
            this.code = code;
            this.desc = desc;
        }
    }

    @Getter
    public enum TaskCodeEnum{

        connect_x,
        like_post_x,
        comment_post_x,
        retweet_post_x,
        follow_x,
        reply_post_x,
        subject_post_x,
        name_x,

        connect_dc,
        connect_discord,
        join_dc,

        join_game,
        play_game,

        external_reward,

        osp_profile_create,
        x_authorize,

        boost_tg_channel,
        tg_premium,

        osp_connect_tg,
        osp_callback,
        join_tg,
        wallet_bind,
        member_castle_level,
        create_dapp,
        create_deek_profile,
        connect_email,
        connect_facebook,
        connect_google,
        connect_twitter,
        connect_instagram,
        connect_line,
        connect_tiktok,
        connect_telegram,
        connect_sahara,
        connect_okx
    }

    @Getter
    public enum TaskConfigScope{
        SUB_TASK, FILTER
    }

    @Getter
    public enum OpenSocialEnum{
        twitter, x, discord
    }

    /**
     * 支持服务端验证的任务类型
     */
    public enum ServerVerifyScene {
        follow_x; // twitter关注

        public static ServerVerifyScene of(String name) {
            return Arrays.stream(values()).filter(s -> StringUtils.equals(s.name(), name)).findFirst().orElse(null);
        }
    }

    @Getter
    public enum TaskType{
        NORMAL, STATIS
    }
}
