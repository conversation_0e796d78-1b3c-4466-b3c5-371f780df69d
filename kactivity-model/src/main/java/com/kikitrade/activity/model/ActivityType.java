package com.kikitrade.activity.model;

import com.kikitrade.activity.model.constant.ActivityConstant;

/**
 * <AUTHOR>
 * @create 2021/9/8 9:10 上午
 * @modify
 */
//1-注册送币
//2-推荐返佣
//3-充值瓜分
//4-充值随机
//5-交易免手续费 (会员交易鼓励金)
//6-理财贴息
//7-充值免提币手续费
//8-邀请送币
//9-随存随取
//10-绑定手机送币
//11-绑定手机给邀请人送币
//12-折扣购买
//13-注册送体验金
//14-三级推荐送体验金
//15-开宝箱
//16-交易时给邀请人返佣金（鼓励金，手续费）
//18-邀请拉新奖励
//21-客户法币入金单笔奖励活动
//22-客户法币入金累计奖励活动
//23-被邀请人kyc通过邀请奖励金
public enum ActivityType {
    GIFT(1, "gift", ActivityConstant.RuleEvent.REGISTER, true), // register
    REBATE(2, "rebate", ActivityConstant.RuleEvent.REBATE, false),
    RECHARGE_PARTITION(3, "recharge_partition", ActivityConstant.RuleEvent.RECHARGE_PARTITION, true),
    RECHARGE_RANDOM_REWARD(4, "recharge_random_reward", ActivityConstant.RuleEvent.DEPOSIT, false), // deposit
    RETURN_FEE(5, "return_fee", ActivityConstant.RuleEvent.RETURN_FEE, false),
    INTEREST_DISCOUNT(6, "interest_discount", ActivityConstant.RuleEvent.INTEREST_DISCOUNT, false),
    WITHDRAW_LIMIT_REWARD(7, "withdraw_limit_reward", ActivityConstant.RuleEvent.DEPOSIT, false), // deposit
    INVITE_GIFT(8, "invite_gift", ActivityConstant.RuleEvent.REGISTER, false), // register
    CURRENT_DISCOUNT(9, "current_discount", ActivityConstant.RuleEvent.CURRENT_DISCOUNT, false),
    BIND_MOBILE_GIFT(10, "bind_mobile_gift", ActivityConstant.RuleEvent.BIND_MOBILE, true), // bind_mobile
    INVITE_BIND_MOBILE_GIFT(11, "invite_bind_mobile_gift", ActivityConstant.RuleEvent.BIND_MOBILE, false), // bind_mobile
    DISCOUNT_BUY(12, "discount_buy", ActivityConstant.RuleEvent.DISCOUNT_BUY, false),
    REGISTER_TREASURE(13, "register_treasure", ActivityConstant.RuleEvent.REGISTER, false),// register
    INVITE_TREASURE(14, "invite_treasure", ActivityConstant.RuleEvent.DEPOSIT, false), // deposit
    LUCKY_BOX(15, "lucky_box", ActivityConstant.RuleEvent.LUCKY_BOX, false),
    INVITE_TRADE_REBATE(16, "invite_trade_rebate", ActivityConstant.RuleEvent.REBATE, false),
    KIKI_TRADE_REBATE(17, "kiki_rebate", ActivityConstant.RuleEvent.REBATE, false),
    KIKI_INVITE_GIFT(18, "legacy_kiki_invite_gift", ActivityConstant.RuleEvent.KIKI_REBATE, true),
    KIKI_FIAT_DEPOSIT_GIFT(19, "kiki_fiat_deposit_gift", ActivityConstant.RuleEvent.KIKI_FIAT_DEPOSIT, true),
    KIKI_SLOT_INVITE_GIFT(20, "kiki_slot_invite_gift", ActivityConstant.RuleEvent.REGISTER, true),
    KIKI_FIAT_DEPOSIT_SINGLE(21, "kiki_fiat_deposit_single", ActivityConstant.RuleEvent.KIKI_FIAT_DEPOSIT, false, ActivityConstant.SecondStep.AUTO_EXCHANGE),
    KIKI_FIAT_DEPOSIT_TOTAL(22, "kiki_fiat_deposit_total", ActivityConstant.RuleEvent.KIKI_FIAT_DEPOSIT_FINISH, true, ActivityConstant.SecondStep.AUTO_EXCHANGE),
    KIKI_INVITE_USER_DEPOSIT_SINGLE(23, "kiki_invite_fiat_deposit", ActivityConstant.RuleEvent.KIKI_FIAT_DEPOSIT, false, ActivityConstant.SecondStep.REWARD_INVITER);

    private int code;
    private String desc;
    private ActivityConstant.RuleEvent event;
    private boolean timesCheck;
    private ActivityConstant.SecondStep secondStep;

    ActivityType(int code, String desc, ActivityConstant.RuleEvent event, boolean timesCheck) {
        this.code = code;
        this.desc = desc;
        this.event = event;
        this.timesCheck = timesCheck;
    }

    ActivityType(int code, String desc, ActivityConstant.RuleEvent event, boolean timesCheck, ActivityConstant.SecondStep secondStep) {
        this.code = code;
        this.desc = desc;
        this.event = event;
        this.timesCheck = timesCheck;
        this.secondStep = secondStep;
    }

    public int getCode() {
        return code;
    }

    public ActivityConstant.SecondStep getSecondStep() {
        return secondStep;
    }

    public boolean needTimesCheck() {
        return timesCheck;
    }

    public ActivityConstant.RuleEvent getEvent() {
        return event;
    }

    public static ActivityType getType(int code) {
        for (ActivityType r : ActivityType.values()) {
            if (r.getCode() == code) {
                return r;
            }
        }
        return null;
    }
}
