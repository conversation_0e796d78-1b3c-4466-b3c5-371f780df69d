package com.kikitrade.activity.model.domain;

import lombok.Data;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/17 18:16
 */
@Data
public class Award implements Serializable {

    private String type;
    private String showAmount;
    private String amount;
    private String currency;
    private Integer index = 0;
    private String vipLevel;

    public String getAmount(String amount, Map<String, Object> param){
        if(param == null){
            return amount;
        }
        EvaluationContext context = new StandardEvaluationContext();
        context.setVariable("map", param);
        Double value = new SpelExpressionParser().parseExpression(amount)
                .getValue(context, Double.class);
        return value == null ? null : BigDecimal.valueOf(value).setScale(2, RoundingMode.HALF_DOWN).toString();
    }
}
