package com.kikitrade.activity.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 法币入金通用活动配置
 *
 * <AUTHOR>
 * @date 2021/01/18 18:13
 **/

@Data
@NoArgsConstructor
public class FiatDepositActivityConfig implements Serializable {

    private static final long serialVersionUID = -4825890686624512635L;

    //法币入金金额
    private BigDecimal fiatAmount;

    //法币入金币种
    private String fiatCurrency;

    //充值用户奖励价值
    private BigDecimal rewardValue;

    //充值用户奖励价值对应币种
    private String rewardCurrency;

    //自动兑换到账币种
    private String exchangeCurrency;

    //是否首次入金
    private boolean firstDeposit;

    //受邀者奖励价值
    private BigDecimal inviteeRewardValue;

    //受邀者奖励币种
    private String inviteeRewardCurrency;

    //受邀者自动兑换到账币种
    private String inviteeExchangeCurrency;

    //邀请人奖励价值
    private BigDecimal inviterRewardValue;

    //邀请人奖励币种
    private String inviterRewardCurrency;

    //邀请人自动兑换到账币种
    private String inviterExchangeCurrency;

}
