package com.kikitrade.activity.model;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Setter
@Getter
public class Result<T> implements Serializable {

    private boolean success;

    private Integer code;

    private String message;

    private T data;

    public Result(){
        this.success = true;
        this.message = "success";
        this.code = 200;
    }

    public Result(boolean success, String message){
        this.success = success;
        this.message = message;
    }

    public Result(boolean success, String message, T data){
        this.success = success;
        this.message = message;
        this.data = data;
    }

    public Result(ResultCode resultCode, String message){
        this.success = resultCode.isSuccess();
        this.code = resultCode.getCode();
        this.message = StringUtils.isBlank(message) ? resultCode.getMessage() : message;
    }

    public Result(ResultCode resultCode, String message, T data){
        this.success = resultCode.isSuccess();
        this.code = resultCode.getCode();
        this.message = StringUtils.isBlank(message) ? resultCode.getMessage() : message;
        this.data = data;
    }

    public static class Constant{
        public static String STATUS_NOTALLOW_AUDIT = "该批次当前状态为%s, 已不能进行%s操作。";
    }

    public static Result of(ResultCode resultCode){
        return new Result(resultCode, null);
    }

    public static Result of(ResultCode resultCode, String message){
        return new Result(resultCode, message);
    }

    public static <T> Result<T> of(ResultCode resultCode, String message, T data){
        return new Result(resultCode, message, data);
    }

    @Getter
    public enum ResultCode{
        SUCCESS(true,200, "success"),
        ACTIVITY_OFFLINE(false,401, "activity offline or activity not exist"),
        PARAM_INVALID(false, 402, "param invalid"),
        SYSTEM_ERROR(false,500, "system error"),
        CHECK_FAILED(false,1001, "check failed")
        ;
        private boolean success;
        private Integer code;
        private String message;

        ResultCode(boolean success, Integer code, String message){
            this.success = success;
            this.code = code;
            this.message = message;
        }
    }
}
