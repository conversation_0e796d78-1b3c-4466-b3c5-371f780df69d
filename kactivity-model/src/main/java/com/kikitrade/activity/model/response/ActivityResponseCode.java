package com.kikitrade.activity.model.response;

/**
 * ActivityResponseCode
 *
 * <AUTHOR>
 * @create 2022/3/9 11:53 上午
 * @modify
 */
public enum ActivityResponseCode {

    SUCCESS("0000", "success", true),
    INVALID_PARAMETER("4001", "invalid.parameter"),
    CUSTOMER_EMPTY("4002", "customer.empty"),
    CUSTOMER_INACTIVE("4003", "customer.inactive"),
    ACCOUNTING_ERROR("4004", "accounting.error"),
    REWARD_FAIL("4005", "reward.fail"),
    REWARD_FORBIDDEN("4006", "reward.forbidden"),
    TWITTER_API_ERROR("4007", "twitter.api.error"),

    TASK_EVENT_INVALID("5001", "task.event.invalid"),
    TASK_STATUS_COMPLETED("5002", "task.status.complete"),
    TASK_RETRY("5003", "task.retry"),
    TASK_EVENT_NOT_FOUND("5004", "task.event.not.found"),
    TASK_NOVICE_EXPIRED("5005", "task.novice.expired"),
    LOTTERY_LIMIT("5006", "lottery.limit.above"),
    TASK_EXPIRED("5007", "task.expired"),
    BLACK_LIST("5008", "black.list"),
    LOTTERY_REWARD_INVALID("6001", "lottery.reward.invalid"),
    LOTTERY_STATUS_INVALID("6002", "lottery.status.invalid"),

    // 系统错误
    SYSTEM_ERROR("9999", "system.error"),
    ACTIVITY_REWARD_EMPTY("6003", "activity.reward.empty"),
    ACCESS_TOKEN_EXPIRE("6004", "access.token.expire"),
    SOCIAL_VERIFY_ALREADY("6005", "social.verify.already"),
    TASK_PRE_CHECK_NOT_PASS("6006", "task.pre.check.not.pass"),
    ACTIVITY_HOT("6007", "activity.hot"),
    ACTIVITY_NOT_SUPPORT("6008", "activity.not.support"),
    SOCIAL_CALL_BACK_WAIT("6009", "social.call.back.wait"),

    CLAIM_REPEAT("6101", "claim.repeat"),
    CLAIM_CODE_INVALID("6102", "claim.code.invalid"),

    AUTH_CODE_INVALID("7001", "auth.code.invalid"),
    AUTH_REPEAT("7002", "auth.repeat"),
    AUTH_NO_SAME_LOGIN("7003", "auth.no.same.login"),
    AUTH_NO_SAME_LAST("7004", "auth.no.same.last"),
    NOT_SUPPORT_SCENE("7005", "not.support.scene"),
    TASK_VERIFY_NO_PASS("7006", "task.verify.no.pass"),
    CHECK_CRATED_DAYS_FAIL("7099", "check.created.days.fail"),
    THIRD_PLATFORM_FAIL("8001", "third.platform.fail"),
    QUESTION_SUBMIT_INVALID("8002", "question.submit.invalid"),
    QUESTION_SUBMIT_TIMEOUT("8003", "question.submit.timeout "),
    EMAIL_DOMAIN_NOT_SUPPORT("8004", "email.domain.not.support"),
    EMAIL_VERIFY_CODE_SEND_TOO_FREQUENT("8005", "email.verify.code.send.too.frequent"),
    ;

    private String key;

    private String code;

    private boolean success;

    /**
     * Full Parameter Constructor
     *
     * @param code
     * @param key
     * @param success
     */
    ActivityResponseCode(String code, String key, boolean success) {
        this.key = key;
        this.code = code;
        this.success = success;
    }

    /**
     * Default success as FALSE
     *
     * @param code
     * @param key
     */
    ActivityResponseCode(String code, String key) {
        this.key = key;
        this.code = code;
        this.success = false;
    }

    public String getKey() {
        return key;
    }

    public String getCode() {
        return code;
    }

    public boolean isSuccess() {
        return success;
    }

}
