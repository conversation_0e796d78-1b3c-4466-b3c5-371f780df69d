package com.kikitrade.activity.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * exchange-activity
 *
 * @Auther: dembin.deng
 * @Date: 2018/7/12 16:09
 * @Description:
 */

@Getter
@Setter

public class ActivityEventMassage implements Serializable {
    private Integer id;
    private Integer type;
    private String params;
    //是否自动生成活动数据
    private boolean autoDataPrepare = true;
    private String jobName;
    private Date batchNo;
    private String saasId;
    private Date crated;
    private Date modified;
}
