package com.kikitrade.activity.model;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * exchange-activity
 *
 * @Auther: dembin.deng
 * @Date: 2018/7/12 16:09
 * @Description:
 */

@Data
public class ActivityMessage implements Serializable {

    // 业务ID
    private String businessId;
    // 创建时间
    private Date created;
    // 更新时间
    private Date modified;
    // 用户ID
    private String customerId;
    // Saas ID
    private String saasId;
    // 事件
    // private ActivityConstant.RuleEvent event;
    private String event;

    //操作类型
    /**
     * {@link ActivityType}
     */
    // private Integer type;
    //参数
    private String params;
    //状态
    private String checkResult;

    private Integer status;

    //desc
    private String desc;

    private boolean isBatch = false;

    private Integer activityId;

    // 一些业务参数的集合
    private JSONObject paramsJson;

    // 活动的奖励信息，格式：<activityId, RewardResult>
    private Map<Integer, RewardResult> rewardResultMap;

    /**
     * 返回指定活动的奖励信息
     * @param activityId
     * @return
     */
    public RewardResult fetchRewardResult(Integer activityId) {
        if (rewardResultMap == null) {
            rewardResultMap = new HashMap<>();
        }
        return activityId == null ? null : rewardResultMap.get(activityId);
    }

    /**
     * 设置活动的奖励信息
     * @param activityId    活动id
     * @param rewardCurrency    奖励币种
     * @param rewardMoney   奖励金额
     * @param exchangeCurrency 奖励实际换算后的币种
     * @param customerId 奖励的客户的id
     * @param
     */
    public void putRewardResult(Integer activityId, String rewardCurrency, BigDecimal rewardMoney, String exchangeCurrency, String customerId) {
        if (rewardResultMap == null) {
            rewardResultMap = new HashMap<>();
        }
        rewardResultMap.put(activityId, new RewardResult(activityId, rewardCurrency, rewardMoney, exchangeCurrency, customerId));
    }

    /**
     * 设置用户邀请活动的奖励信息
     * @param rewardResult    奖励结果
     */
    public void putRewardResult(RewardResult rewardResult) {
        if (rewardResultMap == null) {
            rewardResultMap = new HashMap<>();
        }
        rewardResultMap.put(rewardResult.getActivityId(), rewardResult);
    }

    /**
     * 返回指定的业务属性，不存在时返回null
     * @param attrName  属性名称
     * @return
     */
    public Object fetchBusinessAttr(String attrName) {
        return fetchParamsJson().get(attrName);
    }

    /**
     * 缓存传入的业务属性
     * @param attrName  属性名称
     * @param value     属性值
     */
    public void putBusinessAttr(String attrName, Object value) {
        fetchParamsJson().put(attrName, value);
    }

    /**
     * 把params解析成JSONObject格式，并返回
     * @return
     */
    public JSONObject fetchParamsJson() {
        if (paramsJson == null && StringUtils.isNotBlank(params)) {
            try {
                paramsJson = JSONObject.parseObject(params);
            } catch (Exception e) {
                paramsJson = new JSONObject();
                e.printStackTrace();
            }
        }
        return paramsJson == null ? new JSONObject() : paramsJson;
    }


    @Getter
    @Setter
    public static class RewardResult {
        //活动id
        private Integer activityId;
        //奖励信息列表
        private List<RewardEntity> rewardEntities = new ArrayList<>();

        public RewardResult(Integer activityId) {
            this.activityId = activityId;
        }

        public RewardResult(Integer activityId, String rewardCurrency, BigDecimal rewardMoney, String exchangeCurrency, String customerId) {
            this.activityId = activityId;
            addRewardEntity(rewardCurrency, rewardMoney, exchangeCurrency, customerId);
        }

        // 增加奖励信息
        public RewardResult addRewardEntity(String rewardCurrency, BigDecimal rewardMoney, String exchangeCurrency, String customerId) {
            if (StringUtils.isNoneBlank(rewardCurrency, exchangeCurrency, customerId) && rewardMoney != null) {
                rewardEntities.add(new RewardEntity(rewardCurrency, rewardMoney, exchangeCurrency, customerId, false));
            }
            return this;
        }

        // 增加额外奖励信息
        public RewardResult addExtraRewardEntity(String rewardCurrency, BigDecimal rewardMoney, String exchangeCurrency, String customerId) {
            if (StringUtils.isNoneBlank(rewardCurrency, exchangeCurrency, customerId) && rewardMoney != null) {
                rewardEntities.add(new RewardEntity(rewardCurrency, rewardMoney, exchangeCurrency, customerId, true));
            }
            return this;
        }

        // 返回指定customerId的奖励信息集合
        public List<RewardEntity> getRewardByCustomerId(String customerId) {
            return rewardEntities.stream().filter(rewardEntity -> StringUtils.equals(rewardEntity.getCustomerId(), customerId)).collect(Collectors.toList());
        }

        // 以下是兼容老逻辑的代码，对于大多数活动来说，只有一个奖励信息
        public String getRewardCurrency() {
            return rewardEntities.size() > 0 ? rewardEntities.get(0).getRewardCurrency() : null;
        }

        // 以下是兼容老逻辑的代码，对于大多数活动来说，只有一个奖励信息
        public BigDecimal getRewardMoney() {
            return rewardEntities.size() > 0 ? rewardEntities.get(0).getRewardMoney() : null;
        }

        // 以下是兼容老逻辑的代码，对于大多数活动来说，只有一个奖励信息
        public String getExchangeCurrency() {
            return rewardEntities.size() > 0 ? rewardEntities.get(0).getExchangeCurrency() : null;
        }
    }

    @Getter
    @AllArgsConstructor
    public static class RewardEntity {
        private String rewardCurrency;
        private BigDecimal rewardMoney;
        private String exchangeCurrency;
        private String customerId;
        private boolean isExtraReward;
    }


}
