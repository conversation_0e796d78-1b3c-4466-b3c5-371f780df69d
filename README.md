[TOC]

## 服务介绍

kactivity主要负责kiki及aspen的活动营销类业务，主要提供的功能包含：

- ​	活动发奖：包含point及token的发放
- ​	vip任务：实时任务及离线任务
- ​	空投：包含发空投及领取空投
- ​	抽奖

### 服务架构

​		![活动架构](./resources/活动架构图.png)

### 功能概括

​	![功能描述](./resources/功能描述.png)

### 仪表大盘

#### 	大盘日志

​

| 业务     | 日志格式                                                     |
| -------- | ------------------------------------------------------------ |
| 做任务   | type:【TASK-ACTION】,id: 【targetId】,action:【{{#activityEventMessage.customerId}}】完成任务【{{#activityEventMessage.eventCode}}】 |
| 领取空投 | type:【AIRDROP-RECEIVE】,id: 【receiveId】,action:【{{#luckFortuneReceiveDTO.receiveCode}}】成功领取【{{#luckFortuneReceiveDTO.releaseId}}】空投 |
| 发放空投 | type:【AIRDROP-RELEASE】,id: 【releaseId】,action:【{{#luckFortuneReleaseDTO.customer.id}}】成功发放【{{#luckFortuneReleaseDTO.num}}】个{{luckFortuneReleaseDTO.currency}}空投 |
| 开启空投 | type:【AIRDROP-OPEN】,id: 【receiveId】,action:【{{#luckFortuneOpenDTO.customer.id}}】成功开启空投【{{#luckFortuneOpenDTO.id}}】 |
| 抽奖     | type:【LOTTERY-ACTION】,id: 【id】,action:【{{#customerId}}】从【{{#drew.name}}】奖池中抽中【{{_ret.reward}}{{_ret.currency}}】 |
| 发奖     | type:【ACTIVITY-REWARD】,id: 【rewardId】,action:【{{#request.customerId}}】成功发放【{{#request.amount}}】【{{#request.currency}}】 |

#### 	大盘地址



## 服务发布

### 最新版本

```xml
<dependency>
    <groupId>com.kikitrade</groupId>
    <artifactId>kactivity-api</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### dubboApi

```http
https://api.dev.dipbit.xyz/swagger-ui/index.html#/
```

### 发布历史

| 版本     | 功能描述               | api版本        |
| -------- | ---------------------- | -------------- |
| 20230412 | java17升级             | 2.0.0-SNAPSHOT |
| 20230329 | 增加被点赞、被关注事件 | 1.0.0-SNAPSHOT |
| 20220719 | vip任务                | 1.0.0-SNAPSHOT |
| 20220526 | h5空投                 | 1.0.0-SNAPSHOT |
|          |                        |                |

## 监控报警

```http
https://docs.google.com/spreadsheets/d/1qw8lzpSCQ0uMOUuSfo4Uyo5xh0VZnC_DVAvNZnisils/edit#gid=1630839057
```

## 常见问题

