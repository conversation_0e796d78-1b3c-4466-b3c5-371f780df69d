package com.kikitrade.activity.application;

//import brave.spring.webmvc.DelegatingTracingFilter;
import lombok.SneakyThrows;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;

import javax.servlet.DispatcherType;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
//@Configuration
public class ExchangeActivityWebAppInitializer extends SpringBootServletInitializer {
    @SneakyThrows
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        builder.sources(KActivityApplication.class);
        return builder;
    }


//    @Bean
//    FilterRegistrationBean delegatingTracingFilter(){
//        FilterRegistrationBean<DelegatingTracingFilter> registrationBean = new FilterRegistrationBean<>();
//        registrationBean.setFilter(new DelegatingTracingFilter());
//        registrationBean.setUrlPatterns(Arrays.asList(new String[]{"/*"}));
//        registrationBean.setDispatcherTypes(DispatcherType.REQUEST);
//        return registrationBean;
//    }

}


