package com.kikitrade.activity.application;

import org.springframework.boot.web.servlet.ServletListenerRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextListener;
import org.springframework.web.util.IntrospectorCleanupListener;

/**
 * <AUTHOR>
 */
//@Configuration
public class ServletListenerConfig {
    @Bean
    public ServletListenerRegistrationBean<IntrospectorCleanupListener> introspectorCleanupListener() {
        ServletListenerRegistrationBean<IntrospectorCleanupListener>
                introspectorCleanup = new ServletListenerRegistrationBean<IntrospectorCleanupListener>(new IntrospectorCleanupListener());
        return introspectorCleanup;
    }
    @Bean
    public ServletListenerRegistrationBean<RequestContextListener> requestContextListener() {
        ServletListenerRegistrationBean<RequestContextListener>
                requestContextListener = new ServletListenerRegistrationBean<RequestContextListener>(new RequestContextListener());
        return requestContextListener;
    }
}
