package com.kikitrade.activity.application;

import cn.hutool.extra.spring.EnableSpringUtil;
import com.mzt.logapi.starter.annotation.EnableLogRecord;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;

/**
 * <AUTHOR>
 */
@EnableDubbo(scanBasePackages = {"com.kikitrade.activity"})
@SpringBootApplication(scanBasePackages = {"com.kikitrade.activity"},
        exclude = {SecurityAutoConfiguration.class, ManagementWebSecurityAutoConfiguration.class,
                MongoAutoConfiguration.class, MongoDataAutoConfiguration.class})
@EnableLogRecord(tenant = "com.kikitrade.kactivity")
@EnableSpringUtil
public class KActivityApplication {
    public static void main(String[] args) {
        SpringApplication.run(KActivityApplication.class, args);
    }
}
