package com.kikitrade.activity.application;

//import brave.spring.webmvc.SpanCustomizingHandlerInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
//@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
//    @Resource
//    SpanCustomizingHandlerInterceptzor spanCustomizingHandlerInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
//        registry.addInterceptor(spanCustomizingHandlerInterceptor).addPathPatterns("/**");
    }
}