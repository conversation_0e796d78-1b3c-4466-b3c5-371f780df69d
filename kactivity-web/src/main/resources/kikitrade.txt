${AnsiColor.MAGENTA}
 █████   ████  ███  █████       ███  ███████████                         █████
░░███   ███░  ░░░  ░░███       ░░░  ░█░░░███░░░█                        ░░███
 ░███  ███    ████  ░███ █████ ████ ░   ░███  ░  ████████   ██████    ███████   ██████
 ░███████    ░░███  ░███░░███ ░░███     ░███    ░░███░░███ ░░░░░███  ███░░███  ███░░███
 ░███░░███    ░███  ░██████░   ░███     ░███     ░███ ░░░   ███████ ░███ ░███ ░███████
 ░███ ░░███   ░███  ░███░░███  ░███     ░███     ░███      ███░░███ ░███ ░███ ░███░░░
 █████ ░░████ █████ ████ █████ █████    █████    █████    ░░████████░░████████░░██████
░░░░░   ░░░░ ░░░░░ ░░░░ ░░░░░ ░░░░░    ░░░░░    ░░░░░      ░░░░░░░░  ░░░░░░░░  ░░░░░░
${AnsiColor.BLUE}

Application Name: ${app.name}
Spring Boot Version: ${spring-boot.version}${spring-boot.formatted-version}
