#spring é»è®¤å¬å±éç½®
# for testing env:   -Dspring.profiles.active=testing
spring.application.name=kactivity
spring.output.ansi.enabled=always
spring.main.banner-mode=console
spring.banner.location=classpath:kikitrade.txt
spring.mvc.servlet.path=/
spring.mvc.servlet.load-on-startup=1
server.servlet.session.timeout=86400
app.name=kactivity
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.force=true
server.servlet.encoding.force-request=true
server.servlet.encoding.force-response=true
##??
management.metrics.tags.application=kactivity
management.endpoints.web.exposure.include=env,health,info,httptrace,metrics,heapdump,threaddump,prometheus,dubbo,druid
management.health.db.enabled=true
management.health.solr.enabled=false
management.health.mongo.enabled=true
management.health.cassandra.enabled=false
management.health.elasticsearch.enabled=false
management.health.influxdb.enabled=false
management.health.neo4j.enabled=false
management.server.port=9090
management.health.defaults.enabled=false
management.endpoint.health.show-details=always
management.endpoints.migrate-legacy-ids=true
management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true
management.prometheus.metrics.export.enabled=true
dubbo.metrics.protocol=prometheus
dubbo.metrics.aggregation.enabled=true
dubbo.metrics.prometheus.exporter.enabled=true
dubbo.application.name=kactivity

logging.level.root=info
logging.pattern.console=[%p][%t][%d{yyyy-MM-dd HH:mm:ss.SSS}][%c][%L][%X{traceId}][%X{spanId}]%m%n