saas-id=@app.saasId@

############### ignite éç½® ###############
kiki.ignite.namespace=@namespace@
kiki.ignite.application-name=kactivity
kiki.ignite.local=@environment.local@
kiki.ignite.environment-namespace=@environment.namespace@
kiki.ignite.client=false

###########################zookeeper###################################
#zookeeper.url=@zookeeper.url@

############### dubbo éç½® ###############
dubbo.application.name=kactivity
dubbo.application.id=kactivity
dubbo.application.version=1.0.0
dubbo.protocol.server=netty
dubbo.protocol.name=dubbo
dubbo.protocol.port=20882
dubbo.protocol.threadpool=fixed
dubbo.protocol.threads=500
dubbo.protocol.queues=1000
dubbo.registry.address=zookeeper://@zookeeper.url@
dubbo.provider.timeout=5000
dubbo.provider.retries=1
dubbo.consumer.group=@dubbo.consumer.group@
dubbo.provider.group=@dubbo.provider.group@
dubbo.reference.check=false
dubbo.consumer.check=false
dubbo.registry.check=false
dubbo.consumer.filter=dtm,tracing
dubbo.provider.filter=dtm,tracing

spring.zipkin.baseUrl=http://zipkin.kikitrade-dev.svc.kiki.local:9411
spring.sleuth.sampler.percentage=1.0

################ MySQL Spring Config ##############
spring.datasource.url=@jdbc.ds.url@
spring.datasource.username=@jdbc.username@
spring.datasource.password=@jdbc.password@
spring.datasource.platform=mysql
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

spring.datasource.druid.initial-size=10
spring.datasource.druid.max-active=50
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-wait=10000

########################### dtm config ###################################
kiki.dtm.participant=@activity.dtm.participant@
kiki.dtm.server-address=@activity.dtm.serverAddress@
kiki.dtm.server-port=@activity.dtm.serverPort@
kiki.dtm.local=@activity.dtm.local@

########################### tablestore æ¯è´¦æ·çº§å«çak skï¼ ä¸ossï¼åªåå«kikiçossææåè½ï¼ å¬ç¨ak sk ###################################
kiki.ots.endpoint=@tableStore.endpoint@
kiki.ots.instanceName=@tableStore.instanceName@
kiki.ots.retry-pause-in-millis=5000
kiki.ots.retry-times=1
kiki.ots.skipCheckTableChange=true


####################### producer manage ##########################
# ons
kiki.ons.address=@ons.address@
kiki.ons.env=@ons.env@
kiki.ons.tag=@ons.tag@
kiki.ons.group-id=<EMAIL>@

#spring çº¿ä¸ç¯å¢å¯å¨, é»è®¤ä¸ºçº¿ä¸ç¯å¢ï¼ éåéç½®èªå¨æ¿æ¢çæ¹å¼
debug=true
spring.cloud.nacos.config.enabled=false
spring.servlet.multipart.max-file-size=10MB
server.port=@activity.server.port@

#å®æ¶ä»»å¡åæºï¼éç¾¤æ å¿
#activity.schedule.single=@activity.schedule.single@
#æ´»å¨åå¥åçè°åº¦é»è®¤ä¸æ¬¡è°åº¦ç¬æ°ï¼åæºæ¨¡å¼ï¼
activity.dispatch.limit=@activity.dispatch.limit@
#æ´»å¨åå¥åçè°åº¦ååurl
activity.dispatch.notifyurl=@activity.dispatch.notifyurl@

#ååç¬æ°ï¼éè¦èèHttpgetè¯·æ±æ¥æé¿åº¦ï¼è°åº¦èç¹æ¯æ¬¡åæ§è¡èç¹åéç¬æ°ï¼
#activity.page.num=@activity.page.num@
#æ´»å¨å®æ¶åå¥è°ç¨æ¶é´ï¼åæºæ¨¡å¼ï¼
#activity.time.reward.time=@activity.time.reward.time@

########################### OSS ###################################
app.oss.rolename=@app.oss.rolename@
app.oss.activity.kolExtraReward.key=@app.oss.activity.kolExtraReward.key@
###########################redis###################################
spring.redis.host=@redis.url@
spring.redis.port=@redis.port@
spring.redis.password=@redis.password@
spring.redis.database=@redis.dbIndex@
spring.redis.lettuce.pool.max-active=@redis.maxTotal@
spring.data.redis.host=@redis.url@
spring.data.redis.port=@redis.port@
spring.data.redis.password=@redis.password@
spring.data.redis.database=@redis.dbIndex@
spring.data.redis.lettuce.pool.max-active=@redis.maxTotal@

zipkin.host=@zipkin.host@
########################### mybatis ###################################
mybatis.mapper-locations=classpath:com/kikitrade/activity/dal/mysql/dao/mapper/*.xml
#### tk mapper ####
mapper.mappers=tk.mybatis.mapper.common.Mapper
mapper.notEmpty=true

manage.grpc.port=@manage.grpc.port@

################################ springbatch ####################################
spring.batch.job.enabled=false
spring.batch.initialize-schema=always
spring.batch.table-prefix=kactivity_
spring.batch.schema=classpath:com/kikitrade/activity/dal/mysql/schema-mysql.sql

batch.reward-shard=1
batch.reward.retry-job-count=10
batch.inteface-limiter=rewardService:20;remoteCustomerService:100
batch.import.max-job-count=5

###########################notice#########################
reward.notice-switch=true
activity.area-sort=HK,EN,CN
material.type-properties=
material.type-properties-base=titleCN,titleHK,titleEN,descCN,descHK,descEN,awardRuleCN,awardRuleHK,awardEN,button

##################################odps################################
app.odps.accessId=@app.odps.accessId@
app.odps.accessKey=@app.odps.accessKey@
app.odps.endPoint=@app.odps.endPoint@
app.odps.project=@app.odps.project@

spring.cloud.nacos.config.namespace=d35ed129-2c19-4870-ab9c-25ad7f149440
spring.cloud.nacos.config.server-addr=127.0.0.1:8848

logging.level.root=info
logging.pattern.console=[%p][%t][%d{yyyy-MM-dd HH:mm:ss.SSS}][%c][%L][%X{traceId}][%X{spanId}]%m%n

# æ°æéè¯·ä»»å¡çå¼å§æ¶é´æ³
activity.invite.reward.start.timestamp=*************

# ?????????????:USDC
product.account.currency=USDC

task-white-properties=2024071906435660016061
task.white.date=5
kactivity.rank.season-start-time=*************
activity.clean.taskItem.interval.seconds=7200
activity.clean.taskItem.days=30

kactivity.rank.zeek-saas-id=deek-app-84532
