# 奖励中台后端技术规格书 (OTS最终版)

## 1. 系统概述

本文档为一份完整的后端技术规格说明书，旨在为开发一个功能全面、高内聚、低耦合的**奖励中台**提供清晰、详尽的实现指南。该系统**专为阿里云表格存储（OTS）设计**，作为平台能力，支持由多个外部业务渠道驱动，并集成了动态奖池、多维度风控、多种激励机制和灵活的概率策略。

### 核心功能：

- **概率性抽奖**：
  - **动态奖池**：支持根据用户存储的、与奖池挂钩的偏好（如选择的英雄），动态替换奖池中的专属奖品。
  - **双重概率策略**：支持“整体概率 (OVERALL)”和“单品概率 (SINGLE)”两种模式。
  - **两阶段抽奖**：提供独立的“资产兑换抽奖券”和“消耗抽奖券批量抽奖”接口，由前端编排调用。
- **组合奖励 (礼包/宝箱)**：支持配置“礼包”型奖品，内含多个固定奖励和随机奖励。可通过抽奖获得，也可由外部服务直接按ID触发发放。
- **统一领奖能力**：提供一个统一、抽象的领奖接口，用于核销由不同业务系统（如抽奖进度、任务系统）为用户生成的“领奖凭证”。
- **定向奖励发放**：提供安全的内部API，允许其他微服务直接指定奖励内容并调用本中台进行发放。
- **多维度抽奖风控**：可按日、周、月三个维度精确控制用户的抽奖次数上限。
- **后台管理支持**：提供独立的API接口，供管理后台配置活动和监控库存。

## 2. 系统架构与流程图

(保持不变)

## 3. 数据模型 (OTS 表设计)

**注意**: 所有主键 (PK) 均为复合主键，由应用在写入时提供。第一个主键字段为**分区键**。

### 3.1 `item_master_config` - 物品主数据表 (新增核心)

| **字段**           | **类型**             | **含义**                                                     |
| ------------------ | -------------------- | ------------------------------------------------------------ |
| `saas_id`          | STRING               | **主键1 (分区键)**: 对接的应用/租户ID                        |
| `item_id`          | STRING               | **主键2**: **系统内部**的物品唯一ID (如 `health_potion_s`, `gold_currency`) |
| `item_name`        | STRING               | 物品的官方名称 (如 “小型生命药水”, “金币”)                   |
| `item_icon`        | STRING               | 物品的官方图标URL                                            |
| `item_type`        | STRING               | 物品的类型 (`ITEM`, `CURRENCY`, `AVATAR_FRAME`等)            |
| `external_item_id` | STRING               | **游戏/外部系统的道具ID** (`NULL`表示非实体道具)             |
| `description`      | STRING               | 物品的描述                                                   |
| `is_active`        | BOOLEAN              | 该物品是否在整个系统中可用                                   |
| `create_time`      | BIGINT (Timestamp)   | 创建时间                                                     |
| `update_time`      | BIGINT (Timestamp)   | 更新时间                                                     |
| **PK**             | `(saas_id, item_id)` | 复合主键                                                     |

### 3.2 `prize_pool` - 奖池配置表

(保持不变)

### 3.3 `prize_config` - 奖品配置表

| **字段**              | **类型**                                | **含义**                                                   |
| --------------------- | --------------------------------------- | ---------------------------------------------------------- |
| `saas_id`             | STRING                                  | **主键1 (分区键)**: 对接的应用/租户ID                      |
| `prize_pool_code`     | STRING                                  | **主键2**: 所属奖池编码                                    |
| `config_id`           | STRING                                  | **主键3**: 配置规则的唯一ID (UUID)                         |
| `preference_type`     | STRING                                  | 关联的偏好类型 (`NULL`表示通用)                            |
| `preference_value`    | STRING                                  | 关联的偏好值                                               |
| `prize_name`          | STRING                                  | **冗余**: 奖品名称（用于展示）                             |
| `prize_icon`          | STRING                                  | **冗余**: 奖品图标URL（用于展示）                          |
| `reward_type`         | STRING                                  | **冗余**: 奖励类型 (`ITEM`, `CURRENCY`, `GIFT_PACK`)       |
| `reward_item_id`      | STRING                                  | **引用**: 奖励的业务ID (关联 `item_master_config.item_id`) |
| `reward_quantity`     | BIGINT                                  | **规则**: 奖励数量                                         |
| `winning_probability` | DOUBLE                                  | 中奖概率                                                   |
| `stock_quantity`      | BIGINT                                  | 库存数量 (-1 表示无限)                                     |
| `is_active`           | BOOLEAN                                 | 是否生效                                                   |
| `create_time`         | BIGINT (Timestamp)                      | 创建时间                                                   |
| `update_time`         | BIGINT (Timestamp)                      | 更新时间                                                   |
| **PK**                | `(saas_id, prize_pool_code, config_id)` | 复合主键                                                   |

### 3.4 `user_lottery_profile` - 用户抽奖档案表

(保持不变)

### 3.5 `progress_chest_config` - 进度宝箱配置表

(保持不变)

### 3.6 `user_claim_entitlement` - 用户领奖凭证表

(保持不变)

### 3.7 `user_preference` - 用户偏好表

(保持不变)

### 3.8 `draw_history` - 抽奖历史记录表

(保持不变)

### 3.9 `gift_pack_config` - 礼包内容规则表

| **字段**         | **类型**             | **含义**                                                     |
| ---------------- | -------------------- | ------------------------------------------------------------ |
| `pack_id`        | STRING               | **主键1 (分区键)**: 礼包ID (由 `prize_config` 中 `reward_type`='GIFT_PACK' 的记录引用) |
| `rule_id`        | STRING               | **主键2**: 规则的唯一ID (UUID)                               |
| `rule_type`      | STRING               | 规则类型 (`FIXED_ITEM` 或 `RANDOM_POOL_PICK`)                |
| `item_id`        | STRING               | **引用**: 物品ID (关联 `item_master_config.item_id`)         |
| `quantity_min`   | BIGINT               | **规则**: 最小数量                                           |
| `quantity_max`   | BIGINT               | **规则**: 最大数量                                           |
| `random_pool_id` | STRING               | 随机池ID                                                     |
| `pick_count`     | BIGINT               | 从随机池中抽取的数量                                         |
| **PK**           | `(pack_id, rule_id)` | 复合主键                                                     |

### 3.10 `random_reward_pool` - 随机奖励池定义表

| **字段**       | **类型**             | **含义**                                                     |
| -------------- | -------------------- | ------------------------------------------------------------ |
| `pool_id`      | STRING               | **主键1 (分区键)**: 随机池ID                                 |
| `item_id`      | STRING               | **主键2**: **引用**: 池内包含的物品ID (关联 `item_master_config.item_id`) |
| `quantity_min` | BIGINT               | **规则**: 抽中该物品时获得的最小数量                         |
| `quantity_max` | BIGINT               | **规则**: 抽中该物品时获得的最大数量                         |
| `weight`       | BIGINT               | 抽中该物品的权重                                             |
| **PK**         | `(pool_id, item_id)` | 复合主键                                                     |

### 3.11 `direct_reward_issuance_log` - 定向奖励发放流水表

(保持不变)

### 3.12 `user_progress_tracker` - 用户进度跟踪表

(保持不变)

## 4. 核心业务逻辑

### 4.1 发奖策略 (已重构)

当抽奖命中、礼包开启或直接发放时，系统根据奖励规则中的 `reward_item_id` 和 `reward_quantity` 执行发奖流程：

1. **查询主数据**: 使用 `reward_item_id` 查询 `item_master_config` 表，获取该物品的 `item_type` 和 `external_item_id`。

2. 选择策略: 根据查询到的 item_type，执行不同的发奖策略。

   | item_type | 业务含义       | 后端执行流程                                                               |

   | :---------- | :------------- | :----------------------------------------------------------------------- |

   | ITEM      | 游戏内实体道具 | 调用游戏服的“添加道具”接口，传递 external_item_id 和 reward_quantity。 |

   | CURRENCY  | 游戏内货币     | 调用资产中心的“增加货币”接口，传递 item_id (如 "GOLD") 和 reward_quantity。 |

   | GIFT_PACK | 礼包           | 调用奖励中台自身的“开启礼包”逻辑，传递 reward_item_id (即 pack_id)。     |

   | ...         | ...            | ...                                                                      |

### 4.2 批量抽奖与进度处理

(保持不变)

### 4.3 缓存策略

(保持不变)

## 5. API 接口设计

(保持不变)