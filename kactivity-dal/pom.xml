<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kactivity</artifactId>
        <groupId>com.kikitrade</groupId>
        <version>3.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.kikitrade</groupId>
    <artifactId>kactivity-dal</artifactId>
    <version>3.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <properties>
        <!-- 指定项目是JDK1.7版本 -->
        <java-version>17</java-version>
        <!--指定Maven用什么编码来读取源码及文档 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.property.path>..</project.property.path>
        <!--指定Maven用什么编码来呈现 -->
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kactivity-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-ots-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>protobuf-java</artifactId>
                    <groupId>com.google.protobuf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>protobuf-java</artifactId>
            <groupId>com.google.protobuf</groupId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.8.16</version>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-core</artifactId>
        </dependency>

        <!-- zipkin start -->
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kseq-spring-boot-starter</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kseq-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-redis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jdom</groupId>
            <artifactId>jdom2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-odps-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>protobuf-java</artifactId>
                    <groupId>com.google.protobuf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
