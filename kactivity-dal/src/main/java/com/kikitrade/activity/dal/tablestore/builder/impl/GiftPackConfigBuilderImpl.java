package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.kikitrade.activity.dal.tablestore.builder.GiftPackConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.GiftPackConfig;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.kikitrade.activity.dal.tablestore.model.GiftPackConfig.SEARCH_GIFT_PACK_CONFIG;

/**
 * <AUTHOR>
 * @date 2025/9/4 10:43
 * @description:
 */
@Slf4j
@Component
public class GiftPackConfigBuilderImpl extends WideColumnStoreBuilder<GiftPackConfig> implements GiftPackConfigBuilder {
    /**
     * 根据礼包ID查询礼包配置规则
     *
     * @param packId 礼包ID
     * @param saasId SaaS ID
     * @return 礼包配置规则列表
     */
    @Override
    public List<GiftPackConfig> findByPackId(String packId, String saasId) {
        BoolQuery.Builder builder = QueryBuilders.bool()
                .must(QueryBuilders.term("pack_id", packId))
                .must(QueryBuilders.term("saas_id", saasId));
        return pageSearchQuery(builder.build(), null, 0, 100, SEARCH_GIFT_PACK_CONFIG).getRows();
    }

    /**
     * 根据礼包ID和规则类型查询配置
     *
     * @param packId   礼包ID
     * @param ruleType 规则类型
     * @param saasId   SaaS ID
     * @return 礼包配置规则列表
     */
    @Override
    public List<GiftPackConfig> findByPackIdAndRuleType(String packId, String ruleType, String saasId) {
        return List.of();
    }

    /**
     * 根据SaaS ID查询所有活跃的礼包配置
     *
     * @param saasId SaaS ID
     * @return 礼包配置规则列表
     */
    @Override
    public List<GiftPackConfig> findActiveBySaasId(String saasId) {
        return List.of();
    }
}
