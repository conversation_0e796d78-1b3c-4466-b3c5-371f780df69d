package com.kikitrade.activity.dal.tablestore.builder;

import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardParam;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.ots.RangeResult;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ActivityCustomRewardStoreBuilder {

    PageResult findByBatchIdAndStatus(ActivityRewardParam activityRewardParam, int offset, int limit);

    ActivityCustomReward findByPrimaryId(String batchId, String customerId, String seq);

    boolean insert(ActivityCustomReward reward);

    boolean updateStatus(ActivityCustomReward activityCustomReward);

    boolean existByBatchIdAndStatus(String batchId, String status);

    RangeResult<ActivityCustomReward> findByStatusAndBatchId(String rewardStatus, String batchId, PrimaryKey nextToken);

    RangeResult<ActivityCustomReward> findAllByBatchId(ActivityRewardPageParam activityRewardPageParam);

    Page<ActivityCustomReward> findAllByStatus(ActivityRewardPageParam activityRewardPageParam, int offset, int limit);

    RangeResult<ActivityCustomReward> findByBatchIdAndStatus(ActivityRewardPageParam activityRewardPageParam);

    RangeResult<ActivityCustomReward> findByBatchIdAndStatusAndShard(ActivityRewardPageParam activityRewardPageParam);

    long count(String batchId);

    Result<Map<String,String>> delete(List<String> ids);

    boolean batchInsert(List<ActivityCustomReward> rewardList);

    PageResult findByReferId(String referId, int offset, int limit);

    long countByReferId(String referId);

    BigDecimal sumCostByCustomerIdAndSide(String customerId, String side);

    BigDecimal sumCostByCustomerIdAndSideTime(String customerId, String side, long fromRewardTime);

    PageResult findByCustomerIdAndBusinessType(String customerId, String businessType, int offset, int limit);

    long countByCustomerIdAndBusinessType(String customerId, String businessType);

    PageResult findByBusinessType(int offset, int limit);

    boolean update(ActivityCustomReward activityCustomReward);

    long countRewardsByTypeSide(String customerId, List<ActivityConstant.RewardBusinessType> rewardBusinessTypes,
                                List<ActivityConstant.AwardTypeEnum> rewardTypes, ActivityConstant.SideEnum role, Long fromRewardTime);

    PageResult getRewardsByTypeSide(String customerId, List<ActivityConstant.RewardBusinessType> rewardBusinessTypes,
                                                    List<ActivityConstant.AwardTypeEnum> rewardTypes, ActivityConstant.SideEnum role, Long fromRewardTime, int offset, int limit);

    BigDecimal getSumByTypeSide(String customerId, List<ActivityConstant.RewardBusinessType> rewardBusinessTypes, List<ActivityConstant.AwardTypeEnum> rewardTypes, ActivityConstant.SideEnum role, Long fromRewardTime);

    Map<String, Map<String, BigDecimal>> getSumGroupByBusinessAward(String customerId, List<ActivityConstant.RewardBusinessType> rewardBusinessTypes, ActivityConstant.SideEnum role, Long fromRewardTime);

    ActivityCustomReward findByBusinessId(String businessId);
}
