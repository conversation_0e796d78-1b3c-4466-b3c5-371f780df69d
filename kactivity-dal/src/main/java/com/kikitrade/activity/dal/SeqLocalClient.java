package com.kikitrade.activity.dal;

import com.kikitrade.kseq.api.SeqGenerator;
import com.kikitrade.kseq.api.model.SeqElement;
import com.kikitrade.kseq.api.model.SeqEntity;
import com.kikitrade.kseq.api.model.SeqRule;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class SeqLocalClient implements SeqGenerator {

    @Resource
    private RedisTemplate redisTemplate;

    private Map<String, SeqEntity> localSeqMap = new ConcurrentHashMap();

    public SeqLocalClient() {
    }

    public String next(SeqRule rule, String... params) {
        return this.next(rule, new Date(), params);
    }

    public String next(SeqRule rule, Date date, String... params) {
        SeqEntity seqEntity = (SeqEntity)this.localSeqMap.get(rule.getType());
        if (seqEntity == null) {
            return this.acquireAndGenerate(rule, date, 0, params);
        } else {
            int next = seqEntity.getCurrent().incrementAndGet();
            return this.exhaust(rule, next) ? this.acquireAndGenerate(rule, date, next, params) : SeqElement.encode(rule, date, next, params);
        }
    }

    protected boolean exhaust(SeqRule rule, int next) {
        SeqEntity seq = (SeqEntity)this.localSeqMap.get(rule.getType());
        int max = seq.getStart() + rule.getInterval();
        return next >= max;
    }

    protected String acquireAndGenerate(SeqRule type, Date date, int orgNext, String... params) {
        SeqEntity seqEntity = (SeqEntity)this.localSeqMap.get(type.getType());
        Integer next;
        if (orgNext != 0 && orgNext < seqEntity.getStart()) {
            next = seqEntity.getCurrent().incrementAndGet();
        } else {
            next = this.acquire(type);
        }

        return SeqElement.encode(type, date, next, params);
    }

    protected synchronized Integer acquire(SeqRule rule) {
        SeqEntity seqEntity = this.acquireByRedis(rule);
        this.localSeqMap.put(rule.getType(), seqEntity);
        return seqEntity.getCurrent().get();
    }

    private SeqEntity acquireByRedis(SeqRule rule) {
        Long num = redisTemplate.opsForHash().increment(rule.getType(), "seqNum", (long) rule.getInterval());
        return new SeqEntity(num.intValue(), new AtomicInteger(num.intValue()));
    }
}
