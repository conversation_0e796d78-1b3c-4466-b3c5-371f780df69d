package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.condition.SingleColumnValueCondition;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.tablestore.builder.ActivityTaskItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.ots.TransactionCallback;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
public class ActivityTaskItemBuilderImpl extends WideColumnStoreBuilder<ActivityTaskItem> implements ActivityTaskItemBuilder {

    @Resource
    private SeqGeneraterService seqGeneraterService;

    @Override
    public String getTableName(){
        return "activity_task_item";
    }

    @PostConstruct
    public void init() {
        init(ActivityTaskItem.class);
    }

    /**
     * 插入一个任务项
     *
     * @param activityTaskItem
     * @return
     */
    @Override
    public boolean insert(ActivityTaskItem activityTaskItem) {
        activityTaskItem.setCreated(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        activityTaskItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        return super.putRow(activityTaskItem, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean exist(String customerId, String taskId, String cycle, String targetId)  {
        ActivityTaskItem param = ActivityTaskItem.buildDetailPrimary(customerId, taskId, cycle, targetId);
        return getRow(param) != null;
    }

    /**
     * 更新任务项，主要进度，完成事件
     *
     * @param activityTaskItem
     * @return
     */
    @Override
    public boolean update(ActivityTaskItem activityTaskItem) {
        activityTaskItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return super.updateRow(activityTaskItem, condition);
    }

    @Override
    public boolean updateStatus(ActivityTaskItem activityTaskItem) {
        activityTaskItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return super.updateRow(activityTaskItem, List.of("modified","complete_time", "status"), condition);
    }

    @Override
    public boolean deleteDetail(String customerId, String taskId, String cycle, String targetId) {
        return super.deleteRow(ActivityTaskItem.buildDetailPrimary(customerId, taskId, cycle, targetId));
    }

    public boolean delete(List<ActivityTaskItem> list) {
        return super.batchDeleteRows(list);
    }
    /**
     * 更新任务项，主要进度，完成事件
     *
     * @param activityTaskItem
     * @return
     */
    @Override
    public boolean updateProgress(ActivityTaskItem activityTaskItem) {
        activityTaskItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        condition.setColumnCondition(new SingleColumnValueCondition("progress", SingleColumnValueCondition.CompareOperator.LESS_EQUAL, ColumnValue.fromLong(activityTaskItem.getProgress())));
        return super.updateRow(activityTaskItem, condition);
    }

    /**
     * 查询我在当前周期的任务
     *
     * @param customerId
     * @param cycle
     * @param taskId
     * @return
     */
    @Override
    public ActivityTaskItem findByCustomer(String customerId, String cycle, String taskId) {
        ActivityTaskItem param = ActivityTaskItem.buildPrimary(customerId, taskId, cycle);
        return getRow(param);
    }

    /**
     * 查询我在当前周期的任务
     *
     * @param customerId 用户id
     * @param cycle      当前任务周期
     * @param taskIds     config->id
     * @return
     */
    @Override
    public List<ActivityTaskItem> findByCustomer(String customerId, String cycle, List<String> taskIds) {
        List<ActivityTaskItem> parameters = new ArrayList<>();
        for(String taskId : taskIds){
            parameters.add(ActivityTaskItem.buildPrimary(customerId, taskId, cycle));
        }
        return super.batchGetRow(parameters);
    }

    @Override
    public List<ActivityTaskItem> findByCustomer(String customerId, String taskId) {
        List<RangeQueryParameter> parameters = new ArrayList<>();
        parameters.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.fromString(customerId)));
        parameters.add(new RangeQueryParameter("task_id", PrimaryKeyValue.fromString(taskId)));
        parameters.add(new RangeQueryParameter("cycle", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        parameters.add(new RangeQueryParameter("target_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return rangeQuery(parameters);
    }

    @Override
    public List<ActivityTaskItem> findDetailByCustomer(String customerId, String cycle, String taskId) {
        List<RangeQueryParameter> parameters = new ArrayList<>();
        parameters.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.fromString(customerId)));
        parameters.add(new RangeQueryParameter("task_id", PrimaryKeyValue.fromString(taskId)));
        parameters.add(new RangeQueryParameter("cycle", PrimaryKeyValue.fromString(cycle)));
        parameters.add(new RangeQueryParameter("target_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return rangeQuery(parameters);
    }

    @Override
    public ActivityTaskItem findDetailByCustomer(String customerId, String cycle, String taskId, String targetId) {
        ActivityTaskItem activityTaskItem = new ActivityTaskItem();
        activityTaskItem.setCustomerId(customerId);
        activityTaskItem.setCycle(cycle);
        activityTaskItem.setTaskId(taskId);
        activityTaskItem.setTargetId(targetId);
        return getRow(activityTaskItem);
    }

    @Override
    public ActivityTaskItem findByTarget(String platform, String targetId, String cycle, String taskId) {
        List<RangeQueryParameter> parameters = new ArrayList<>();
        parameters.add(new RangeQueryParameter("target_id", PrimaryKeyValue.fromString(targetId)));
        parameters.add(new RangeQueryParameter("task_id", PrimaryKeyValue.fromString(taskId)));
        parameters.add(new RangeQueryParameter("cycle", PrimaryKeyValue.fromString(cycle)));
        parameters.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return rangeQueryOne(ActivityTaskItem.INDEX_SOCIAL_TASK, parameters);
    }

    @Override
    public Long incrementProgress(ActivityTaskItem taskItem, Integer inc) {
        return super.incrementAndReturn(taskItem, inc != null ? inc : 1, "progress");
    }

    @Override
    public boolean execute(ActivityTaskItem partition, TransactionCallback<ActivityTaskItem> action) {
        return super.execute(partition, action);
    }

    @Override
    public TokenPage<ActivityTaskItem> findByCodeList(List<String> codeList,String startTime, String endTime, List<String> status, String nextToken,
        int limit) {

        BoolQuery query = QueryBuilders.bool()
            .must(QueryBuilders.terms("event").terms(codeList.toArray()))
            .must(QueryBuilders.bool().filter(QueryBuilders.range("cycle").greaterThan(startTime).lessThanOrEqual(endTime)))
            .build();

        return pageSearchQuery(query, null, nextToken, limit, "activity_task_item_index");
    }

    @Override
    public Page<ActivityTaskItem> findByIdList(String customerId, String taskId, String startTime, String endTime, int limit) {

        BoolQuery query = QueryBuilders.bool()
                .must(QueryBuilders.term("customer_id", customerId))
                .must(QueryBuilders.term("task_id", taskId))
                .must(QueryBuilders.term("target_id", "-1"))
                .must(QueryBuilders.bool().filter(QueryBuilders.range("cycle").greaterThanOrEqual(startTime).lessThanOrEqual(endTime)))
                .build();
        Sort sort = new Sort(Arrays.asList(new FieldSort("cycle", SortOrder.DESC)));
        return pageSearchQuery(query, sort, 0, limit, "activity_task_item_index");
    }
}
