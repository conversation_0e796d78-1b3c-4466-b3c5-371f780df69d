package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.kikitrade.activity.dal.tablestore.builder.UserLotteryProfileBuilder;
import com.kikitrade.activity.dal.tablestore.model.UserLotteryProfile;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用户抽奖档案表数据访问层实现
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class UserLotteryProfileBuilderImpl extends WideColumnStoreBuilder<UserLotteryProfile> implements UserLotteryProfileBuilder {

    @PostConstruct
    public void init() {
        super.init(UserLotteryProfile.class);
    }

    @Override
    public UserLotteryProfile findByUserId(String userId) {
        try {
            UserLotteryProfile profile = new UserLotteryProfile();
            profile.setUserId(userId);
            return super.getRow(profile);
        } catch (Exception e) {
            log.error("查询用户抽奖档案失败: userId={}", userId, e);
            return null;
        }
    }

    @Override
    public boolean insert(UserLotteryProfile profile) {
        return super.putRow(profile, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean update(UserLotteryProfile profile) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return super.updateRow(profile, condition);
    }

    @Override
    public UserLotteryProfile findById(String userId) {
        UserLotteryProfile profile = new UserLotteryProfile();
        profile.setUserId(userId);
        return getRow(profile);
    }
}