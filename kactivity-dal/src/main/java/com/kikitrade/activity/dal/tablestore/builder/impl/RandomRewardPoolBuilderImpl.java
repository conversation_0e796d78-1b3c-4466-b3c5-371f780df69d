package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.kikitrade.activity.dal.tablestore.builder.RandomRewardPoolBuilder;
import com.kikitrade.activity.dal.tablestore.model.RandomRewardPool;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.kikitrade.activity.dal.tablestore.model.RandomRewardPool.SEARCH_RANDOM_REWARD_POOL;

/**
 * <AUTHOR>
 * @date 2025/9/4 10:48
 * @description:
 */
@Slf4j
@Component
public class RandomRewardPoolBuilderImpl extends WideColumnStoreBuilder<RandomRewardPool> implements RandomRewardPoolBuilder {
    /**
     * 根据随机池ID查询奖励池配置
     *
     * @param poolId 随机池ID
     * @param saasId SaaS ID
     * @return 奖励池配置列表
     */
    @Override
    public List<RandomRewardPool> findByPoolId(String poolId, String saasId) {
        BoolQuery.Builder builder = QueryBuilders.bool()
                .must(QueryBuilders.term("pool_id", poolId))
                .must(QueryBuilders.term("saas_id", saasId));
        return pageSearchQuery(builder.build(), null, 0, 100, SEARCH_RANDOM_REWARD_POOL).getRows();
    }

    /**
     * 根据随机池ID和物品类型查询奖励池配置
     *
     * @param poolId   随机池ID
     * @param itemType 物品类型
     * @param saasId   SaaS ID
     * @return 奖励池配置列表
     */
    @Override
    public List<RandomRewardPool> findByPoolIdAndItemType(String poolId, String itemType, String saasId) {
        return List.of();
    }

    /**
     * 根据SaaS ID查询所有活跃的奖励池配置
     *
     * @param saasId SaaS ID
     * @return 奖励池配置列表
     */
    @Override
    public List<RandomRewardPool> findActiveBySaasId(String saasId) {
        return List.of();
    }
}
