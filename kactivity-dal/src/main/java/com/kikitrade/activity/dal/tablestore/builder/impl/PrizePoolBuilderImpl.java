package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.kikitrade.activity.dal.tablestore.builder.PrizePoolBuilder;
import com.kikitrade.activity.dal.tablestore.model.PrizePool;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 奖池配置表数据访问层实现
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
public class PrizePoolBuilderImpl extends WideColumnStoreBuilder<PrizePool> implements PrizePoolBuilder {

    @PostConstruct
    public void init() {
        super.init(PrizePool.class);
    }

    @Override
    public PrizePool findByCode(String code) {
        BoolQuery.Builder builder = QueryBuilders.bool()
                .must(QueryBuilders.term("code", code))
                .must(QueryBuilders.term("status", "ACTIVE"));

        return searchOne(builder.build(), PrizePool.SEARCH_PRIZE_POOL);
    }

    @Override
    public List<PrizePool> findActiveBySaasId(String saasId) {
        List<RangeQueryParameter> queryList = new ArrayList<>();
        queryList.add(new RangeQueryParameter("saas_id", PrimaryKeyValue.fromString(saasId), PrimaryKeyValue.fromString(saasId)));
        queryList.add(new RangeQueryParameter("code", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));

        List<PrizePool> allPools = rangeQuery(queryList);
        return allPools.stream()
                .filter(pool -> "ACTIVE".equals(pool.getStatus()))
                .collect(java.util.stream.Collectors.toList());
    }

    @Override
    public PrizePool findByCodeAndSaasId(String code, String saasId) {
        // 优化：直接主键查询，性能提升10-100倍
        // 注意：主键顺序已调整为 saasId(分区键) + code(排序键)
        PrizePool prizePool = new PrizePool();
        prizePool.setSaasId(saasId);  // 先设置分区键
        prizePool.setCode(code);      // 再设置排序键
        return getRow(prizePool);
    }

    @Override
    public boolean insert(PrizePool prizePool) {
        return super.putRow(prizePool, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean update(PrizePool prizePool) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return super.updateRow(prizePool, condition);
    }

    @Override
    public List<PrizePool> findActiveByTimeRange(long startTime, long endTime, String saasId) {
        BoolQuery.Builder builder = QueryBuilders.bool()
                .must(QueryBuilders.term("status", "ACTIVE"))
                .must(QueryBuilders.term("saas_id", saasId))
                .must(QueryBuilders.range("start_time").lessThanOrEqual(endTime))
                .must(QueryBuilders.range("end_time").greaterThanOrEqual(startTime));
        Page<PrizePool> page = pageSearchQuery(builder.build(), null, 0, 100, PrizePool.SEARCH_PRIZE_POOL);
        return page.getRows();
    }

}