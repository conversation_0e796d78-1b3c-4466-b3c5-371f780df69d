package com.kikitrade.activity.dal;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.kikitrade.kseq.api.SeqClient;
import com.kikitrade.kseq.api.model.SeqRule;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/20 14:09
 */
@Component
public class IdGenerator {

    @Resource
    private SeqClient seqClient;

    private static SeqClient client;
    private static final Snowflake snowflake = IdUtil.getSnowflake();
    private static final SeqRule TASK_SEQ = new SeqRule("IDX_ACTIVITY_TASK", 1, null, null);

    public IdGenerator(){
        client = seqClient;
    }

    public static String nextSnowflake(){
        return snowflake.nextIdStr();
    }

    public static String next(SeqRule seqRule){
        return client.next(seqRule);
    }

    public static String next(String prefix, SeqRule seqRule){
        seqRule.setRulePrefix(prefix);
        return client.next(seqRule);
    }
}
