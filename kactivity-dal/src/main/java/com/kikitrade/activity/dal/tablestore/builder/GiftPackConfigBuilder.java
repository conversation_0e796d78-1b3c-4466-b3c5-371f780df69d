package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.GiftPackConfig;

import java.util.List;

/**
 * 礼包内容规则表数据访问层
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface GiftPackConfigBuilder {

    /**
     * 根据礼包ID查询礼包配置规则
     * 
     * @param packId 礼包ID
     * @param saasId SaaS ID
     * @return 礼包配置规则列表
     */
    List<GiftPackConfig> findByPackId(String packId, String saasId);

    /**
     * 根据礼包ID和规则类型查询配置
     * 
     * @param packId 礼包ID
     * @param ruleType 规则类型
     * @param saasId SaaS ID
     * @return 礼包配置规则列表
     */
    List<GiftPackConfig> findByPackIdAndRuleType(String packId, String ruleType, String saasId);

    /**
     * 根据SaaS ID查询所有活跃的礼包配置
     * 
     * @param saasId SaaS ID
     * @return 礼包配置规则列表
     */
    List<GiftPackConfig> findActiveBySaasId(String saasId);
}
