package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.kikitrade.activity.dal.tablestore.builder.ProgressChestConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.ProgressChestConfig;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/9/3 20:37
 * @description:
 */
@Slf4j
@Component
public class ProgressChestConfigBuilderImpl extends WideColumnStoreBuilder<ProgressChestConfig> implements ProgressChestConfigBuilder {

    @Override
    public boolean insert(ProgressChestConfig progressChestConfig) {
        log.info("insert progressChestConfig:{}", progressChestConfig);
        return putRow(progressChestConfig, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean update(ProgressChestConfig progressChestConfig) {
        return false;
    }

    @Override
    public List<ProgressChestConfig> findBySaasIdAndPrizePoolCode(String saasId, String prizePoolCode) {
        log.info("findBySaasIdAndPrizePoolCode saasId:{}, prizePoolCode:{}", saasId, prizePoolCode);
        List<RangeQueryParameter> queryList = new ArrayList<>();
        queryList.add(new RangeQueryParameter("saas_id", PrimaryKeyValue.fromString(saasId), PrimaryKeyValue.fromString(saasId)));
        queryList.add(new RangeQueryParameter("prize_pool_code", PrimaryKeyValue.fromString(prizePoolCode), PrimaryKeyValue.fromString(prizePoolCode)));
        queryList.add(new RangeQueryParameter("chest_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));

        return rangeQuery(queryList);
    }

    @Override
    public ProgressChestConfig findByChestId(String chestId, String saasId) {
        log.info("findByChestId chestId:{}, saasId:{}", chestId, saasId);
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.must(QueryBuilders.term("chest_id", chestId));
        builder.must(QueryBuilders.term("saas_id", saasId));

        return searchOne(builder.build(), ProgressChestConfig.SEARCH_PROGRESS_CHEST_CONFIG);
    }
}
