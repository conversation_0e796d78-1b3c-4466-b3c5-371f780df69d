package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.TaskConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/20 11:50
 */
public interface TaskConfigBuilder {

    boolean insert(TaskConfig taskConfig);
    boolean batchInsert(List<TaskConfig> taskConfigs);
    boolean update(TaskConfig taskConfig);
    boolean update(TaskConfig taskConfig, List<String> updateColumns);

    /**
     * 根据taskId查询
     * @param taskId
     * @return
     */
    TaskConfig getTaskById(String taskId);

    /**
     * 根据code查询任务列表
     * @param code
     * @return
     */
    List<TaskConfig> getTaskByCode(String saasId, String code);

    /**
     * 根据groupId查询任务列表
     * @param groupId
     * @return
     */
    List<TaskConfig> getTaskByGroupId(String groupId);

    /**
     * 根据platform查询任务列表
     * @param platform
     * @return
     */
    List<TaskConfig> findTaskBySaasId(String saasId, String channel, String positions);

    boolean delete(List<String> ids);
}
