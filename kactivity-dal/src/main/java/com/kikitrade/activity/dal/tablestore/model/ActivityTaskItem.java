package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.Index;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Table(name = "activity_task_item")
public class ActivityTaskItem implements Serializable {

    public static final String DEFAULT_TARGET = "-1";
    public static final String DEFAULT_CYCLE = "19900101";

    public static final String INDEX_SOCIAL_TASK = "idx_social_task_v1";

    public static final String INDEX_TASK = "idx_task";

    @PartitionKey(name = "customer_id")
    @Index(name = INDEX_SOCIAL_TASK, pkColumn = "customer_id", pkValue = 3)
    @Index(name = INDEX_TASK, pkColumn = "customer_id", pkValue = 0)
    private String customerId;

    /**
     * config表的taskId
     */
    @PartitionKey(name = "task_id", value = 1)
    @Index(name = INDEX_SOCIAL_TASK, pkColumn = "task_id", pkValue = 1)
    @Index(name = INDEX_TASK, pkColumn = "task_id", pkValue = 3)
    private String taskId;

    @PartitionKey(name = "cycle", value = 2)
    @Index(name = INDEX_SOCIAL_TASK, pkColumn = "cycle", pkValue = 2)
    @Index(name = INDEX_TASK, pkColumn = "cycle", pkValue = 1)
    private String cycle;

    @PartitionKey(name = "target_id", value = 3)
    @Index(name = INDEX_SOCIAL_TASK, pkColumn = "target_id")
    @Index(name = INDEX_TASK, pkColumn = "target_id", pkValue = 2)
    private String targetId;

    @Column(name = "event", isDefined = true)
    @Index(name = INDEX_SOCIAL_TASK, dfColumn = "event")
    private String event;

    @Column(name = "business_id", isDefined = true)
    private String businessId;

    @Column(name = "complete_time", isDefined = true)
    @Index(name = INDEX_SOCIAL_TASK, dfColumn = "complete_time")
    private String completeTime;


    @Column(name = "status", isDefined = true)
    @Index(name = INDEX_SOCIAL_TASK, dfColumn = "status")
    @Index(name = INDEX_TASK, dfColumn = "status")
    private String status;

    /**
     * 任务进度limit
     */
    @Column(name = "complete_threshold", isDefined = true, type = Column.Type.INTEGER)
    private Integer completeThreshold;

    /**
     * 当前进度
     */
    @Column(name = "progress", isDefined = true, type = Column.Type.INTEGER)
    @Index(name = INDEX_TASK, dfColumn = "progress")
    @Index(name = INDEX_SOCIAL_TASK, dfColumn = "progress")
    private Integer progress;

    @Column(name = "activity_id", isDefined = true)
    private String activityId;

    @Column(name = "expired_time", isDefined = true)
    private String expiredTime;

    @Column(name = "created", isDefined = true)
    private String created;

    @Column(name = "modified", isDefined = true)
    private String modified;


    @Column(name = "platform", isDefined = true)
    private String platform;

    @Column(name = "amount", type = Column.Type.DOUBLE, isDefined = true)
    @Index(name = INDEX_SOCIAL_TASK, dfColumn = "amount")
    private BigDecimal amount;

    @Column(name = "currency")
    private String currency;

    @Column(name = "user_icon")
    private String userIcon;

    public static ActivityTaskItem buildPrimary(String customerId, String taskId, String cycle){
        ActivityTaskItem item = new ActivityTaskItem();
        item.setCustomerId(customerId);
        item.setTaskId(taskId);
        item.setCycle(cycle);
        item.setTargetId(DEFAULT_TARGET);
        return item;
    }

    public static ActivityTaskItem buildDetailPrimary(String customerId, String taskId, String cycle, String targetId){
        ActivityTaskItem item = new ActivityTaskItem();
        item.setCustomerId(customerId);
        item.setTaskId(taskId);
        item.setCycle(cycle);
        item.setTargetId(targetId);
        return item;
    }
}
