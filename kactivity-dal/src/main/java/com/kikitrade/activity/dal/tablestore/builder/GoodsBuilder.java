package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.Goods;
import com.kikitrade.framework.common.model.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/12/26 15:26
 */
public interface GoodsBuilder {

    /**
     * 新增商品
     * @param goods
     * @return
     */
    boolean insert(Goods goods);

    /**
     * 修改商品
     * @param goods
     * @return
     */
    boolean update(Goods goods);

    /**
     * 根据id查询商品
     * @param goodsId
     * @return
     */
    Goods findById(String goodsId);

    Page<Goods> findAll(int offset, int limit, String saasId, String exclude);

    String nextId();

    List<Goods> findBySceneCode(String sceneCode);
}
