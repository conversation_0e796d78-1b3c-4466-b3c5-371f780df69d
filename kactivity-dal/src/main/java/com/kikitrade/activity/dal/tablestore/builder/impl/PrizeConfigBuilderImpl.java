package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.activity.dal.tablestore.builder.PrizeConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.PrizeConfig;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 奖品配置表数据访问层实现
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
public class PrizeConfigBuilderImpl extends WideColumnStoreBuilder<PrizeConfig> implements PrizeConfigBuilder {

    @PostConstruct
    public void init() {
        super.init(PrizeConfig.class);
    }

    @Override
    public List<PrizeConfig> findActiveByPrizePoolCode(String saasId, String prizePoolCode) {
        List<RangeQueryParameter> parameters = new ArrayList<>();
        parameters.add(new RangeQueryParameter("saas_id", PrimaryKeyValue.fromString(saasId)));
        parameters.add(new RangeQueryParameter("prize_pool_code", PrimaryKeyValue.fromString(prizePoolCode)));
        parameters.add(new RangeQueryParameter("config_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        List<PrizeConfig> prizeConfigs = rangeQuery(parameters);
        prizeConfigs = prizeConfigs.stream().filter(PrizeConfig::getIsActive).collect(java.util.stream.Collectors.toList());
        return prizeConfigs;
    }

    @Override
    public List<PrizeConfig> findByPrizePoolCodeAndPreference(String saasId, String prizePoolCode, String preferenceType, String preferenceValue) {
        BoolQuery.Builder builder = QueryBuilders.bool()
            .must(QueryBuilders.term("prize_pool_code", prizePoolCode))
            .must(QueryBuilders.term("saas_id", saasId))
            .must(QueryBuilders.term("is_active", true));

        if (preferenceType != null && preferenceValue != null) {
            builder.must(QueryBuilders.term("preference_type", preferenceType))
                   .must(QueryBuilders.term("preference_value", preferenceValue));
        } else {
            // 查询通用奖品（preference_type不存在）
            builder.mustNot(QueryBuilders.exists("preference_type"));
        }

        Page<PrizeConfig> page = pageSearchQuery(builder.build(), null, 0, 100, PrizeConfig.SEARCH_PRIZE_CONFIG);
        return page.getRows();
    }

    @Override
    public List<PrizeConfig> findCommonPrizesByPoolCode(String saasId, String prizePoolCode) {
        BoolQuery query = QueryBuilders.bool()
            .must(QueryBuilders.term("saas_id", saasId))
            .must(QueryBuilders.term("prize_pool_code", prizePoolCode))
            .must(QueryBuilders.term("is_active", true))
            .mustNot(QueryBuilders.exists("preference_type"))
            .build();
        Page<PrizeConfig> page = pageSearchQuery(query, null, 0, 100, PrizeConfig.SEARCH_PRIZE_CONFIG);
        return page.getRows();
    }

    @Override
    public List<PrizeConfig> findByPrizeType(String prizePoolCode, String prizeType) {
        BoolQuery.Builder builder = QueryBuilders.bool()
                .must(QueryBuilders.term("prize_pool_code", prizePoolCode))
                .must(QueryBuilders.term("prize_type", prizeType))
                .must(QueryBuilders.term("is_active", true));
        Page<PrizeConfig> page = pageSearchQuery(builder.build(), null, 0, 100, PrizeConfig.SEARCH_PRIZE_CONFIG);
        return page.getRows();
    }

    @Override
    public boolean insert(PrizeConfig prizeConfig) {
        return super.putRow(prizeConfig, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean update(PrizeConfig prizeConfig) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return super.updateRow(prizeConfig, condition);
    }

    @Override
    public PrizeConfig findById(String prizeId) {
        PrizeConfig prizeConfig = new PrizeConfig();
        prizeConfig.setPrizeId(prizeId);
        return getRow(prizeConfig);
    }
}