package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.DirectRewardIssuanceLog;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.TokenPage;

import java.util.List;

/**
 * 定向奖励发放流水表数据访问层
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface DirectRewardIssuanceLogBuilder {

    /**
     * 根据交易ID查询发放记录（用于幂等性检查）
     * 
     * @param transactionId 交易ID
     * @return 发放记录
     */
     DirectRewardIssuanceLog findByTransactionId(String transactionId);

    /**
     * 根据用户ID查询发放记录
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param limit 查询限制
     * @return 发放记录列表
     */
    TokenPage<DirectRewardIssuanceLog> findByUserId(String userId, String saasId, int limit);

    /**
     * 根据渠道查询发放记录
     * 
     * @param channel 渠道
     * @param saasId SaaS ID
     * @param limit 查询限制
     * @return 发放记录列表
     */
    List<DirectRewardIssuanceLog> findByChannel(String channel, String saasId, int limit);

    /**
     * 根据发放状态查询记录
     * 
     * @param issueStatus 发放状态
     * @param saasId SaaS ID
     * @param limit 查询限制
     * @return 发放记录列表
     */
    List<DirectRewardIssuanceLog> findByIssueStatus(String issueStatus, String saasId, int limit);

    /**
     * 插入发放记录
     * 
     * @param log 发放记录
     * @return 是否成功
     */
    boolean insert(DirectRewardIssuanceLog log);

    /**
     * 更新发放记录
     * 
     * @param log 发放记录
     * @return 是否成功
     */
    boolean update(DirectRewardIssuanceLog log);
}
