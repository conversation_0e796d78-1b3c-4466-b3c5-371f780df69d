package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alibaba.fastjson.JSON;
import com.alicloud.openservices.tablestore.model.*;
import com.alicloud.openservices.tablestore.model.filter.CompositeColumnValueFilter;
import com.alicloud.openservices.tablestore.model.filter.SingleColumnValueFilter;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.SearchResponse;
import com.alicloud.openservices.tablestore.model.search.agg.AggregationBuilders;
import com.alicloud.openservices.tablestore.model.search.groupby.GroupByBuilders;
import com.alicloud.openservices.tablestore.model.search.groupby.GroupByFieldResultItem;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.google.common.collect.Lists;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardParam;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.ots.RangeResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward.*;

@Service
@Slf4j
public class ActivityCustomRewardStoreBuilderImpl extends WideColumnStoreBuilder<ActivityCustomReward> implements ActivityCustomRewardStoreBuilder {

    @PostConstruct
    public void init() {
        init(ActivityCustomReward.class);
    }

    @Override
    public PageResult findByBatchIdAndStatus(ActivityRewardParam activityRewardParam, int offset, int limit) {
        BoolQuery boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("batch_id", activityRewardParam.getBatchId()))
                .must(QueryBuilders.term("status", activityRewardParam.getStatus()))
                .must(QueryBuilders.term("shard", activityRewardParam.getShard()))
                .build();

        return pageSearch(boolQuery, null, offset, limit, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX);
    }

    @Override
    public ActivityCustomReward findByPrimaryId(String batchId, String customerId, String seq) {
        ActivityCustomReward reward = new ActivityCustomReward();
        reward.setBatchId(batchId);
        reward.setCustomerId(customerId);
        reward.setSeq(seq);
        return super.getRow(reward);
    }

    @Override
    public boolean insert(ActivityCustomReward reward) {
        return putRow(reward, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean batchInsert(List<ActivityCustomReward> rewardList) {
        return batchPutRow(rewardList, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean updateStatus(ActivityCustomReward activityCustomReward) {
        activityCustomReward.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return updateRow(activityCustomReward);
    }

    @Override
    public boolean existByBatchIdAndStatus(String batchId, String rewardStatus) {
        List<RangeQueryParameter> queryList = new ArrayList<>();
        queryList.add(new RangeQueryParameter("status", PrimaryKeyValue.fromString(rewardStatus), PrimaryKeyValue.fromString(rewardStatus)));
        queryList.add(new RangeQueryParameter("batch_id", PrimaryKeyValue.fromString(batchId), PrimaryKeyValue.fromString(batchId)));
        queryList.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        queryList.add(new RangeQueryParameter("seq", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return CollectionUtils.isNotEmpty(super.rangeQuery(INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, queryList, 1));
    }


    @Override
    public RangeResult<ActivityCustomReward> findByStatusAndBatchId(String rewardStatus, String batchId, PrimaryKey nextToken) {
        List<RangeQueryParameter> queryList = new ArrayList<>();
        queryList.add(new RangeQueryParameter("status", PrimaryKeyValue.fromString(rewardStatus), PrimaryKeyValue.fromString(rewardStatus)));
        queryList.add(new RangeQueryParameter("batch_id", PrimaryKeyValue.fromString(batchId), PrimaryKeyValue.fromString(batchId)));
        queryList.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        queryList.add(new RangeQueryParameter("seq", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return rangeQueryWithNextToken(INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, queryList, null, Direction.FORWARD, 100, nextToken);
    }

    @Override
    public RangeResult<ActivityCustomReward> findAllByBatchId(ActivityRewardPageParam activityRewardPageParam) {
        List<RangeQueryParameter> queryList = new ArrayList<>();
        if(StringUtils.isBlank(activityRewardPageParam.getBatchId())){
            return null;
        }

        queryList.add(new RangeQueryParameter("batch_id", PrimaryKeyValue.fromString(activityRewardPageParam.getBatchId()), PrimaryKeyValue.fromString(activityRewardPageParam.getBatchId())));
        queryList.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        queryList.add(new RangeQueryParameter("seq", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));

        return super.rangeQueryWithNextToken(INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, queryList, null, Direction.FORWARD, 500, activityRewardPageParam.getNextToken());
    }

    @Override
    public Page<ActivityCustomReward> findAllByStatus(ActivityRewardPageParam activityRewardPageParam, int offset, int limit) {

        BoolQuery boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.terms("status").terms(activityRewardPageParam.getStatusList().toArray()))
                .build();
        Sort sort = new Sort(Collections.singletonList(new FieldSort("reward_time", SortOrder.DESC)));
        return pageSearchQuery(boolQuery, sort, offset, limit, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX);
    }

    @Override
    public RangeResult<ActivityCustomReward> findByBatchIdAndStatus(ActivityRewardPageParam activityRewardPageParam) {
        List<RangeQueryParameter> queryList = new ArrayList<>();
        if(StringUtils.isBlank(activityRewardPageParam.getBatchId())){
            return null;
        }

        queryList.add(new RangeQueryParameter("batch_id", PrimaryKeyValue.fromString(activityRewardPageParam.getBatchId()), PrimaryKeyValue.fromString(activityRewardPageParam.getBatchId())));
        queryList.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        queryList.add(new RangeQueryParameter("seq", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        CompositeColumnValueFilter filter = new CompositeColumnValueFilter(CompositeColumnValueFilter.LogicOperator.OR);
        for(String status : activityRewardPageParam.getStatusList()){
            filter.addFilter(new SingleColumnValueFilter("status", SingleColumnValueFilter.CompareOperator.EQUAL, ColumnValue.fromString(status)));
        }
        return super.rangeQueryWithNextToken(INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, queryList, filter, Direction.FORWARD, 500, activityRewardPageParam.getNextToken());
    }

    @Override
    public RangeResult<ActivityCustomReward> findByBatchIdAndStatusAndShard(ActivityRewardPageParam activityRewardPageParam) {
        List<RangeQueryParameter> queryList = new ArrayList<>();
        if(StringUtils.isBlank(activityRewardPageParam.getBatchId())){
            return null;
        }

        queryList.add(new RangeQueryParameter("batch_id", PrimaryKeyValue.fromString(activityRewardPageParam.getBatchId()), PrimaryKeyValue.fromString(activityRewardPageParam.getBatchId())));
        queryList.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        queryList.add(new RangeQueryParameter("seq", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        CompositeColumnValueFilter filter = new CompositeColumnValueFilter(CompositeColumnValueFilter.LogicOperator.AND);
        filter.addFilter(new SingleColumnValueFilter("shard", SingleColumnValueFilter.CompareOperator.EQUAL, ColumnValue.fromLong(activityRewardPageParam.getShard())));
        for(String status : activityRewardPageParam.getStatusList()){
            CompositeColumnValueFilter statusFilter = new CompositeColumnValueFilter(CompositeColumnValueFilter.LogicOperator.OR);
            statusFilter.addFilter(new SingleColumnValueFilter("status", SingleColumnValueFilter.CompareOperator.EQUAL, ColumnValue.fromString(status)));
            filter.addFilter(statusFilter);
        }
        return super.rangeQueryWithNextToken(INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, queryList, filter, Direction.FORWARD, 500, activityRewardPageParam.getNextToken());
    }

    @Override
    public long count(String batchId) {
        String aggName = "count_agg_customer_reward_by_batch_id";
        SearchQuery query = SearchQuery.newBuilder().query(QueryBuilders.term("batch_id", batchId))
                .addAggregation(AggregationBuilders.count(aggName, "batch_id")).build();

        return searchCount(query, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, aggName);
    }

    @Override
    public Result<Map<String, String>> delete(List<String> ids) {
        boolean success = true;
        Map<String, String> map = new HashMap<>();
        for(String id : ids){
            String[] idArr = id.trim().split("-");
            String batchId = idArr[0];
            String customerId = idArr[1];
            String seq = idArr[2];
            boolean result = true;
            try{
                ActivityCustomReward customReward = findByPrimaryId(batchId, customerId, seq);
                if(customReward == null){
                    success = false;
                    map.put(id, "删除失败，记录不存在");
                }else{
                    customReward.setStatus(ActivityConstant.RewardStatusEnum.DELETE.name());
                    result = updateStatus(customReward);
                    success = success & result;
                    if(result){
                        map.put(id, "删除成功");
                    }else{
                        map.put(id, "删除失败");
                    }
                }
            }catch (Exception ex){
                log.error("custom_reward del fail, id:{}", id,ex);
                success = success & result;
                map.put(id, ex.getMessage());
            }
        }
        Result<Map<String,String>> result = new Result<>(success, "");
        result.setData(map);
        return result;
    }

    @Override
    public PageResult findByReferId(String referId, int offset, int limit) {
        BoolQuery boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("customer_id", referId))
                .must(QueryBuilders.term("side", ActivityConstant.SideEnum.INVITER.name()))
                .must(QueryBuilders.term("status", ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name()))
                .build();
        Sort sort = new Sort(Collections.singletonList(new FieldSort("reward_time", SortOrder.DESC)));
        return pageSearch(boolQuery, sort, offset, limit, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX);
    }

    @Override
    public long countByReferId(String referId) {
        String aggName = "count_agg_activity_reward_by_customer_id";

        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("customer_id", referId))
                .must(QueryBuilders.term("side", ActivityConstant.SideEnum.INVITER.name()))
                .must(QueryBuilders.term("status", ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name()));
        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .addAggregation(AggregationBuilders.count(aggName, "customer_id")).build();

        return searchCount(searchQuery, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, aggName);
    }

    @Override
    public BigDecimal sumCostByCustomerIdAndSide(String customerId, String side) {
        String aggName = "sum_agg_activity_reward_by_customer_side";

        BoolQuery.Builder boolQuery = QueryBuilders.bool().must(QueryBuilders.term("customer_id", customerId))
                .must(QueryBuilders.term("side", side))
                .must(QueryBuilders.term("status", ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name()));

        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .addAggregation(AggregationBuilders.sum(aggName, "cost")).build();

        return BigDecimal.valueOf(searchSum(searchQuery, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, aggName));
    }

    @Override
    public BigDecimal sumCostByCustomerIdAndSideTime(String customerId, String side, long fromRewardTime) {
        String aggName = "sum_agg_activity_reward_by_customer_side";
        ArrayList<ActivityConstant.AwardTypeEnum> rewardTypes = Lists.newArrayList(ActivityConstant.AwardTypeEnum.Token, ActivityConstant.AwardTypeEnum.TOKEN);

        BoolQuery.Builder boolQuery = QueryBuilders.bool().must(QueryBuilders.term("customer_id", customerId))
                .must(QueryBuilders.term("side", side))
                .must(QueryBuilders.term("business_type", ActivityConstant.RewardBusinessType.reward.name()))
                .must(QueryBuilders.terms("reward_type").terms(rewardTypes.stream().map(ActivityConstant.AwardTypeEnum::name).toArray()))
                .must(QueryBuilders.term("status", ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name()));

        if (fromRewardTime > 0) {
            boolQuery.must(QueryBuilders.range("reward_time").greaterThanOrEqual(fromRewardTime));
        }

        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .addAggregation(AggregationBuilders.sum(aggName, "cost")).build();

        return BigDecimal.valueOf(searchSum(searchQuery, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, aggName));
    }

    @Override
    public PageResult findByCustomerIdAndBusinessType(String customerId, String type, int offset, int limit) {
        List<ActivityConstant.RewardBusinessType> rewardBusinessTypes = Arrays.stream(StringUtils.split(type, ","))
                .map(s -> ActivityConstant.RewardBusinessType.getByKey(s)).collect(Collectors.toList());
        List<ActivityConstant.AwardTypeEnum> rewardTypes = null;
        if(rewardBusinessTypes.contains(ActivityConstant.RewardBusinessType.reward)){
            rewardTypes = Lists.newArrayList(ActivityConstant.AwardTypeEnum.TOKEN, ActivityConstant.AwardTypeEnum.Token);
        }

        BoolQuery.Builder boolQuery = buildBoolQuery(customerId, rewardBusinessTypes, rewardTypes);
        Sort sort = new Sort(Collections.singletonList(new FieldSort("reward_time", SortOrder.DESC)));
        return pageSearch(boolQuery.build(), sort, offset, limit, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX);
    }

    @Override
    public long countByCustomerIdAndBusinessType(String customerId, String businessType) {
        List<ActivityConstant.RewardBusinessType> rewardBusinessTypes = Arrays.stream(StringUtils.split(businessType, ","))
                .map(s -> ActivityConstant.RewardBusinessType.getByKey(s)).collect(Collectors.toList());
        List<ActivityConstant.AwardTypeEnum> rewardTypes = null;
        if(rewardBusinessTypes.contains(ActivityConstant.RewardBusinessType.reward)){
            rewardTypes = Lists.newArrayList(ActivityConstant.AwardTypeEnum.TOKEN, ActivityConstant.AwardTypeEnum.Token);
        }

        String aggName = "count_agg_activity_rewards_by_customer_id";
        BoolQuery.Builder boolQuery = buildBoolQuery(customerId, rewardBusinessTypes, rewardTypes);
        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .addAggregation(AggregationBuilders.count(aggName, "customer_id")).build();

        return searchCount(searchQuery, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, aggName);
    }

    private BoolQuery.Builder buildBoolQuery(String customerId, List<ActivityConstant.RewardBusinessType> rewardBusinessTypes, List<ActivityConstant.AwardTypeEnum> rewardTypes){
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("customer_id", customerId))
                .must(QueryBuilders.term("status", ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name()));

        if (rewardBusinessTypes != null && !rewardBusinessTypes.isEmpty()) {
            boolQuery.must(QueryBuilders.terms("business_type").terms(rewardBusinessTypes.stream().map(ActivityConstant.RewardBusinessType::name).toArray()));
        }
        if (rewardTypes != null && !rewardTypes.isEmpty()) {
            boolQuery.must(QueryBuilders.terms("reward_type").terms(rewardTypes.stream().map(ActivityConstant.AwardTypeEnum::name).toArray()));
        }
        return boolQuery;
    }

    @Override
    public PageResult findByBusinessType(int offset, int limit) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .mustNot(QueryBuilders.exists("business_type"));
        Sort sort = new Sort(Collections.singletonList(new FieldSort("customer_id")));
        return pageSearch(boolQuery.build(), sort, offset, limit, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX);
    }

    @Override
    public boolean update(ActivityCustomReward activityCustomReward) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return updateRow(activityCustomReward);
    }

    @Override
    public long countRewardsByTypeSide(String customerId, List<ActivityConstant.RewardBusinessType> rewardBusinessTypes, List<ActivityConstant.AwardTypeEnum> rewardTypes, ActivityConstant.SideEnum role, Long fromRewardTime) {

        String aggName = "count_agg_activity_rewards_by_type_side";

        BoolQuery.Builder boolQuery = buildBoolQuery(customerId, rewardBusinessTypes, rewardTypes, role, fromRewardTime);

        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .addAggregation(AggregationBuilders.count(aggName, "customer_id")).build();

        return searchCount(searchQuery, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, aggName);
    }

    @Override
    public PageResult getRewardsByTypeSide(String customerId, List<ActivityConstant.RewardBusinessType> rewardBusinessTypes, List<ActivityConstant.AwardTypeEnum> rewardTypes, ActivityConstant.SideEnum role, Long fromRewardTime, int offset, int limit) {
        BoolQuery.Builder boolQuery = buildBoolQuery(customerId, rewardBusinessTypes, rewardTypes, role, fromRewardTime);
        Sort sort = new Sort(Arrays.asList(new FieldSort("reward_time", SortOrder.DESC)));
        return pageSearch(boolQuery.build(), sort, offset, limit, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX);
    }

    @Override
    public BigDecimal getSumByTypeSide(String customerId, List<ActivityConstant.RewardBusinessType> rewardBusinessTypes, List<ActivityConstant.AwardTypeEnum> rewardTypes, ActivityConstant.SideEnum role, Long fromRewardTime) {
        BoolQuery.Builder boolQuery = buildBoolQuery(customerId, rewardBusinessTypes, rewardTypes, role, fromRewardTime);
        String aggName = "sum_agg_activity_reward_by_customer_business_type";
        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .addAggregation(AggregationBuilders.sum(aggName, "cost")).build();
        return BigDecimal.valueOf(searchSum(searchQuery, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, aggName));
    }

    @Override
    public Map<String, Map<String, BigDecimal>> getSumGroupByBusinessAward(String customerId, List<ActivityConstant.RewardBusinessType> rewardBusinessTypes, ActivityConstant.SideEnum role, Long fromRewardTime) {
        BoolQuery.Builder boolQuery = buildBoolQuery(customerId, rewardBusinessTypes, null, role, fromRewardTime);
        String aggName = "sum_agg_activity_reward_by_customer_business_type_reward_type";
        String groupName1 = "group_activity_reward_by_business_type";
        String groupName2 = "group_activity_reward_by_reward_type";
        // select sum(cost) from activity_customer_reward where ... group by business_type, reward_type;
        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .limit(0)
                .addGroupBy(GroupByBuilders.groupByField(groupName1, "business_type")
                                           .addSubGroupBy(GroupByBuilders.groupByField(groupName2, "reward_type")
                                                                         .addSubAggregation(AggregationBuilders.sum(aggName, "cost"))))
                .build();
        SearchResponse resp = searchGroupByMultiField(searchQuery, true, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX);
        log.info("getSumGroupByBusinessAward searchResponse={}", JSON.toJSONString(resp));
        Map<String, Map<String, BigDecimal>> rewards = new HashMap<>();
        //获取统计聚合结果
        for (GroupByFieldResultItem item : resp.getGroupByResults().getAsGroupByFieldResult(groupName1).getGroupByFieldResultItems()) {
            String businessType = item.getKey();
            if (!rewards.containsKey(businessType)) {
                rewards.put(businessType, new HashMap<>());
            }
            for (GroupByFieldResultItem subItem : item.getSubGroupByResults().getAsGroupByFieldResult(groupName2).getGroupByFieldResultItems()) {
                rewards.get(businessType).put(subItem.getKey(), BigDecimal.valueOf(subItem.getSubAggregationResults().getAsSumAggregationResult(aggName).getValue()));
            }
        }
        return rewards;
    }

    private BoolQuery.Builder buildBoolQuery(String customerId, List<ActivityConstant.RewardBusinessType> rewardBusinessTypes, List<ActivityConstant.AwardTypeEnum> rewardTypes, ActivityConstant.SideEnum role, Long fromRewardTime) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("customer_id", customerId))
                .must(QueryBuilders.term("status", ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name()));
        if (rewardBusinessTypes != null && !rewardBusinessTypes.isEmpty()) {
            boolQuery.must(QueryBuilders.terms("business_type").terms(rewardBusinessTypes.stream().map(ActivityConstant.RewardBusinessType::name).toArray()));
        }
        if (rewardTypes != null && !rewardTypes.isEmpty()) {
            boolQuery.must(QueryBuilders.terms("reward_type").terms(rewardTypes.stream().map(ActivityConstant.AwardTypeEnum::name).toArray()));
        }
        if (role != null) {
            boolQuery.must(QueryBuilders.term("side", role.name()));
        } else {
            boolQuery.must(QueryBuilders.exists("side"));
        }
        if (fromRewardTime != null && fromRewardTime > 0) {
            boolQuery.must(QueryBuilders.range("reward_time").greaterThanOrEqual(fromRewardTime));
        }
        return boolQuery;
    }

    public ActivityCustomReward findByBusinessId(String businessId){
        BoolQuery boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("business_id", businessId))
                .build();
        return searchOne(boolQuery, INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX);
    }
}
