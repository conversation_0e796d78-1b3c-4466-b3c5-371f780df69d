package com.kikitrade.activity.dal;

import com.kikitrade.kseq.api.SeqClient;
import com.kikitrade.kseq.api.model.SeqRule;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
public class SeqGeneraterService {

    @Resource
    private SeqLocalClient seqLocalClient;
    @Resource
    private SeqClient seqClient;

    public String next(SeqRule seqRule){

        String property = System.getProperty("spring.profiles.active");
        if("local".equals(property)){
            return seqLocalClient.next(seqRule);
        }

        return seqClient.next(seqRule);
    }
}
