package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户进度数据模型
 * 记录用户在各种进度类型上的当前进度和历史记录
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Table(name = "user_progress_tracker")
public class UserProgressTracker implements Serializable {

    public static final String SEARCH_USER_PROGRESS_TRACKER = "search_user_progress_tracker";

    /**
     * 用户ID
     */
    @PartitionKey(name = "user_id")
    @SearchIndex(name = SEARCH_USER_PROGRESS_TRACKER, column = "user_id")
    private String userId;

    /**
     * 关联的奖池编码
     */
    @PartitionKey(name = "prize_pool_code", value = 1)
    @SearchIndex(name = SEARCH_USER_PROGRESS_TRACKER, column = "prize_pool_code")
    private String prizePoolCode;

    /**
     * 当前周期的开始时间
     */
    @PartitionKey(name = "cycle_start_time", value = 2)
    @SearchIndex(name = SEARCH_USER_PROGRESS_TRACKER, column = "cycle_start_time")
    private Long cycleStartTime;

    /**
     * 当前进度值
     */
    @Column(name = "current_progress", type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_USER_PROGRESS_TRACKER, column = "current_progress")
    private Integer currentProgress;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_USER_PROGRESS_TRACKER, column = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_USER_PROGRESS_TRACKER, column = "update_time")
    private Long updateTime;
}
