package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * 随机奖励池表数据模型
 * 对应技术规格书中的 random_reward_pool 表
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Table(name = "random_reward_pool")
public class RandomRewardPool implements Serializable {

    public static final String SEARCH_RANDOM_REWARD_POOL = "search_random_reward_pool";

    /**
     * 主键ID
     */
    @PartitionKey(name = "id")
    private String id;

    /**
     * 随机池ID
     */
    @Column(name = "pool_id")
    @SearchIndex(name = SEARCH_RANDOM_REWARD_POOL, column = "pool_id")
    private String poolId;

    /**
     * 物品ID
     */
    @Column(name = "item_id")
    private String itemId;

    /**
     * 物品类型 (GAME_ITEM等)
     */
    @Column(name = "item_type")
    private String itemType;

    /**
     * 物品名称
     */
    @Column(name = "item_name")
    private String itemName;

    /**
     * 最小数量
     */
    @Column(name = "quantity_min", type = Column.Type.INTEGER)
    private Integer quantityMin;

    /**
     * 最大数量
     */
    @Column(name = "quantity_max", type = Column.Type.INTEGER)
    private Integer quantityMax;

    /**
     * 权重 (用于随机抽取)
     */
    @Column(name = "weight", type = Column.Type.INTEGER)
    private Integer weight;

    /**
     * 是否启用
     */
    @Column(name = "is_active", type = Column.Type.BOOLEAN)
    private Boolean isActive;

    /**
     * 物品描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 物品图标URL
     */
    @Column(name = "icon_url")
    private String iconUrl;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    private Long updateTime;

    /**
     * SaaS ID
     */
    @Column(name = "saas_id")
    @SearchIndex(name = SEARCH_RANDOM_REWARD_POOL, column = "saas_id")
    private String saasId;
}
