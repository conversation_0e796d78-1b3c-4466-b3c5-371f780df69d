package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户偏好表数据模型
 * 对应技术规格书中的 user_preference 表
 * 存储用户针对特定奖池的偏好设置
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Table(name = "user_preference")
public class UserPreference implements Serializable {

    public static final String SEARCH_USER_PREFERENCE = "search_user_preference";

    /**
     * 用户ID（分区键）
     */
    @PartitionKey(name = "user_id")
    private String userId;

    /**
     * 关联的奖池编码（排序键1）
     */
    @PartitionKey(name = "prize_pool_code", value = 1)
    private String prizePoolCode;

    /**
     * 偏好类型（排序键2）
     * 如：SELECTED_HERO, VIP_LEVEL, USER_LEVEL, PREFERRED_REGION
     */
    @PartitionKey(name = "preference_type", value = 2)
    private String preferenceType;

    /**
     * 偏好值
     * 如：HERO_A_001, GOLD, 15, ASIA
     */
    @Column(name = "preference_value")
    private String preferenceValue;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    private Long updateTime;


    /**
     * 偏好描述（可选）
     */
    @Column(name = "description")
    private String description;

    /**
     * 偏好来源
     * USER_MANUAL: 用户手动设置
     * SYSTEM_AUTO: 系统自动设置
     * ADMIN_CONFIG: 管理员配置
     */
    @Column(name = "source")
    private String source;

    /**
     * 是否启用
     */
    @Column(name = "is_enabled", type = Column.Type.BOOLEAN)
    private Boolean isEnabled;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    private Long createTime;

    /**
     * 扩展属性（JSON格式）
     */
    @Column(name = "extra_properties")
    private String extraProperties;
}
