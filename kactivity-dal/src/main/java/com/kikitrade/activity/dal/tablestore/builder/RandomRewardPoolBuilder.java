package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.RandomRewardPool;

import java.util.List;

/**
 * 随机奖励池表数据访问层
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface RandomRewardPoolBuilder {

    /**
     * 根据随机池ID查询奖励池配置
     * 
     * @param poolId 随机池ID
     * @param saasId SaaS ID
     * @return 奖励池配置列表
     */
    List<RandomRewardPool> findByPoolId(String poolId, String saasId);

    /**
     * 根据随机池ID和物品类型查询奖励池配置
     * 
     * @param poolId 随机池ID
     * @param itemType 物品类型
     * @param saasId SaaS ID
     * @return 奖励池配置列表
     */
    List<RandomRewardPool> findByPoolIdAndItemType(String poolId, String itemType, String saasId);

    /**
     * 根据SaaS ID查询所有活跃的奖励池配置
     * 
     * @param saasId SaaS ID
     * @return 奖励池配置列表
     */
    List<RandomRewardPool> findActiveBySaasId(String saasId);
}
