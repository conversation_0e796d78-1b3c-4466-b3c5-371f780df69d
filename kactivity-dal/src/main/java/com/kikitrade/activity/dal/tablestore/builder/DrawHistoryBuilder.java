package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.DrawHistory;
import com.kikitrade.framework.common.model.TokenPage;

import java.util.List;

/**
 * 抽奖历史记录表数据访问层接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface DrawHistoryBuilder {

    /**
     * 插入单条抽奖历史记录
     * 
     * @param drawHistory 抽奖历史记录
     * @return 是否成功
     */
    boolean insert(DrawHistory drawHistory);

    /**
     * 批量插入抽奖历史记录
     * 使用阿里云OTS的BatchWriteRow接口，支持原子性操作
     * 
     * @param drawHistories 抽奖历史记录列表
     * @return 是否成功
     */
    boolean batchInsert(List<DrawHistory> drawHistories);

    /**
     * 根据用户ID查询抽奖历史记录
     * 
     * @param userId 用户ID
     * @param nextToken 分页token
     * @param limit 查询限制
     * @return 抽奖历史记录列表
     */
    TokenPage<DrawHistory> findByUserId(String userId, String nextToken, int limit);

    /**
     * 根据批量交易ID查询抽奖历史记录
     * 
     * @param batchTransactionId 批量交易ID
     * @param nextToken 分页token
     * @param limit 查询限制
     * @return 抽奖历史记录列表
     */
    TokenPage<DrawHistory> findByBatchTransactionId(String batchTransactionId, String nextToken, int limit);

    /**
     * 根据上游交易ID查询抽奖历史记录
     * 
     * @param upstreamTransactionId 上游交易ID
     * @param nextToken 分页token
     * @param limit 查询限制
     * @return 抽奖历史记录列表
     */
    TokenPage<DrawHistory> findByUpstreamTransactionId(String upstreamTransactionId, String nextToken, int limit);

    /**
     * 根据奖池编码查询抽奖历史记录
     * 
     * @param prizePoolCode 奖池编码
     * @param saasId SaaS ID
     * @param nextToken 分页token
     * @param limit 查询限制
     * @return 抽奖历史记录列表
     */
    TokenPage<DrawHistory> findByPrizePoolCode(String prizePoolCode, String saasId, String nextToken, int limit);

    /**
     * 根据用户ID和时间范围查询抽奖历史记录
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param nextToken 分页token
     * @param limit 查询限制
     * @return 抽奖历史记录列表
     */
    TokenPage<DrawHistory> findByUserIdAndTimeRange(String userId, Long startTime, Long endTime, String nextToken, int limit);

    /**
     * 根据主键查询单条抽奖历史记录
     * 
     * @param userId 用户ID
     * @param eventId 事件ID
     * @return 抽奖历史记录
     */
    DrawHistory findByPrimaryKey(String userId, String eventId);
}
