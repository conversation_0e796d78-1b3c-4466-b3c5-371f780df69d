package com.kikitrade.activity.dal.redis.impl;

import com.kikitrade.activity.dal.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

@Component
@Slf4j
public class RedisServiceImpl implements RedisService {

    @Resource
    private RedisTemplate redisTemplate;

    @Override
    public Boolean del(String key) {
        return redisTemplate.delete(key);
    }

    @Override
    public Long del(List<String> keys) {
        return redisTemplate.delete(keys);
    }

    @Override
    public Boolean expire(String key, final long time) {
        return redisTemplate.expire(key, time, TimeUnit.SECONDS);
    }

    /**
     * 设置过期时间
     * @param key  key值
     * @param time 时间戳
     * @return 返回成功
     */
    @Override
    public Boolean expireAt(String key, Date time) {
        return redisTemplate.expireAt(key, time);
    }

    @Override
    public Long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    @Override
    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    @Override
    public Long increaseBy(String key, long delta) {
        return redisTemplate.opsForValue().increment(key, delta);
    }

    @Override
    public Long decreaseBy(String key, long delta) {
        return redisTemplate.opsForValue().increment(key, -delta);
    }

    @Override
    public Object hGet(String key, String hashKey) {
        return (Object) redisTemplate.opsForHash().get(key, hashKey);
    }

    @Override
    public Boolean hSet(String key, String hashKey, Object value, long time) {
        redisTemplate.opsForHash().put(key, hashKey, value);
        return expire(key, time);
    }

    @Override
    public void hSet(String key, String hashKey, Object value) {
        redisTemplate.opsForHash().put(key, hashKey, value);
    }

    @Override
    public Map hGetAllMap(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    @Override
    public List hGetAllList(String key) {
        return redisTemplate.opsForHash().values(key);
    }

    @Override
    public Boolean hSetAll(String key, Map<String, Object> map, long time) {
        redisTemplate.opsForHash().putAll(key, map);
        return expire(key, time);
    }

    @Override
    public void hSetAll(String key, Map<String, ?> map) {
        redisTemplate.opsForHash().putAll(key, map);
    }

    @Override
    public void hDel(String key, Object... hashKey) {
        redisTemplate.opsForHash().delete(key, hashKey);
    }

    @Override
    public Boolean hHasKey(String key, String hashKey) {
        return redisTemplate.opsForHash().hasKey(key, hashKey);
    }

    @Override
    public Long hIncrease(String key, String hashKey, Long delta) {
        return redisTemplate.opsForHash().increment(key, hashKey, delta);
    }

    @Override
    public Long hDecrease(String key, String hashKey, Long delta) {
        return redisTemplate.opsForHash().increment(key, hashKey, -delta);
    }

    @Override
    public Long lSize(String key) {
        return redisTemplate.opsForList().size(key);
    }

    @Override
    public Object lIndex(String key, long index) {
        return redisTemplate.opsForList().index(key, index);
    }

    @Override
    public Long lRemove(String key, long count, Object value) {
        return redisTemplate.opsForList().remove(key, count, value);
    }

    public Long lpushAll(String key, Collection<String> collections){
        return redisTemplate.opsForList().leftPushAll(key, collections);
    }

    @Override
    public Long lpush(String key, String value) {
        return redisTemplate.opsForList().leftPush(key, value);
    }

    @Override
    public Object lpop(String key) {
        return redisTemplate.opsForList().leftPop(key);
    }

    @Override
    public Object lpop(String key, Duration timeout) {
        return redisTemplate.opsForList().leftPop(key, timeout);
    }

    @Override
    public Object rpop(String key) {
        return redisTemplate.opsForList().rightPop(key);
    }

    @Override
    public List<String> lrange(String key) {
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    @Override
    public List<String> lrange(String key, long start, long end) {
        return redisTemplate.opsForList().range(key, start, end);
    }

    public Object rightPopAndLeftPush(String sourceKey, String destinationKey, Duration timeout){
        return redisTemplate.opsForList().rightPopAndLeftPush(sourceKey, destinationKey, timeout);
    }

    @Override
    public void saveMap(String key, Map<String, String> map) {
        redisTemplate.opsForHash().putAll(key, map);
    }

    @Override
    public Map<String, String> getMap(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    @Override
    public Long delete(String key) {
        redisTemplate.delete(key);
        return 1L;
    }

    @Override
    public String lock(String key) {
        return lock(key, 30);
    }

    /**
     * 此方法用于给某个key加锁。适合在分布式负载情况下给 key加锁 ，实现跨虚拟机的的锁机制。
     * <p>
     * 一个锁只能存活30秒, 并且有效期是两秒。所以在调用这个方法的时候必须手动的释放锁。下面的方法是解锁。
     */
    @Override
    public String lock(String key, long expire) {
        try {
            String lockKey = "lock_" + key;
            long threadId = Thread.currentThread().getId();
            boolean lock = redisTemplate.opsForValue().setIfAbsent(lockKey, threadId, expire, TimeUnit.SECONDS);
            if(lock){
                Timer timer = new Timer();
                timer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        if(BooleanUtils.isFalse(redisTemplate.opsForValue().setIfPresent(lockKey, threadId, expire, TimeUnit.SECONDS))){
                            timer.cancel();
                        }
                    }
                }, expire * 1000/2, expire * 1000/2 - 100);
            }
            return lock ? lockKey : null;
        } catch (Exception e) {
            log.error("redis lock exception, key={}", key, e);
        }
        return null;
    }

    /**
     * 此方法用于给某个key加锁。适合在分布式负载情况下给 key加锁 ，实现跨虚拟机的的锁机制。
     */
    @Override
    public String onceLock(String key, long expire) {
        try {
            String lockKey = "lock_" + key;
            long threadId = Thread.currentThread().getId();
            boolean lock = redisTemplate.opsForValue().setIfAbsent(lockKey, threadId, expire, TimeUnit.SECONDS);
            return lock ? lockKey : null;
        } catch (Exception e) {
            log.error("redis lock exception, key={}", key, e);
        }
        return null;
    }

    @Override
    public String tryLock(String key, long expire, long waitTime) {
        String lock;
        AtomicLong total = new AtomicLong(0);
        do{
            lock = lock(key, expire);
            try {
                if(StringUtils.isBlank(lock)){
                    TimeUnit.MILLISECONDS.sleep(1);
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }while (StringUtils.isBlank(lock) || total.addAndGet(1) > waitTime);
        return lock;
    }

    /**
     * 释放锁
     */
    @Override
    public void unLock(String key) {
        Object o = redisTemplate.opsForValue().get(key);
        if(o != null && Thread.currentThread().getId() == Long.parseLong(String.valueOf(o))){
            redisTemplate.expire(key, Duration.ZERO);
        }
    }

    @Override
    public String save(String key, String value) {
        redisTemplate.opsForValue().set(key, value);
        return value;
    }

    public boolean setIfAbsent(String key, String value, long expire){
        return redisTemplate.opsForValue().setIfAbsent(key, value, expire, TimeUnit.SECONDS);
    }

    @Override
    public String get(String key) {
        return (String) redisTemplate.opsForValue().get(key);
    }

    @Override
    public Set<String> keys(String patt) {
        return redisTemplate.keys("*" + patt + "*");
    }

    public String scriptEval(String script, List<String> keys, Object... argv){
        return (String)redisTemplate.execute(RedisScript.of(script, String.class), keys, argv);
    }

    @Override
    public long ttl(String key, TimeUnit timeUnit) {
        return redisTemplate.getExpire(key);
    }
}
