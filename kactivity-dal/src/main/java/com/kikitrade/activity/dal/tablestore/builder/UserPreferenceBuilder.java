package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.UserPreference;

import java.util.List;
import java.util.Map;

/**
 * 用户偏好表数据访问层接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface UserPreferenceBuilder {

    /**
     * 插入用户偏好
     * 
     * @param preference 用户偏好
     * @return 是否成功
     */
    boolean insert(UserPreference preference);

    /**
     * 更新用户偏好
     * 
     * @param preference 用户偏好
     * @return 是否成功
     */
    boolean update(UserPreference preference);

    /**
     * 删除用户偏好
     * 
     * @param userId 用户ID
     * @param prizePoolCode 奖池编码
     * @param preferenceType 偏好类型
     * @return 是否成功
     */
    boolean delete(String userId, String prizePoolCode, String preferenceType);

    /**
     * 根据用户ID和奖池编码查询用户偏好
     * 
     * @param userId 用户ID
     * @param prizePoolCode 奖池编码
     * @return 用户偏好列表
     */
    List<UserPreference> findByUserIdAndPrizePoolCode(String userId, String prizePoolCode);

    /**
     * 根据用户ID、奖池编码和偏好类型查询用户偏好
     * 
     * @param userId 用户ID
     * @param prizePoolCode 奖池编码
     * @param preferenceType 偏好类型
     * @return 用户偏好
     */
    UserPreference findByUserIdAndPrizePoolCodeAndType(String userId, String prizePoolCode, String preferenceType);

    /**
     * 根据用户ID查询所有偏好
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @return 用户偏好列表
     */
    List<UserPreference> findByUserId(String userId);

    /**
     * 获取用户针对特定奖池的偏好Map
     * 
     * @param userId 用户ID
     * @param prizePoolCode 奖池编码
     * @return 偏好Map，key为偏好类型，value为偏好值
     */
    Map<String, String> getUserPreferenceMap(String userId, String prizePoolCode);

    /**
     * 批量设置用户偏好
     * 
     * @param userId 用户ID
     * @param prizePoolCode 奖池编码
     * @param preferences 偏好Map
     * @param saasId SaaS ID
     * @return 是否成功
     */
    boolean batchSetPreferences(String userId, String prizePoolCode, Map<String, String> preferences);
}
