package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * 定向奖励发放流水表
 * 对应技术规格书中的 direct_reward_issuance_log 表
 * 用于记录直接奖励发放的流水信息，支持幂等性处理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Table(name = "direct_reward_issuance_log")
public class DirectRewardIssuanceLog implements Serializable {

    public static final String SEARCH_DIRECT_REWARD_ISSUANCE_LOG = "search_direct_reward_issuance_log";

    /**
     * 外部渠道的唯一交易ID (幂等键)
     */
    @PartitionKey(name = "transaction_id")
    private String transactionId;

    /**
     * 目标用户ID
     */
    @Column(name = "user_id")
    @SearchIndex(name = SEARCH_DIRECT_REWARD_ISSUANCE_LOG, column = "user_id")
    private String userId;

    /**
     * SaaS ID
     */
    @Column(name = "saas_id")
    @SearchIndex(name = SEARCH_DIRECT_REWARD_ISSUANCE_LOG, column = "saas_id")
    private String saasId;

    /**
     * 发放的奖励内容 (JSON格式)
     */
    @Column(name = "rewards_content")
    private String rewardsContent;

    /**
     * 来源渠道/场景Code
     */
    @Column(name = "channel")
    @SearchIndex(name = SEARCH_DIRECT_REWARD_ISSUANCE_LOG, column = "channel")
    private String channel;

    /**
     * 发放状态 (SUCCESS, FAILED, PROCESSING)
     */
    @Column(name = "issue_status")
    @SearchIndex(name = SEARCH_DIRECT_REWARD_ISSUANCE_LOG, column = "issue_status")
    private String issueStatus;

    /**
     * 发放描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 失败原因（如果失败）
     */
    @Column(name = "failure_reason")
    private String failureReason;

    /**
     * 客户端IP
     */
    @Column(name = "client_ip")
    private String clientIp;

    /**
     * 设备信息
     */
    @Column(name = "device_info")
    private String deviceInfo;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_DIRECT_REWARD_ISSUANCE_LOG, column = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    private Long updateTime;
}
