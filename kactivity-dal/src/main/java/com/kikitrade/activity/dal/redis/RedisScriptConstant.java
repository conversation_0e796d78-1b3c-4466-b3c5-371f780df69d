package com.kikitrade.activity.dal.redis;

public class RedisScriptConstant {

    /**
     * 领取空投脚本
     * 1：待领取空投队列 2：个人待激活空投
     * 3：已领取空投队列 4：空投过期事件
     * 5：领取的空投过期时间 6：领取人
     * 7：当前时间
     */
    public static final String RECEIVE_SCRIPT = "do\n" +
            "    local notDrawKey = tostring(KEYS[1]);\n" +
            "    local receiveDetailKey = tostring(KEYS[2]);\n" +
            "    local releaseDrewKey = tostring(KEYS[3]);\n" +
            "    local itemExpiredTime = tonumber(KEYS[4]);\n" +
            "    local expiredTime = tostring(KEYS[5]);\n" +
            "    local receiveCode = tostring(KEYS[6]);\n" +
            "    local nowTime = tostring(KEYS[7]);\n" +
            "\n" +
            "    --检查是否已经抢过\n" +
            "    if redis.call('hexists', receiveDetailKey, cjson.encode(receiveCode)) ~= 0 then\n" +
            "        -- 1 已经抢过\n" +
            "        return cjson.encode('exist')\n" +
            "    end\n" +
            "    local r = redis.call('rpop', notDrawKey)\n" +
            "    if not r then\n" +
            "        --0 已抢完\n" +
            "        return cjson.encode('finish')\n" +
            "    end\n" +
            "\n" +
            "    -- 记录用户抢购信息\n" +
            "    local item = cjson.decode(cjson.decode(r))\n" +
            "    item['receiveCode'] = receiveCode\n" +
            "    item['receiveTime'] = nowTime\n" +
            "    item['expiredTime'] = expiredTime\n" +
            "    local k = cjson.encode(cjson.encode(item))\n" +
            "    redis.call('hset', receiveDetailKey, cjson.encode(receiveCode), k)\n" +
            "    redis.call('lpush', releaseDrewKey, k)\n" +
            "    if itemExpiredTime > 0 then\n" +
            "        redis.call('expire', receiveDetailKey, itemExpiredTime)\n" +
            "        redis.call('expire', releaseDrewKey, itemExpiredTime)\n" +
            "    end\n" +
            "    return k\n" +
            "end";

    /**
     * 空投回退脚本
     * 1：待领取空投队列 2：个已领取空投
     * 3：已领取空投队列 4：领取人
     * 5：过期的空投
     */
    public static final String RECEIVE_REVERT_SCRIPT = "do\n" +
            "    local notDrawKey = tostring(KEYS[1]);\n" +
            "    local receiveDetailKey = tostring(KEYS[2]);\n" +
            "    local releaseDrewKey = tostring(KEYS[3]);\n" +
            "    local receiveCode = tostring(KEYS[4]);\n" +
            "    local expireDrewKey = tostring(KEYS[5]);\n" +
            "\n" +
            "    --检查是否已经抢过\n" +
            "    if redis.call('hexists', receiveDetailKey, cjson.encode(receiveCode)) ~= 1 then\n" +
            "        -- 1 \n" +
            "        return cjson.encode('not_exist')\n" +
            "    end\n" +
            "    -- 记录用户抢购信息\n" +
            "    local r = redis.call('hget', receiveDetailKey, cjson.encode(receiveCode))\n" +
            "    redis.call('hdel', receiveDetailKey, cjson.encode(receiveCode))\n" +
            "    redis.call('lrem', releaseDrewKey, 0, r)\n" +
            "    local item = cjson.decode(cjson.decode(r))\n" +
            "    item['receiveCode'] = ''\n" +
            "    item['receiveTime'] = ''\n" +
            "    item['expiredTime'] = ''\n" +
            "    local k = cjson.encode(cjson.encode(item))\n" +
            "    redis.call('lpush', notDrawKey, k)\n" +
            "    return cjson.encode('finish')\n" +
            "end";
}
