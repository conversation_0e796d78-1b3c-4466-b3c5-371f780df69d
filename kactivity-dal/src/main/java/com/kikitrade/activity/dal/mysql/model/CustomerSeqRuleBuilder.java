package com.kikitrade.activity.dal.mysql.model;

import com.kikitrade.kseq.api.model.SeqRule;
import com.kikitrade.kseq.api.model.SeqRuleBuilder;

public class CustomerSeqRuleBuilder extends SeqRuleBuilder {

    private static final String APP_CODE = "kactivity";
    private static final String DEFAULT_PREFIX = "yyyyMMddHHmmssSSS";

    public static final SeqRule RULE_BATCH_TOKEN = new CustomerSeqRuleBuilder(String.format("%s:%s", APP_CODE, "batch_token"), DEFAULT_PREFIX).build();
    public static final SeqRule RULE_TABLE_REWARD_BUSINESS = new CustomerSeqRuleBuilder(String.format("%s:%s", APP_CODE, "table_business"), "").build();
    public static final SeqRule ODPS_SEQ = new CustomerSeqRuleBuilder(String.format("%s:%s", APP_CODE, "odps_seq"), "").build();

    private final String type;
    private final String prefix;

    public CustomerSeqRuleBuilder(String type, String prefix){
        this.type = String.format("%s:%s",APP_CODE,type);
        this.prefix = prefix;
    }

    @Override
    public String type() {
        return type;
    }

    @Override
    public int interval() {
        return 200;
    }

    @Override
    public String rulePrefix() {
        return prefix;
    }

    @Override
    public String[] ruleParts() {
        return new String[0];
    }

    public static SeqRule instance(String type){
        return new CustomerSeqRuleBuilder(type, DEFAULT_PREFIX).build();
    }

    public static SeqRule instance(String type, String prefix){
        return new CustomerSeqRuleBuilder(type, prefix).build();
    }
}
