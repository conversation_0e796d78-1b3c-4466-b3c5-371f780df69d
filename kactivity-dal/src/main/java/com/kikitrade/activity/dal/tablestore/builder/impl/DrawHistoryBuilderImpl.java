package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.query.RangeQuery;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.activity.dal.tablestore.builder.DrawHistoryBuilder;
import com.kikitrade.activity.dal.tablestore.model.DrawHistory;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 抽奖历史记录表数据访问层实现
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class DrawHistoryBuilderImpl extends WideColumnStoreBuilder<DrawHistory> implements DrawHistoryBuilder {

    @PostConstruct
    public void init() {
        super.init(DrawHistory.class);
    }

    @Override
    public boolean insert(DrawHistory drawHistory) {
        try {
            if (drawHistory.getCreateTime() == null) {
                drawHistory.setCreateTime(System.currentTimeMillis());
            }
            return super.putRow(drawHistory, RowExistenceExpectation.EXPECT_NOT_EXIST);
        } catch (Exception e) {
            log.error("插入抽奖历史记录失败: userId={}, eventId={}", 
                     drawHistory.getUserId(), drawHistory.getEventId(), e);
            return false;
        }
    }

    @Override
    public boolean batchInsert(List<DrawHistory> drawHistories) {
        try {
            if (drawHistories == null || drawHistories.isEmpty()) {
                log.warn("批量插入抽奖历史记录失败: 记录列表为空");
                return false;
            }

            // 设置创建时间
            long currentTime = System.currentTimeMillis();
            for (DrawHistory drawHistory : drawHistories) {
                if (drawHistory.getCreateTime() == null) {
                    drawHistory.setCreateTime(currentTime);
                }
            }

            log.info("开始批量插入抽奖历史记录: 记录数量={}", drawHistories.size());
            boolean result = super.batchPutRow(drawHistories, RowExistenceExpectation.EXPECT_NOT_EXIST);
            
            if (result) {
                log.info("批量插入抽奖历史记录成功: 记录数量={}", drawHistories.size());
            } else {
                log.error("批量插入抽奖历史记录失败: 记录数量={}", drawHistories.size());
            }
            
            return result;
        } catch (Exception e) {
            log.error("批量插入抽奖历史记录异常: 记录数量={}", 
                     drawHistories != null ? drawHistories.size() : 0, e);
            return false;
        }
    }

    @Override
    public TokenPage<DrawHistory> findByUserId(String userId, String nextToken, int limit) {
        try {
            BoolQuery boolQuery = QueryBuilders.bool()
                    .must(QueryBuilders.term("user_id", userId))
                    .build();
            
            Sort sort = new Sort(Collections.singletonList(new FieldSort("draw_time", SortOrder.DESC)));
            return pageSearchQuery(boolQuery, sort, nextToken, limit, DrawHistory.SEARCH_DRAW_HISTORY);
        } catch (Exception e) {
            log.error("根据用户ID查询抽奖历史记录失败: userId={}", userId, e);
            return new TokenPage<>();
        }
    }

    @Override
    public TokenPage<DrawHistory> findByBatchTransactionId(String batchTransactionId, String nextToken, int limit) {
        try {
            BoolQuery boolQuery = QueryBuilders.bool()
                    .must(QueryBuilders.term("batch_transaction_id", batchTransactionId))
                    .build();
            
            Sort sort = new Sort(Collections.singletonList(new FieldSort("draw_time", SortOrder.DESC)));
            return pageSearchQuery(boolQuery, sort, nextToken, limit, DrawHistory.SEARCH_DRAW_HISTORY);
        } catch (Exception e) {
            log.error("根据批量交易ID查询抽奖历史记录失败: batchTransactionId={}", batchTransactionId, e);
            return new TokenPage<>();
        }
    }

    @Override
    public TokenPage<DrawHistory> findByUpstreamTransactionId(String upstreamTransactionId, String nextToken, int limit) {
        try {
            BoolQuery boolQuery = QueryBuilders.bool()
                    .must(QueryBuilders.term("upstream_transaction_id", upstreamTransactionId))
                    .build();
            
            Sort sort = new Sort(Collections.singletonList(new FieldSort("draw_time", SortOrder.DESC)));
            return pageSearchQuery(boolQuery, sort, nextToken, limit, DrawHistory.SEARCH_DRAW_HISTORY);
        } catch (Exception e) {
            log.error("根据上游交易ID查询抽奖历史记录失败: upstreamTransactionId={}", upstreamTransactionId, e);
            return new TokenPage<>();
        }
    }

    @Override
    public TokenPage<DrawHistory> findByPrizePoolCode(String prizePoolCode, String saasId, String nextToken, int limit) {
        try {
            BoolQuery boolQuery = QueryBuilders.bool()
                    .must(QueryBuilders.term("prize_pool_code", prizePoolCode))
                    .must(QueryBuilders.term("saas_id", saasId))
                    .build();
            
            Sort sort = new Sort(Collections.singletonList(new FieldSort("draw_time", SortOrder.DESC)));
            return pageSearchQuery(boolQuery, sort, nextToken, limit, DrawHistory.SEARCH_DRAW_HISTORY);
        } catch (Exception e) {
            log.error("根据奖池编码查询抽奖历史记录失败: prizePoolCode={}, saasId={}", prizePoolCode, saasId, e);
            return new TokenPage<>();
        }
    }

    @Override
    public TokenPage<DrawHistory> findByUserIdAndTimeRange(String userId, Long startTime, Long endTime, String nextToken, int limit) {

       return null;
    }

    @Override
    public DrawHistory findByPrimaryKey(String userId, String eventId) {
        try {
            DrawHistory drawHistory = new DrawHistory();
            drawHistory.setUserId(userId);
            drawHistory.setEventId(eventId);
            return super.getRow(drawHistory);
        } catch (Exception e) {
            log.error("根据主键查询抽奖历史记录失败: userId={}, eventId={}", userId, eventId, e);
            return null;
        }
    }
}
