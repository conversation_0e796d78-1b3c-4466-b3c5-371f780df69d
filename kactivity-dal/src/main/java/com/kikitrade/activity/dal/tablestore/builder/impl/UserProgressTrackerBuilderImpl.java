package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.activity.dal.tablestore.builder.UserProgressTrackerBuilder;
import com.kikitrade.activity.dal.tablestore.model.UserProgressTracker;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/9/3 10:10
 * @description:
 */
@Slf4j
@Component
public class UserProgressTrackerBuilderImpl extends WideColumnStoreBuilder<UserProgressTracker> implements UserProgressTrackerBuilder {

    @PostConstruct
    public void init() {
        super.init(UserProgressTracker.class);
    }

    /**
     * 插入用户进度
     *
     * @param userProgressTracker 用户进度
     * @return 是否成功
     */
    @Override
    public boolean insert(UserProgressTracker userProgressTracker) {
        userProgressTracker.setCreateTime(System.currentTimeMillis());
        userProgressTracker.setUpdateTime(System.currentTimeMillis());
        return super.putRow(userProgressTracker, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    /**
     * 更新用户进度
     *
     * @param userProgressTracker 用户进度
     * @return 是否成功
     */
    @Override
    public boolean update(UserProgressTracker userProgressTracker) {
        userProgressTracker.setUpdateTime(System.currentTimeMillis());
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        try {
            return super.updateRow(userProgressTracker, condition);
        } catch (Exception e) {
            log.error("更新用户进度失败: userId={}, prizePoolCode={}",
                     userProgressTracker.getUserId(), userProgressTracker.getPrizePoolCode(), e);
        }
        return false;
    }

    /**
     * 根据用户ID和奖池编码查询用户进度
     *
     * @param userId        用户id
     * @param prizePoolCode 奖池code
     * @return 用户进度
     */
    @Override
    public UserProgressTracker findByUserIdAndPrizePoolCode(String userId, String prizePoolCode) {
        try {
            //这里需要先查找，userId+prizePoolCode查询，按照createTime倒序排列,获取第一条数据
            BoolQuery boolQuery = QueryBuilders.bool()
                    .must(QueryBuilders.term("user_id", userId))
                    .must(QueryBuilders.term("prize_pool_code", prizePoolCode))
                .build();
            Sort sort = new Sort(Arrays.asList(new FieldSort("cycle_start_time", SortOrder.DESC)));

            Page<UserProgressTracker> userProgressTrackerPage =
                pageSearchQuery(boolQuery, sort, 0, 1, UserProgressTracker.SEARCH_USER_PROGRESS_TRACKER);

            return userProgressTrackerPage.getRows().get(0);
        } catch (Exception e) {
            log.error("查询用户进度失败: userId={}, prizePoolCode={}", userId, prizePoolCode, e);
        }
        return null;
    }

}
